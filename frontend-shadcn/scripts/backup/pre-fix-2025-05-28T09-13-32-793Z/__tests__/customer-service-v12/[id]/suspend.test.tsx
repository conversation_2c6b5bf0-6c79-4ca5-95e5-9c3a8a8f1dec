import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Suspend } from '@/components/customer-service-v12/[id]/suspend';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Suspend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Suspend />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Suspend')).toBeInTheDocument();
  });
});