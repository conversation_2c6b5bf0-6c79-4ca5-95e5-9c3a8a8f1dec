<?php
/**
 * This file manages the invoices on fooddialer system
 * The payment status of invoices are managed through this file
 * Invoice bill can be paid through this file
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: InvoiceController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use DOMPDFModule\View\Model\PdfModel;

use Lib\Utility;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class InvoiceController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\InvoiceTable model
	 *
	 * @var QuickServe\Model\InvoiceTable $frontTable
	 */
	protected $invoicetable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderTable $ordertable
	 */
	protected $ordertable;
	/**
	 * This function displays the list of invoices
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
        
		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		//echo "kitchen data<pre>"; print_r($kitchen_data); exit();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$view = $this->params()->fromRoute('view');
		 
		$iden = $this->authservice->getIdentity();

		$select = new QSelect();
		$collection = $this->getInvoiceTable()->fetchAll();
		
		//echo "<pre>";print_r($collection);die;	
		if($this->request->isPost()){
		    $params = $this->params()->fromPost();
		   
		    if(isset($params['subaction'])){
		        switch($params['subaction']){
		            
		        	case "print":
		        	case "export":
		        	    return $this->forward()->dispatch('Admin\Controller\Invoice', array(
                            'printData' => $collection,
                            'service' => $params['service'],
                            'action' => 'printReport',
                            'subaction' => $params['subaction'],
                        ));
		        	    break;
		        	case "search";
		        	    break;         
		            
		        }
		    }
		}		
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Invoice",'description'=>"Raised Invoice Details",'breadcrumb'=>"Invoice"));
		return new ViewModel(array(
				'paginator' => $collection,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'view'=>$view,
				'kitchen_data' =>$kitchen_data
		));
	}
	
	
	
	public function ajxInvoiceAction(){
	
	    error_reporting(E_ALL);
	    ini_set('display_errors','On');
	    if (! $this->authservice)
	    {
	        $this->authservice = $this->getServiceLocator()->get('AuthService');
	    }
	    
	    $iden = $this->authservice->getIdentity();
	    
	    $session_setting = new Container("setting");
	    $setting = $session_setting->setting;
	    $status = $this->params()->fromQuery('status');
	    $fromdate = $this->params()->fromQuery('minDate');
	    $todate = $this->params()->fromQuery('maxDate');
	    
	    
	    $utility = Utility::getInstance();
	     
	    $layout = $this->layout();
	    $acl = $layout->acl;
	    
	    $viewModel = new ViewModel();
	    
	    $loggedUser = $layout->loggedUser;
	    
	    $select = new QSelect();
	     
	    //echo $view;die;
	    
	    //$order_by = $this->params()->fromRoute('order_by') ? $this->params()->fromRoute('order_by') : 'pk_order_no';
	     
	    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
	    $arrColumns = array('0'=>'invoice_id','1'=>'invoice_no','2'=>'cust_name','3'=>'phone','4'=>"invoice_amount","5"=>"date","6"=>"status");
	     
	    $order_by = $arrColumns[$arrOrder[0]['column']];
	    $order = $arrOrder[0]['dir'];
	    
	    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
	    
	    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
	    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
	    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
	    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
	    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	    
	    
	    //$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
	    $kitchenscreen = $_SESSION['adminkitchen'];
	    
	    //echo $kitchenscreen; die;
	    
	    $columns = $this->params()->fromQuery('columns');
	     
	    if(isset($search) && $search !=""){
	         
	        $select->where(
	    
	            new \Zend\Db\Sql\Predicate\PredicateSet(
	                array(
	                    new \Zend\Db\Sql\Predicate\Operator('invoice_no', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('cust_name', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('invoice_amount', 'LIKE', '%'.$search.'%'),
	                ),
	                // optional; OP_AND is default
	                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
	            )
	             
	        );
	         
	    }
	    
	    if($status !=""){
	    	
	    	$select->where('invoice.status = '.$status,\Zend\Db\Sql\Predicate\PredicateSet::OP_AND);
	    	
	    }
	    
	    if($kitchenscreen!='all')
	    {
	    	$select->where("invoice.fk_kitchen_code = $kitchenscreen");
	    }else{
            $select->where->in('invoice.fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
	    
	    if(isset($fromdate) && $fromdate!='')
	    {
	    	$fromdate = date('Y-m-d',strtotime($fromdate));
	    	$select->where("invoice.date >= '$fromdate'");
	    }
	     
	    if(isset($todate) && $todate!='')
	    {
	    	$todate = date('Y-m-d',strtotime($todate));
	    	$select->where("invoice.date <= '$todate'");
	    }
    
	    $select->order($order_by . ' ' . $order);
	     
	    $invoices = $this->getInvoiceTable()->fetchAll($select,$page);
	    
// 	    echo "<pre>"; print_r($invoices); exit();
	     
	    $invoices->setCurrentPageNumber($page)
	    ->setItemCountPerPage($itemsPerPage)
	    ->setPageRange(7);
	    
	    $returnVar = array();
	    $returnVar['draw'] = $draw;
	    $returnVar['recordsTotal'] = $invoices->getTotalItemCount();
	    $returnVar['recordsFiltered'] = $invoices->getTotalItemCount();
	    $returnVar['data'] = array();
	    
	    foreach($invoices as $invoice){
// 	    	echo "<pre>"; print_r($invoice); exit();
	        //echo $customer->customer_name."<br />";
	        $arrTmp = array();
	        
	        $strInput = '<input type="checkbox" class="case sel_invoice"  name="selectinvoice[]" id="'.$invoice['invoice_id'].'" value="'.$invoice['invoice_id'].'" />';
	        
	        array_push($arrTmp,$strInput);
	        
	        //array_push($arrTmp,"fsdfsdfsdf");
	        
	        array_push($arrTmp,$invoice['invoice_no']);
	        array_push($arrTmp,$invoice['cust_name']);
// 	        array_push($arrTmp,$invoice['customer_address']);
	       // array_push($arrTmp,$invoice['phone']);
	      
	       // array_push($arrTmp,"");
	        //array_push($arrTmp,$invoice['invoice_amount']);
	        
	        //$strInvoiceAmount = '<i class="fa fa-rupee"></i>'.$invoice['invoice_amount']."/-";
	        
	        $strInvoiceAmount = $utility->getLocalCurrency($invoice['invoice_amount'])."/-";

	        array_push($arrTmp,$strInvoiceAmount);
	        //array_push($arrTmp,$invoice['date']);
	        array_push($arrTmp,$utility->displayDate($invoice['date'],$setting['DATE_FORMAT']));
	        $strInvoice = '<span class="'.( ( $invoice['status']=="1") ?'paid':'unpaid').'">'.( ( $invoice->status=="1" ) ? 'Paid':'Unpaid').'</span>';
	        
	        array_push($arrTmp,$strInvoice);
	        
	        array_push($returnVar['data'],$arrTmp);
	    }
	     
	    return new JsonModel($returnVar);
	    
	}
	
	/**
	 * This function displays the invoice of using invoice id in PDF format
	 * The invoice viewed in pdf format & it can be print through printer
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function billAction()
	{
		$invoice_id = $_POST['selectinvoice'];
		$invoice_data = $this->getInvoiceTable()->getInvoice($invoice_id);
		$config = $this->getServiceLocator()->get("config");
		$newArray = array();
		foreach ($invoice_data as $invoice)
		{
			$temp_Array = array();
			$temp_Array = $invoice;
			$temp_Array['bill'] =  $this->getInvoiceTable()->getBill($invoice['invoice_id']);
			$temp_Array['payment'] =  $this->getInvoiceTable()->getPayment($invoice['invoice_id']);
			$temp_Array['discount'] =  $this->getInvoiceTable()->getDiscounts($invoice['invoice_id']);
			$temp_Array['taxes'] =  $this->getInvoiceTable()->getTaxes($invoice['invoice_id']);
			$temp_Array['paymentsummary'] =  $this->getInvoiceTable()->getPaymentSummary($invoice['invoice_id']);
			$newArray[] = $temp_Array;
		}
			$view = new ViewModel(array(
				'invoices'=> $newArray,
				'config'=>$config
	
		));

 		//echo "<pre>"; print_r($view); die;	
			
		$view->setTerminal(true);
		return $view;
	}

	/**
	 * This function used to view multiple invoice in pdf format
	 * The invoices are viewed one by one
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function printcollectionlistAction(){
		$invoices = $this->getInvoiceTable()->get_invoice_payments();
	
		$customer_invoices_array=array();
		$customer_main = array();
		foreach($invoices as $invoice){
			//$customer_invoices_array = array();
			$customer_invoices_array[$invoice['cust_name']]['sum'] +=$invoice['amount_due'];
			$customer_invoices_array[$invoice['cust_name']]['customer_Address']=$invoice['customer_Address'];
			$customer_invoices_array[$invoice['cust_name']]['company_name']=$invoice['company_name'];
			$customer_invoices_array[$invoice['cust_name']]['phone']=$invoice['phone'];
			//$customer_main[] = $customer_invoices_array;
		}
		//echo "<pre>";print_r($customer_main);exit;
		$view = new ViewModel(array(
				'customer_invoices'=> $customer_invoices_array,
		));
		$view->setTerminal(true);
		return $view;
	}
	/**
	 * Get instance of QuickServe\Model\InvoiceTable
	 *
	 * @return QuickServe\Model\InvoiceTable
	 */
	public function getInvoiceTable()
	{
		if (!$this->invoicetable)
		{
			$sm = $this->getServiceLocator();
			$this->invoicetable = $sm->get('QuickServe\Model\InvoiceTable');
		}
		return $this->invoicetable;
	}
	
	/**
	 * Print report as list or export it in pdf format.
	 */
	public function printReportAction(){
	    
	    $data = $this->params()->fromRoute('printData');
	    $service = $this->params()->fromRoute('service');
	    $subAction = $this->params()->fromRoute('subaction');
	    // $service - sales , invoice , orders , collection 
	    
	    $columns = array(
	    	
	        'invoice'=>array('Invoice No','Name','Address','Contact No','Total Amount','Status'),
	        'sales'=>array('Order No','Customer Name','Group','Phone','Delivery Location','Product','Quantity','Amount','Order Status'),
	        'order'=>array(''),
	        'collection'=>array()
	        
	    );
	    
	    switch($subAction){
	        
	    	case "export":
	    	    
        	    $model = new PdfModel();
        	    $model->setOption('fileName', 'invoice-45');
        	    $model->setOption('paperSize', 'A4');
        	    $model->setOption('paperOrientation', 'portrait');
        	    $sm = $this->getServiceLocator();
        	    $config = $sm->get('config');
        	    
        	    $variables = array();
        	    
        	    $variables = $this->getRequest()
        	    ->getPost()
        	    ->toArray();
        	    
        	    $variables['root_url'] = $config['root_url'];
        	    
        	    $model->setVariables($variables);
        	    $model->setTemplate('admin/report/templates/'.$service);
        	    return $model;
        	    
	        break;
	        
	        case "print":
	            
	            $view = new ViewModel();
	            $view->setTemplate('admin/report/templates/'.$service);
	            $view->setTerminal(true);
	            return $view;	            
	            
	        break;
	    }
	    
	}
	
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
}
