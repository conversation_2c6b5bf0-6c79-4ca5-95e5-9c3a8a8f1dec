
	<script type="text/javascript" src="/admin/js/plugins/jquery.flot.min.js"></script>
		<script type="text/javascript" src="/admin/js/plugins/jquery.flot.resize.min.js"></script>
<!--<script type="text/javascript" src="/admin/js/custom/dashboard.js"></script>  -->
		   <script type="text/javascript" src="https://www.google.com/jsapi"></script>
		 <script>
		 <?php if(is_array($products_orders) && (count($products_orders) > 0) ) { ?>

		  google.load("visualization", "1", {packages:["corechart"]});
	      google.setOnLoadCallback(drawChart);
	      function drawChart() {
	        var data = google.visualization.arrayToDataTable([
	          ['Products', 	'Orders' ,{ role: 'style' },'Prepared',{ role: 'style' }],
	          <?php
	          foreach ($products_orders as $ord)
	          {
				echo '["'.$ord['name'].'" , '.$ord['total_order'].'  ,"#9E1C20" , '.$ord['prepared'].' , "#339933" ] ,';
	          }
	          ?>

	        ]);

	        var options = {
	          title: 'Kitchen Overview',
	          hAxis: {title: 'Products', titleTextStyle: {color: '#9E1C20'}} ,
	          series: [{color: '#9E1C20', visibleInLegend: true}, {color: '#339933', visibleInLegend: true}]
	        };

	        var chart = new google.visualization.ColumnChart(document.getElementById('chart_div'));
	        chart.draw(data, options);
	      }
		jQuery(document).ready(function(){



			//datepicker
			jQuery('#datepicker').datepicker();

			//show tabbed widget
			jQuery('#tabs').tabs();




				//get data from server and inject it next to row
		jQuery('.stdtable a.toggle').click(function(){

			//show all hidden row and remove all showed data
			jQuery(this).parents('table').find('tr').each(function(){
				jQuery(this).removeClass('hiderow');
				if(jQuery(this).hasClass('togglerow'))
					jQuery(this).remove();
			});

			var parentRow = jQuery(this).parents('tr');
			var numcols = parentRow.find('td').length + 1;				//get the number of columns in a table. Added 1 for new row to be inserted
			var url = jQuery(this).attr('href');

			//this will insert a new row next to this element's row parent
			parentRow.after('<tr class="togglerow"><td colspan="'+numcols+'"><div class="toggledata"></div></td></tr>');

			var toggleData = parentRow.next().find('.toggledata');

			parentRow.next().hide();

			//get data from server
			jQuery.post(url,function(data){
				toggleData.append(data);						//inject data read from server
				parentRow.next().fadeIn();						//show inserted new row
				parentRow.addClass('hiderow');					//hide this row to look like replacing the newly inserted row
			});

			return false;
		});

		jQuery('.toggledata button.cancel, .toggledata button.submit').live('click',function(){
			jQuery(this).parents('.toggledata').animate({height: 0},200, function(){
				jQuery(this).parents('tr').prev().removeClass('hiderow');
				jQuery(this).parents('tr').remove();
			});
			return false;
		});

		/***** WIDGET LIST HOVER *****/
		jQuery('.widgetlist a').hover(function(){
			jQuery(this).switchClass('default', 'hover');
		},function(){
			jQuery(this).switchClass('hover', 'default');
		});

	});
		<?php } ?>
		</script>
<ul class="maintabmenu">
          <li class="current"><a href="./dashboard.html">Dashboard</a></li>
        </ul>
        <!--maintabmenu-->

        <div class="content">
          <ul class="widgetlist">
            <li class="orange"> <a href="#" class="order">
              <h1> New Order</h1>
              <h2><?php echo (int) $orders['new']; ?></h2>
              </a> </li>
            <li class="carrot"> <a href="#" class="message">
              <h1>Order In Process</h1>
              <h2><?php echo (int)$orders['in_process']; ?></h2>
              </a> </li>
            <li class="pumpkin"> <a href="#" class="upload">
              <h1>Delievered Orders</h1>
              <h2><?php echo (int)$orders['delivered']; ?></h2>
              </a> </li>
            <li class="last alizarin"> <a href="#" class="events">
              <h1>
             Total Amount Due
              <h1>
              <h2><?php //echo setlocale(LC_MONETARY, 'en');
					 echo 'INR '. @number_format($orders['unpaid'],2); ?> </h2>
              </a> </li>
          </ul>
          <br clear="all" />
          <br />
          <div class="widgetbox">
            <div class="title">
              <h2 class="chart"><span>Today's Orders</span></h2>
            </div>
            <div class="chartbox widgetcontent">
              <!--  <div id="chartplace" class="chartplace"></div>-->
				 <div id="chart_div" style="width: 1300px; height: 500px;">
				  <?php if(empty($products_orders)) { ?>
				  	<font color="red"> No Products </font>
				  <?php } ?>
				 </div>
            </div>
            <!--widgetcontent-->
          </div>
          <!--widgetbox-->
        </div>
        <!--content-->