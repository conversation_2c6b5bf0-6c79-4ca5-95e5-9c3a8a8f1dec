# CubeOneBiz Codebase Index

This directory contains the codebase index for the CubeOneBiz application. The index is a comprehensive catalog of all code files in the repository, classified by their type and containing extracted code symbols.

## Index Structure

The main index file is `oneapp-index.json`, which has the following structure:

```json
{
  "metadata": {
    "generated_at": "2023-06-15 12:34:56",
    "version": "1.0.0"
  },
  "statistics": {
    "total_files": 11147,
    "legacy_zend_files": 5169,
    "laravel_service_files": 1735,
    "next_mfe_files": 2968,
    "unclassified_files": 1275
  },
  "files": {
    "path/to/file.php": {
      "classification": "LEGACY_ZEND",
      "symbols": {
        "classes": ["ClassName1", "ClassName2"],
        "interfaces": ["InterfaceName"],
        "traits": ["TraitName"],
        "functions": ["functionName"]
      },
      "extension": "php",
      "size": 1234
    },
    // More files...
  }
}
```

## Classification Labels

Each file in the index is classified with exactly one of the following labels:

- `LEGACY_ZEND`: Zend Framework code (deprecated)
- `LARAVEL_SERVICE`: Laravel 12 microservices
- `NEXT_MFE`: Next.js micro-frontend applications
- `UNCLASSIFIED`: Files that don't match any of the above classifications

## Extracted Symbols

Depending on the file type and classification, the following symbols may be extracted:

### LEGACY_ZEND
- `classes`: PHP class names
- `interfaces`: PHP interface names
- `traits`: PHP trait names
- `functions`: PHP function names

### LARAVEL_SERVICE
- `classes`: PHP class names
- `interfaces`: PHP interface names
- `models`: Laravel model names
- `controllers`: Laravel controller names
- `routes`: Laravel route paths

### NEXT_MFE
- `components`: React component names
- `hooks`: React hook names
- `page`: Boolean indicating if the file is a Next.js page

## Usage

### Regenerating the Index

To regenerate the index, run the following command from the repository root:

```bash
./scripts/reindex-codebase.sh
```

### Filtering the Index

You can use the index to filter files by classification, for example:

```php
$index = json_decode(file_get_contents('.ide/oneapp-index.json'), true);

// Get all Laravel service files
$laravelFiles = array_filter($index['files'], function($file) {
    return $file['classification'] === 'LARAVEL_SERVICE';
});

// Get all Next.js components
$nextComponents = array_filter($index['files'], function($file) {
    return $file['classification'] === 'NEXT_MFE' && 
           isset($file['symbols']['components']);
});
```

## Important Notes

- The `/legacy-zend/**` codebase must be treated as read-only reference
- Do not apply any refactoring, linting, or code modifications to files tagged as `LEGACY_ZEND`
- Only analyze and index the legacy code; do not attempt to modernize it
