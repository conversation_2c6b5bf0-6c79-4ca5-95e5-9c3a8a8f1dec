import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Admin[id]GenerateCode } from '@/components/admin-service-v12/[id]/generate-code';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Admin[id]GenerateCode', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Admin[id]GenerateCode />
      </QueryClientProvider>
    );

    expect(screen.getByText('Admin[id]GenerateCode')).toBeInTheDocument();
  });
});