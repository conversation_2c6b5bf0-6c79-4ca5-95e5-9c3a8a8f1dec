<?php
/**
 * This File provides the input fields which needs to create add & update tax
 *
 * PHP versions 7.0
 *
 * Project name FoodDialer
 * @version 1.1: TaxForm.php 2017-07-12 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 4.0.0
 */
namespace Admin\Form;

use Zend\Form\Element;
use Zend\Form\Form;
use Lib\QuickServe\Db\Sql\QSql;

class TaxForm extends Form
{

    private $service_locator;

    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');

        $this->add(array(
			'name' => 'tax_id',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes' => array(
					'id' => 'tax_id',
			),
			'options' => array(
			),
        ));
        
         $this->add(array(
            'name' => 'tax_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'tax_name',
                'placeholder' => 'Enter Tax Name',
                'required' => 'required',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Tax Name<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'tax',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'tax',
                'placeholder' => 'Enter Tax',
                'required' => 'required',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Tax<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        $this->add(array(
            'name' => 'base_amount',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'base_amount',
                'placeholder' => 'Enter Base Amount',
                'required' => 'required',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Base Amount<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
			'name'=>'tax_type',
			'type' => 'Zend\Form\Element\Radio',
			'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'tax_type'
			),
			'options'=>array(
				'label'=>'Tax Type <span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline mr5',
				),                      
				'value_options' => array(
					array(
						'value' => 'percent',
						'label' => 'Percent',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
						'selected' => true,
					),
					array(
						'value' => 'fixed',
						'label' => 'Fixed',
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
				),
				'label_options' => array('disable_html_escape' => true),
			)
        ));

        $this->add(array(
                'name'=>'apply_all_product',
                'type' => 'Zend\Form\Element\Radio',
                'attributes'=>array(
					'class'=>'selectpicker small',
					'id' => 'apply_all_product'
                ),
                'options'=>array(
					'label'=>'Apply To All Product <span class="red">*</span>',
					'label_attributes' => array(
						'class' => 'inline mr5',
					),                      
					'value_options' => array(
						array(
							'value' => 'yes',
							'label' => 'Yes',
							'label_attributes' => array(
								'class'=>"left mr5"
							),                                      
							'selected' => true,
						),
						array(
							'value' => 'no',
							'label' => 'No',
							'label_attributes' => array(
								'class'=>"left mr5"
							),                                      
						),
					),
					'label_options' => array('disable_html_escape' => true),
                )
        ));

        $this->add(array(
			'name'=>'tax_on',
			'type' => 'Zend\Form\Element\Radio',
			'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'tax_on'
			),
			'options'=>array(
				'label'=>'Tax On <span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline mr5',
				),                      
				'value_options' => array(
					array(
						'value' => 'food',
						'label' => 'Food',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
						'selected' => true,
					),
					array(
						'value' => 'service',
						'label' => 'Service',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
					),
					array(
						'value' => 'delivery',
						'label' => 'Delivery',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
					),
				),
				'label_options' => array('disable_html_escape' => true),
			)
        ));

        $this->add(array(
			'name'=>'apply_for_catalog',
			'type' => 'Zend\Form\Element\Radio',
			'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'apply_for_catalog'
			),
			'options'=>array(
				'label'=>'Apply For Catalog <span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline mr5',
				),                      
				'value_options' => array(
					array(
						'value' => '1',
						'label' => 'Yes',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
						'selected' => true,
					),
					array(
						'value' => '0',
						'label' => 'No',
						'label_attributes' => array(
							'class'=>"left mr5"
						),                                      
					),
				),
				'label_options' => array('disable_html_escape' => true),
			)
        ));


        $this->add(array(
			'name' => 'date_effective_from',
			'type' => 'Zend\Form\Element\Text',
			'attributes' => array(
				'class'=> 'left&#x20;filterSelect calender',
				'id'=>'maxDate',
				'placeholder' => 'Enter Start Date',
				'autofocus' => true
			),
			'options' => array(
				'label' => 'Start Date <span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
			),
        ));


        $this->add(array(
			'name' => 'date_effective_till',
			'type' => 'Zend\Form\Element\Text',
			'attributes' => array(
				'class'=> 'left&#x20;filterSelect calender',
				'id'=>'minDate',
				'placeholder' => 'Enter End Date',
				'autofocus' => true
			),
			'options' => array(
				'label' => 'End Date <span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
			),
        ));

        $this->add(array(
            'name' => 'priority',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'priority',
               //'required' => 'required',

            ),
            'options' => array(
                'label' => 'Priority<span class="red">*</span>',
                'value_options' => array(
                    '1' => '1',
                    '2' => '2',
                    '3' => '3',
                    '4' => '4',
                    '5' => '5',
                ),
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name'=>'city',
            'type' => 'Zend\Form\Element\Select',
            'attributes'=>array(
				'id'=>'city',
				'class'=>'selectpicker small',
            ),
            'options'=>array(
				'label'=>'City<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
				'value_options' => $this->getCities()
            )
        ));


        $this->add(array(
            'name' => 'status',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'status',
               //'required' => 'required',

            ),
            'options' => array(
                'label' => 'Status<span class="red">*</span>',
                'value_options' => array(
                    '1' => 'Active',
                    '0' => 'Inactive',
                ),
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
			'name' => 'submit',
			'attributes' => array(
				'type'  => 'submit',
				'value' => 'Go',
				'id' => 'submitbutton',
			),
        ));

        $this->add(array(
			'name' => 'cancel',
			'attributes' => array(
				'type'  => 'button',
				'value' => 'Cancel',
				'id' => 'cancelbutton',

			),
        ));

        $this->add(array(
			'name'=>'backurl',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes'=>array(
				'id'=>'backurl',
				'value'=>'/tax',
			),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));





    }

    /**
     * To get list of cities
     * @method getCities()
     * @return array $selectData
     */
    public function getCities()
    {
        $sql = new QSql($this->service_locator);
        $select = $sql->select();
        $select->from('city');
        $select->where('status',1);
        
        $results = $sql->execQuery($select);
        //$selectData[''] = "select your city";
        foreach ($results as $res) {
            $selectData[$res['pk_city_id']] = $res['city'];
        }
    
        return $selectData;
    
    }
    
}