<?php

namespace App\Services\ThirdPartyDelivery;

use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class YourGuy implements TPDeliveryInterface
{
    /**
     * @var string
     */
    private string $baseUrl;
    
    /**
     * @var string
     */
    private string $version;
    
    /**
     * @var string
     */
    private string $authToken;
    
    /**
     * Create a new YourGuy instance.
     */
    public function __construct()
    {
        $this->baseUrl = config('delivery.yourguy.base_url', 'http://yourguytestserver.herokuapp.com/api/');
        $this->version = config('delivery.yourguy.version', 'v2');
        $this->authToken = config('delivery.yourguy.auth_token', 'MTIzNDU1NDMyMTp2ZW5kb3I=');
    }
    
    /**
     * Book a delivery.
     *
     * @param array $data
     * @return array
     */
    public function book(array $data): array
    {
        $url = $this->baseUrl . $this->version . '/order/0/place_order/';
        
        $input = $this->exchangeArray($data);
        
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->post($url, $input);
            
            $result = $response->json();
            $code = $response->status();
            
            if ($code == 201) {
                // Update order with third-party delivery ID
                $this->updateOrderWithThirdPartyId($data['pk_order_no'], $result['data']['order_id'][0]);
                
                return [
                    'code' => $code,
                    'status' => $result['message'],
                    'time' => date("h:i A", strtotime($input['pickup_datetime'])),
                    'date' => date(config('delivery.date_format', 'Y-m-d'), strtotime($input['pickup_datetime']))
                ];
            } else {
                return ['code' => $code, 'status' => $result['error'] ?? 'Unknown error'];
            }
        } catch (\Exception $e) {
            Log::error('Error booking third-party delivery: ' . $e->getMessage(), [
                'order_id' => $data['pk_order_no']
            ]);
            
            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Cancel a delivery.
     *
     * @param int $orderId
     * @return array
     */
    public function cancel(int $orderId): array
    {
        $url = $this->baseUrl . $this->version . '/order/' . $orderId . '/cancel/';
        
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->post($url);
            
            $result = $response->json();
            $code = $response->status();
            
            return ['code' => $code, 'status' => $result['message'] ?? 'Unknown response'];
        } catch (\Exception $e) {
            Log::error('Error cancelling third-party delivery: ' . $e->getMessage(), [
                'order_id' => $orderId
            ]);
            
            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get delivery status.
     *
     * @param int $orderId
     * @param array $data
     * @return array
     */
    public function getStatus(int $orderId, array $data): array
    {
        $url = $this->baseUrl . $this->version . '/order/' . $orderId . '/status/';
        
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . $this->authToken,
                'Content-Type' => 'application/json',
                'X-Requested-With' => 'XMLHttpRequest'
            ])->get($url);
            
            $result = $response->json();
            $code = $response->status();
            
            if ($code == 200) {
                $output = [
                    'status' => $result['status'] ?? 'Unknown',
                    'deliveryguy_name' => $result['deliveryguy_name'] ?? null,
                    'deliveryguy_phone_number' => $result['deliveryguy_phonenumber'] ?? null,
                    'pickupguy_name' => $result['pickupguy_name'] ?? null,
                    'pickupguy_phone_number' => $result['pickupguy_phonenumber'] ?? null,
                ];
                
                return ['code' => $code, 'status' => $output];
            } else {
                return ['code' => $code, 'status' => $result['detail'] ?? 'Unknown error'];
            }
        } catch (\Exception $e) {
            Log::error('Error getting third-party delivery status: ' . $e->getMessage(), [
                'order_id' => $orderId
            ]);
            
            return ['code' => 500, 'status' => 'Error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Exchange array for YourGuy API.
     *
     * @param array $data
     * @return array
     */
    private function exchangeArray(array $data): array
    {
        // Format data for YourGuy API
        $pickupTime = $this->formatPickupTime($data['order_date'], $data['fk_kitchen_code'], $data['order_menu']);
        
        return [
            'pickup_datetime' => $pickupTime,
            'pickup_address' => config('delivery.merchant_address', 'Default Address'),
            'drop_name' => $data['customer_name'],
            'drop_phone' => $data['customer_phone'],
            'drop_address' => $data['ship_address'],
            'amount' => $data['amount'],
            'order_id' => $data['pk_order_no']
        ];
    }
    
    /**
     * Format pickup time.
     *
     * @param string $orderDate
     * @param string $kitchenCode
     * @param string $orderMenu
     * @return string
     */
    private function formatPickupTime(string $orderDate, string $kitchenCode, string $orderMenu): string
    {
        // Logic to determine pickup time based on kitchen and menu type
        $pickupTime = '';
        
        if (strtolower($orderMenu) == 'lunch') {
            $pickupTime = config('delivery.lunch_pickup_time', '12:30:00');
        } else {
            $pickupTime = config('delivery.dinner_pickup_time', '19:30:00');
        }
        
        return date('Y-m-d', strtotime($orderDate)) . ' ' . $pickupTime;
    }
    
    /**
     * Update order with third-party ID.
     *
     * @param int $orderId
     * @param string $thirdPartyOrderId
     * @return void
     */
    private function updateOrderWithThirdPartyId(int $orderId, string $thirdPartyOrderId): void
    {
        try {
            $order = \App\Models\Order::find($orderId);
            if ($order) {
                $order->update(['tp_delivery_order_id' => $thirdPartyOrderId]);
            }
        } catch (\Exception $e) {
            Log::error('Error updating order with third-party ID: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'third_party_order_id' => $thirdPartyOrderId
            ]);
        }
    }
}
