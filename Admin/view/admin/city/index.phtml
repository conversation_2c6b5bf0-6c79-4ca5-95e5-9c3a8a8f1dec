<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addcity" class="common-orange-btn-on-hover"> Add City</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updcity" class="common-orange-btn-on-hover">Update City details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactcity" class="common-orange-btn-on-hover">Deactivate City</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>

<div id="content" class="clearfix">
    <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>						
            <div  data-alert="" class="alert-box success round">
             <?php echo $msg; ?>
                <a href="#" class="close">&times;</a>
            </div>			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
                <?php echo $msg; ?>
               <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        <div class="portlet box yellow">        
            <div class="portlet-title">
                <h4><i class="fa fa-table"></i>City</h4>  
                <ul class="toolOption">
                    <li>
                    <?php if($acl->isAllowed($loggedUser->rolename,'city','add')){  ?>
                    <div class="addRecord">
                        <button class="btn" onClick="location.href='<?php echo $this->url('city', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add City</button>
                        </div>
                      <?php } ?>
                    </li>
                </ul>
            </div>        
            <div class="portlet-body">        
                <table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th style="width: 160px;">City</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>          
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {

	$('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
        "ajax": "/city/ajx-city",
        "aoColumnDefs": [
                    {
                       bSortable: false,
                       aTargets: [ -1,-2 ]
                    }
                  ],
    });
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addcity",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add City");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updcity",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-intro", "Click here to edit City details");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactcity",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-intro", "Click here to deactivate City.");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-intro");
    });
</script>  