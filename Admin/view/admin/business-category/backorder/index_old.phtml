<script type="text/javascript" src="/admin/js/plugins/colorpicker.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.jgrowl.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.alerts.js"></script>
  <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Order For Existing Customers</span></h2>
          </div>
          <!--contenttitle-->

          <br />
           <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'index')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		      echo $this->form()->openTag($form);
     		 ?>
            <p>
                <label>Enter Contact No. OR Email ID</label>
              <span class="field">
                <?php   echo $this->formElement($form->get('phone')); ?>
      		  <?php   echo $this->formelementerrors($form->get('phone')); ?>
              </span> </p>
           <!--  <p>
              <label>Name</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="Sandeep Gore" disabled="disabled" />
              </span> </p>
            <p>
              <label>Delivery Location</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="Nerul" disabled="disabled" />
              </span> </p>
            <p>
              <label>Company Name</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="FS pvt ltd" disabled="disabled" />
              </span> </p>
            <p>
              <label>Address</label>
              <span class="field">
              <textarea rows="3" class="smallinput" disabled="disabled">FS pvt ltd, Plot 35, sector - 12, Navi Mumbai - 400088</textarea>
              </span> </p>
            <p>
              <label>Promo Code</label>
              <span class="field">
              <input type="text" value="EDFFWEEK" name="input1" class="smallinput" disabled="disabled" />
              </span> </p>-->
            <p class="stdformbutton">
                <?php echo $this->formSubmit($form->get('submit')); ?>
             <!--  <input type="button" class="button radius2" value="Edit" />-->
            </p>
           <?php echo $this->formElement($form->get('csrf')); ?>
        	<?php  echo $this->form()->closeTag(); ?>
          <br />
          <br />
        </div>