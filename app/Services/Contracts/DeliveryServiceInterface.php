<?php

namespace App\Services\Contracts;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface DeliveryServiceInterface
{
    /**
     * Get orders for delivery.
     *
     * @param int $userId
     * @param int|null $locationId
     * @param string|null $date
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getDeliveryOrders(int $userId, ?int $locationId = null, ?string $date = null): LengthAwarePaginator;
    
    /**
     * Search orders.
     *
     * @param int $userId
     * @param string $searchTerm
     * @param int|null $locationId
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchOrders(int $userId, string $searchTerm, ?int $locationId = null): LengthAwarePaginator;
    
    /**
     * Update delivery status.
     *
     * @param \App\DTOs\DeliveryStatusUpdateDTO $dto
     * @return bool
     */
    public function updateDeliveryStatus(DeliveryStatusUpdateDTO $dto): bool;
    
    /**
     * Get delivery locations for user.
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDeliveryLocations(int $userId): Collection;
    
    /**
     * Assign delivery person to order.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     * @return bool
     */
    public function assignDeliveryPerson(int $orderId, int $deliveryPersonId): bool;
    
    /**
     * Book third-party delivery.
     *
     * @param \App\DTOs\OrderDeliveryDTO $dto
     * @return array
     */
    public function bookThirdPartyDelivery(OrderDeliveryDTO $dto): array;
    
    /**
     * Cancel third-party delivery.
     *
     * @param int $orderId
     * @return bool
     */
    public function cancelThirdPartyDelivery(int $orderId): bool;
    
    /**
     * Get third-party delivery status.
     *
     * @param int $orderId
     * @return array
     */
    public function getThirdPartyDeliveryStatus(int $orderId): array;
}
