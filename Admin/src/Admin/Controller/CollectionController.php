<?php
/**
 * This File used to manage collection of money paid by customers
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CollectionController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\View\Model\JsonModel;

use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Wallet as Qswallet;

use Front\Model\FrontTable;

class CollectionController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\CollectionTable model
	 *
	 * @var QuickServe\Model\CollectionTable $collectiontable
	 */
	protected $collectiontable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * It has an instance of QuickServe\Model\InvoiceTable model
	 *
	 * @var QuickServe\Model\InvoiceTable $invoicetable
	 */
	protected $invoicetable;
	protected $frontTable;
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderTable $ordertable
	 */
	protected $ordertable;
	/**
	 * This function displays the list of invoices with the paid & remaining amount
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$iden = $this->authservice->getIdentity();

		//$select = New QSelect();
		//$collection = $this->getCollectionTable()->fetchAll();

		//echo "<pre>";print_r($collection->toArray());die;
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;



		$this->layout()->setVariables(array('page_title'=>"Collections",'description'=>"Check paid/unpaid status",'breadcrumb'=>"Collections"));
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
				'kitchen_data' =>$kitchen_data
		));
	}


	public function ajxCollectionAction(){
		
		$utility = Utility::getInstance();
        $setting_session = new Container('setting');
		$setting = $setting_session->setting;
       //	 dd($setting[GLOBAL_CUSTOMER_PAYMENT_MODE]);
	    if (! $this->authservice)
	    {
	        $this->authservice = $this->getServiceLocator()->get('AuthService');
	    }

	    $iden = $this->authservice->getIdentity();

	    $layout = $this->layout();
	    $acl = $layout->acl;
	    $fromdate = $this->params()->fromQuery('minDate');
	    $todate = $this->params()->fromQuery('maxDate');

	    $viewModel = new ViewModel();

	    $loggedUser = $layout->loggedUser;

	    $select = new QSelect();

	    //echo $view;die;

	    //$order_by = $this->params()->fromRoute('order_by') ? $this->params()->fromRoute('order_by') : 'pk_order_no';

	    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
	    $arrColumns = array('0'=>'invoice_id','1'=>'cust_name' ,'2'=>'invoice_amount','3'=>'amount_due','4'=>'mode_of_payment');

	    $order_by = $arrColumns[$arrOrder[0]['column']];
       
	    $order = $arrOrder[0]['dir'];
	    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

	    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
	    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
	    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
	    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
	    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";

	    $columns = $this->params()->fromQuery('columns');
	    
	    $status = $this->params()->fromQuery('status') ? $this->params()->fromQuery('status') : "";
	    //$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
	    
	    $kitchenscreen = $_SESSION['adminkitchen'];
	    
	   // $status = $this->params()->fromQuery('status');
	    
	  //  echo"<pre> status = ";print_r($status);die;

	    if(isset($search) && $search !=""){

	        $select->where(

	            new \Zend\Db\Sql\Predicate\PredicateSet(
	                array(
	                    new \Zend\Db\Sql\Predicate\Operator('invoice_no', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('cust_name', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('invoice_amount', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('amount_due', 'LIKE', '%'.$search.'%'),
	                ),
	                // optional; OP_AND is default
	                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
	            )

	        );

	    }

	  	if($status !=""){
	  		if($status == "paid")
	  		{
	  			$select->where('invoice_payments.amount_due = 0',\Zend\Db\Sql\Predicate\PredicateSet::OP_AND);
	  			
	  		}
	  		else 
	  		{
	  			$select->where('invoice_payments.amount_due > 0',\Zend\Db\Sql\Predicate\PredicateSet::OP_AND);
	  		}
	     
	    } 
	    
	    if($kitchenscreen!='all')
	    {
	    	$select->where("fk_kitchen_code = $kitchenscreen");
	    }else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
	    
	    if(isset($fromdate) && $fromdate!='')
	    {
	    	$fromdate = date('Y-m-d',strtotime($fromdate));
	    	$select->where("invoice.date >= '$fromdate'");
	    }
	    
	    if(isset($todate) && $todate!='')
	    {
	    	$todate = date('Y-m-d',strtotime($todate));
	    	$select->where("invoice.date <= '$todate'");
	    }
	    
	    
        //echo $order_by;die;

	    $select->order($order_by . ' ' . $order);
	    
	    //echo $select->getSqlString();die;

	    $collections = $this->getCollectionTable()->fetchAll($select,$page);
	    
	

	    $collections->setCurrentPageNumber($page)
	    ->setItemCountPerPage($itemsPerPage)
	    ->setPageRange(7);

	    $returnVar = array();
	    $returnVar['draw'] = $draw;
	    $returnVar['recordsTotal'] = $collections->getTotalItemCount();
	    $returnVar['recordsFiltered'] = $collections->getTotalItemCount();
	    $returnVar['data'] = array();
     
	    
	    foreach($collections as $collection){
	        //echo "<pre>"; print_r($collection); die;
	        $arrTmp = array();
	        array_push($arrTmp,$collection['invoice_no']);
	        array_push($arrTmp,$collection['cust_name']);
// 	        array_push($arrTmp,$collection['cust_address']);
	        $invoice_amount = '<span id="total'.$collection->invoice_id.'" data-totalamount="'.$collection['invoice_amount'].'">'.$utility->getLocalCurrency($collection['invoice_amount']).'</span>';
	        array_push($arrTmp,$invoice_amount);

	        $strAmountDue = '<input class="disabledColor" type="text" disabled value="'.$collection->amount_due.'" id="due'.$collection->invoice_id.'"/>';
	        array_push($arrTmp,$strAmountDue);
          
	        $strSelect = '<select  id="mode_of_payment'.$collection->invoice_id.'" >
                                
                            	<option value="cash">Cash</option>
                            	<!--<option value="cc">Credit Card</option>-->
                            	<option value="cheque">Cheque</option>';
	        			
            if($utility->checkSubscription('customer_wallet','allowed')){
                 $strSelect .=' <option value="wallet">Wallet</option>';
            } 
            
            $strSelect .= "</select>";
	        array_push($arrTmp,$strSelect);
           
	        if($collection->status){
	            $attr="disabled";
	            $clsname="paidbutton";
	            $sts="Paid";
	        }
	        else
	        {
	            $attr="";
	            $clsname="";
	            $sts="Pay";
	        }

	        $strAction = '<input type="text"'.$attr.' class="collect-input pay"  data-mobile="'.$collection->phone.'" data-fromdate="'.date('m/d/Y',strtotime($collection->from_date)).'" data-todate="'.date('m/d/Y',strtotime($collection->to_date)).'" data-custname="'.$collection->cust_name.'" id="pay'.$collection->invoice_id.'" />
                            <button class="paybtn '.$clsname.'" '.$attr.' id="'.$collection->invoice_id.'" >'.$sts.'</button>
                            <span class="loader_show'.$collection->invoice_id.'" style="display:none;float:right"">
								<img src="/admin/images/ajax-loader.gif" />
							</span>';

	        array_push($arrTmp,$strAction);

	        array_push($returnVar['data'],$arrTmp);
	    }

	    return new JsonModel($returnVar);

	}

	/**
	 * This function called using AJAX method
	 * This function used to pay the Bill againse invoice of customer
	 *
	 * @param integer invoice_id
	 * @param decimal pay_amount
	 * @param decimal due_amount
	 * @param string cust_name
	 * @param int from
	 * @param int to
	 * @return /Zend/Json/view/viewModel
	 */
	public function payAction()
	{
			
			$sm = $this->getServiceLocator();
			$adapt = $sm->get('Write_Adapter');
			$libCommon = Qscommon::getInstance($sm);
			$libCustomer = QSCustomer::getInstance($sm);
			
			$utility = Utility::getInstance();
			
			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			
			$libWallet = Qswallet::getInstance($sm);
			
			$layoutviewModel = $this->layout();
			$acl = $layoutviewModel->acl;
			$loguser = $layoutviewModel->loggedUser;
			
			$payment_check = new \Lib\Email\Paymentcheck($adapt);
			$invoice_id = $this->params()->fromPost('invoice_id');  
			$pay_amount = $this->params()->fromPost('pay_amount'); 
			$due_amount = $this->params()->fromPost('due_amount'); 
			$total = $this->params()->fromPost('total'); 
			$mode_of_payment = $this->params()->fromPost('mode_of_payment'); 

			$data=array(
				'invoice_id'=>$invoice_id,
				'pay_amount'=>$pay_amount,
				'due_amount'=>$due_amount,
				'total'=>$total,
				'mode_of_payment'=>$mode_of_payment,
				'cust_name'	=> $this->params()->fromPost('cust_name'), 
				'from'	=> $this->params()->fromPost('from'), 
				'to'	=> $this->params()->fromPost('to'), 
			);

			$invoice_data = $this->getInvoiceTable()->getInvoice(explode(',', $invoice_id));
			
			$balanced = $libCustomer->getBal($invoice_data[0]['cust_ref_id'],true,true,true);
			
			if($mode_of_payment=='wallet' && $balanced['avail_bal'] < $pay_amount){
				echo json_encode(array("status"=>"error","msg"=>"Sorry can not collect amount due to insufficient amount in wallet"));
				die;
			}
			
			$collection = $this->getCollectionTable()->payInvoice($data);
			
			
			$msg = "Invoice : ". $utility->getLocalCurrency($data['pay_amount']). "received against invoice no from". $data['cust_name'];
				
			if($collection)
			{
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'collection';
				$activity_log_data['action']= 'pay';
				$activity_log_data['description']= $msg;
					
				//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
				$libCommon->saveActivityLog($activity_log_data);
			}
			
			
			// if payment is made by wallet then deduct money from wallet..
			switch($mode_of_payment){
				
				case "wallet":
					$wallet_data['amount'] = $pay_amount;
				
					$data1 = array(
							'amount' =>	$pay_amount,
							'date' => date('Y-m-d'),
							'id' => $invoice_data[0]['cust_ref_id'],
							'description' => $utility->getLocalCurrency($pay_amount).'  charges deducted against Invoice No. '.$invoice_data[0]['invoice_no'],
					);
					
					$libWallet->saveWalletTransaction($data1,"debit",'admin');
					
					break;
				case "cash":
					$walletData1 = array();
					$walletData1['amount'] = $pay_amount;
					$walletData1['id'] = $invoice_data[0]['cust_ref_id'];
					$walletData1['description'] = $utility->getLocalCurrency($pay_amount).' received from collection against invoice '.$invoice_data[0]['invoice_no'];
					$walletData1['payment_date'] = date('Y-m-d');
					$walletData1['created_date'] = date('Y-m-d');
					$walletData1['amount_type'] = "cr";
						
					$libWallet->saveWalletTransaction($walletData1,'cash','admin');
					
					$wallet_data['amount'] = $pay_amount;
					
					$data1 = array(
							'amount' =>	$pay_amount,
							'date' => date('Y-m-d'),
							'id' => $invoice_data[0]['cust_ref_id'],
							'description' => $utility->getLocalCurrency($pay_amount).'  charges deducted against Invoice No. '.$invoice_data[0]['invoice_no'],
					);
						
					$libWallet->saveWalletTransaction($data1,"debit",'admin');

					break;
					
				case "cheque":
					
					$walletData1 = array();
					$walletData1['amount'] = $pay_amount;
					$walletData1['id'] = $invoice_data[0]['cust_ref_id'];
					$walletData1['description'] = $utility->getLocalCurrency($pay_amount).' received by cheque from collection against invoice '.$invoice_data[0]['invoice_no'];
					$walletData1['payment_date'] = date('Y-m-d');
					$walletData1['created_date'] = date('Y-m-d');
					$walletData1['amount_type'] = "cr";
					
					$libWallet->saveWalletTransaction($walletData1,'cheque','admin');
						
					$wallet_data['amount'] = $pay_amount;

					$data1 = array(
							'amount' =>	$pay_amount,
							'date' => date('Y-m-d'),
							'id' => $invoice_data[0]['cust_ref_id'],
							'description' => $utility->getLocalCurrency($pay_amount).'  charges deducted against Invoice No. '.$invoice_data[0]['invoice_no'],
					);
					
					$libWallet->saveWalletTransaction($data1,"debit",'admin');
					
					//$payment_check->debitCustomerWallet($invoice_data[0]['cust_ref_id'], $wallet_data, $invoice_data[0]['invoice_no'],'invoice');
					break;
					
				case "voucher":
						
					$walletData1 = array();
					$walletData1['amount'] = $pay_amount;
					$walletData1['id'] = $invoice_data[0]['cust_ref_id'];
					$walletData1['description'] = $utility->getLocalCurrency($pay_amount).' received by voucher from collection against invoice '.$invoice_data[0]['invoice_no'];
					$walletData1['payment_date'] = date('Y-m-d');
					$walletData1['created_date'] = date('Y-m-d');
					$walletData1['amount_type'] = "cr";
						
					$libWallet->saveWalletTransaction($walletData1,'cash','admin');
				
					$wallet_data['amount'] = $pay_amount;
					
					$data1 = array(
							'amount' =>	$pay_amount,
							'date' => date('Y-m-d'),
							'id' => $invoice_data[0]['cust_ref_id'],
							'description' => $utility->getLocalCurrency($pay_amount).'  charges deducted against Invoice No. '.$invoice_data[0]['invoice_no'],
					);
						
					$libWallet->saveWalletTransaction($data1,"debit",'admin');
				
					//$payment_check->debitCustomerWallet($invoice_data[0]['cust_ref_id'], $wallet_data, $invoice_data[0]['invoice_no'],'invoice');
					break;
			}
			
			$newArray = array();
			foreach ($invoice_data as $invoice)
			{
				$temp_Array = array();
				$temp_Array = $invoice;
				$temp_Array['bill'] =  $this->getInvoiceTable()->getBill($invoice['invoice_id']);
				$temp_Array['payment'] =  $this->getInvoiceTable()->getPayment($invoice['invoice_id']);
				$temp_Array['discount'] =  $this->getInvoiceTable()->getDiscounts($invoice['invoice_id']);
				$temp_Array['taxes'] =  $this->getInvoiceTable()->getTaxes($invoice['invoice_id']);
				$newArray[] = $temp_Array;
			}
			//echo json_encode($newArray);exit;
			$invoice_all_details = $newArray[0];
			//echo '<pre>';print_r($invoice_all_details);exit;
			//send sms
			if($invoice_all_details['email_address']!=''){
			$mailer = new \Lib\Email\Email();
			
			$mailer->setAdapter($sm);
			//get sms configuration
			$sms_config = $sm->get('Config')['sms_configuration'];
			//SET sms configuration to mailer
			$mailer->setSMSConfiguration($sms_config);
			//check for mobile no and give it to
			$mailer->setMobileNo($this->params()->fromPost('phone'));

			$sms_common = $libCommon->getSmsConfig($setting);
			$mailer->setMerchantData($sms_common);
			$sms_array = array(
				'towards' => $this->params()->fromPost('cust_name'), //$_REQUEST['cust_name'],
				'company'	=> $setting['MERCHANT_COMPANY_NAME'],
				'bill_month'	=> $this->params()->fromPost('from').' to '.$this->params()->fromPost('to'),
				'payment'	=> $utility->getLocalCurrency($pay_amount,'','','SMS')
			);
			$message = $libCommon->getSMSTemplateMsg('invoice_collection',$sms_array);
			
			//echo "<pre>collection message =";print_r($message);die;
			
			if($message){
				$mailer->setSMSMessage($message);
				$sms_returndata = $mailer->sendmessage();
			}
			//end sms send
			/* $welcome_email_subject = 'Food Dialer-Payment confirmed for '.$this->params()->fromPost('from').'-'.$this->params()->fromPost('to');
			$welcome_email_content = include( realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/invoice_collection.phtml' ); */
			
			$order_datetime = date('d-m-Y h:i A');
			$bill_details = "";
			foreach($invoice_all_details['bill'] as $bill)	{
				$bill_details .= '<tr>';
				$bill_details .= '<td><b>'.$bill['name'].'</b></td>';
				$bill_details .= '<td><b>'.$bill['quantity'].'</b></td>';
				$bill_details .= '<td><b>'.$utility->getLocalCurrency($bill['unit_price'],'','','Email').'</b></td>';
				$bill_details .= '<td><b>'.$utility->getLocalCurrency($bill['amount'] + $bill['discount'],'','','Email').'</b></td>';
				$bill_details .= '</tr>';
			}
			$bill_details .= '<tr><td colspan="3"><b>Total</b></td><td><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['actual_invoice_amount'],'','','Email').'</b></td></tr>';
			if(count($invoice_all_details['taxes']) > 0) {
				foreach ($invoice_all_details['taxes'] as $tax) {
					$bill_details .= '<tr>';
					$bill_details .= '<td colspan="3"><b>'.$tax['tax_name'].'</b></td>';
					$bill_details .= '<td><b>'.$utility->getLocalCurrency($tax['amount'],'','','Email').'</b></td>';
					$bill_details .=  '</tr>';
				}
			}
			$bill_details .= '<tr><td colspan="3"><b>Discount</b></td><td class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['discounted_amount'],'','','Email').'</b></td> </tr>';
			$bill_details .= '<tr><td colspan="3"><b>Total </b></td>';
			$bill_details .= '<td class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['invoice_amount'],'','','Email').'</b></td></tr>';
			if(count($invoice_all_details['discount']) > 0)
			{
				$disc_details = '<p><b>Discount Details</b></p>';
				$disc_details .= '<table border="1" cellpadding="10">';
				$disc_details .= '<tr><th>Product</th><th>Discount </th><th>Amount </th></tr>';
				foreach($invoice_all_details['discount'] as $discount)
				{
					$disc_details .= '<tr>';
					$disc_details .= '<td><span>'.$discount['name'].'</span></td>';
					$disc_details .= '<td><span>'.$discount['discount_name'].'</span></td>';
					$disc_details .= '<td><span>'.$utility->getLocalCurrency($discount['amount'],'','','Email').'</span></td>';
					$disc_details .= '</tr>';
				}
				$disc_details .= '</table>';
			}
            
            foreach($_SESSION['customer']['customer_address'] as $addressKey =>$addressValue){
                    $address = $addressValue['location_address'];
            }
           
			if($invoice_all_details['payment'][0]['mode_of_payment'] && $invoice_all_details['payment'][0]['date'])
			{
				$payment_details = '<p><b>Payment Details</b></p>';
				$payment_details .= '<table border="1" style="border-collapse: collapse; width: 100%;">';
				$payment_details .= '<tr><th>Payment Method</th><th>Date/Time</th><th>Amount Paid</th><th>Amount Due</th></tr>';
				$payment_details .= '<tr><td>'.$invoice_all_details['payment'][0]['mode_of_payment'].'</td><td>'.$invoice_all_details['payment'][0]['date'].'</td><td>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_paid'],'','','Email').'</td><td>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_due'],'','','Email').'</td></tr>';
				$payment_details .= '</table>';
			}                        
			$email_vars_array = array(
					'towards' => $this->params()->fromPost('cust_name'), //$_REQUEST['cust_name'],
					'bill_month'	=> $this->params()->fromPost('from')."-".$this->params()->fromPost('to'),
					'cust_name'	=> $pre_messages['cust_name'],
					//'payment_rs'	=> $libCommon->getCurrencyEntity($setting['GLOBAL_CURRENCY'], $pay_amount),
					'payment_rs'	=> $utility->getLocalCurrency($pay_amount,'','','Email'),
					'invoice_no' => $invoice_all_details['invoice_no'],
					'inv_date'	=> $invoice_all_details['date'],
					'cust_add'	=> $address,
					'bill_details' => $bill_details,
					'disc_details'	=> $disc_details,
					'payment_details'	=> $payment_details,

                    'support_email'	=>$setting['MERCHANT_SUPPORT_EMAIL'],
                    'company_name' => $setting['MERCHANT_COMPANY_NAME'],

					'website'	=> $setting['CLIENT_WEB_URL'],

			);
			
			$subject_vars_array = array(
					'invoice_from_date' => $this->params()->fromPost('from'), //$_REQUEST['cust_name'],
					'invoice_to_date'	=> $this->params()->fromPost('to'),
			);
			
			$signature_vars_array = array(
					'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
			);
			$email_data = $libCommon->getEmailTemplateMsg('invoice_collection',$email_vars_array,$signature_vars_array,$subject_vars_array);
			
			$email_conf = $libCommon->getEmailID($email_data, $invoice_data[0]['cust_ref_id']);
			
			$signature = $email_data['signature'];
			//echo $welcome_email_content;exit;
			$mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
			$mailer->setConfiguration($mailer_config);
			$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
		
			// get email storage queue
			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);

			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);
			$contenttype = $email_data['type'];
			
			
			if($email_data['subject']!="" && $email_data['body']!="")
			{
				if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
					$mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
				}
			}
			
		}

		echo json_encode($collection);
		exit;
	}
	/**
	 * Get instance of QuickServe\Model\CollectionTable
	 *
	 * @return QuickServe\Model\CollectionTable
	 */
	public function getCollectionTable()
	{
		if (!$this->collectiontable)
		{
			$sm = $this->getServiceLocator();
			$this->collectiontable = $sm->get('QuickServe\Model\CollectionTable');
		}
		return $this->collectiontable;
	}
	/**
	 * Get instance of QuickServe\Model\InvoiceTable
	 *
	 * @return QuickServe\Model\InvoiceTable
	 */
	public function getInvoiceTable(){

		if (!$this->invoicetable)
		{
			$sm = $this->getServiceLocator();
			$this->invoicetable = $sm->get('QuickServe\Model\InvoiceTable');
		}
		return $this->invoicetable;
	}
	
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable(){

		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}

}