import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueApplyPromo } from '@/components/catalogue-service-v12/apply-promo';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueApplyPromo', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueApplyPromo />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueApplyPromo')).toBeInTheDocument();
  });
});