<?php
/**
 * This File provides the input fields which needs to create add & update promocode
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PromoCodeForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;


use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;
use Zend\Session\Container;
use Lib\Utility;

use Lib\QuickServe\Db\Sql\QSql;

class PromoCodeForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create promocode form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('promo_code');
        $this->adapter = $adapter;
        $this->service_locator = $sm;
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;
        $currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);

        $this->setAttribute('method', 'post');
        $this->add(array(
            'name' => 'pk_promo_code',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));
		$this->add(array(
        		'name' => 'product_code',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        		'class'=>'testSelAll2',
        		'multiple' => 'multiple',
        	  	//'required' => 'required',
        				'value' => '0',
        		),
        		'options' => array(
        				'label' => 'Meal Name<span class="red">*</span>',
        				'value_options' => $this->getProducts(),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        $this->add(array(
        		'name' => 'menu_type',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        		'class'=>'testSelAll2',
        		'multiple' => 'multiple',
        	  	//'required' => 'required',
        				'value' => '0',
        		),
        		'options' => array(
        				'label' => 'Menu Type<span class="red">*</span>',
        				'value_options' => array(
                            array(
       								'value' => 'breakfast',
       								'label' => 'Breakfast',
       								'attributes' => array(
       										'class'=>"cashcheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'lunch',
       								'label' => 'Lunch',
       								'attributes' => array(
       										'class'=>"neftcheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'dinner',
       								'label' => 'Dinner',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'snacks',
       								'label' => 'Snacks',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'fastfood',
       								'label' => 'Fastfood',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       				        array(
       								'value' => 'instantorder',
       								'label' => 'Instant Order',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
                        ),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        $this->add(array(
            'name' => 'promo_code',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Promo Code',
             //  'required' => 'required',
            ),
            'options' => array(
                'label' => 'Promo Code<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

         $this->add(array(
         		'name' => 'Product_order_quantity',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes' => array(
         				'class' => 'smallinput',
         				'placeholder' => 'Enter Quantity',
//         				 'required' => 'required',
         		),
         		'options' => array(
                    
         				'label' => 'Product Order Quantity<span class="red">*</span>',
                   
         				'label_options' => array('disable_html_escape' => true),
         		),
         ));
         $this->add(array(
            'name' => 'amount',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
               // 'required' => 'required',
            	'placeholder' => 'Enter Discount Amount',
            ),
            'options' => array(
				'label' => 'Discount Amount ('.$currencySymbol.')<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

//          $this->add(array(
//            'name' => 'min_amount',
//            'type' => 'Zend\Form\Element\Text',
//            'attributes' => array(
//                'class' => 'smallinput',
//               // 'required' => 'required',
//            	'placeholder' => 'Enter Minimum Amount',
//            ),
//            'options' => array(
//				'label' => 'Minimum Amount ('.$currencySymbol.')<span class="red">*</span>',
//            	'label_options' => array('disable_html_escape' => true),
//            ),
//        ));
         $this->add(array(
            'name' => 'discount_type',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
               //'required' => 'required',

            ),
            'options' => array(
                'label' => 'Discount Type<span class="red">*</span>',
                'value_options' => array(
                    'fixed' => 'Fixed',
                    'percentage' => 'Percentage',
                ),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
         
         $this->add(array(
         		'name' => 'start_date',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes' => array(
         				'id' => 'start_date',
         				'placeholder' => 'Start Date',
         				'class'=>'calender ',
         				'placeholder' => 'Select Start Date',
         
         
         		),
         		'options' => array(
         				'label' => 'Start Date<span class="red">*</span>',
         				'label_options' => array('disable_html_escape' => true),
         		),
         ));
         
         

         $this->add(array(
         		'name' => 'end_date',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes' => array(
         				'id' => 'end_date',
         				'placeholder' => 'End Date',
         				'class'=>'calender ',
         				'placeholder' => 'Select End Date',
         		),
         		'options' => array(
         				'label' => 'End Date<span class="red">*</span>',
         				'label_options' => array('disable_html_escape' => true),
         		),
         ));
         
        
         
         $this->add(array(
         		'name' => 'promo_limit',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes' => array(
         				'class' => 'smallinput',
         				'placeholder' => 'Enter Promo Limit',
         				 //'required' => 'required',
         		),
         		'options' => array(
         				'label' => 'Promo Limit<span class="red">*</span>',
         				'label_options' => array('disable_html_escape' => true),
         		),
         ));
         $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
				),
		));

        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/promocode',
        		),
        ));
        
        $this->add(array(
       		'name'=>'promo_type',
       		'type' => 'Zend\Form\Element\Radio',
            
       		'attributes'=>array(
                'value' => 'discount',
       		),
       		'options'=>array(
       				'label'=>'Promo Code Type<span class="red">*</span>',
       				
       				'value_options' => ['discount' => 'discount', 'cashback' => 'cashback'],
       				'label_options' => array('disable_html_escape' => true),
                                'label_attributes' => array(
                                    'class'  => 'left mr10'
                                ),
       		),
        ));
         $this->add(array(
       		'name'=>'menu_operator',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
                'id'=>'menu_operator',
                'value' => '&&',
       		),
       		'options'=>array(
       				'label'=>'Menu Operator<span class="red">*</span>',
       				'value_options' => ['and'=>'&&'],
//       				'value_options' => ['and'=>'&&','or'=>'or'],
       				'label_options' => array('disable_html_escape' => true),
                                'label_attributes' => array(
                                    'class'  => 'left mr10'
                                ),
       		),
        ));
        $this->add(array(
       		'name'=>'applied_on',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
                    'value' => 'order',
       		),
       		'options'=>array(
       				'label'=>'Applied On<span class="red">*</span>',
       				
       				'label_options' => array('disable_html_escape' => true),
                                'label_attributes' => array(
                                    'class'  => 'left mr10'
                                ),
       		),
        ));
        
        $options = array('order' => 'order','menu'=>'menu','plan'=>'plan');
        
        if($utility->checksubscription('customer_wallet','allowed')){
            $wallet = array(
                'value' => 'wallet',
                'label' => 'Wallet',
                'attributes' => array(
                          'value' => 'order',
                ),
                'label_attributes' => array(
                         'value' => 'wallet',
                ),
            );
            
            array_push($options,$wallet);
        }
        
        // if($utility->checksubscription('payment_online','allowed')){
        //     $online =  array(
        //                 'value' => 'online',
        //                 'label' => 'Pay Online',
        //                 'attributes' => array(
        //                         'class'=>"onlinecheck" ,
        //                         'data-open'=>".onlinerow"
        //                 ),
        //                 'label_attributes' => array(
        //                         'class'  => 'inline left mr5',
        //                         'style'=>'line-height: 0.9em;'
        //                 ),
        //             );
            
        //     array_push($options,$online);
        // }
        $this->get('applied_on')->setValueOptions($options);
        
        
         $this->add(array(
            'name' => 'wallet_amount',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
            	'placeholder' => 'Enter Wallet Amount',
            ),
            'options' => array(
				'label' => 'Wallet Amount ('.$currencySymbol.')<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
    }
    /**
     * To get the list of active products
     * @return array
     */
    public function getProducts()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('products');
    	$select->where(array(
//     			'status' => 1,
    			'product_type' => 'Meal',
    	));
       //echo $select->getSqlString();die; 
    	$statement = $sql->prepareStatementForSqlObject($select);
    	$results = $statement->execute();
    	foreach ($results as $res) {
    		$selectData[$res['pk_product_code']."@".$res['name']] = $res['name'];      //concatinating Prod_code and Prod_name
    	}
    	return $selectData;
    
    }


}
