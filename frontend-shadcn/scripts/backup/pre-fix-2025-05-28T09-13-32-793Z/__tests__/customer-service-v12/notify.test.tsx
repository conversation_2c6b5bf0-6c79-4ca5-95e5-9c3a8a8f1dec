import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomerNotify } from '@/components/customer-service-v12/notify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerNotify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerNotify />
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerNotify')).toBeInTheDocument();
  });
});