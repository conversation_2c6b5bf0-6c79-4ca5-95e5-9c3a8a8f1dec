<div id="content">
							<div class="large-12 columns">

							 <?php
								if ($this->FlashMessenger()->hasSuccessMessages()){
									foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
							 ?>
								<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
								
								<div  data-alert="" class="alert-box success round">
					 			 <?php echo $msg; ?>
					  				<a href="#" class="close">&times;</a>
								</div>
								
							<?php
									}
								}else if($this->FlashMessenger()->hasErrorMessages()){
									foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
								<div data-alert class="alert-box alert round">
									   <?php echo $msg; ?>
									  <a href="#" class="close">&times;</a>
								</div>
							<?php 	}
								}
							?>	
							<div id="msg2"></div>
								<div class="portlet box yellow">
									<div class="portlet-title">
										<h4 class="white"><i class="fa fa-table"></i> Email Log List</h4>
										<ul class="toolOption">

											<li>
												<div class="addRecord">
													<button class="btn"  onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'index'));?>'">
														<i class="fa fa-eye"></i> &nbsp;View Email Template
													</button>
													<button class="btn  deletelog">
													<!-- <button class="btn"  onClick="location.href='<?php //echo $this->url('emailtemplate', array('action'=>'deletelog'));?>'"> -->
														<i class="fa fa-trash"></i>&nbsp;Delete Log
													</button>
												</div>
											</li>
											<!-- <li>
												<div class="print">
													<button class="btn dropdown" data-dropdown="dropPrint">
														<i class="fa fa-print"></i>&nbsp;Print/Export
													</button>
													<ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
														<li data-tooltip class="has-tip tip-top" title="Print">
															<a href="#"><i class="fa fa-print"></i></a>
														</li>
														<li data-tooltip class="has-tip tip-top" title="Export PDF">
															<a href="#"><i class="fa fa-file-pdf-o"></i></a>
														</li>
														<li data-tooltip class="has-tip tip-top" title="Export EXCEL">
															<a href="#"><i class="fa fa-file-excel-o"></i></a>
														</li>
													</ul>
												</div>
											</li> -->

										</ul>
									</div>
									<div class="portlet-body">
										<table id="customer" class="display displayTable">
											<thead>
												<tr>
													<th style="width: 10%">Status</th>
													<th style="width: 10%">Date</th>
													<th style="width: 15%">Sender</th>
													<th style="width: 20%">Send To</th>
													<th style="width: 23%">Subject</th>
													<th style="width: 5%" >Open</th>
													<th style="width: 5%">Clicks</th>
													<th style="width: 12%">Action</th>
												</tr>
											</thead>
										</table>
									</div>

								</div>
								<div class="clearBoth20"></div>

							</div>
						</div>
						
						
	 <script type="text/javascript">
    $(document).ready(function() {
    
    	//myPageTable.init();

    	$('#customer').dataTable( {
            "processing": true,
            "serverSide": true,
            "ajax": "/emailtemplate/ajx-emaillog",
            "order": [[ 1, "desc" ]],
            "aoColumnDefs": [
            	                {
            	                 //  bSortable: false,
            	               //  aTargets: [ -1,-2,-3]
            	                }
            	              ],
        });

        $(document).on('click','.resend',function(){
         var id = $(this).data('id');

            var r = confirm("Are you sure you want to resend the mail ?");
            if (r == true) {

            	$.ajax({
   				 url:"/emailtemplate/resendmail",
   				 type: "POST",
   				 data: {'id':id},
   				
   				 success:function(result)
   				 {
   					if(result=="1")
   					{
   	   					alert("Mail sent");
   	   					window.location.reload();
   	   				}
   				 } 				 
   			 });
            } else {
            	 return false;
            }
           
          });

        $(document).on("click",".deletelog",function(){

			var checkstr =  confirm('Are you sure you want to delete log?');
	   		 if(checkstr == true){
	   			$.ajax({
					 url:"/emailtemplate/deletelog",
					 type: "POST",
					 success:function(result)
					 {
						$('#msg2').html('<div  data-alert="" class="alert-box success round"><div>'+result.msg+'</div><a href="#" class="close">&times;</a></div>');
						 window.setTimeout(function() {
							 window.location.reload();  
							}, 4000);
				
					 }
				}); 
	   		 }
		});
  });
    </script>
  
