<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Create Customer Addresses Table
 * 
 * This migration creates the customer_address table.
 */
class CreateCustomerAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_address', function (Blueprint $table) {
            $table->id('pk_customer_address_id');
            $table->unsignedBigInteger('customer_code');
            $table->string('address_type', 50);
            $table->text('address');
            $table->string('landmark')->nullable();
            $table->string('location_code', 50)->nullable();
            $table->string('location_name', 100)->nullable();
            $table->string('city', 50)->nullable();
            $table->string('city_name', 100)->nullable();
            $table->string('state', 50)->nullable();
            $table->string('country', 50)->nullable();
            $table->string('pincode', 20)->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->tinyInteger('is_default')->default(0);
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();
            
            $table->foreign('customer_code')->references('pk_customer_code')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_address');
    }
}
