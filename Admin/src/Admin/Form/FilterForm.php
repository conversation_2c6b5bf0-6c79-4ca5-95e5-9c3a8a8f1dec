<?php
/**
 * This form created for the filtering mechanism..
 * It is used to get the reports on the basis of the inputs selected by user of this form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: FilterForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;
use Zend\Db\Sql\Expression;
use Lib\QuickServe\Db\Sql\QSql;

class FilterForm extends Form
{
	private $year;
	private $month;
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
        private $service_locator;
        
        protected $setting;
	/**
	 * It adds an input fields which are needed to create customer group login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
        //dd($sm->get("config"));
		parent::__construct('filter_form');
		$this->service_locator = $sm;
        $setting = new Container('setting');
        $this->setting = $setting->setting;
	}
	public function getForm(){

		$this->setAttribute('method','post');
		$this->setAttribute('style','display:block;');
		//$this->setAttribute('onSubmit','return validFilter()');

		$this->add(array(
				'name' => 'filter_order_options',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_order_options',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getFilterOrderOptions(),
				),
		));
	
		$this->add(array(
				'name' => 'filter_order_delivery_option',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_order_delivery_option',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getDeliveryStatusFilterOption(),
				),
		));
                // added payment mode pradeep
                $this->add(array(
				'name' => 'filter_payment_mode',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_payment_mode',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getPaymentMode(),
				),
		));
		
		$this->add(array(
				'name' => 'filter_sales_options',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_sales_options',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getFilterSalesOptions(),
				),
		));
        
        $this->add(array(
				'name' => 'filter_thirdparty_type',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_thirdparty_type',
						'class' => 'left filterSelect',
                        'onchange' => 'getThirdPartyByType(this.value)'
				),
				'options' => array(
						'label' => 'Third Party',
						'value_options' => array(0 => 'Third-Party Delivery', 1 => 'Third-Party Aggregator'),
				),
		));
        
        $this->add(array(
				'name' => 'filter_thirdparty_options',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_thirdparty_options',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Third Party',
						'value_options' => $this->getFilterThirdPartyOptions(),
				),
		));
        
        $this->add(array(
				'name' => 'delivery_type',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'delivery_type',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Third Party',
						'value_options' => array(
                                                    '' => 'Select Delivery Type',
                                                    'pickup' => 'Pickup',
                                                    'delivery' => 'Delivery'
                                                ),
				),
		));
                
              
                $this->add(array(
				'name' => 'filter_delivery_person',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_delivery_person',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getAllDeliveryPersonFilterOption(),
				),
		));
        
		$this->add(array(
				'name' => 'filter_invoice_options',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_invoice_options',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => 'Orders',
						'value_options' => $this->getFilterInvoiceOptions(),
				),
		));

		$this->add(array(
				'name' => 'filter_year',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_year',
						'class' => 'filterSelect',
				),
				'options' => array(
						'label' => 'Year',
						'value_options' => $this->getYears(),
				),
		));

		$this->add(array(
				'name' => 'filter_year_type',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_year_type',
						'class' => 'filterSelect',
				),
				'options' => array(
						'label' => 'Type',
						'value_options' => $this->getYearTypes(),
						'Value' => 'all',
				),
		));
   
		$this->add(array(
				'name' => 'filter_month',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_month',
						'value' => $this->getCurrentMonth(),
						'class' => 'filterSelect',
				),
				'options' => array(
						'label' => 'Month',
						'value_options' => $this->getMonths(),
				),
		));

		$this->add(array(
				'name' => 'filter_quarter_number',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_quarter_number',
						'class' => 'filterSelect',
				),
				'options' => array(
						'label' => 'Quarterly',
						'value_options' => $this->getQuarterMonths(),
						'Value' => 0,
				),
		));
		$this->add(array(
				'name' => 'filter_week_number',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'filter_week_number',
						'class' => 'filterSelect',
				),
				'options' => array(
						'label' => 'Week',
						'value_options' => $this->getWeeksOfMonth(),
				),
		));
		$this->add(array(
				'name' => 'minDate',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'minDate',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => '&nbsp;From&nbsp;',
						'label_attributes' => array(
         				   'class'  => 'left inline'
						 ),
						'label_options' => array('disable_html_escape' => true),
				),
		));
		$this->add(array(
				'name' => 'maxDate',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'maxDate',
						'class' => 'left filterSelect',
				),
				'options' => array(
						'label' => '&nbsp;To&nbsp;',
						'label_attributes' => array(
								'class'  => 'left inline',
								'style' => 'margin-left:0',
						),
						'label_options' => array('disable_html_escape' => true),
				),
		));
		$this->add(array(
				'name' => 'submit',
				'attributes' => array(
						'type'  => 'submit',
						'value' => 'Go',
						'id' => 'submitbutton',
						'class' => 'button left tiny left5 dark-greenBg',
						'data-text-swap' => 'Wait..',
				),
		));
         $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
	}
	/**
	 * To get the list of delivery locations
	 *
	 * @return array
	 */
	/* private function getFilterOrderOptions()
	{
		$selectData['all'] = 'All';
		$selectData['cancelled'] = 'Cancelled';
		$selectData['unbilled'] = 'Unbilled';
		$selectData['preorder'] = 'Preorder';
		$selectData['dispatch_order'] = 'Dispatch Order';
		return $selectData;

	} */
	private function getFilterOrderOptions()
	{
		$selectData['all'] = 'Select Order Status';
		$selectData['New']="New";
		$selectData['Cancel'] = 'Cancelled';
		$selectData['UnDelivered'] = 'UnDelivered';
		$selectData['Rejected'] = 'Rejected';
		$selectData['Complete'] = 'Complete';
		return $selectData;
	
	}
    private function getOrderType()
	{
		$selectData['preorder'] = 'preorder';

		return $selectData;
	
	}
	private function getFilterInvoiceOptions()
	{
		$selectData['all'] = 'Select Payment Status';
		$selectData['paid'] = 'Paid';
		$selectData['unpaid'] = 'Unpaid';
		return $selectData;
	}
	private function getFilterSalesOptions()
	{
		$selectData['all'] = 'Select Delivery Status';
		$selectData['Delivered'] = 'Delivered';
		$selectData['Rejected'] = 'Rejected';
		$selectData['UnDelivered'] = 'UnDelivered';
		return $selectData;
	}
    
	private function getYears()
	{
		$currentYear = date('Y');
		for ($y=$currentYear; $y >= 2010; $y--) {
			$selectData[$y] = $y;
		}
		//echo '<pre>';print_r($selectData);exit();
		return $selectData;

	}
	private function getYearTypes()
	{
		$selectData['all'] = 'All';
		$selectData['monthly'] = 'Monthly';
		$selectData['quarterly'] = 'Quarterly';
		return $selectData;

	}
	private function getMonths()
	{
		for ($i = 1;$i <= 12; $i++){
			$selectData[$i] = date("F", mktime(0, 0, 0, $i, 10));
		}
		return $selectData;

	}
	private function getWeekDates($year,$month,$week)
	{
		$week = str_pad($week, 2, '0', STR_PAD_LEFT);
		$from = date("Y-m-d",  strtotime("{$year}-W{$week}-1"));  //Returns the date of monday in week
		$to = date("Y-m-d", strtotime("{$year}-W{$week}-7"));   //Returns the date of sunday in week
		$start = mktime(0, 0, 0, $month, 1, $year);
		$end = mktime(0, 0, 0, $month, date('t', $start), $year);
		$startMonth = date('n',strtotime($from, time()));
		$endMonth = date('n',strtotime($to, time()));
		if($startMonth > $endMonth){
			if($endMonth == $month){
				$week_first_day = date('Y-n-j', $start);//first date of the month
				$week_last_day = $to;
			}else{
				$week_first_day = $from;
				$week_last_day = date('Y-n-t', $end);//last date of the month
			}

		}
		if($startMonth < $endMonth) {
			$currentmonth = $month;
			if($currentmonth == $endMonth) {
				$week_first_day = date('Y-n-j', $start);//first date of the month
				$week_last_day = $to;
			}
			else {
				$week_first_day = $from;
				$week_last_day = date('Y-n-t', $end);//last date of the month
			}
		}if($startMonth == $endMonth) {
			$week_first_day = $from;
			$week_last_day = $to;
		}
		return  date("Y-m-d",strtotime($week_first_day))." - ".date("Y-m-d",strtotime($week_last_day));

	}
	public function setMonthYear($year_old,$month_old){
		$this->year = $year_old;
		$this->month = $month_old;
	}
	private function getMonthForm(){
		return (int)$this->month;
	}
	private function getYearForm(){
		return (int)$this->year;
	}
	private function getWeeksOfMonth()
	{
		$month = date('m');
		$year = date('Y');
		if($this->getMonthForm() && $this->getYearForm()){
			$month = $this->getMonthForm();
			$year = $this->getYearForm();
		}
		$start = mktime(0, 0, 0, $month, 1, $year);
		$start_week = (int)date('W',$start);      
                $end_week = (int)date('W',mktime(0, 0, 0, $month, date('t',$start) , $year));
		if ($end_week < $start_week) { // Month wraps
			$end_week  = 53;
		}
		$returndata = array();
		$returndata[0] = 'All';
 		for($i = $start_week; $i <= $end_week; $i++ ){
			$returndata[$i] = $this->getWeekDates($year,$month,$i);
		}
		return $returndata;
                
	}
	private function getCurrentMonth(){
		return date('m');
	}
	private function getQuarterMonths()
	{
		$selectData[1] = 'Jan-Mar';
		$selectData[2] = 'Apr-Jun';
		$selectData[3] = 'Jul-Sep';
		$selectData[4] = 'Oct-Dec';
		return $selectData;

	}
	private function getDeliveryStatusFilterOption(){
		$selectData['all'] = 'Select Delivery Status';
		$selectData['Pending']="Pending";
		$selectData['Dispatched'] = 'Dispatched';
		$selectData['Delivered'] = 'Delivered';
		$selectData['Rejected'] = 'Rejected';
		$selectData['UnDelivered'] = 'UnDelivered';
	
		return $selectData;
	}

    private function getFilterThirdPartyOptions()
	{
		$sql = new QSql($this->service_locator);
    	$select = $sql->select();
		$select->from('third_party');
		$select->columns(array('third_party_id', 'name'));
		$select->where(array('status'=>1,'is_aggregator'=>0));

        //$sql = "SELECT third_party_id, name FROM `third_party` WHERE status = 1 AND is_aggregator = 0";
    	//echo $sql;exit;
		//$statement = $sql->prepareStatementForSqlObject($select);
    	//$vendors = $statement->execute();
    	//$vendors = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
        
        $vendors = $sql->execQuery($select);           

    	$returnData = array();
        $returnData[''] = "All";
        
    	foreach($vendors as $vendor){
    		$returnData[$vendor['third_party_id']] = $vendor['name'];
    	}
        
    	return $returnData; 
	}
        
   //by pradeep 28-feb-17
        
    public function getAllDeliveryPersonFilterOption()
    {       
		$sql = new QSql($this->service_locator);
    	$select = $sql->select();
		$select->from('users');
		$select->columns(array( 
            'pk_user_code'     => new Expression(' DISTINCT(pk_user_code)'),
            'name'    => new Expression(" CONCAT(first_name, ' ',last_name) ")
        ));
		$select->join('roles','users.role_id=roles.pk_role_id',array());
		$select->where(array('role_name'=>'Delivery Person','users.status'=>'1'));
		$select->order('users.first_name ASC');

		//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();

        $returnData[''] = 'Select Delivery Person';
        $results = $sql->execQuery($select);
    	foreach($results as $val){
    		$returnData[$val['pk_user_code']] = $val['name'];
    	}        
    	return $returnData;           	 
    }
    private function getPaymentMode() {
        
        $returnData[''] = 'Select Payment Mode';
        
        $results =  $this->setting['GLOBAL_CUSTOMER_PAYMENT_MODE'];
        foreach($results as $val){
    		$returnData[$val] = ucfirst($val);
    	}        
    	return $returnData;           	 
    }
    
}