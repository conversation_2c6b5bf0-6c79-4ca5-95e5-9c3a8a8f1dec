import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id]Checkout } from '@/components/catalogue-service-v12/[id]/checkout';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]Checkout', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id]Checkout />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]Checkout')).toBeInTheDocument();
  });
});