<?php
/**
 * Security Test Script
 *
 * This script tests the security improvements implemented in the authentication system.
 *
 * Usage:
 * php bin/security-test.php
 */

// Define application path
define('APPLICATION_PATH', realpath(__DIR__ . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Register namespaces
$loader = new \Composer\Autoload\ClassLoader();
$loader->addPsr4('SanAuth\\', APPLICATION_PATH . '/module/SanAuth/src/SanAuth');
$loader->addPsr4('Lib\\', APPLICATION_PATH . '/vendor/Lib');
$loader->register();

// Load environment variables
if (file_exists(APPLICATION_PATH . '/.env')) {
    $dotenv = new \Dotenv\Dotenv(APPLICATION_PATH);
    $dotenv->load();
}

// Define a simple EnvLoader class if it doesn't exist
if (!class_exists('Lib\QuickServe\Env\EnvLoader')) {
    class Lib_QuickServe_Env_EnvLoader {
        public static function get($name, $default = null) {
            $value = getenv($name);
            return $value !== false ? $value : $default;
        }
    }
    class_alias('Lib_QuickServe_Env_EnvLoader', 'Lib\QuickServe\Env\EnvLoader');
}

// Import required classes
use Lib\QuickServe\Auth\JwtTokenUtil;
use Lib\QuickServe\Env\EnvLoader;
use SanAuth\Service\PasswordHashingService;
use SanAuth\Service\CsrfTokenManager;
use SanAuth\Service\ErrorHandlingService;

// Initialize output
echo "===========================================\n";
echo "Security Improvements Test\n";
echo "===========================================\n\n";

// Test password hashing
echo "Testing Password Hashing...\n";
$passwordHashingService = new PasswordHashingService();
$password = "Test123!@#";
$hash = $passwordHashingService->hashPassword($password);
echo "Password: $password\n";
echo "Hash: $hash\n";
$result = $passwordHashingService->verifyPassword($password, $hash);
echo "Verification result: " . ($result['valid'] ? 'Valid' : 'Invalid') . "\n";
echo "Needs rehash: " . ($result['needs_rehash'] ? 'Yes' : 'No') . "\n\n";

// Test password complexity validation
echo "Testing Password Complexity Validation...\n";
$validationResult = $passwordHashingService->validatePasswordComplexity($password);
echo "Validation result: " . ($validationResult['valid'] ? 'Valid' : 'Invalid') . "\n";
if (!$validationResult['valid']) {
    echo "Errors:\n";
    foreach ($validationResult['errors'] as $error) {
        echo "- $error\n";
    }
}
echo "\n";

// Test secure password generation
echo "Testing Secure Password Generation...\n";
$securePassword = $passwordHashingService->generateSecurePassword();
echo "Generated password: $securePassword\n";
$validationResult = $passwordHashingService->validatePasswordComplexity($securePassword);
echo "Validation result: " . ($validationResult['valid'] ? 'Valid' : 'Invalid') . "\n\n";

// Test CSRF token generation and validation
echo "Testing CSRF Protection...\n";
$csrfTokenManager = new CsrfTokenManager();
$token = $csrfTokenManager->generateToken('test_form');
echo "Generated token: $token\n";
$validationResult = $csrfTokenManager->validateToken('test_form', $token);
echo "Validation result: " . ($validationResult ? 'Valid' : 'Invalid') . "\n";
$validationResult = $csrfTokenManager->validateToken('test_form', $token);
echo "Second validation result (should be invalid): " . ($validationResult ? 'Valid' : 'Invalid') . "\n\n";

// Test JWT token generation and validation
echo "Testing JWT Token Handling...\n";
$jwtTokenUtil = new JwtTokenUtil([
    'jwt_secret' => EnvLoader::get('JWT_SECRET', 'test-secret'),
    'issuer' => EnvLoader::get('JWT_ISSUER', 'tenant.cubeonebiz.com'),
    'audience' => EnvLoader::get('JWT_AUDIENCE', 'tenant-api')
]);
$token = $jwtTokenUtil->generateToken('test-company', ['admin'], 3600, 'test-user');
echo "Generated token: " . substr($token, 0, 20) . "...\n";
$payload = $jwtTokenUtil->decodeToken($token);
echo "Token payload: " . json_encode($payload, JSON_PRETTY_PRINT) . "\n";
$validationResult = $jwtTokenUtil->validateTokenForQuickServe($token, 'test-company');
echo "Validation result: " . ($validationResult ? 'Valid' : 'Invalid') . "\n\n";

// Test token blacklisting
echo "Testing Token Blacklisting...\n";
$blacklistResult = $jwtTokenUtil->blacklistToken($payload['jti'], $payload['exp']);
echo "Blacklist result: " . ($blacklistResult ? 'Success' : 'Failure') . "\n";
$isBlacklisted = $jwtTokenUtil->isTokenBlacklisted($payload['jti']);
echo "Is blacklisted: " . ($isBlacklisted ? 'Yes' : 'No') . "\n";
$payload = $jwtTokenUtil->decodeToken($token);
echo "Token validation after blacklisting: " . ($payload ? 'Valid' : 'Invalid') . "\n\n";

// Test error handling
echo "Testing Error Handling...\n";
$errorHandlingService = new ErrorHandlingService(true);
$error = $errorHandlingService->handleAuthError(
    ErrorHandlingService::ERROR_INVALID_CREDENTIALS,
    'Invalid username or password',
    ['username' => '<EMAIL>', 'ip' => '127.0.0.1']
);
echo "Error code: " . $error['code'] . "\n";
echo "Error message: " . $error['message'] . "\n";
echo "Error timestamp: " . date('Y-m-d H:i:s', $error['timestamp']) . "\n\n";

echo "===========================================\n";
echo "Security test completed successfully!\n";
echo "===========================================\n";

// Make the script executable
chmod(__FILE__, 0755);
