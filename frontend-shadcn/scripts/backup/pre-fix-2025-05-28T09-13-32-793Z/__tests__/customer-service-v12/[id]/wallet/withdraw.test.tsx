import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletWithdraw } from '@/components/customer-service-v12/[id]/wallet/withdraw';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletWithdraw', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletWithdraw />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletWithdraw')).toBeInTheDocument();
  });
});