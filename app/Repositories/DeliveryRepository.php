<?php

namespace App\Repositories;

use App\Models\Order;
use App\Models\UserLocation;
use App\Repositories\Contracts\DeliveryRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class DeliveryRepository implements DeliveryRepositoryInterface
{
    /**
     * Get orders for delivery.
     *
     * @param int $userId
     * @param int|null $locationId
     * @param string|null $date
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getOrdersForDelivery(int $userId, ?int $locationId = null, ?string $date = null): LengthAwarePaginator
    {
        $date = $date ?? date('Y-m-d');
        
        $query = Order::query()
            ->where('order_date', $date)
            ->whereIn('delivery_status', ['Dispatched', 'Delivered']);
        
        if ($locationId) {
            $query->where('location_code', $locationId);
        } else {
            // Get all locations assigned to this user
            $locationIds = UserLocation::where('user_id', $userId)
                ->where('status', true)
                ->pluck('location_id')
                ->toArray();
            
            $query->whereIn('location_code', $locationIds);
        }
        
        return $query->with('location')
            ->orderBy('delivery_status')
            ->orderBy('pk_order_no')
            ->paginate(15);
    }
    
    /**
     * Search orders.
     *
     * @param int $userId
     * @param string $searchTerm
     * @param int|null $locationId
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchOrders(int $userId, string $searchTerm, ?int $locationId = null): LengthAwarePaginator
    {
        $query = Order::query()
            ->where(function ($q) use ($searchTerm) {
                $q->where('order_no', 'like', $searchTerm . '%')
                    ->orWhere('pk_order_no', 'like', $searchTerm . '%')
                    ->orWhere('customer_name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('customer_phone', 'like', $searchTerm . '%');
            })
            ->whereIn('delivery_status', ['Dispatched', 'Delivered']);
        
        if ($locationId) {
            $query->where('location_code', $locationId);
        } else {
            // Get all locations assigned to this user
            $locationIds = UserLocation::where('user_id', $userId)
                ->where('status', true)
                ->pluck('location_id')
                ->toArray();
            
            $query->whereIn('location_code', $locationIds);
        }
        
        return $query->with('location')
            ->orderBy('delivery_status')
            ->orderBy('pk_order_no')
            ->paginate(15);
    }
    
    /**
     * Update delivery status.
     *
     * @param int $orderId
     * @param int $userId
     * @param bool $orderCompleted
     * @return bool
     */
    public function updateDeliveryStatus(int $orderId, int $userId, bool $orderCompleted = false): bool
    {
        $order = $this->getOrderById($orderId);
        
        if (!$order) {
            return false;
        }
        
        $data = [
            'delivery_status' => 'Delivered',
            'order_status' => 'Complete',
            'delivery_person' => $userId
        ];
        
        if ($orderCompleted) {
            $data['amount_paid'] = true;
        }
        
        return $order->update($data);
    }
    
    /**
     * Get order by ID.
     *
     * @param int $orderId
     * @return \App\Models\Order|null
     */
    public function getOrderById(int $orderId): ?Order
    {
        return Order::find($orderId);
    }
    
    /**
     * Assign delivery person to order.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     * @return bool
     */
    public function assignDeliveryPerson(int $orderId, int $deliveryPersonId): bool
    {
        $order = $this->getOrderById($orderId);
        
        if (!$order) {
            return false;
        }
        
        return $order->update([
            'delivery_person' => $deliveryPersonId,
            'delivery_status' => 'Dispatched'
        ]);
    }
}
