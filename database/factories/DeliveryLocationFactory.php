<?php

namespace Database\Factories;

use App\Models\DeliveryLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeliveryLocation>
 */
class DeliveryLocationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeliveryLocation::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'location' => fake()->city(),
            'city' => fake()->city(),
            'sub_city_area' => fake()->streetName(),
            'pin' => fake()->postcode(),
            'delivery_charges' => fake()->randomFloat(2, 0, 100),
            'delivery_time' => fake()->numberBetween(15, 60),
            'is_default' => fake()->boolean(20),
            'status' => fake()->boolean(80),
        ];
    }
}
