<?php

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Lib\QuickServe\CommonConfig as QSCommon;
use Zend\Session\Container;
use Zend\View\Model\JsonModel;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;
use Admin\Form\TimeslotForm;
use QuickServe\Model\TimeslotValidator;

/**
 * This controller is used to manage timeslots for each menus based on kitchen.
 * All the BRD requirements are to be incorporated into the software 
 * with this controller.
 *
 * PHP 7
 *
 * Project name FoodDialer
 * @version 1.0
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.0
 */

 class TimeslotController extends AbstractActionController {
    /**
     * It has an instance of QuickServe\Model\TimeslotTable model
     *
     * @var QuickServe\Model\TimeslotTable $timeslotTable
     */
    protected $timeslotTable;
    /**
     * this returns the wizard view.
     * @return \Admin\Controller\ViewModel
    */
    public function indexAction() {

        if (! $this->authservice)
        {
            $this->authservice = $this->getServiceLocator()
            ->get('AuthService');
        }
        
        $iden = $this->authservice->getIdentity();        
       
        $setting_session = new Container('setting');
        $libCommon = QSCommon::getInstance($this->getServiceLocator());
        $kitchens = $libCommon->getKitchenScreen();
        //$timeslot_data = $this->getTimeslotTable()->fetchAll();
        //dd($timeslot_data);
        $menus = $setting_session->setting['MENU_TYPE'];

        $layoutviewModel = $this->layout();
        $acl = $layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;    	
    	$this->layout()->setVariables(array('page_title' => "Timeslot Manager", 'description' => "Timeslot Manager", 'breadcrumb' => "timeslot "));
        
        return new ViewModel(array(
            'acl' => $acl,
            'loggedUser' => $loguser,        	
            'settings' => $setting_session->setting,
            'menus' => $menus, 
            'kitchens' => $kitchens,
            //'timeslots' => $timeslot_data,
            'flashMessages' => $this->flashMessenger()->getMessages()
        ));
    }

    public function addAction() {

        $layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$sm = $this->getServiceLocator();
		
		$adapt = $sm->get('Write_Adapter');
        $setting_session = new Container('setting');
		$libCommon = Qscommon::getInstance($sm);
		$config_variables = $sm->get('config');
        
        $form = new TimeslotForm($sm);

        $request = $this->getRequest();
        /*
        if ($request->isPost()) {

            $timeslot = new TimeslotValidator();
            $form->setInputFilter($timeslot->getInputFilter());            
            $form->setData($request->getPost());

            if ($form->isValid()) {
                
                $data = $form->getData();
                //dd($data);    
            }            
        }
        */

        $this->layout()->setVariables(array('page_title'=>"Add Timeslot",'breadcrumb'=>"Add timeslot"));

		return array(
            'form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages'],
            'settings' => $setting_session->setting,
		);   
    }


    public function createAction() {
        
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCommon = Qscommon::getInstance($sm);
        $config_variables = $sm->get('config');

        $form = new TimeslotForm($sm);

        $request = $this->getRequest();
        if ($request->isPost()) {

            $timeslot = new TimeslotValidator();
            $form->setInputFilter($timeslot->getInputFilter());            
            $form->setData($request->getPost());

            if ($form->isValid()) {

                $data = $form->getData();
                //$slotArr = array();
                $daysofweek = array( 0 => 'Sunday', 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'All' );

                $start = new \DateTime($data['starttime']);
                $end = new \DateTime($data['endtime']);
                $interval = new \DateInterval("PT" . $data['interval']. "M");
                //$break = 15;
                $break = $data['span'];
                $breakInterval = new \DateInterval("PT" . $break. "M");

                for ($intStart = $start; $intStart < $end; $intStart->add($interval)->add($breakInterval)) {
                       $endPeriod = clone $intStart;
                       $endPeriod->add($interval);
                       if ($endPeriod > $end) {
                         $endPeriod=$end;
                       }
                       //$slots[] = $intStart->format('H:iA') . ' - ' . $endPeriod->format('H:iA');
                       //$slot = $intStart->format('H:iA') . ' - ' . $endPeriod->format('H:iA');
                       $slotArr[] = array('starttime' => $intStart->format('H:i'), 'endtime' => $endPeriod->format('H:i'),'day' => $daysofweek[$data['days']], 'menu_type' => ($data['menu_type'] == '0') ? 'All' : $data['menu_type'], 'kitchen' => ($data['fk_kitchen_code'] == '0') ? 'All' : $data['fk_kitchen_code']);
                }    
                
                //echo "<pre>"; print_r($slotArr); echo "</pre>"; die;
            }
            else {
                echo '<pre>';print_r($form->getMessages());exit;
            }
        } 

        $this->layout()->setVariables(array('page_title'=>"Create Timeslot",'breadcrumb'=>"Create timeslot"));       

        return array(
            'form' => $form,
            'data' => $data,
            'slots' => $slotArr,
            'language_array' => $config_variables['supported_nonenglish_languages']
        );
    }

    public function addslotsAction() {

        $sm = $this->getServiceLocator();
        $storage_adapter = $sm->get("Write_Adapter");
        
        //$id = $this->params()->fromPost('id');
        $slots = $this->params()->fromPost('slots');
        //$request = $this->getRequest()->getPost();
        //$request = $this->params()->fromPost();
        //echo "add slots...."; 
        //dd($slots);
        $data = $this->getTimeslotTable()->saveSlots($slots);
        //dd($request['slots']);
        if($data) {
            return new JsonModel(array('status' => 'success'));
        }
        else {
            return new JsonModel(array('status' => 'error'));
        }
    }

    public function editAction() {

        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        
        $id = (int) $this->params('id');

        if (!$id) {
            return $this->redirect()->toRoute('timeslot');
        }
        
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');     
        
        //$id = $this->params()->fromPost('id');

        $data = $this->getTimeslotTable()->updateSlot($id);

        /*
        if($data) {
            return new JsonModel(array('status' => 'success'));
        }
        else {
            return new JsonModel(array('status' => 'error'));
        }
        */
        return $this->redirect()->toRoute('timeslot');
    }

    public function ajxTimeslotAction() {
        //dd('gotcha here...');
        if (! $this->authservice)
        {
            $this->authservice = $this->getServiceLocator()
            ->get('AuthService');
        }

        $iden = $this->authservice->getIdentity();

        $layout = $this->layout();
        $acl = $layout->acl;

        $day = $this->params()->fromQuery('day');
        $menu = $this->params()->fromQuery('menu');
        $kitchen = $this->params()->fromQuery('kitchen');
        $status = $this->params()->fromQuery('status');

        $filter = array(
            'day' => $day,
            'menu' => $menu,
            'kitchen' => $kitchen,
            'status' => $status
        ); 

        $viewModel = new ViewModel();
        
        $loggedUser = $layout->loggedUser;
        
        $select = new QSelect(); 

        $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
        $arrColumns = array('0'=>'id','1'=>'starttime','2'=>'endtime','3'=>'day','4'=>'menu_type','5'=>"kitchen",'6'=>"status");
        
        $order_by = $arrColumns[$arrOrder[0]['column']];
        $order = $arrOrder[0]['dir'];
         
        $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
         
        $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
        $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
        $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
        $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
        $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";

        $status = $this->params()->fromQuery('status');      
        
        $select->where(
        
            new \Zend\Db\Sql\Predicate\PredicateSet(
                array(                                                          
                    new \Zend\Db\Sql\Predicate\Operator('starttime', 'LIKE', '%'.$search.'%'),                               
                    new \Zend\Db\Sql\Predicate\Operator('endtime', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('day', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('menu_type', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('kitchen', 'LIKE', '%'.$search.'%'),
                ),
                // optional; OP_AND is default
                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )       
        );  

        $select->order($order_by . ' ' . $order);   

        $timeslot_data = $this->getTimeslotTable()->fetchAll($select,$page,$filter);  

        $timeslot_data->setCurrentPageNumber($page)
        ->setItemCountPerPage($itemsPerPage)
        ->setPageRange(7);

        $returnVar = array();
        $returnVar['draw'] = $draw;
        $returnVar['recordsTotal'] = $timeslot_data->getTotalItemCount();
        $returnVar['recordsFiltered'] = $timeslot_data->getTotalItemCount();
        $returnVar['data'] = array();  

        foreach ($timeslot_data as $timeslot) {
            $arrTmp = array();
            array_push($arrTmp, $timeslot['id']);
            $start = new \DateTime($timeslot['starttime']);
            $end = new \DateTime($timeslot['endtime']);
            array_push($arrTmp, $start->format('H:i'));
            array_push($arrTmp, $end->format('H:i'));
            array_push($arrTmp, $timeslot['day']);
            array_push($arrTmp, $timeslot['menu_type']);
            array_push($arrTmp, $timeslot['kitchen']);

            $status = ($timeslot['status'] == 1) ? '<span class="active">Active</span>' : '<span class="inactive">Inactive</span>';
            $action = ($timeslot['status'] == 1) ? '<a href="'.$this->url()->fromRoute('timeslot', array('action' => 'edit', 'id' => $timeslot['id'])).'" class="btn btn5 btn_pencil5"><button class="smBtn redBg has-tip tip-top" data-tooltip title="Deactivate" data-text-swap="Wait.." data-id="'.$timeslot['id'].'"><i class="fa fa-ban"></i></button></a>' : '<a href="'.$this->url()->fromRoute('timeslot', array('action' => 'edit', 'id' => $timeslot['id'])).'" class="btn btn5 btn_pencil5"><button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip title="Activate" data-id="'.$timeslot['id'].'"><i class="fa fa-check-circle"></i></button></a>';            

            array_push($arrTmp, $status);
            array_push($arrTmp, $action);

            array_push($returnVar['data'],$arrTmp);
        } 

        return new JsonModel($returnVar);      

    }

     /**
     * Get instance of QuickServe\Model\TimeslotTable
     *
     * @return QuickServe\Model\TimeslotTable
     */
    public function getTimeslotTable()
    {
        if (!$this->timeslotTable) {
            $sm = $this->getServiceLocator();
            $this->timeslotTable = $sm->get('QuickServe\Model\TimeslotTable');
        }
        return $this->timeslotTable;
    }    
    
 }