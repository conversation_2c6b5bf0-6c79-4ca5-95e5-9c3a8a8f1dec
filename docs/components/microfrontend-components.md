# Microfrontend Components

This document provides detailed information about the components specific to each microfrontend in the QuickServe frontend application.

## Overview

Each microfrontend has its own set of components that are specific to its business domain. These components are organized in the following directory structure:

```
src/components/microfrontends/
├── customer/      # Customer components
├── payment/       # Payment components
├── order/         # Order components
├── kitchen/       # Kitchen components
├── delivery/      # Delivery components
└── dashboard/     # Dashboard components
```

## Customer Microfrontend Components

### CustomerList

The `CustomerList` component displays a list of customers with search, filtering, and pagination.

**File**: `src/components/microfrontends/customer/customer-list.tsx`

**Props**:
- `initialPage?: number`: The initial page to display (default: 1)
- `perPage?: number`: The number of items per page (default: 10)

**State**:
- Uses `useCustomerStore` for customer data and operations

**Example**:
```tsx
<CustomerList initialPage={1} perPage={20} />
```

### CustomerDetail

The `CustomerDetail` component displays detailed information about a customer.

**File**: `src/components/microfrontends/customer/customer-detail.tsx`

**Props**:
- `customerId: number`: The ID of the customer to display

**State**:
- Uses `useCustomerStore` for customer data and operations

**Example**:
```tsx
<CustomerDetail customerId={123} />
```

### CustomerForm

The `CustomerForm` component provides a form for creating and editing customers.

**File**: `src/components/microfrontends/customer/customer-form.tsx`

**Props**:
- `customer?: Customer`: The customer to edit (if not provided, a new customer will be created)
- `onSubmit?: (customer: Customer) => void`: Callback function called when the form is submitted
- `onCancel?: () => void`: Callback function called when the form is cancelled

**State**:
- Uses `useCustomerStore` for customer operations

**Example**:
```tsx
<CustomerForm
  customer={selectedCustomer}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

## Payment Microfrontend Components

### PaymentList

The `PaymentList` component displays a list of payments with search, filtering, and pagination.

**File**: `src/components/microfrontends/payment/payment-list.tsx`

**Props**:
- `initialPage?: number`: The initial page to display (default: 1)
- `perPage?: number`: The number of items per page (default: 10)

**State**:
- Uses `usePaymentStore` for payment data and operations

**Example**:
```tsx
<PaymentList initialPage={1} perPage={20} />
```

### PaymentDetail

The `PaymentDetail` component displays detailed information about a payment.

**File**: `src/components/microfrontends/payment/payment-detail.tsx`

**Props**:
- `paymentId: number`: The ID of the payment to display

**State**:
- Uses `usePaymentStore` for payment data and operations

**Example**:
```tsx
<PaymentDetail paymentId={123} />
```

### PaymentForm

The `PaymentForm` component provides a form for creating and processing payments.

**File**: `src/components/microfrontends/payment/payment-form.tsx`

**Props**:
- `orderId?: number`: The ID of the order to pay for
- `onSubmit?: (payment: Payment) => void`: Callback function called when the form is submitted
- `onCancel?: () => void`: Callback function called when the form is cancelled

**State**:
- Uses `usePaymentStore` for payment operations

**Example**:
```tsx
<PaymentForm
  orderId={456}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

## Order Microfrontend Components

### OrderList

The `OrderList` component displays a list of orders with search, filtering, and pagination.

**File**: `src/components/microfrontends/order/order-list.tsx`

**Props**:
- `initialPage?: number`: The initial page to display (default: 1)
- `perPage?: number`: The number of items per page (default: 10)

**State**:
- Uses `useOrderStore` for order data and operations

**Example**:
```tsx
<OrderList initialPage={1} perPage={20} />
```

### OrderDetail

The `OrderDetail` component displays detailed information about an order.

**File**: `src/components/microfrontends/order/order-detail.tsx`

**Props**:
- `orderId: number`: The ID of the order to display

**State**:
- Uses `useOrderStore` for order data and operations

**Example**:
```tsx
<OrderDetail orderId={123} />
```

### OrderForm

The `OrderForm` component provides a form for creating and editing orders.

**File**: `src/components/microfrontends/order/order-form.tsx`

**Props**:
- `order?: Order`: The order to edit (if not provided, a new order will be created)
- `onSubmit?: (order: Order) => void`: Callback function called when the form is submitted
- `onCancel?: () => void`: Callback function called when the form is cancelled

**State**:
- Uses `useOrderStore` for order operations

**Example**:
```tsx
<OrderForm
  order={selectedOrder}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

## Kitchen Microfrontend Components

### KitchenOrderList

The `KitchenOrderList` component displays a list of orders for the kitchen with search, filtering, and pagination.

**File**: `src/components/microfrontends/kitchen/kitchen-order-list.tsx`

**Props**:
- `kitchenId?: number`: The ID of the kitchen to display orders for
- `initialPage?: number`: The initial page to display (default: 1)
- `perPage?: number`: The number of items per page (default: 10)

**State**:
- Uses `useKitchenStore` for kitchen data and operations

**Example**:
```tsx
<KitchenOrderList kitchenId={1} initialPage={1} perPage={20} />
```

### KitchenOrderDetail

The `KitchenOrderDetail` component displays detailed information about an order for the kitchen.

**File**: `src/components/microfrontends/kitchen/kitchen-order-detail.tsx`

**Props**:
- `orderId: number`: The ID of the order to display

**State**:
- Uses `useKitchenStore` for kitchen data and operations

**Example**:
```tsx
<KitchenOrderDetail orderId={123} />
```

## Delivery Microfrontend Components

### DeliveryOrderList

The `DeliveryOrderList` component displays a list of orders for delivery with search, filtering, and pagination.

**File**: `src/components/microfrontends/delivery/delivery-order-list.tsx`

**Props**:
- `initialPage?: number`: The initial page to display (default: 1)
- `perPage?: number`: The number of items per page (default: 10)

**State**:
- Uses `useDeliveryStore` for delivery data and operations

**Example**:
```tsx
<DeliveryOrderList initialPage={1} perPage={20} />
```

### DeliveryOrderDetail

The `DeliveryOrderDetail` component displays detailed information about a delivery order.

**File**: `src/components/microfrontends/delivery/delivery-order-detail.tsx`

**Props**:
- `orderId: number`: The ID of the order to display

**State**:
- Uses `useDeliveryStore` for delivery data and operations

**Example**:
```tsx
<DeliveryOrderDetail orderId={123} />
```

### DeliveryMap

The `DeliveryMap` component displays a map with delivery routes and agent locations.

**File**: `src/components/microfrontends/delivery/delivery-map.tsx`

**Props**:
- `route: DeliveryRoute`: The delivery route to display
- `agent?: DeliveryAgent`: The delivery agent to display

**Example**:
```tsx
<DeliveryMap route={deliveryRoute} agent={deliveryAgent} />
```

## Dashboard Microfrontend Components

### DashboardOverview

The `DashboardOverview` component displays an overview of the system with key metrics and quick actions.

**File**: `src/components/microfrontends/dashboard/dashboard-overview.tsx`

**Props**: None

**State**:
- Uses various stores for data

**Example**:
```tsx
<DashboardOverview />
```

### DashboardCharts

The `DashboardCharts` component displays charts and visualizations for the dashboard.

**File**: `src/components/microfrontends/dashboard/dashboard-charts.tsx`

**Props**:
- `type: 'orders' | 'revenue' | 'customers'`: The type of chart to display
- `period: 'day' | 'week' | 'month' | 'year'`: The time period for the chart

**Example**:
```tsx
<DashboardCharts type="orders" period="week" />
```

## Component Communication

Components within a microfrontend communicate through:

1. **Props**: For parent-child communication
2. **Zustand Stores**: For shared state within a microfrontend
3. **Callbacks**: For child-parent communication

Components from different microfrontends should not communicate directly. Instead, they should use:

1. **URL Parameters**: For simple data passing during navigation
2. **Zustand Stores**: For shared state that needs to be accessed by multiple microfrontends
3. **Event Bus**: For loosely coupled communication between microfrontends

## Component Testing

Each component should have its own tests in a `__tests__` directory next to the component. Tests should cover:

1. **Rendering**: Test that the component renders correctly
2. **User Interactions**: Test that user interactions work correctly
3. **State Changes**: Test that state changes are reflected in the UI
4. **Error Handling**: Test that errors are handled correctly

Example test for `CustomerList`:

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerList } from '../customer-list';
import { useCustomerStore } from '@/lib/store/customer-store';

// Mock the store
jest.mock('@/lib/store/customer-store');

describe('CustomerList', () => {
  beforeEach(() => {
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: false,
      error: null,
      fetchCustomers: jest.fn(),
    });
  });

  it('renders the customer list', () => {
    render(<CustomerList />);
    expect(screen.getByText('Customers')).toBeInTheDocument();
  });

  // ... other tests
});
```

## Conclusion

Each microfrontend has its own set of components that are specific to its business domain. These components are organized in a consistent directory structure and follow the same design patterns and guidelines. By keeping components specific to their microfrontend, we can ensure that each microfrontend is independent and can be developed, tested, and deployed separately.
