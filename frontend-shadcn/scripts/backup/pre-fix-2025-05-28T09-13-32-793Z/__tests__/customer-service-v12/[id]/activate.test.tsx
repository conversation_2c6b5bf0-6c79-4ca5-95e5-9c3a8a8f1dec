import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Activate } from '@/components/customer-service-v12/[id]/activate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Activate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Activate />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Activate')).toBeInTheDocument();
  });
});