<?php
$form = $this->form;
$form->setAttribute('action', $this->url('tax', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
      <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
          <fieldset>
          <legend>
          Tax Info
        </legend>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('tax_name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                  echo $this->formHidden($form->get('tax_id'));
                  echo $this->formElement($form->get('tax_name'));
                  echo $this->formElementErrors()
                    ->setMessageOpenFormat('<small class="error">')
                    
                    ->setMessageCloseString('</small>')
                    ->render($form->get('tax_name'));
                ?>
        
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('tax')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php 
                    echo $this->formElement($form->get('tax'));
                    echo $this->formElementErrors($form->get('tax')); 
                  ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('tax_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('tax_type'));
                  echo $this->formElementErrors($form->get('tax_type')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('city')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('city'));
                  echo $this->formElementErrors($form->get('city')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('priority')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('priority'));
                  echo $this->formElementErrors($form->get('priority')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('apply_all_product')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('apply_all_product'));
                  echo $this->formElementErrors($form->get('apply_all_product')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('base_amount')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('base_amount'));
                  echo $this->formElementErrors($form->get('base_amount')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('tax_on')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('tax_on'));
                  echo $this->formElementErrors($form->get('tax_on')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('apply_for_catalog')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              <?php 
                  echo $this->formElement($form->get('apply_for_catalog'));
                  echo $this->formElementErrors($form->get('apply_for_catalog')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('date_effective_from')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <div class="dateText">
                  <?php 
                    echo $this->formElement($form->get('date_effective_from'));
                    echo $this->formElementErrors($form->get('date_effective_from')); 
                  ?>
                 </div> 
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('date_effective_till')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <div class="dateText">
                  <?php 
                      echo $this->formElement($form->get('date_effective_till'));
                      echo $this->formElementErrors($form->get('date_effective_till')); 
                    ?>
                  </div>  
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns statuscls">
                <?php 
                  echo $this->formElement($form->get('status'));
                  echo $this->formElementErrors($form->get('status'));
                  echo $this->formElement($form->get('csrf')); 
                ?>
              </div>
        <?php echo $this->formElement($form->get('backurl')); ?>
            </div>
      </fieldset>
      
      <div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
                <button type="submit"  id="submitbutton" class="button  left tiny left5 dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
                <button type="submit" id="cancelbutton" class="button left tiny left5 redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div></div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>


<script type="text/javascript">
  $("#maxDate").datepicker({minDate:0});   
  $("#minDate").datepicker({minDate:0});   
   $(document).ready(function() {

     $("form").submit(function(){
  
       //$(document).on("click","#submitbutton",function()
        
        var startDateStr = $("#maxDate").val();
        var startDateArr = startDateStr.split("/");
        var startDate = new Date(startDateArr[2], startDateArr[0], startDateArr[1]);
        
        var endDateStr = $("#minDate").val();
        var endDateArr = endDateStr.split("/");
        var endDate = new Date(endDateArr[2], endDateArr[0], endDateArr[1]);
  
        
        if(startDate  > endDate)
        {
          alert("Start date cannot be greater than end date.");
          return false;
        }
      });
    });
</script>

