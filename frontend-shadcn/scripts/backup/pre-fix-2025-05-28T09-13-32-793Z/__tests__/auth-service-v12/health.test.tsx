import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthHealth } from '@/components/auth-service-v12/health';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthHealth', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthHealth />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthHealth')).toBeInTheDocument();
  });
});