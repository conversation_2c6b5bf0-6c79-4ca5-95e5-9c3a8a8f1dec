openapi: 3.1.0
info:
  title: Food Delivery API Gateway
  description: API Gateway for the Food Delivery microservices architecture
  version: 2.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/v2
    description: Local development server
  - url: https://tenant.cubeonebiz.com/v2
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        status:
          type: string
          enum: [error]
          description: Response status
          example: error
        message:
          type: string
          description: Error message
          example: Unauthorized access

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [ok, error]
          description: Health status
          example: ok
        service:
          type: string
          description: Service name
          example: auth-service
        version:
          type: string
          description: Service version
          example: 12.0
        timestamp:
          type: string
          format: date-time
          description: Current timestamp
          example: 2023-06-15T12:34:56Z
        database:
          type: string
          enum: [connected, disconnected]
          description: Database connection status
          example: connected

paths:
  # Auth Service Routes
  /auth/login:
    post:
      summary: Login
      description: Authenticates a user and returns a token
      operationId: login
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  description: Username
                  example: admin
                password:
                  type: string
                  description: Password
                  example: admin123
              required:
                - username
                - password
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                        properties:
                          id:
                            type: integer
                            example: 1
                          first_name:
                            type: string
                            example: Admin
                          last_name:
                            type: string
                            example: User
                          email:
                            type: string
                            example: <EMAIL>
                      token:
                        type: string
                        description: Authentication token
                      token_type:
                        type: string
                        example: Bearer
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/logout:
    post:
      summary: Logout
      description: Logs out a user
      operationId: logout
      tags:
        - Auth
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful logout
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Successfully logged out
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Health Check Routes
  /health/auth:
    get:
      summary: Auth Service Health Check
      description: Check the health of the Auth Service
      operationId: authHealthCheck
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '500':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /health/customer:
    get:
      summary: Customer Service Health Check
      description: Check the health of the Customer Service
      operationId: customerHealthCheck
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '500':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /health/meal:
    get:
      summary: Meal Service Health Check
      description: Check the health of the Meal Service
      operationId: mealHealthCheck
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '500':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
