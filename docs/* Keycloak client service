/**
 * Keycloak client service
 *
 * @var \SanAuth\Service\KeycloakClient
 */
protected $keycloakClient;

/**
 * OneSso user service
 *
 * @var \SanAuth\Service\OnessoUserService
 */
protected $onessoUserService;

/**
 * Constructor
 *
 * @param \Zend\Authentication\AuthenticationService $authservice
 * @param \SanAuth\Model\SanStorage $storage
 * @param \QuickServe\Model\UserTable $usertable
 * @param array $config
 * @param \SanAuth\Service\KeycloakClient $keycloakClient
 * @param \SanAuth\Service\OnessoUserService $onessoUserService
 */
public function __construct($authservice, $storage, $usertable, $config, $keycloakClient = null, $onessoUserService = null)
{
    $this->authservice = $authservice;
    $this->storage = $storage;
    $this->usertable = $usertable;
    $this->config = $config;
    $this->keycloakClient = $keycloakClient;
    $this->onessoUserService = $onessoUserService;
}