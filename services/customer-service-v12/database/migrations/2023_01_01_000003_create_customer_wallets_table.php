<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_wallet', function (Blueprint $table) {
            $table->id('customer_wallet_id');
            $table->unsignedBigInteger('fk_customer_code');
            $table->decimal('wallet_amount', 10, 2);
            $table->enum('amount_type', ['cr', 'dr', 'lock']);
            $table->text('description')->nullable();
            $table->date('payment_date')->nullable();
            $table->string('payment_type', 50)->nullable();
            $table->string('context', 50)->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->string('reference_no', 100)->nullable();
            $table->timestamps();

            $table->foreign('fk_customer_code')
                  ->references('pk_customer_code')
                  ->on('customers')
                  ->onDelete('cascade');

            $table->index(['fk_customer_code', 'amount_type']);
            $table->index(['fk_customer_code', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_wallet');
    }
};
