name: CD

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  deploy:
    name: Deploy to Kubernetes
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service-v12, payment-service-v12, quickserve-service-v12, customer-service-v12]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/${{ github.repository }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,format=short

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./services/${{ matrix.service }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Set up Kubernetes tools
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: 'latest'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name ${{ secrets.EKS_CLUSTER_NAME }} --region ${{ secrets.AWS_REGION }}

      - name: Deploy to Kubernetes (Blue)
        run: |
          # Set image tag
          IMAGE_TAG=$(echo ${{ steps.meta.outputs.tags }} | cut -d' ' -f1)
          
          # Update Kubernetes deployment
          cd kubernetes/${{ matrix.service }}
          
          # Update image in values.yaml
          sed -i "s|image: .*|image: $IMAGE_TAG|g" values.yaml
          
          # Deploy blue environment
          helm upgrade --install ${{ matrix.service }}-blue . \
            --set environment=blue \
            --set replicaCount=2 \
            --set service.port=8000 \
            --wait

      - name: Run smoke tests
        run: |
          # Wait for deployment to be ready
          kubectl rollout status deployment/${{ matrix.service }}-blue
          
          # Get service URL
          SERVICE_URL=$(kubectl get svc ${{ matrix.service }}-blue -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
          
          # Run smoke tests
          cd services/${{ matrix.service }}
          php artisan test --filter=SmokeTest

      - name: Switch traffic to blue environment
        run: |
          # Update Kong API Gateway configuration
          cd kong/services
          
          # Update service URL in Kong configuration
          sed -i "s|host: ${{ matrix.service }}|host: ${{ matrix.service }}-blue|g" ${{ matrix.service }}.yaml
          
          # Apply Kong configuration
          kubectl apply -f ${{ matrix.service }}.yaml

      - name: Deploy to Kubernetes (Green)
        run: |
          # Set image tag
          IMAGE_TAG=$(echo ${{ steps.meta.outputs.tags }} | cut -d' ' -f1)
          
          # Update Kubernetes deployment
          cd kubernetes/${{ matrix.service }}
          
          # Update image in values.yaml
          sed -i "s|image: .*|image: $IMAGE_TAG|g" values.yaml
          
          # Deploy green environment
          helm upgrade --install ${{ matrix.service }}-green . \
            --set environment=green \
            --set replicaCount=2 \
            --set service.port=8000 \
            --wait

      - name: Run smoke tests on green environment
        run: |
          # Wait for deployment to be ready
          kubectl rollout status deployment/${{ matrix.service }}-green
          
          # Get service URL
          SERVICE_URL=$(kubectl get svc ${{ matrix.service }}-green -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
          
          # Run smoke tests
          cd services/${{ matrix.service }}
          php artisan test --filter=SmokeTest

      - name: Switch traffic to green environment
        run: |
          # Update Kong API Gateway configuration
          cd kong/services
          
          # Update service URL in Kong configuration
          sed -i "s|host: ${{ matrix.service }}-blue|host: ${{ matrix.service }}-green|g" ${{ matrix.service }}.yaml
          
          # Apply Kong configuration
          kubectl apply -f ${{ matrix.service }}.yaml

      - name: Clean up old deployment
        run: |
          # Delete old deployment
          helm uninstall ${{ matrix.service }}-blue

  notify:
    name: Notify Slack
    runs-on: ubuntu-latest
    needs: deploy
    if: always()
    
    steps:
      - name: Notify Slack on success
        if: ${{ needs.deploy.result == 'success' }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: Deployment Successful
          SLACK_MESSAGE: 'Services have been deployed successfully to production!'
          SLACK_COLOR: good

      - name: Notify Slack on failure
        if: ${{ needs.deploy.result != 'success' }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: Deployment Failed
          SLACK_MESSAGE: 'Deployment to production failed. Please check the logs.'
          SLACK_COLOR: danger
