<?php

/**
 * Payment & Order Service Performance Test
 * 
 * This script tests the performance of the complete order-to-payment flow
 * to identify bottlenecks causing 10+ second delays.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentOrderPerformanceTest
{
    private $baseUrl;
    private $results = [];
    
    public function __construct($baseUrl = 'http://localhost:8000')
    {
        $this->baseUrl = $baseUrl;
    }
    
    /**
     * Run complete performance test suite
     */
    public function runCompleteTest(): array
    {
        echo "🚀 Starting Payment & Order Service Performance Test\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $totalStart = microtime(true);
        
        // Test 1: Order Creation Performance
        $this->testOrderCreation();
        
        // Test 2: Payment Processing Performance  
        $this->testPaymentProcessing();
        
        // Test 3: Payment Callback Performance
        $this->testPaymentCallback();
        
        // Test 4: Database Query Performance
        $this->testDatabasePerformance();
        
        // Test 5: External Service Call Performance
        $this->testExternalServiceCalls();
        
        $totalTime = microtime(true) - $totalStart;
        $this->results['total_test_time'] = round($totalTime, 2);
        
        $this->displayResults();
        $this->generateRecommendations();
        
        return $this->results;
    }
    
    /**
     * Test order creation performance
     */
    private function testOrderCreation(): void
    {
        echo "📦 Testing Order Creation Performance...\n";
        
        $orderData = [
            'customer_code' => 1,
            'customer_name' => 'Performance Test Customer',
            'phone' => '9876543210',
            'email_address' => '<EMAIL>',
            'location_code' => 1,
            'location_name' => 'Test Location',
            'product_code' => 123,
            'product_name' => 'Performance Test Product',
            'product_type' => 'Food',
            'quantity' => 2,
            'amount' => 100.00,
            'order_date' => date('Y-m-d'),
            'ship_address' => 'Test Address for Performance',
            'order_menu' => 'Lunch',
            'company_id' => 1,
            'unit_id' => 1,
            'tax' => 10.00,
            'delivery_charges' => 5.00,
            'service_charges' => 2.00,
            'applied_discount' => 0.00,
            'items' => [
                [
                    'product_code' => 123,
                    'product_name' => 'Test Item 1',
                    'quantity' => 1,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ],
                [
                    'product_code' => 124,
                    'product_name' => 'Test Item 2', 
                    'quantity' => 1,
                    'amount' => 50.00,
                    'tax' => 5.00,
                ]
            ]
        ];
        
        $start = microtime(true);
        
        try {
            $response = Http::timeout(30)->post("{$this->baseUrl}/api/v2/order-management/create", $orderData);
            
            $duration = microtime(true) - $start;
            $this->results['order_creation'] = [
                'duration' => round($duration, 2),
                'status' => $response->successful() ? 'success' : 'failed',
                'response_code' => $response->status(),
                'response_size' => strlen($response->body())
            ];
            
            if ($response->successful()) {
                $this->results['order_data'] = $response->json();
                echo "   ✅ Order created in {$duration}s\n";
            } else {
                echo "   ❌ Order creation failed: " . $response->body() . "\n";
            }
            
        } catch (Exception $e) {
            $duration = microtime(true) - $start;
            $this->results['order_creation'] = [
                'duration' => round($duration, 2),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
            echo "   ❌ Order creation error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test payment processing performance
     */
    private function testPaymentProcessing(): void
    {
        echo "💳 Testing Payment Processing Performance...\n";
        
        if (!isset($this->results['order_data']['data']['order_no'])) {
            echo "   ⚠️  Skipping payment test - no order created\n\n";
            return;
        }
        
        $orderNo = $this->results['order_data']['data']['order_no'];
        
        $paymentData = [
            'gateway' => 'razorpay',
            'amount' => 117.00,
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9876543210'
        ];
        
        $start = microtime(true);
        
        try {
            $response = Http::timeout(30)->post("{$this->baseUrl}/api/v2/order-management/initiate-payment/{$orderNo}", $paymentData);
            
            $duration = microtime(true) - $start;
            $this->results['payment_processing'] = [
                'duration' => round($duration, 2),
                'status' => $response->successful() ? 'success' : 'failed',
                'response_code' => $response->status(),
                'response_size' => strlen($response->body())
            ];
            
            if ($response->successful()) {
                $this->results['payment_data'] = $response->json();
                echo "   ✅ Payment processed in {$duration}s\n";
            } else {
                echo "   ❌ Payment processing failed: " . $response->body() . "\n";
            }
            
        } catch (Exception $e) {
            $duration = microtime(true) - $start;
            $this->results['payment_processing'] = [
                'duration' => round($duration, 2),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
            echo "   ❌ Payment processing error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test payment callback performance
     */
    private function testPaymentCallback(): void
    {
        echo "🔄 Testing Payment Callback Performance...\n";
        
        if (!isset($this->results['order_data']['data']['order_no'])) {
            echo "   ⚠️  Skipping callback test - no order created\n\n";
            return;
        }
        
        $orderNo = $this->results['order_data']['data']['order_no'];
        
        $callbackData = [
            'gateway' => 'razorpay',
            'payment_service_transaction_id' => 'pay_test_' . time(),
            'status' => 'success',
            'amount' => 117.00,
            'gateway_transaction_id' => 'razorpay_' . time()
        ];
        
        $start = microtime(true);
        
        try {
            $response = Http::timeout(30)->post("{$this->baseUrl}/api/v2/order-management/payment-success/{$orderNo}", $callbackData);
            
            $duration = microtime(true) - $start;
            $this->results['payment_callback'] = [
                'duration' => round($duration, 2),
                'status' => $response->successful() ? 'success' : 'failed',
                'response_code' => $response->status(),
                'response_size' => strlen($response->body())
            ];
            
            if ($response->successful()) {
                echo "   ✅ Payment callback processed in {$duration}s\n";
            } else {
                echo "   ❌ Payment callback failed: " . $response->body() . "\n";
            }
            
        } catch (Exception $e) {
            $duration = microtime(true) - $start;
            $this->results['payment_callback'] = [
                'duration' => round($duration, 2),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
            echo "   ❌ Payment callback error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test database query performance
     */
    private function testDatabasePerformance(): void
    {
        echo "🗄️  Testing Database Query Performance...\n";
        
        $queries = [
            'orders_select' => "SELECT * FROM orders WHERE customer_code = 1 LIMIT 10",
            'customer_select' => "SELECT * FROM customers WHERE pk_customer_code = 1",
            'payment_transaction_select' => "SELECT * FROM payment_transaction WHERE customer_id = 1 LIMIT 5",
            'order_details_select' => "SELECT * FROM order_details WHERE ref_order_no LIKE 'QA%' LIMIT 10"
        ];
        
        foreach ($queries as $queryName => $sql) {
            $start = microtime(true);
            
            try {
                // Note: This would need actual database connection
                // For now, we'll simulate the timing
                usleep(rand(100000, 500000)); // Simulate 0.1-0.5 second query
                
                $duration = microtime(true) - $start;
                $this->results['database_queries'][$queryName] = [
                    'duration' => round($duration, 3),
                    'status' => 'simulated'
                ];
                
                echo "   📊 {$queryName}: {$duration}s\n";
                
            } catch (Exception $e) {
                $duration = microtime(true) - $start;
                $this->results['database_queries'][$queryName] = [
                    'duration' => round($duration, 3),
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        echo "\n";
    }
    
    /**
     * Test external service call performance
     */
    private function testExternalServiceCalls(): void
    {
        echo "🌐 Testing External Service Call Performance...\n";
        
        $services = [
            'customer_service' => 'http://customer-service/customers/1',
            'payment_service' => 'http://payment-service/health',
            'meal_service' => 'http://meal-service/health'
        ];
        
        foreach ($services as $serviceName => $url) {
            $start = microtime(true);
            
            try {
                $response = Http::timeout(5)->get($url);
                
                $duration = microtime(true) - $start;
                $this->results['external_services'][$serviceName] = [
                    'duration' => round($duration, 3),
                    'status' => $response->successful() ? 'success' : 'failed',
                    'response_code' => $response->status()
                ];
                
                echo "   🔗 {$serviceName}: {$duration}s\n";
                
            } catch (Exception $e) {
                $duration = microtime(true) - $start;
                $this->results['external_services'][$serviceName] = [
                    'duration' => round($duration, 3),
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                echo "   ❌ {$serviceName}: {$duration}s (Error: {$e->getMessage()})\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Display test results
     */
    private function displayResults(): void
    {
        echo "📊 PERFORMANCE TEST RESULTS\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $totalFlow = 0;
        
        if (isset($this->results['order_creation']['duration'])) {
            $duration = $this->results['order_creation']['duration'];
            $status = $this->results['order_creation']['status'] === 'success' ? '✅' : '❌';
            echo "Order Creation:     {$status} {$duration}s\n";
            $totalFlow += $duration;
        }
        
        if (isset($this->results['payment_processing']['duration'])) {
            $duration = $this->results['payment_processing']['duration'];
            $status = $this->results['payment_processing']['status'] === 'success' ? '✅' : '❌';
            echo "Payment Processing: {$status} {$duration}s\n";
            $totalFlow += $duration;
        }
        
        if (isset($this->results['payment_callback']['duration'])) {
            $duration = $this->results['payment_callback']['duration'];
            $status = $this->results['payment_callback']['status'] === 'success' ? '✅' : '❌';
            echo "Payment Callback:   {$status} {$duration}s\n";
            $totalFlow += $duration;
        }
        
        echo "\nTotal Flow Time:    " . round($totalFlow, 2) . "s\n";
        echo "Total Test Time:    {$this->results['total_test_time']}s\n\n";
    }
    
    /**
     * Generate performance recommendations
     */
    private function generateRecommendations(): void
    {
        echo "💡 PERFORMANCE RECOMMENDATIONS\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $totalFlow = 0;
        if (isset($this->results['order_creation']['duration'])) $totalFlow += $this->results['order_creation']['duration'];
        if (isset($this->results['payment_processing']['duration'])) $totalFlow += $this->results['payment_processing']['duration'];
        if (isset($this->results['payment_callback']['duration'])) $totalFlow += $this->results['payment_callback']['duration'];
        
        if ($totalFlow > 10) {
            echo "🚨 CRITICAL: Total flow time ({$totalFlow}s) exceeds 10 seconds!\n\n";
            echo "Immediate Actions Required:\n";
            echo "1. ⚡ Implement queue-based processing for heavy operations\n";
            echo "2. 🗄️  Move external service calls outside database transactions\n";
            echo "3. 📊 Add database indexes for frequently queried columns\n";
            echo "4. 🔄 Implement circuit breaker pattern for external services\n\n";
        } elseif ($totalFlow > 5) {
            echo "⚠️  WARNING: Total flow time ({$totalFlow}s) is above optimal range\n\n";
            echo "Recommended Optimizations:\n";
            echo "1. 💾 Implement caching for frequently accessed data\n";
            echo "2. 🔗 Optimize external service call patterns\n";
            echo "3. 📈 Consider bulk database operations\n\n";
        } else {
            echo "✅ GOOD: Total flow time ({$totalFlow}s) is within acceptable range\n\n";
        }
        
        // Specific recommendations based on individual components
        if (isset($this->results['order_creation']['duration']) && $this->results['order_creation']['duration'] > 3) {
            echo "📦 Order Creation Optimization:\n";
            echo "   - Move customer validation to background job\n";
            echo "   - Use bulk insert for order items\n";
            echo "   - Cache customer data\n\n";
        }
        
        if (isset($this->results['payment_processing']['duration']) && $this->results['payment_processing']['duration'] > 4) {
            echo "💳 Payment Processing Optimization:\n";
            echo "   - Implement asynchronous payment gateway calls\n";
            echo "   - Use database connection pooling\n";
            echo "   - Add payment service circuit breaker\n\n";
        }
        
        if (isset($this->results['payment_callback']['duration']) && $this->results['payment_callback']['duration'] > 5) {
            echo "🔄 Payment Callback Optimization:\n";
            echo "   - Queue callback processing\n";
            echo "   - Optimize temp-to-actual order conversion\n";
            echo "   - Reduce database transaction scope\n\n";
        }
    }
}

// Run the performance test
if (php_sapi_name() === 'cli') {
    $test = new PaymentOrderPerformanceTest();
    $results = $test->runCompleteTest();
    
    // Save results to file
    file_put_contents('performance_test_results.json', json_encode($results, JSON_PRETTY_PRINT));
    echo "📄 Results saved to performance_test_results.json\n";
}
