<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                   <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="makepay" class="common-orange-btn-on-hover"> Make Payment</a>
                </li>
            </ul>
        </div>
    </div>
</div>
      <div id="content" class="clearfix">
        <div class="large-12 columns">


				<div class="filter">
                                    <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/invoice" method="post">
                                        <div class="row">
                                            <div class="medium-12 columns">
                                                <div class="type left">
                                                    <label style="margin:0px" class="left inline" for="right-label">Status &nbsp;:&nbsp;</label>
                                                        <select class="left filterSelect" name="status" id="status">
                                                            <option value="">All</option>
                                                            <option value="paid">Paid</option>
                                                            <option value="unpaid">Unpaid</option>
                                                        </select>

                                                    <label class="left inline" for="minDate"> From  </label>
                                                        <input class="left filterSelect" name ="minDate" id="minDate" readonly="readonly" type="text"   />

                                                   <label class="left inline" for="maxDate" style="margin-left:0"> To  </label>
                                                        <input class="left filterSelect" name ="maxDate" id="maxDate" readonly="readonly" type="text"   />


                                                        <button id="submitButton" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                                                    </div>
                                            </div>
                                        </div>
                                    </form>
				</div>
		
		
		<div class="clearBoth10"></div>
			
        <div class="portlet box yellow">
        	<div class="portlet-title">
            	<h4><i class="fa fa-table"></i>Collections</h4>
			</div>
        	<div class="portlet-body sales_data_table">
        		<div class="filter">
					<div>
						<a class="advance_search_click"> Hide Advance Search </a>
					</div>
				</div>
				
  	      		<table id="collection" class="display" width="100%">
                    <thead>
                        <tr>
                            <th>Invoice No</th>
                            <th>Name</th>
<!--                             <th>Address</th> -->
                            <th>Total Amount <!-- <i class="fa fa-rupee"></i> --></th>
                            <th class="no_sort">Amount Due <!-- <i class="fa fa-rupee"></i> --></th>
                            <th class="no_sort mode">Payment Mode</th>
                            <th class="no_sort action" style="width:220px;">Action</th>
                        </tr>
                    </thead>
				</table>
  			</div>
        </div>
	</div>
</div>
    <!-- END PAGE CONTAINER-->




<script type="text/javascript">
$(document).ready(function() {

	// myPageTable.init();
	
	//alert($("#status").val());
    
    
	function payCollection(invoice_id)
		{
			$('.loader_show'+invoice_id).show();
			var total=$("#total"+invoice_id).data('totalamount');
			var pay_amount=$("#pay"+invoice_id).val();
			var cust_name=$("#pay"+invoice_id).data('custname');
			var fromdate=$("#pay"+invoice_id).data('fromdate');
			var todate=$("#pay"+invoice_id).data('todate');
			var phone = $("#pay"+invoice_id).data('mobile');
			var due_amount=$("#due"+invoice_id).val();

			var mode_of_payment= $("#mode_of_payment"+invoice_id).val();

			if(parseFloat(pay_amount) > parseFloat(due_amount))
			{
				$('.loader_show'+invoice_id).hide();
				alert("Amount must be less than or equal to due amount");
				return false;
			}
			$.ajax({
				 url:'<?php echo $this->basePath()."/collection/pay" ?>',
				 type: "POST",
				 data : {"invoice_id":invoice_id,"cust_name":cust_name,"phone":phone,"from":fromdate,"to":todate,"pay_amount":pay_amount,"total":total,"due_amount":due_amount,"mode_of_payment":mode_of_payment},

				 success:function(result)
				 {
					//alert(result);
					$('.loader_show'+invoice_id).hide();
					var res = jQuery.parseJSON(result);
					if(res)
					{
						
						if(res.status == undefined){
							
							var due_amnt=due_amount-pay_amount;
							$("#due"+invoice_id).val(parseFloat(due_amnt).toFixed(2));
							$("#pay"+invoice_id).val('');
							
							if(due_amnt==0)
							{
							 	// $("#" + invoice_id).removeClass("paginate_button");
							 	// $("#" + invoice_id).removeClass("pay");
	
							  	 $("#" + invoice_id).addClass("paidbutton");
							 	 $("#" + invoice_id).html("Paid");
							 	$("#pay"+invoice_id).attr("disabled", "disabled");
							}
						}else if(res.status == "error"){

							alert(res.msg);
						}
					}
					return false;
				 }
			});


			return false;
		}
		$(document).on('click','.paybtn',function(){
			var invoice_id=$(this).attr("id");

			var pay_amount=$("#pay"+invoice_id).val();
	    	if(pay_amount=="")
	    	{
		    	alert("Please enter payment amount");
		    	return false;
	    	}
	    	else
	    	{
				var checkstr =  confirm('Are you sure you want to pay?');
		   		 if(checkstr == true){
			    	payCollection(invoice_id);
			    	return false;
		   		 }
		    }

		});
    var aoColumns = [];
    $('#collection thead th').each( function () {
        if ( $(this).hasClass('no_sort')) {
        	aoColumns.push( { "bSortable": false } );
        } else {
        	aoColumns.push( null );
        }
    });
    
    var collectionTable = $('#collection').dataTable( {
        "processing": true,
        "serverSide": true,
        "aoColumns":aoColumns,
        "aaSorting": [[0,'desc']],
        "ajax": {
            "url":"/collection/ajx-collection",
            "data": function ( d ) {
                d.status = $("#status").val();
                d.kitchenscreen = $('#selectkitchen').val();
                d.minDate = $('#minDate').val();
                d.maxDate = $('#maxDate').val();
                
         	}
        }


    });

    $("#submitButton").on('click',function(){
       
    	collectionTable.api().ajax.reload();
    });
	/* $("#status").on('change',function(){

		collectionTable.api().ajax.reload(null, true);
		
	}); */

});
</script>
<script type="text/javascript">
  $(document).on('click',"#makepay",function(e){
      e.preventDefault();
      $('.mode').attr("data-step", "1");
      $('.mode').attr("data-intro", "Select payment mode.");
      $('.mode').attr("data-position", "left");
      $('.action').attr("data-step", "2");
      $('.action').attr("data-intro", "Enter amount and click on 'Pay' button");
      $('.action').attr("data-position", "left");
      introJs().start();
      $('.mode').removeAttr("data-step");
      $('.mode').removeAttr("data-intro");
      $('.action').removeAttr("data-step");
      $('.action').removeAttr("data-intro");

  });

</script> 