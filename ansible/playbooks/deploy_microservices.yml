---
- name: Deploy Microservices to EKS
  hosts: bastion
  become: false
  vars:
    microservices:
      - name: auth-service
        namespace: cubeonebiz
        image: cubeonebiz/auth-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: customer-service
        namespace: cubeonebiz
        image: cubeonebiz/customer-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: payment-service
        namespace: cubeonebiz
        image: cubeonebiz/payment-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: analytics-service
        namespace: cubeonebiz
        image: cubeonebiz/analytics-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: meal-service
        namespace: cubeonebiz
        image: cubeonebiz/meal-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: subscription-service
        namespace: cubeonebiz
        image: cubeonebiz/subscription-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
      - name: quickserve-service
        namespace: cubeonebiz
        image: cubeonebiz/quickserve-service:latest
        replicas: 2
        port: 8000
        health_check_path: /api/health
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
  
  tasks:
    - name: Create namespace
      shell: |
        kubectl create namespace {{ item.namespace }} --dry-run=client -o yaml | kubectl apply -f -
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Create deployment manifest
      template:
        src: templates/deployment.yml.j2
        dest: "/tmp/{{ item.name }}-deployment.yml"
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Create service manifest
      template:
        src: templates/service.yml.j2
        dest: "/tmp/{{ item.name }}-service.yml"
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Create HPA manifest
      template:
        src: templates/hpa.yml.j2
        dest: "/tmp/{{ item.name }}-hpa.yml"
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Apply deployment manifest
      shell: |
        kubectl apply -f /tmp/{{ item.name }}-deployment.yml
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Apply service manifest
      shell: |
        kubectl apply -f /tmp/{{ item.name }}-service.yml
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Apply HPA manifest
      shell: |
        kubectl apply -f /tmp/{{ item.name }}-hpa.yml
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
    
    - name: Wait for deployments to be ready
      shell: |
        kubectl rollout status deployment/{{ item.name }} -n {{ item.namespace }} --timeout=300s
      loop: "{{ microservices }}"
      loop_control:
        label: "{{ item.name }}"
