# Structural and Design Issues Audit Report

## Overview

This document contains the results of a comprehensive audit of the codebase for structural and design issues. The audit focused on identifying violations of the Single Responsibility Principle, hardcoded configuration, tight coupling between modules, and missing factory/service registrations.

## 1. Single Responsibility Principle Violations

### 1.1 Large Controllers with Multiple Responsibilities

Several controllers in the codebase handle multiple responsibilities, violating the Single Responsibility Principle:

#### Example 1: `ProductController`

The `ProductController` in the Admin module handles multiple responsibilities:
- Product management
- Meal management
- Product calendar management
- File uploads and image processing
- Import/export functionality
- Data validation

```php
// module/Admin/src/Admin/Controller/ProductController.php
class ProductController extends AbstractActionController
{
    protected $productTable;
    protected $planTable;
    protected $productCalendarTable;
    // Many more properties...

    // Many actions handling different responsibilities
    public function addBulkProductsAction() { /* ... */ }
    // Many more methods...
}
```

#### Example 2: `OrderController`

The `OrderController` in multiple modules handles:
- Order creation
- Order processing
- Order dispatching
- Order delivery
- Payment processing
- Email notifications

```php
// module/Payment/src/Payment/Model/OrderController.php
public function dispatchOrderAction() { /* ... */ }
public function deliverOrderAction() { /* ... */ }
// Many more methods...
```

#### Example 3: `CustomerController`

The `CustomerController` in the QuickServe module handles:
- Customer management
- Payment processing
- Order management
- Activity logging
- Customer import/export

```php
// module/QuickServe/src/QuickServe/Model/CustomerController.php
public function paymentoptionAction() { /* ... */ }
public function orderAction() { /* ... */ }
public function importAction() { /* ... */ }
```

### 1.2 Models with Multiple Responsibilities

Several model classes handle multiple responsibilities:

#### Example: `FrontTable`

The `FrontTable` class handles multiple data access concerns:
- Product data
- Customer data
- Order data
- Location data

### Recommendations:

1. **Split Large Controllers**: Divide large controllers into smaller, focused controllers that handle specific responsibilities.
2. **Create Service Classes**: Extract business logic from controllers into dedicated service classes.
3. **Use Command Pattern**: Implement command classes for specific operations.
4. **Implement Repository Pattern**: Separate data access logic from business logic.

## 2. Hardcoded Configuration

### 2.1 Hardcoded API Keys and Credentials

The codebase contains hardcoded API keys, credentials, and other sensitive information:

#### Example 1: AWS Credentials in `global.php`

```php
// config/autoload/global.php
'aws_s3_credentials'=>array(
    'access_key'=>'********************',
    'secret_key'=>'snCk9n6YgK9Fj3Cq9bYrBegje2IY0PYwtf2i+FDQ',
    'region'=> 'ap-south-1',
    'signatureVersion'=> 'v4',
),
```

#### Example 2: Database Credentials in Configuration Files

```php
// config/autoload/fooddialer.staging.php-bkp
'master_db' => array(
    'driver'    => 'Pdo',
    'username'  => 'admin',
    'password'  => 'password',
    'dsn'       => 'mysql:dbname=test_quickserve_master;host=***********',
    'driver_options' => array(
        PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''
    ),
),
```

### 2.2 Hardcoded URLs and Endpoints

The codebase contains hardcoded URLs and endpoints:

```php
// module/QuickServe/Module.php
$sso_login_path = $config['account_domain'].'oauth/authorize?client_id='.$settings['SSO_CLIENT_ID'].'&response_type=code&redirect_uri='.$config['auth_server_redirect_scheme'].$_SESSION['tenant']['company_details']['domain'].'/callback&scope=';
```

### Recommendations:

1. **Use Environment Variables**: Move sensitive information to environment variables.
2. **Implement Secrets Management**: Use a secrets management solution for API keys and credentials.
3. **Centralize Configuration**: Create a centralized configuration service.
4. **Use Configuration Files**: Move all configuration to appropriate configuration files.

## 3. Tight Coupling Between Modules

### 3.1 Direct Instantiation of Classes

Many classes are directly instantiated instead of being injected:

#### Example 1: Direct Instantiation in Controllers

```php
// module/Stdcatalogue/src/Stdcatalogue/Controller/MenuController.php
$libCommon = QSConfig::getInstance($sm);
$libCatalog = QSCatalogue::getInstance($sm);
$libPromoCode = QSPromoCode::getInstance($sm);
```

#### Example 2: Singleton Pattern Usage

```php
// vendor/Lib/QuickServe/Order.php
$libCustomer = QSCustomer::getInstance($this->service_locator);
```

### 3.2 Direct Access to Other Modules' Components

Controllers and models directly access components from other modules:

```php
// module/Front/src/Front/Controller/FrontController.php
public function getCustomerTable()
{
    if (!$this->customertable)
    {
        $sm = $this->getServiceLocator();
        $this->customertable = $sm->get('QuickServe\Model\CustomerTable');
    }
    return $this->customertable;
}
```

### 3.3 Service Locator Usage

Heavy reliance on the service locator pattern creates hidden dependencies:

```php
// module/Api/src/Api/Controller/ProductController.php
public function getProductInfoAction(){
    $sm = $this->getServiceLocator();
    $adapt = $sm->get('Write_Adapter');
    $libCatalogue = QSCatalogue::getInstance($sm);
    // ...
}
```

### Recommendations:

1. **Use Dependency Injection**: Inject dependencies through constructors instead of using service locator.
2. **Create Factories**: Implement factory classes for creating service instances.
3. **Define Interfaces**: Create interfaces for cross-module communication.
4. **Implement Event System**: Use events for loose coupling between modules.

## 4. Missing Factory/Service Registration

### 4.1 Missing Controller Factories

Many controllers are registered as invokables instead of using factories:

```php
// module/Admin/config/module.config_bk_11112021.php
'controllers' => array(
    'invokables' => array(
        'Admin\Controller\Dashboard' => 'Admin\Controller\DashboardController',
        'Admin\Controller\Customer' => 'Admin\Controller\CustomerController',
        // Many more controllers...
    ),
),
```

### 4.2 Missing Service Registrations

Many services are directly instantiated but not properly registered:

```php
// vendor/Lib/QuickServe/Catalogue.php
public function getProductById($id){
    $libCommon = QSConfig::getInstance($this->service_locator);
    // ...
}
```

### 4.3 Inconsistent Service Registration

Services are registered differently across modules:

```php
// module/QuickServe/config/module.config.php
'service_manager' => array(
    'factories' => array(
        'Zend\Db\Adapter\Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
        // ...
    ),
),
```

```php
// module/Stdcatalogue/Module.php
public function getServiceConfig()
{
    return array(
        'factories' => array(
            'Read_Adapter' => function($sm)
            {
                $dbParams = $sm->get('config');
                $dbAdapter = new Adapter($dbParams['dbr']);
                return $dbAdapter;
            },
            // ...
        ),
    );
}
```

### Recommendations:

1. **Create Controller Factories**: Replace invokables with factories for all controllers.
2. **Register All Services**: Ensure all services are properly registered in Module.php.
3. **Standardize Registration**: Use consistent patterns for service registration.
4. **Use Abstract Factories**: Implement abstract factories for similar service types.

## Conclusion

The codebase exhibits several structural and design issues that affect maintainability, testability, and scalability. By addressing these issues, the codebase can be improved to follow better software design principles and practices.

## Next Steps

1. Prioritize issues based on impact and effort required
2. Create a refactoring plan for each category of issues
3. Implement changes incrementally to minimize disruption
4. Add automated tests to ensure refactoring doesn't break functionality
5. Document design decisions and patterns for future reference
