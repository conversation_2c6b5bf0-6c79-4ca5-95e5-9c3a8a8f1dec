import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id]Config } from '@/components/catalogue-service-v12/[id]/config';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]Config', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id]Config />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]Config')).toBeInTheDocument();
  });
});