<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class CircuitBreakerService
{
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';

    public function __construct(
        private int $failureThreshold = 5,
        private int $timeout = 60, // seconds
        private int $successThreshold = 3 // for half-open state
    ) {}

    /**
     * Execute a callback with circuit breaker protection
     */
    public function execute(callable $callback, string $serviceName = 'default')
    {
        $circuitKey = "circuit_breaker:{$serviceName}";
        $state = $this->getCircuitState($circuitKey);

        switch ($state) {
            case self::STATE_OPEN:
                if ($this->shouldAttemptReset($circuitKey)) {
                    $this->setCircuitState($circuitKey, self::STATE_HALF_OPEN);
                    return $this->executeInHalfOpenState($callback, $circuitKey);
                } else {
                    throw new CircuitBreakerOpenException("Circuit breaker is open for service: {$serviceName}");
                }

            case self::STATE_HALF_OPEN:
                return $this->executeInHalfOpenState($callback, $circuitKey);

            case self::STATE_CLOSED:
            default:
                return $this->executeInClosedState($callback, $circuitKey);
        }
    }

    /**
     * Execute callback in closed state
     */
    private function executeInClosedState(callable $callback, string $circuitKey)
    {
        try {
            $result = $callback();
            $this->recordSuccess($circuitKey);
            return $result;
        } catch (Exception $e) {
            $this->recordFailure($circuitKey);
            
            if ($this->shouldOpenCircuit($circuitKey)) {
                $this->openCircuit($circuitKey);
                Log::warning('Circuit breaker opened due to failures', [
                    'circuit_key' => $circuitKey,
                    'failure_count' => $this->getFailureCount($circuitKey)
                ]);
            }
            
            throw $e;
        }
    }

    /**
     * Execute callback in half-open state
     */
    private function executeInHalfOpenState(callable $callback, string $circuitKey)
    {
        try {
            $result = $callback();
            $this->recordSuccess($circuitKey);
            
            if ($this->shouldCloseCircuit($circuitKey)) {
                $this->closeCircuit($circuitKey);
                Log::info('Circuit breaker closed after successful recovery', [
                    'circuit_key' => $circuitKey
                ]);
            }
            
            return $result;
        } catch (Exception $e) {
            $this->openCircuit($circuitKey);
            Log::warning('Circuit breaker reopened after failure in half-open state', [
                'circuit_key' => $circuitKey
            ]);
            throw $e;
        }
    }

    /**
     * Get current circuit state
     */
    private function getCircuitState(string $circuitKey): string
    {
        return Cache::get("{$circuitKey}:state", self::STATE_CLOSED);
    }

    /**
     * Set circuit state
     */
    private function setCircuitState(string $circuitKey, string $state): void
    {
        Cache::put("{$circuitKey}:state", $state, 3600); // 1 hour TTL
    }

    /**
     * Record a successful execution
     */
    private function recordSuccess(string $circuitKey): void
    {
        $successKey = "{$circuitKey}:success_count";
        $currentCount = Cache::get($successKey, 0);
        Cache::put($successKey, $currentCount + 1, 300); // 5 minutes TTL
        
        // Reset failure count on success
        Cache::forget("{$circuitKey}:failure_count");
        Cache::forget("{$circuitKey}:last_failure");
    }

    /**
     * Record a failed execution
     */
    private function recordFailure(string $circuitKey): void
    {
        $failureKey = "{$circuitKey}:failure_count";
        $currentCount = Cache::get($failureKey, 0);
        Cache::put($failureKey, $currentCount + 1, 300); // 5 minutes TTL
        Cache::put("{$circuitKey}:last_failure", time(), 300);
    }

    /**
     * Get current failure count
     */
    private function getFailureCount(string $circuitKey): int
    {
        return Cache::get("{$circuitKey}:failure_count", 0);
    }

    /**
     * Get current success count
     */
    private function getSuccessCount(string $circuitKey): int
    {
        return Cache::get("{$circuitKey}:success_count", 0);
    }

    /**
     * Check if circuit should be opened
     */
    private function shouldOpenCircuit(string $circuitKey): bool
    {
        return $this->getFailureCount($circuitKey) >= $this->failureThreshold;
    }

    /**
     * Check if circuit should be closed
     */
    private function shouldCloseCircuit(string $circuitKey): bool
    {
        return $this->getSuccessCount($circuitKey) >= $this->successThreshold;
    }

    /**
     * Check if we should attempt to reset the circuit
     */
    private function shouldAttemptReset(string $circuitKey): bool
    {
        $lastFailure = Cache::get("{$circuitKey}:last_failure");
        return $lastFailure && (time() - $lastFailure) >= $this->timeout;
    }

    /**
     * Open the circuit
     */
    private function openCircuit(string $circuitKey): void
    {
        $this->setCircuitState($circuitKey, self::STATE_OPEN);
        Cache::put("{$circuitKey}:opened_at", time(), 3600);
    }

    /**
     * Close the circuit
     */
    private function closeCircuit(string $circuitKey): void
    {
        $this->setCircuitState($circuitKey, self::STATE_CLOSED);
        Cache::forget("{$circuitKey}:failure_count");
        Cache::forget("{$circuitKey}:success_count");
        Cache::forget("{$circuitKey}:last_failure");
        Cache::forget("{$circuitKey}:opened_at");
    }

    /**
     * Get circuit breaker statistics
     */
    public function getStats(string $serviceName = 'default'): array
    {
        $circuitKey = "circuit_breaker:{$serviceName}";
        
        return [
            'service_name' => $serviceName,
            'state' => $this->getCircuitState($circuitKey),
            'failure_count' => $this->getFailureCount($circuitKey),
            'success_count' => $this->getSuccessCount($circuitKey),
            'failure_threshold' => $this->failureThreshold,
            'timeout' => $this->timeout,
            'last_failure' => Cache::get("{$circuitKey}:last_failure"),
            'opened_at' => Cache::get("{$circuitKey}:opened_at")
        ];
    }

    /**
     * Manually reset a circuit breaker
     */
    public function reset(string $serviceName = 'default'): void
    {
        $circuitKey = "circuit_breaker:{$serviceName}";
        $this->closeCircuit($circuitKey);
        
        Log::info('Circuit breaker manually reset', [
            'service_name' => $serviceName
        ]);
    }

    /**
     * Get all circuit breaker statistics
     */
    public function getAllStats(): array
    {
        // This is a simplified version - in production you'd track all service names
        $services = ['customer_service', 'payment_service', 'meal_service', 'default'];
        $stats = [];
        
        foreach ($services as $service) {
            $stats[$service] = $this->getStats($service);
        }
        
        return $stats;
    }
}

/**
 * Exception thrown when circuit breaker is open
 */
class CircuitBreakerOpenException extends Exception
{
    //
}
