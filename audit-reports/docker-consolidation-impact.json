{"target_path": "Dockerfile", "audit_timestamp": "2025-12-23T10:30:00Z", "mode": "dry-run", "references": {"code_references": [], "import_statements": [], "config_references": ["./docker-compose.onefooddialer.yml"], "docker_references": ["./unified-frontend/Dockerfile", "./unified-frontend/Dockerfile.dev", "./Dockerfile", "./Dockerfile.unified", "./frontend-shadcn/Dockerfile", "./Dockerfile.service", "./services/order/Dockerfile", "./services/catalogue-service-v12/Dockerfile", "./services/auth-service-v12/Dockerfile", "./services/quickserve-service-v12/Dockerfile", "./services/customer-service-v12/Dockerfile", "./services/payment-service-v12/Dockerfile", "./services/kitchen-service-v12/Dockerfile", "./services/delivery-service-v12/Dockerfile", "./services/analytics-service-v12/Dockerfile", "./services/admin-service-v12/Dockerfile", "./services/catalogue-service-v12/Dockerfile", "./services/notification-service-v12/Dockerfile", "./services/misscall-service-v12/Dockerfile"], "test_references": [], "documentation_references": ["./CONSOLIDATION_IMPLEMENTATION_PLAN.md", "./COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md"]}, "risk_assessment": {"level": "MEDIUM", "factors": ["Multiple Dockerfile references in docker-compose.yml", "Service-specific build contexts", "Configuration file dependencies"], "recommendations": ["Update docker-compose.yml to use unified Dockerfile with targets", "Ensure all service contexts are preserved", "Test each service build individually", "Validate container functionality after consolidation"]}, "migration_required": true, "safe_to_proceed": true, "detailed_analysis": {"docker_compose_references": {"file": "./docker-compose.onefooddialer.yml", "services_using_dockerfile": ["auth-service-v12", "quickserve-service-v12", "customer-service-v12", "payment-service-v12", "kitchen-service-v12", "delivery-service-v12", "analytics-service-v12", "admin-service-v12", "catalogue-service-v12", "notification-service-v12", "misscall-service-v12"], "build_contexts": ["./services/auth-service-v12", "./services/quickserve-service-v12", "./services/customer-service-v12", "./services/payment-service-v12", "./services/kitchen-service-v12", "./services/delivery-service-v12", "./services/analytics-service-v12", "./services/admin-service-v12", "./services/catalogue-service-v12", "./services/notification-service-v12", "./services/misscall-service-v12"]}, "consolidation_strategy": {"approach": "Replace service-specific Dockerfiles with unified multi-stage Dockerfile", "target_file": "Dockerfile.unified", "required_updates": ["Update docker-compose.yml build configurations", "Add target specifications for each service", "Preserve build contexts", "Maintain port mappings and environment variables"]}}}