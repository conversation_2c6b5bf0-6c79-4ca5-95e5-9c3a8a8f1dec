<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
		<?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<div class="isa_success col-md-8"><?php echo $msg ?></div>
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div class="isa_error col-md-8"><?php echo $msg ?></div>
		<?php 	}
			}
		?>
   <div class="content">



          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Customer List</span></h2>
          </div>
          <!--contenttitle-->
 		 <?php
                if($acl->isAllowed($loggedUser->rolename,'customer','add')){  ?>
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('customer', array('action'=>'add'));?>" class="btn btn_add"><span>Add Customer</span></a></div>
		 <?php } ?>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <!-- <th class="head0">Food Ref.</th> -->
	            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <th class="head1">Customer Name</th>
                <th class="head0">Address</th>
                <th class="head1">Phone</th>
                <th class="head0">Email</th>
                <th class="head1">Location</th>
                <th class="head0">Group</th>
                <th class="head0">Registered On</th>
                <th class="head1">Registered From</th>
                <!-- <th class="head0">Food Ref.</th> -->
                <th class="head0">Status</th>
                <th class="head0">Action</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head1">Customer Name</th>
                <th class="head0">Address</th>
                <th class="head1">Phone</th>
                <th class="head0">Email</th>
                <th class="head1">Location</th>
                <th class="head0">Group</th>
                <th class="head0">Registered On</th>
                <th class="head1">Registered From</th>
               <!-- <th class="head0">Food Ref.</th> -->
                <th class="head0">Status</th>
                <th class="head0">Action</th>
              </tr>
            </tfoot>
            <tbody>
            <?php foreach ($paginator as $customer) { ?>
            	  <tr>
				     <td><?php echo $this->escapeHtml($customer['customer_name']);?></td>
				     <td><?php echo $this->escapeHtml($customer['customer_Address']);?></td>
				     <td><?php echo $this->escapeHtml($customer['phone']);?></td>
				     <td><?php echo $this->escapeHtml($customer['email_address']);?></td>
				     <td><?php echo $this->escapeHtml($customer['location']);?></td>
				     <td><?php echo $this->escapeHtml($customer['group_name']);?></td>
				     <td><?php echo $this->escapeHtml($customer['registered_on']);?></td>
				      <td><?php echo $this->escapeHtml($customer['registered_from']);?></td>
				     <!--  <td><?php //echo $this->escapeHtml($customer['food_referance']);?></td>  -->
				     <td><?php echo ($this->escapeHtml($customer['customer_status']))=="1"? 'Active':'<span class="red">Deactive</span>';?></td>
				     <td class="center">
				      <?php
                		if($acl->isAllowed($loggedUser->rolename,'customer','edit')){  ?>
				        <a href="<?php echo $this->url('customer', array('action'=>'edit', 'id' => $customer['pk_customer_code']));?>" class="btn btn5 btn_pencil5"></a>
	         				  <?php $textadd = ($customer['customer_status']) == "0"? 'Activate' :'Deactive'; ?>
	         			<?php } ?>
	         			 <?php
                		if($acl->isAllowed($loggedUser->rolename,'customer','delete')){  ?>
	        			<a href="<?php echo $this->url('customer',array('action'=>'delete', 'id' => $customer['pk_customer_code']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this customer ?')" class="btn btn5 btn_trash5"></a>
	        			<?php } ?>
				     </td>
				 </tr>
            <?php }//endforeach; ?>
            </tbody>
          </table>
        </div>
<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>