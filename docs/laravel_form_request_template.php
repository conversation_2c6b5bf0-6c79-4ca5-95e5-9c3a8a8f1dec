<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Class StoreProductRequest
 * 
 * This form request handles validation for creating a new product.
 */
class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // Check if the user has permission to create products
        return $this->user() && $this->user()->can('create', \App\Models\Product::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'category_id' => ['required', 'integer', 'exists:categories,id'],
            'image' => ['nullable', 'image', 'max:2048'], // 2MB max
            'is_available' => ['boolean'],
            'sku' => ['required', 'string', 'max:100', 'unique:products,sku'],
            'stock' => ['required', 'integer', 'min:0'],
            'weight' => ['nullable', 'numeric', 'min:0'],
            'dimensions' => ['nullable', 'string', 'max:100'],
            'categories' => ['sometimes', 'array'],
            'categories.*' => ['integer', 'exists:categories,id'],
            'tags' => ['sometimes', 'array'],
            'tags.*' => ['string', 'max:50'],
            'attributes' => ['sometimes', 'array'],
            'attributes.*.name' => ['required_with:attributes', 'string', 'max:100'],
            'attributes.*.value' => ['required_with:attributes', 'string', 'max:255'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'product name',
            'description' => 'product description',
            'price' => 'product price',
            'category_id' => 'category',
            'image' => 'product image',
            'is_available' => 'availability status',
            'sku' => 'SKU',
            'stock' => 'stock quantity',
            'weight' => 'product weight',
            'dimensions' => 'product dimensions',
            'categories' => 'categories',
            'categories.*' => 'category',
            'tags' => 'tags',
            'tags.*' => 'tag',
            'attributes' => 'attributes',
            'attributes.*.name' => 'attribute name',
            'attributes.*.value' => 'attribute value',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The product name is required.',
            'description.required' => 'The product description is required.',
            'price.required' => 'The product price is required.',
            'price.numeric' => 'The product price must be a number.',
            'price.min' => 'The product price must be at least :min.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'The selected category is invalid.',
            'image.image' => 'The file must be an image.',
            'image.max' => 'The image size must not exceed 2MB.',
            'sku.required' => 'The SKU is required.',
            'sku.unique' => 'This SKU is already in use.',
            'stock.required' => 'The stock quantity is required.',
            'stock.integer' => 'The stock quantity must be an integer.',
            'stock.min' => 'The stock quantity must be at least :min.',
            'categories.*.exists' => 'One or more selected categories are invalid.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Convert boolean strings to actual booleans
        if ($this->has('is_available') && is_string($this->is_available)) {
            $this->merge([
                'is_available' => $this->is_available === 'true' || $this->is_available === '1',
            ]);
        }

        // Convert price to float if it's a string with comma as decimal separator
        if ($this->has('price') && is_string($this->price) && str_contains($this->price, ',')) {
            $this->merge([
                'price' => (float) str_replace(',', '.', $this->price),
            ]);
        }
    }
}
