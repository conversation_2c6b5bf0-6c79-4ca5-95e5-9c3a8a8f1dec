#!/bin/bash

# Test script to verify enhanced customer orders API with meal items from order_details
echo "🧪 Testing Customer Orders API - Meal Items from order_details"
echo "=============================================================="
echo ""

BASE_URL="http://*************:8003/api/v2/order-management"
CUSTOMER_ID=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎯 Testing Enhancements:${NC}"
echo "1. meal_items: Now fetched from order_details table as CSV string"
echo "2. plan_type: Subscription duration based on unique order count"
echo ""
echo "Expected meal_items format:"
echo "  \"Indian Breakfast Main, Indian Breakfast Side, Seasonal Fruits\""
echo ""

echo -e "${YELLOW}📡 API Call: Customer Orders${NC}"
echo "URL: GET ${BASE_URL}/customer/${CUSTOMER_ID}"
echo ""

# Make API call
echo "Making API call..."
response=$(curl -s -X GET "${BASE_URL}/customer/${CUSTOMER_ID}" \
  -H "Accept: application/json" \
  --max-time 10 2>/dev/null)

if [ $? -eq 0 ] && [ -n "$response" ]; then
    echo "Response received:"
    echo "$response" | jq . 2>/dev/null || echo "$response"
else
    echo "❌ API call failed or timed out"
    echo "Service might be down or database connection issues"
fi

echo ""

echo -e "${GREEN}🔍 Verification Checklist:${NC}"
echo "Check the response for the following enhancements:"
echo ""
echo "1. ✅ Each order should have 'meal_items' field containing:"
echo "   - CSV string format (comma-separated)"
echo "   - Actual meal component names from order_details table"
echo "   - Examples: \"Indian Breakfast Main, Indian Breakfast Side, Seasonal Fruits\""
echo ""
echo "2. ✅ Each order should have 'plan_type' field showing:"
echo "   - 'single day' for 1 order"
echo "   - 'X day' for X orders (e.g., '5 day', '20 day')"
echo ""
echo "3. ✅ meal_items should be fetched using this logic:"
echo "   - Query: SELECT product_name FROM order_details WHERE ref_order_no = ? AND order_date = ?"
echo "   - Deduplication: Remove duplicate product names"
echo "   - Format: Join with ', ' to create CSV string"
echo ""

echo -e "${BLUE}📊 Sample Expected Order Structure:${NC}"
cat << 'EOF'
{
  "order_id": 12345,
  "order_no": "8SPS250725",
  "order_date": "2025-07-25",
  "order_status": "Confirmed",
  "total_amount": "160.00",
  "plan_type": "10 day",                                                    // ✅ Based on order count
  "meal_items": "Indian Breakfast Main, Indian Breakfast Side, Seasonal Fruits", // ✅ CSV from order_details
  "delivery_time": "08:00:00",
  "customer_address": "Student Address",
  "location_name": "School Location"
}
EOF

echo ""
echo -e "${GREEN}✅ Enhancement Status:${NC}"
echo "✅ meal_items: Now fetched from order_details table (not temp data)"
echo "✅ Format: CSV string with actual meal component names"
echo "✅ plan_type: Added based on unique order_no count"
echo "✅ Deduplication: Removes duplicate meal items"
echo "✅ Sorting: Alphabetical order by product_name"
echo "✅ Error handling: Fallback message for missing data"
echo "✅ Backward compatibility: All existing fields preserved"
echo ""

echo -e "${YELLOW}🔧 Database Verification:${NC}"
echo "To manually verify the meal items, run this SQL query:"
echo ""
echo "SELECT ref_order_no, order_date, product_name"
echo "FROM order_details"
echo "WHERE ref_order_no = '8SPS250725'"
echo "AND order_date = '2025-07-25'"
echo "ORDER BY product_name;"
echo ""
echo "Expected results should match the meal_items CSV in the API response."
echo ""

echo -e "${BLUE}📝 Implementation Details:${NC}"
echo "1. Method: getMealItemsFromOrderDetails(orderNo, orderDate)"
echo "2. Query: Fetches product_name from order_details table"
echo "3. Processing: Removes duplicates, sorts alphabetically"
echo "4. Output: CSV string format"
echo "5. Fallback: 'Meal items not available' on error"
echo ""

echo "🎉 Enhanced Customer Orders API with meal items from order_details is ready!"
