<?php
/**
 * This File manages the products on fooddialer system
 * It is used to add ,update delete product
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Session\Container;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as QSCommon;

use QuickServe\Model\ApplicationSettingValidator;
use QuickServe\Model\SystemSettingValidator;
use QuickServe\Model\PlanMaster;

use Admin\Form\PlanManagerForm;
use Admin\Form\SettingForm;
use Admin\Form\ApplicationSettingForm;
use Admin\Form\SystemSettingForm;
use Admin\Form\ThemeSettingForm;

class SettingController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\ProductTable model
	 *
	 * @var QuickServe\Model\ProductTable $productTable
	 */
    protected $settingTable;
    /**
     * It has an instance of AuthService model
     *
     * @var AuthService $authservice
     */
    protected $authservice;
	/**
	 * To display the seting form and save settings
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
    public function indexAction()
    {
    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	$sm = $this->getServiceLocator();
    	///$adapter = $sm->get($this->adapter);
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$utility = \Lib\Utility::getInstance();
		
    	$form = new SettingForm($sm);
    	
		$setting_data = $this->getSettingTable()->fetchAll();
    	
		
    	//$setting_data = $adapter->query('SELECT * FROM `settings`',Adapter::QUERY_MODE_EXECUTE);
    	$createNewArr = array();
    	
    	foreach($setting_data as $set){
    		
    		$createNewArr[$set['key']] = (strpos($set['value'],',') !== false)?explode(',', $set['value']) : $set['value'];
    	}
    	
    	$newArray = new \ArrayObject($createNewArr);
		
    	foreach ($newArray['MENU_TYPE'] as $menu){
    		if($menu=='breakfast'){
    			$form->get('BREAKFAST_TIMINGS')->setChecked("breakfast");
    		}
    		if($menu=='lunch'){
    			$form->get('LUNCH_TIMINGS')->setChecked("lunch");
    		}
    		if($menu=='dinner'){
    			$form->get('DINNER_TIMINGS')->setChecked("dinner");
    		}
    	}
    	
    	$form->bind($newArray);
    	
    	$request = $this->getRequest();
    
    	
    	if ($request->isPost()) {
		  	//	echo "<pre>";print_r($request->getPost());die;
    		$validator = new SettingValidator();
    		$form->setInputFilter($validator->getInputFilter());
    		
    		$form_fields = array_merge($this->getRequest()
    				->getPost()
    				->toArray(), $this->getRequest()
    				->getFiles()
    				->toArray());

    	 	
    	 if(isset($form_fields['ONLINE_PAYMENT_GATEWAY']) && $form_fields['ONLINE_PAYMENT_GATEWAY']=='fd_icici_first_data' && $form_fields['PAYMENT_METHODS']=='Prepaid'){
	    	 	if( $form_fields['old_file']!=1)
	    	 	{
	    		    $validator->addkey();
	    	 	}
	    		$validator->getInputFilter()
    			->get('MERCHANT_ID')
    			->setRequired(true);
    		}  
    		

    		if($form_fields['PAYMENT_METHODS']=='Prepaid'){
    			$validator->getInputFilter()
    			->get('GLOBAL_CUSTOMER_PAYMENT_MODE')
    			->setRequired(true);
    			
    			$validator->getInputFilter()
    			->get('ONLINE_PAYMENT_GATEWAY')
    			->setRequired(true);
    			
    			$validator->getInputFilter()
    			->get('MERCHANT_ID')
    			->setRequired(true);
    		}
    		else{
    			$validator->getInputFilter()
    			->get('GLOBAL_CUSTOMER_PAYMENT_MODE')
    			->setRequired(false);
    			
    			$validator->getInputFilter()
    			->get('ONLINE_PAYMENT_GATEWAY')
    			->setRequired(false);
    			
    			$validator->getInputFilter()
    			->get('MERCHANT_ID')
    			->setRequired(false);
    			
    		}
    		
    		if(!$utility->checkSubscription('phone_verification_otp','allowed') && !$utility->checkSubscription('phone_verification_misscall','allowed')){
    			
    			$validator->getInputFilter()
    			->get('PHONE_VERIFICATION_METHOD')
    			->setRequired(false);
    		}
    		
    		
    		$form->setData($form_fields);
    		
    		if ($form->isValid()) 
    		{
    			$menu_settings = array();
    			
    			if($form_fields['BREAKFAST_TIMINGS']!='0'){
    				array_push($menu_settings,$form_fields['BREAKFAST_TIMINGS']);
    			}
    			if($form_fields['LUNCH_TIMINGS']!='0'){
    				array_push($menu_settings,$form_fields['LUNCH_TIMINGS']);
    			}
    			if($form_fields['DINNER_TIMINGS']!='0'){
    				array_push($menu_settings,$form_fields['DINNER_TIMINGS']);
    			} 
    			
    			$validator->exchangeArray($form->getData());

    			$updateThisArray = $validator->getInputValues($menu_settings);
    			$res= $this->getSettingTable()->changeSetting($updateThisArray);

    			$this->flashMessenger()->addSuccessMessage("Setting saved successfully.");
    			return $this->redirect()->toRoute('setting');
    			
    		} /* else{
    			
    			echo "<pre>hhh";print_r($form->getMessages());die;
    		} */
    	}
    	
    	$this->layout()->setVariables(array('page_title'=>"Application Setting",'description'=>"change my setting",'breadcrumb'=>"Setting"));
    	
    	return new ViewModel(array(
    			'acl' => $acl,
    			'loggedUser' => $loguser,
    			'flashMessages'=> $this->flashMessenger()->getMessages(),
    			'form'=>$form
    	));
    }
    
    public function systemAction(){

    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get("write_Adapter");
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	 
    	$utility = \Lib\Utility::getInstance();
    	
    	$form = new SettingForm($sm);
    	 
    	$setting_data = $this->getSettingTable()->fetchAll();
    	

    	foreach($setting_data as $set){
    		
    		$createNewArr[$set['key']] = (strpos($set['value'],',') !== false)?explode(',', $set['value']) : $set['value'];
    	}
    	 
    	$newArray = new \ArrayObject($createNewArr);  
    	
    	//echo print_r($newArray);die;

    	$form->bind($newArray);

    	$this->layout()->setVariables(array('page_title'=>"System Setting",'description'=>"change my setting",'breadcrumb'=>"Setting"));
    	
    	return new ViewModel(array(
    			'acl' => $acl,
    			'loggedUser' => $loguser,
    			'flashMessages'=> $this->flashMessenger()->getMessages(),
    			'form'=>$form
    	));
    }

	/**
	 * use to get instance of SettingTable()
	 */    		
    public function getSettingTable(){
    	
    	if (!$this->settingTable) {
    				$sm = $this->getServiceLocator();
    				$this->settingTable = $sm->get('QuickServe\Model\SettingTable');
    	}
    	return $this->settingTable;
    }
    
    /**
     * Use to add application settings
     * @return multitype:\Admin\Form\ApplicationSettingForm
     */
    public function applicationSettingAction()
    {
        $utility = \Lib\Utility::getInstance();
    	$sm = $this->getServiceLocator();
    	$libCommon = Qscommon::getInstance($sm);
        $subscription_keys = new Container('subscription_keys');
    	$form = new ApplicationSettingForm($sm);
    	$form->get('submit')->setAttribute('value', 'Add');
        $setting_data = $libCommon->getSettings();

        $setting_data['FOOD_TYPE'] = implode(',', $setting_data['FOOD_TYPE']);       
    	
    	$form->bind($setting_data);
    	
    	$request = $this->getRequest();
    	
    	if ($request->isPost()){
    		$applicationsetting = new ApplicationSettingValidator();
            $post = $request->getPost();
//           
            //if($subscription_keys->keys['CUSTOM_SENDER_ID_ALLOWED'] == 'N'){
            //    $applicationsetting->getInputFilter()->get('MERCHANT_SENDER_ID')->setRequired(false);
            //}
            if(in_array('neft',$post['GLOBAL_CUSTOMER_PAYMENT_MODE'])){
                
                $applicationsetting->getInputFilter()->get('MERCHANT_BANK_ACCOUNT_NAME')->setRequired(true);
                $applicationsetting->getInputFilter()->get('MERCHANT_BANK_ACCOUNT_NO')->setRequired(true);
                $applicationsetting->getInputFilter()->get('MERCHANT_BANK_NAME')->setRequired(true);
                $applicationsetting->getInputFilter()->get('MERCHANT_BANK_IFSC_CODE')->setRequired(true);
                $applicationsetting->getInputFilter()->get('MERCHANT_BANK_BRANCH_ADDRESS')->setRequired(true);
            }
            
            $form->setInputFilter($applicationsetting->getInputFilter());
    		$form->setData($request->getPost());
            if ($form->isValid()){
                
    			$data = $form->getData();
    			$applicationsetting->exchangeArray($data);    	
    			$data_application_setting = $this->getSettingTable()->saveSetting($applicationsetting);
    			if($data_application_setting){
    				$this->flashMessenger()->addSuccessMessage("Application Settings added successfully");
    			}
    			else{
    				$this->flashMessenger()->addErrorMessage("Error in adding setting.");
    			}
    			return $this->redirect()->toRoute('setting',array('action' => 'application-setting'));
    		}else{
//				echo '<pre>';print_r($form->getMessages());exit;
			}
            
        }
        
        $this->layout()->setVariables(array('page_title'=>"Application Settings",'breadcrumb'=>"Application Settings"));
        return array('form' => $form);

    }

    
    /**
     * Use to view system settings
     * @return \Zend\View\Model\ViewModel
     */
 	public function viewSystemSettingAction(){
    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	$sm = $this->getServiceLocator();
    	$libCommon = QSCommon::getInstance($sm);
    	$setting_data = $libCommon->getSettings();
    	
    	$locale = $libCommon->getLocaleCountry($setting_data['GLOBAL_CURRENCY']);
    	
    	$setting_data['GLOBAL_COUNTRY'] = $locale['country_name'];
    	
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
        $log_user_type = $loguser->rolename;
        
    	$this->layout()->setVariables(array('page_title'=>"System Settings",'description'=>"System Settings",'breadcrumb'=>"System Settings"));
    	return new ViewModel(array(
    		'setting_data' => $setting_data,
    		'log_user_type' => $log_user_type,
            'payment_gateway' => explode(',',$setting_data['ONLINE_PAYMENT_GATEWAY']),
    	));
    }
    
    
    public function manageHolidayAction(){

    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	
    	$sm = $this->getServiceLocator();
    	//$adapt = $sm->get($this->adapter);
    	$libCommon = QSCommon::getInstance($sm);
    	$setting_data = $libCommon->getSettings();
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;

    	$result1 ='';
    	$result2 ='';
    	
    	if(isset($_POST) && !empty($_POST)){
    		
    		try{
    			
    			if(isset($_POST['weekOff']) && !empty($_POST['weekOff'])){
    				
    				$postweekoff = array();
    				
    				switch ($_POST['weekOff']){

    					case "ms":	$postweekoff['holiday_date']=$_POST['weekOff'];
    								$postweekoff['holiday_description']= '0';
    								break;

    					case "mf":	$postweekoff['holiday_date']=$_POST['weekOff'];
    								$postweekoff['holiday_description']= '0,6';
    								break;
    								
    					case "msu": $postweekoff['holiday_date']=$_POST['weekOff'];
    								$postweekoff['holiday_description']= '';
    								break;
						
    					case "chooseDay":$postweekoff['holiday_date']=$_POST['weekOff'];
    									 $postweekoff['holiday_description']= implode(",",$_POST['unique']);
    									 break;
    				}
    				
    				$result1 = $libCommon->saveWeekOffs($postweekoff);
                    
    			}
    			
    			if(isset($_POST['select_year']) && !empty($_POST['select_year'])){
    				
    				$postholidays = array();
                    
    				foreach ($_POST['holiday_date'] as $h_key=>$h_date){
                        $postholidays[$h_key]['company_id'] = $GLOBALS['company_id'];    
                        $postholidays[$h_key]['unit_id'] = $GLOBALS['unit_id'];
    					$postholidays[$h_key]['holiday_date']=date("Y-m-d",strtotime(strval($h_date)));
                        $postholidays[$h_key]['holiday_description'] = $_POST['holiday_description'][$h_key];	
    					$postholidays[$h_key]['holiday_type']='holiday';
    				}
                    
    				$result2 = $libCommon->saveHolidays($postholidays,$_POST['select_year']);
    			}
    			
    			if($result1 || $result2){
    				$this->flashMessenger()->addSuccessMessage("Successfully added holidays/weekoff");
    				return $this->redirect()->toRoute('setting',array('action'=>'manage-holiday'));
    			}else{
    				$this->flashMessenger()->addSuccessMessage("Please perform atleast one operation");
    				return $this->redirect()->toRoute('setting',array('action'=>'manage-holiday'));
    			}

    		}catch(\Exception $e){
    			$error = $e->getMessage();
    			
    			$this->flashMessenger()->addSuccessMessage($error);
    		}	
    	}
    	$weekOff=$libCommon->fetchHolidaysList('weekoff');
    	 
    	$currentYear = date("Y");
    	
    	$weekOffs = array(
    			'mf'=>'Saturday-Sunday',
    			'ms'=>'Only Sunday',
    			'msu'=>'No Weekoff',
    			'chooseDay'=>'Chooseday',
    	);
    	 
    	$unique=array(
    			1=>"Monday",
    			2=>"Tuesday",
    			3=>"Wedensday",
    			4=>"Thursday",
    			5=>"Friday",
    			6=>"Saturday",
    			0=>"Sunday",
    	);
    	
    	$this->layout()->setVariables(array('page_title'=>"Holiday Manager",'description'=>"Holiday Manager",'breadcrumb'=>"Holiday Manager"));
    	
    	return new ViewModel(array(
    			'setting' => $setting_data,
    			'currentYear' => intval($currentYear),
    			'weekoffs' => $weekOffs,
    			'setweekoff'=>(isset($weekOff[0]) && !empty($weekOff[0]))?$weekOff[0]:'',
    			'unique' =>$unique,
    	));	
    }
    
    public function ajaxgetholidaysAction(){
  	
    	$request = $this->getRequest();
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	
    	$libCommon = QSCommon::getInstance($sm);
    	$setting_data = $libCommon->getSettings();
    	
    	$currentYear = $request->getPost("currentYear");

    	$select = new QSelect();
    	
    	$select->where("holiday_type = 'holiday' AND YEAR(holiday_date) = ".$currentYear);
    	
    	$result_holiday = $libCommon->fetchHolidays($select)->toArray();
    	  		$html_view='';

    	if(count($result_holiday)>0){
  
    		$i=0;
    		foreach ($result_holiday as $key_h=>$val_h){
	    		$html_view .= "<div class='multi-field'>
	    		<div class='row'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Holiday Name<span class='red'>*</span> </label>
	    		</div>
	    		<div class='large-3 small-3 medium-3 columns'>
	    		<input type='text' name='holiday_description[]' class='smallinput' value='".$val_h['holiday_description']."'>
	    		</div>
	    		<div class='productadd'>
	    		
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Date <span class='red'>*</span>
	    		</div>
	    		<div class='large-8 small-8 medium-8 columns'>
	    		
	    		<div class='dateText'>
	    		
	    		<input type='text' name='holiday_date[]' class='left&#x20;filterSelect calender  dateselect' value='".date("m/d/Y",strtotime($val_h['holiday_date']))."'>
														</div>
	    				</div>
	    				</div>
	    				<button class='smBtn has-tip remove-field' title='Remove' type='button'><i class='fa fa-trash-o'></i></button>";
	    			if($i == count($result_holiday)-1){
	    		$html_view .=		"<button class='add-field smBtn has-tip'  title='Add more' type='button'  >
	    				<i class='fa fa-plus'></i>
						</button>";
	    			}
	    		$html_view .="</div>
	    		</div>
	    		</div>";
	    		$i++;
    		}
    		/* $html_view .= "<div class='multi-field'>
	    		<div class='row'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Holiday Name<span class='red'>*</span> </label>
	    		</div>
	    		<div class='large-3 small-3 medium-3 columns'>
	    		<input type='text' name='holiday_description[]' class='smallinput' value=''>
	    		</div>
	    		<div class='productadd'>
	   
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Date <span class='red'>*</span>
	    		</div>
	    		<div class='large-8 small-8 medium-8 columns'>
	   
	    		<div class='dateText'>
	   
	    		<input type='text' name='holiday_date[]' id='' class='left&#x20;filterSelect calender  dateselect' value=''>
							</div>
	    				</div>
	    				</div>
	    				<button class='smBtn has-tip remove-field' title='Remove' type='button'><i class='fa fa-trash-o'></i></button>
	    				<button class='add-field smBtn has-tip'  title='Add more' type='button'  >
	    				<i class='fa fa-plus'></i>
						</button>
	   
	    		</div>
	    		</div>
	    		</div>"; */
    		
//holiday[".$currentYear."]['holiday_description'][".$i."] ,,, 
    	}else{
    			$html_view .= "<div class='multi-field'>
	    		<div class='row'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Holiday Name<span class='red'>*</span> </label>
	    		</div>
	    		<div class='large-3 small-3 medium-3 columns'>
	    		<input type='text' name='holiday_description[]' class='smallinput' value=''>
	    		</div>
	    		<div class='productadd'>
	    		
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<div class='large-4 small-4 medium-4 columns'>
	    		<label class='inline right'>Date <span class='red'>*</span>
	    		</div>
	    		<div class='large-8 small-8 medium-8 columns'>
	    		
	    		<div class='dateText'>
	    		
	    		<input type='text' name='holiday_date[]' id='' class='left&#x20;filterSelect calender minDate dateselect' value=''>
														</div>
	    				</div>
	    				</div>
	    				
	    				<button class='add-field smBtn has-tip'  title='Add more' type='button'  >
	    				<i class='fa fa-plus'></i>
						</button>
	    		
	    		</div>
	    		</div>
	    		</div>";
    	}
    	
    	return new JsonModel(array('status'=>'success','view'=>$html_view));
    
    }
    
    /**
     * Use to view system settings
     * @return \Zend\View\Model\ViewModel
     */
    public function planSettingAction()
    {

    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	
    	$sm = $this->getServiceLocator();
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$utility = \Lib\Utility::getInstance();
    	$form = new SystemSettingForm($sm);
    	$setting_data = $this->getSettingTable()->getPlans();
//    	/dd($setting_data);
    	$setting_session = new Container('setting');
    	
    	$setting = $setting_session->setting;

    	$this->layout()->setVariables(array('page_title'=>"Plan Settings",'breadcrumb'=>"Plan Settings"));
    
    	return new ViewModel(array(
    			'setting_data' => $setting_data,
    			'utility'=>$utility,
    			'setting'=>$setting,
//     			'log_user_type' => $log_user_type
    	));
    }    
    
    /**
     * add plan
     * @return \Zend\Http\Response|\Zend\View\Model\ViewModel
     */
       
    
    public function planAddAction() {

    	$plan_type = "periodbased";
    	$sm = $this->getServiceLocator();
        //$adapt = $sm->get('Write_Adapter');
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$libCommon = QSCommon::getInstance($sm);
    	
    	$form = new PlanManagerForm($sm);
    	$form->get('submit')->setAttribute('value', 'add');
    	
        $promo_options = $form->get('promo_code')->getOption('value_options');
        
        if(count($promo_options) == 1){
            
            $form->remove('promo_code');
            $form->getInputFilter()->remove('promo_code');
        }
        
    	$request = $this->getRequest();
    	
    	$form->setData($request->getPost());
      
    	if ($request->isPost()) {
    		
    		$plan_type = $_POST['plan_type'];
    		
    		$plan_type = ($plan_type == 'periodbased')? 'datebased':'periodbased';
    		
    		$applicationsetting = new PlanMaster();
    		
    		//$applicationsetting->setAdapter($adapt);
    		
    		$form->setInputFilter($applicationsetting->getInputFilter());
    		
    		$applicationsetting->getInputFilter()->get('plan_quantity')
    		->getValidatorChain()                  // Filters are run second w/ FileInput
    		->attach(new \Zend\Validator\Db\NoRecordExists(array(
				'table'     => 'plan_master',
				'field'     => 'plan_quantity',
				'adapter'   => $sm->get('Write_Adapter'),
				'message'   => 'Plan already exists',
				'exclude' => array(
					'field' => 'plan_type',
					'value' => $plan_type,
				)
    		)
               
    		));
           
    		if ($form->isValid()) {
                           
    			$applicationsetting->exchangeArray($form->getData()); 
                
    			$data_application_setting = $libCommon->addPlan($applicationsetting);
    			
    			
    			if($data_application_setting){
    			
    				$full_name=$loguser->first_name." ".$loguser->last_name;
    				$plan_name=$applicationsetting->plan_name;
    				$activity_log_data=array();
    				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
    				$activity_log_data['context_name']= $full_name;
    				$activity_log_data['context_type']= 'user';
    				$activity_log_data['controller']= 'setting';
    				$activity_log_data['action']= 'add-plan';
    				$activity_log_data['description']= "Plan : Plan '$plan_name' added.";
//    				$activity_log_data['description']= "'$promo_code' promo code updated by $full_name";
    				$libCommon->saveActivityLog($activity_log_data);
    				$this->flashMessenger()->addSuccessMessage("Plan ".$plan_name." added successfully");
    			}
    			return $this->redirect()->toRoute('setting',array('action' => 'plan-setting'));
    		}
  
    	}
    	
    	$this->layout()->setVariables(array('page_title'=>"Add Plans",'breadcrumb'=>"Plan Settings"));
    	
    	return new ViewModel(array(
    			'form' => $form,
    	));
    }

    /**
     * edit plan form
     * @return \Admin\Form\PlanManagerForm
     */
    
    public function editPlanAction(){

        $plan_type = "periodbased";
   		$id = (int) $this->params('id');

    	if (!$id) {
    		return $this->redirect()->toRoute('promocode', array('action' => 'add'));
    	}
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$date = date('m/d/Y ', time());
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	 
    	$libCommon = Qscommon::getInstance($sm);

    	$plan = $libCommon->getPlan($id);
    	
    	$plan->plan_start_date = date('m/d/Y',strtotime($plan->plan_start_date));
    	$plan->plan_end_date = date('m/d/Y',strtotime($plan->plan_end_date));
        $plan->promo_code = $plan->fk_promo_code;
        
        $plan_promo = isset($plan->fk_promo_code) ? $plan->fk_promo_code: NULL;
        
    	$form = new PlanManagerForm($sm);
    	 
    	$form->get('submit')->setAttribute('value', 'Edit');
       
    	$request = $this->getRequest();
        
        $promo_options = $form->get('promo_code')->getOption('value_options');
        
        
        if(count($promo_options) == 1){
            
            $form->remove('promo_code');
            $form->getInputFilter()->remove('promo_code');
        }
        
        $form->setData($request->getPost());
        
        $form->bind($plan);
    	
    	if ($request->isPost()) {
    	
    		$plan_type = $_POST['plan_type'];
    		$plan_quant = $_POST['plan_quantity'];
            $promo_code = $_POST['promo_code'];
    		$plan_type = ($plan_type == 'periodbased')? 'datebased':'periodbased';
    	
    		$applicationsetting = new PlanMaster();
    	
    		//$applicationsetting->setAdapter($adapt);
    	
    		$form->setInputFilter($applicationsetting->getInputFilter());
                
//    		$applicationsetting->getInputFilter()->get('plan_quantity')
//    		->getValidatorChain()                  // Filters are run second w/ FileInput
//    		->attach(new \Zend\Validator\NotEmpty(array(
//    				'table'     => 'plan_master',
//    				'field'     => 'plan_quantity',
//    				'adapter'   => $adapt,
//    				'message'   => 'Value cannot be empty',
//    				'exclude' => "(plan_type != '$plan_type' AND pk_plan_code != '$id')",
//    		)
//    		));

    	 
    		if($form->isValid()){
               
    			$data = $form->getData(); 
                
                if(!array_key_exists('promo_code', $data)) $data['promo_code'] = $plan_promo;
                
    			$applicationsetting->exchangeArray($form->getData());
    			
    			$data_application_setting = $libCommon->savePlan($applicationsetting);
    			
    			if($data_application_setting){
    				
    				$full_name=$loguser->first_name." ".$loguser->last_name;
    				$plan_name=$plan->plan_name;
    				$activity_log_data=array();
    				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
    				$activity_log_data['context_name']= $full_name;
    				$activity_log_data['context_type']= 'user';
    				$activity_log_data['controller']= 'setting';
    				$activity_log_data['action']= 'edit-plan';
    				$activity_log_data['description']= "Plan : Plan '$plan_name' updated.";
    				//$activity_log_data['description']= "'$promo_code' promo code updated by $full_name";
    				$libCommon->saveActivityLog($activity_log_data);

    			}

    			$this->flashMessenger()->addSuccessMessage("Plan ".$plan_name." updated successfully");
    			// Redirect to list of albums
    			return $this->redirect()->toRoute('setting',array('action'=>'plan-setting'));
    		}
    		else
    		{
//                dd($form->getMessages());
                $form->bind($request->getPost());
            }
    	}
    	
    	$this->layout()->setVariables(array('page_title'=>"Edit Plan",'breadcrumb'=>"Edit Plan"));
    	
    	return array(
    			'id' => $id,
    			'form' => $form
    	);

    }
    
    /**
     * To update system setting
     * @return \Admin\Form\SystemSettingForm
     */
    public function systemSettingAction(){

    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
        $setting_session = new Container('setting');
    	$settings = $setting_session->setting;
    	$sm = $this->getServiceLocator();
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$utility = \Lib\Utility::getInstance();
    	$form = new SystemSettingForm($sm);
        
        $settings->offsetSet('GLOBAL_ENABLE_MEAL_PLANS', $settings['GLOBAL_ENABLE_MEAL_PLANS']);
    	$setting_data = $this->getSettingTable()->fetchAll();
        
        $createNewArr = array();
    	
    	foreach($setting_data as $set){

    		$createNewArr[$set['key']] = (strpos($set['value'],',') !== false) ? ( $set['key'] !='MENU_TYPE' ? explode(',', $set['value']) : $set['value'] ) : $set['value'];
    	}
    	
    	$newArray = new \ArrayObject($createNewArr);
    	//echo "<pre>"; print_r($newArray);die;
        //dd($newArray);
        $form->bind($newArray);
        
    	$request = $this->getRequest();
        if ($request->isPost())
    	{

    		$systemsetting = new SystemSettingValidator();
            $systemsetting->getInputFilter()->get('GLOBAL_THEME')->setRequired(false);
            $systemsetting->getInputFilter()->get('GLOBAL_STYLE')->setRequired(false);
            $systemsetting->getInputFilter()->get('GLOBAL_SKIN')->setRequired(false);
    		//$systemsetting->setAdapter($adapter);
    		
    		$post = $request->getPost();
    		foreach($post['ONLINE_PAYMENT_GATEWAY'] as $gateway){
    		    
    		    if($gateway=='payu'){
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_ID')->setRequired(true);
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_KEY')->setRequired(true);
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_SALT')->setRequired(true);
    		    }
    		    if($gateway=='instamojo'){
    		        $systemsetting->getInputFilter()->get('GATEWAY_INSTAMOJO_MERCHANT_KEY')->setRequired(true);
    		        $systemsetting->getInputFilter()->get('GATEWAY_INSTAMOJO_MERCHANT_TOKEN')->setRequired(true);
    		    }
    		    if($gateway=='paytm'){
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_ID')->setRequired(true);
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_KEY')->setRequired(true);
    		        $systemsetting->getInputFilter()->get('GATEWAY_PAYU_MERCHANT_SALT')->setRequired(true);
    		    }
                if($gateway=='payeezy'){
                    $systemsetting->getInputFilter()->get('PAYEEZY_HCO_LOGIN')->setRequired(true);
                    $systemsetting->getInputFilter()->get('PAYEEZY_HCO_TRANSACTION_KEY')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_PAYEEZY_ID')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_PAYEEZY_KEY')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_PAYEEZY_SECRET')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_PAYEEZY_HMAC_KEY')->setRequired(true);
                }
                if($gateway=='mobikwik'){
                    $systemsetting->getInputFilter()->get('GATEWAY_MOBIKWIK_MERCHANT_NAME')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_MOBIKWIK_MERCHANT_ID')->setRequired(true);
                    $systemsetting->getInputFilter()->get('GATEWAY_MOBIKWIK_MERCHANT_KEY')->setRequired(true);
                }
				
    		}
    		//dd($post['ONLINE_PAYMENT_GATEWAY']);
    		
    		$form->setInputFilter($systemsetting->getInputFilter());
    		$form->setData($request->getPost());
    		if ($form->isValid()){
    			$systemsetting->exchangeArray($form->getData());
				//unset($systemsetting->inputFilter);
				//unset($systemsetting['inputFilter']);
    			$data_system_setting = $this->getSettingTable()->changeSetting($systemsetting);
    			($data_system_setting) ?$this->flashMessenger()->addSuccessMessage("System Settings updated successfully"):$this->flashMessenger()->addErrorMessage("Error in addingg setting.");
    			return $this->redirect()->toRoute('setting',array('action' => 'view-system-setting'));
    		}
    		/* else
    		 {
    		 echo "not valid"; exit();
    		 } 
    		 */
    		else
    		 {
                
    		 	//echo '<pre>';print_r($form->getMessages());exit;
    		 }
    		 
    	}
    	
    	$this->layout()->setVariables(array('page_title'=>"System Setting",'description'=>"change my setting",'breadcrumb'=>"Setting"));
    	
    	return new ViewModel(array(
    			'acl' => $acl,
    			'loggedUser' => $loguser,
    			'flashMessages'=> $this->flashMessenger()->getMessages(),
    			'form'=>$form,
                'payment_gateway' => $newArray['ONLINE_PAYMENT_GATEWAY']
    	));
    }
    
    /**
     * use to display subscription keys
     * @return \Zend\View\Model\ViewModel
     */
    public function subscriptionAction()
    {
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libCommon = QSCommon::getInstance($sm);
    	$data = $libCommon->getSubsrciptionKeys();
    
    	$arrval = array();
        if(!empty($data)){
            foreach ($data as $key=>$val)
            {
                if($val['value'] =='Y')
                {
                    $value = "Yes";
                }
                else if($val['value'] =='N')
                {
                    $value = "No";
                }
                else
                {
                    $value = $val['value'];
                }

                $arrval[$val['key']] = $value;
            }
        }    
    	$this->layout()->setVariables(array('page_title'=>"Subscribe",'description'=>"Subscription Form",'breadcrumb'=>"Subscribe"));
    	return new ViewModel(array(
    
    			'data' => $arrval,
    	));
    
    }
    /////////////////************Ashwini 13/04/2017*********/////////////////////
    
    public function themeSettingAction()
    {
       
     if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
    	$iden = $this->authservice->getIdentity();
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$utility = \Lib\Utility::getInstance();
        $form = new ThemeSettingForm($sm);
        
    	$setting_data = $this->getSettingTable()->fetchAll();
    	$createNewArr = array();
    	
    	foreach($setting_data as $set){
    		$createNewArr[$set['key']] = (strpos($set['value'],',') !== false)?explode(',', $set['value']) : $set['value'];
    	}
    	
    	$newArray = new \ArrayObject($createNewArr);
    	
        $form->bind($newArray);
 
    	$request = $this->getRequest();
    	
    	if ($request->isPost()){	
    		
    		//$systemsetting = new SystemSettingValidator();
    		//$systemsetting->setAdapter($adapter);
            
    		$post = $request->getPost();
    		
    		
    		//dd($post['ONLINE_PAYMENT_GATEWAY']);
    		              
            //$systemsetting = array();
            $form->setValidationGroup('GLOBAL_THEME', 'GLOBAL_STYLE', 'GLOBAL_SKIN');
    		//$form->setInputFilter($systemsetting->getInputFilter());
    		$form->setData($request->getPost());
    	   
    		if($form->isValid()){

    			//$systemsetting->exchangeArray($form->getData());
                
                $systemsettingData = $form->getData();
                
    			//echo "<pre>"; print_r($systemsetting); die;
    			$data_system_setting = $this->getSettingTable()->changeSetting($systemsettingData);
    			($data_system_setting) ?$this->flashMessenger()->addSuccessMessage("System Settings updated successfully"):$this->flashMessenger()->addErrorMessage("Error in addingg setting.");
    			return $this->redirect()->toRoute('setting',array('action' => 'theme-setting'));
    		}else{
    		 	//echo '<pre>';print_r($form->getMessages());exit;
    		}
    		 
    	}
    	
    	$this->layout()->setVariables(array('page_title'=>"Theme Setting",'description'=>'List of registered Customers','breadcrumb'=>"Theme Preview"));
    	
    	return new ViewModel(array(
    			'acl' => $acl,
    			'loggedUser' => $loguser,
    			'flashMessages'=> $this->flashMessenger()->getMessages(),
    			'form'=>$form,
                
    	));
    }
     
  
}
