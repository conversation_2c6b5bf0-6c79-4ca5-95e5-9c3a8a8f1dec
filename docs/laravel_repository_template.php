<?php

namespace App\Repositories;

use App\Models\Product;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class ProductRepository
 * 
 * This repository handles database operations for products.
 */
class ProductRepository implements ProductRepositoryInterface
{
    /**
     * The product model instance.
     *
     * @var \App\Models\Product
     */
    protected $model;

    /**
     * Create a new repository instance.
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function __construct(Product $product)
    {
        $this->model = $product;
    }

    /**
     * Get all products with optional filtering.
     *
     * @param  int  $perPage
     * @param  int|null  $categoryId
     * @param  string|null  $search
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllProducts(int $perPage = 15, ?int $categoryId = null, ?string $search = null): LengthAwarePaginator
    {
        $query = $this->model->query();

        // Apply category filter if provided
        if ($categoryId) {
            $query->whereHas('categories', function ($q) use ($categoryId) {
                $q->where('categories.id', $categoryId);
            });
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Order by creation date, newest first
        $query->orderBy('created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Find a product by ID.
     *
     * @param  int  $id
     * @return \App\Models\Product|null
     */
    public function findById(int $id): ?Product
    {
        return $this->model->find($id);
    }

    /**
     * Create a new product.
     *
     * @param  array  $data
     * @return \App\Models\Product
     */
    public function create(array $data): Product
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing product.
     *
     * @param  \App\Models\Product  $product
     * @param  array  $data
     * @return \App\Models\Product
     */
    public function update(Product $product, array $data): Product
    {
        $product->update($data);
        return $product->fresh();
    }

    /**
     * Delete a product.
     *
     * @param  \App\Models\Product  $product
     * @return bool
     */
    public function delete(Product $product): bool
    {
        return $product->delete();
    }

    /**
     * Get products by category.
     *
     * @param  int  $categoryId
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProductsByCategory(int $categoryId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->whereHas('categories', function ($query) use ($categoryId) {
            $query->where('categories.id', $categoryId);
        })->paginate($perPage);
    }

    /**
     * Search products by name or description.
     *
     * @param  string  $query
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->paginate($perPage);
    }

    /**
     * Get featured products.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedProducts(int $limit = 10): Collection
    {
        return $this->model->where('is_featured', true)
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->get();
    }

    /**
     * Get related products.
     *
     * @param  \App\Models\Product  $product
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRelatedProducts(Product $product, int $limit = 4): Collection
    {
        // Get the categories of the current product
        $categoryIds = $product->categories->pluck('id')->toArray();

        // Find products in the same categories, excluding the current product
        return $this->model->whereHas('categories', function ($query) use ($categoryIds) {
            $query->whereIn('categories.id', $categoryIds);
        })
        ->where('id', '!=', $product->id)
        ->orderBy('created_at', 'desc')
        ->take($limit)
        ->get();
    }

    /**
     * Get products by IDs.
     *
     * @param  array  $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProductsByIds(array $ids): Collection
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    /**
     * Get products by price range.
     *
     * @param  float  $minPrice
     * @param  float  $maxPrice
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProductsByPriceRange(float $minPrice, float $maxPrice, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->whereBetween('price', [$minPrice, $maxPrice])
            ->paginate($perPage);
    }

    /**
     * Get latest products.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLatestProducts(int $limit = 10): Collection
    {
        return $this->model->orderBy('created_at', 'desc')
            ->take($limit)
            ->get();
    }

    /**
     * Get popular products based on order count.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPopularProducts(int $limit = 10): Collection
    {
        return $this->model->withCount('orderItems')
            ->orderBy('order_items_count', 'desc')
            ->take($limit)
            ->get();
    }
}
