<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add cancellation policy settings
        $settings = [
            // Kitchen 1 Breakfast Settings
            [
                'key' => 'K1_BREAKFAST_ORDER_CUT_OFF_TIME',
                'value' => '00:01:00',
                'company_id' => 8163,
                'unit_id' => 8163,
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_BREAKFAST_ORDER_CUT_OFF_DAY',
                'value' => '0',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_TIME',
                'value' => '00:01:00',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_DAY',
                'value' => '0',
                'created_date' => now(),
                'modified_date' => now()
            ],
            
            // Kitchen 1 Lunch Settings
            [
                'key' => 'K1_LUNCH_ORDER_CUT_OFF_TIME',
                'value' => '00:01:00',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_LUNCH_ORDER_CUT_OFF_DAY',
                'value' => '0',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_LUNCH_ORDER_CANCEL_CUT_OFF_TIME',
                'value' => '00:01:00',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'K1_LUNCH_ORDER_CANCEL_CUT_OFF_DAY',
                'value' => '0',
                'created_date' => now(),
                'modified_date' => now()
            ],
            
            // Partial Refund Percentages
            [
                'key' => 'BREAKFAST_PARTIAL_REFUND_PERCENTAGE',
                'value' => '0', // 0% refund for breakfast between 00:01:00-08:00:00
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'LUNCH_PARTIAL_REFUND_PERCENTAGE',
                'value' => '50', // 50% refund for lunch between 00:01:00-08:00:00
                'created_date' => now(),
                'modified_date' => now()
            ],
            
            // General Cancellation Settings
            [
                'key' => 'CANCELLATION_PARTIAL_REFUND_START_TIME',
                'value' => '00:01:00',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'CANCELLATION_PARTIAL_REFUND_END_TIME',
                'value' => '08:00:00',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'CANCELLATION_NO_REFUND_START_TIME',
                'value' => '08:00:01',
                'created_date' => now(),
                'modified_date' => now()
            ],
            
            // Enable/Disable Features
            [
                'key' => 'ENABLE_TIME_BASED_CANCELLATION',
                'value' => 'yes',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'ENABLE_WALLET_LOCKING',
                'value' => 'yes',
                'created_date' => now(),
                'modified_date' => now()
            ],
            [
                'key' => 'ENABLE_AUTOMATIC_REFUND',
                'value' => 'yes',
                'created_date' => now(),
                'modified_date' => now()
            ]
        ];

        // Insert settings with proper defaults
        foreach ($settings as $setting) {
            // Check if setting already exists
            $exists = DB::table('settings')
                ->where('key', $setting['key'])
                ->exists();

            if (!$exists) {
                // Add default values for required fields
                $setting['company_id'] = $setting['company_id'] ?? 8163;
                $setting['unit_id'] = $setting['unit_id'] ?? 8163;

                DB::table('settings')->insert($setting);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added settings
        $settingsToRemove = [
            'K1_BREAKFAST_ORDER_CUT_OFF_TIME',
            'K1_BREAKFAST_ORDER_CUT_OFF_DAY',
            'K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_TIME',
            'K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_DAY',
            'K1_LUNCH_ORDER_CUT_OFF_TIME',
            'K1_LUNCH_ORDER_CUT_OFF_DAY',
            'K1_LUNCH_ORDER_CANCEL_CUT_OFF_TIME',
            'K1_LUNCH_ORDER_CANCEL_CUT_OFF_DAY',
            'BREAKFAST_PARTIAL_REFUND_PERCENTAGE',
            'LUNCH_PARTIAL_REFUND_PERCENTAGE',
            'CANCELLATION_PARTIAL_REFUND_START_TIME',
            'CANCELLATION_PARTIAL_REFUND_END_TIME',
            'CANCELLATION_NO_REFUND_START_TIME',
            'ENABLE_TIME_BASED_CANCELLATION',
            'ENABLE_WALLET_LOCKING',
            'ENABLE_AUTOMATIC_REFUND'
        ];

        DB::table('settings')
            ->whereIn('key', $settingsToRemove)
            ->delete();
    }
};
