<?php
/**
 * This File provides the input fields which needs to create  add & update customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ImportCustomerForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

class ImportProductForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
     private $service_locator;
	/**
	 * It adds an input fields which are needed to create customer login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('import-product');
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'import_file',
        		'type'=>'file',
        		'attributes' => array(
        				'class' => 'smallinput',
        				'accept'=>".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",
        		),
        		'options' => array(
        				'label' => 'Upload File <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        				 
        		),
        ));
        

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Upload',
        				'id' => 'submitbutton',
        				'class'=>'button left tiny left5 dark-greenBg'
        		),
        		
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        				'class'=>'button left tiny left5 redBg'

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/product',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
        
    }

}