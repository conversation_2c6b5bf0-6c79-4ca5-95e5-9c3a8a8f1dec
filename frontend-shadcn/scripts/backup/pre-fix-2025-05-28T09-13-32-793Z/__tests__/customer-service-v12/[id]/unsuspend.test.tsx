import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Unsuspend } from '@/components/customer-service-v12/[id]/unsuspend';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Unsuspend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Unsuspend />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Unsuspend')).toBeInTheDocument();
  });
});