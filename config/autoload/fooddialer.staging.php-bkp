<?php
/**
 * PRODUCTION ENVIRONMENT CONFIGURATION
 *
 * Global Configuration Override
 *
 * You can use this file for overriding configuration values from modules, etc.
 * You would place values in here that are agnostic to the environment and not
 * sensitive to security.
 *
 * @NOTE: In practice, this file will typically be INCLUDED in your source
 * control, so do not include passwords or other sensitive information in this
 * file.
 */

return array(
    /**
     * application domain name.
     */
    'domain_name' => 'http://company.fooddialer.co.in/',
    /**
     * auth server domain name.
     */
    'auth_domain' => 'http://auth.vezaone.com/',
    /**
     * account edit domain name.
     */
    'account_domain' => 'http://account.vezaone.com/',
    
    /**
     * master database connection.
     */
    'master_db' => array(
        'driver'    => 'Pdo',
        'username'  => 'admin',
        'password'  => 'password',
        'dsn'            => 'mysql:dbname=test_quickserve_master;host=***********',
        'driver_options' => array(
            PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''
        ),
    ),
    /**
     * AWS s3 bucket and its bucket url details.
     *
     * @var array aws_bucket_details
     */
    'aws_bucket_details'=>array(
        'bucket'=>'fdc01.fooddialer.in',
        'bucket_url'=>'fdc01.fooddialer.in.s3.amazonaws.com',
        //'cname'=>'fdc01.fooddialer.in',
        'cname'=>'fdc01.fooddialer.in.s3.amazonaws.com',
        //'cname'=>'d1n6wrhfqubf4w.cloudfront.net'
    ),
);