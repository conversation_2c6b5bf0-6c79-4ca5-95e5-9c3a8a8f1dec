import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletFreeze } from '@/components/customer-service-v12/[id]/wallet/freeze';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletFreeze', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletFreeze />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletFreeze')).toBeInTheDocument();
  });
});