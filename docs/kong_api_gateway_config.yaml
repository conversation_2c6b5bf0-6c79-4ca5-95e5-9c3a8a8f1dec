_format_version: "2.1"
_transform: true

services:
  # Auth Service
  - name: auth-service
    url: http://auth-service:8000
    routes:
      - name: auth-routes
        paths:
          - /v2/auth
        strip_path: false
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Customer Service
  - name: customer-service
    url: http://customer-service:8000
    routes:
      - name: customer-routes
        paths:
          - /v2/customers
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Product Service
  - name: product-service
    url: http://product-service:8000
    routes:
      - name: product-routes
        paths:
          - /v2/products
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 120
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Order Service
  - name: order-service
    url: http://order-service:8000
    routes:
      - name: order-routes
        paths:
          - /v2/orders
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Payment Service
  - name: payment-service
    url: http://payment-service:8000
    routes:
      - name: payment-routes
        paths:
          - /v2/payments
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 30
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Kitchen Service
  - name: kitchen-service
    url: http://kitchen-service:8000
    routes:
      - name: kitchen-routes
        paths:
          - /v2/kitchen
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Delivery Service
  - name: delivery-service
    url: http://delivery-service:8000
    routes:
      - name: delivery-routes
        paths:
          - /v2/delivery
        strip_path: false
    plugins:
      - name: jwt
        config:
          secret_is_base64: false
          claims_to_verify:
            - exp
            - nbf
      - name: rate-limiting
        config:
          minute: 60
          policy: local
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Authorization
            - Content-Type
            - Origin
            - X-Requested-With
          credentials: true
          max_age: 3600
          preflight_continue: false

consumers:
  - username: admin-api
    jwt_secrets:
      - algorithm: HS256
        key: admin-api-key
        secret: your-admin-api-secret

  - username: mobile-app
    jwt_secrets:
      - algorithm: HS256
        key: mobile-app-key
        secret: your-mobile-app-secret

  - username: web-app
    jwt_secrets:
      - algorithm: HS256
        key: web-app-key
        secret: your-web-app-secret

plugins:
  # Global plugins applied to all services
  - name: request-transformer
    config:
      add:
        headers:
          - X-API-Version:2.0
  - name: response-transformer
    config:
      add:
        headers:
          - X-Powered-By:Food Delivery API
  - name: key-auth
    config:
      key_names:
        - apikey
  - name: acl
    config:
      allow:
        - admin
        - user
