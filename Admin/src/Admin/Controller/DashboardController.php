<?php
/**
 * This File shows the dashboard of fooddialer system
 * It shows new order,orders in process,delivered orders & total amount due of customers
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DashboardController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\Mvc\Controller\Plugin\Layout;

use Zend\Db\Sql\Expression;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Config\Reader\Json;
use Zend\Session\Zend\Session;
use Zend\Session\Container;

use Lib\Utility;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Admin\Report as QSreport;
use Lib\QuickServe\CommonConfig as QSConfig;


class DashboardController extends AbstractActionController
{
	
	public $todaysdate;
	public $yesterdaysdate;
	public $lastweekstartdate;
	public $lastweekenddate;
	public $thisweekstartdate;
	public $thisweekendtdate;
	public $lastmonthstartdate;
	public $lastmonthenddate;
	public $thismonthstartdate;
	public $thismonthenddate;
	public $adminitoarray;
	public $toarraycondition;
	public $formatter;
    private $authservice;

    function __construct(){
	
		$this->calculatedates();

	}
	
	
	/**
	 * This is the default action of dashboard
	 * It gives all the results for new orders,order in process,delivered orders & amount due of customers till today
	 *
	 * @return \Zend\View\Model\ViewModel
	 */ 
    
    public function indexAction()
    {
    	if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
		$iden = $this->authservice->getIdentity();
        
		$selectedMenu = $this->params()->fromRoute('selectedMenu','lunch');
        
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
    	
		$sql = new QSql($sm);
        $select = new QSelect ();
		
        $session_setting = new Container('setting');
        $comapny_name = $session_setting->setting['MERCHANT_COMPANY_NAME'];
            
		$today = date('Y-m-d');
        $select=  $sql->select()->from('orders');
        
        $select->columns(array('count' => new Expression("COUNT('pk_order_no')")));
        $select->where(array(
        	'order_date' => $today
        ));
       
//        $select->group('order_no');
//        dd($select->getSqlString());
        $statement = $sql->prepareStatementForSqlObject($select);
        
        $result = $statement->execute($statement);  
        $rows = new ResultSet();
        $orders['new'] = $rows->initialize($result)->count();
        
		$select=  $sql->select()->from('orders')->where(array('order_date' => $today, 'order_status' => 'Complete', 'delivery_status'=>'Delivered'));
//		$select->group('order_no');
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute($statement);
        $rows = new ResultSet();
        $orders['delivered'] = $rows->initialize($result)->count();
         
		$select=  $sql->select()->from('orders');
        $select->where(array( 
	      'order_date' => $today,
        	
        ));
        
     	$select->where->in('order_status',  array('Complete','New') );
//        $select->group('order_no');
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result=$statement->execute($statement);
        $rows = new ResultSet();
     
        $orders['in_process'] = $rows->initialize($result)->count();
       
        $select=  $sql->select()->from('invoice_payments');
        $select->columns(array('unpaid' => new \Zend\Db\Sql\Expression('SUM(amount_due)')));
         
        $select->join('invoice', 'invoice.invoice_id = invoice_payments.invoice_ref_id', array());
        
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result=$statement->execute($statement);
        $rows = new ResultSet();
        $unp = $rows->initialize($result)->toArray();
        //print_r($unp);exit;
        $orders['unpaid'] = $unp[0]['unpaid'];
        //echo $orders['unpaid'];exit;
        
        $orders = QSreport::getInstance($sm);

        /*******Ashwini***********/
        $this->table = "activity_log";
		
//		$sm = $this->service_locator;
//		$adapt = $sm->get('Write_Adapter');	
//        $this->table="activity_log";
//		$select->from( $this->table );
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $order_summary = $orders->getReportSummary(array('date'=>date("Y-m-d")),  array_column($iden->kitchens, 'fk_kitchen_code'));
//        echo "<pre>";print_r($order_summary);exit;
        $this->layout()->setVariables(array('page_title'=>"Today's Dashboard",'description'=>"statistics and more",'breadcrumb'=>"Today's Dashboard",'activetab'=>'dashboard'));
//       $sql= $select->where(array('context_name !='' order by modified_date DESC LIMIT 5'));
        $sql = "SELECT * FROM activity_log where context_name != '' order by modified_date DESC LIMIT 5";
        
//       $selectString = $sql->getSqlStringForSqlObject($select);
//       echo($selectString);
        $activity = $adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
        
        $loggedUser = $this->layout()->loggedUser;
        
		 return new ViewModel(array(
	 		'flashMessages'=> $this->flashMessenger()->getMessages(),
			 //'orders' => $orders,
		 	'orders' => $order_summary,
		 	'activity' => $activity,
		 	'loggedUser'=>	$loggedUser,
             'comapany_name'=>$comapny_name,
		 	//'formatter'=> 'xyz'	
		 //	'products_orders' => $kitchenArray ,
		 ));
		
    }
	/**
	 * This is test action
	 *
	 * @return array
	 * @deprecated No longer used by internal code and not recommended.
	 */
    public function fooAction()
    {
        // This shows the :controller and :action parameters in default route
        // are working when you browse to /dashboard/dashboard/foo
        return array();
    }
    
    public function getDataAction()
    {	
		$selectedYear = $this->getRequest()->getQuery('selectedYear');
		//print_r($id['selectedYear']);
        $sm = $this->getServiceLocator();
        $adapter = $sm->get('Write_Adapter');
        $sql=new QSql($sm);
       // $count=new Expression('COUNT("city")');
        //$yearExp = new Expression('YEAR(order_date)');
		$distinctCity = new Expression('DISTINCT city');
        //$clause=new Expression("CASE QUARTER(order_date)  WHEN 1 THEN 'Jan-Mar' WHEN 2 THEN 'Apr-Jun' WHEN 3 THEN 'July-Sep' WHEN 4 THEN 'Oct-Dec' END");
        $select=  $sql->select()
        		->columns(array('distinct'=>$distinctCity),false)
        
        		->from('orders')
        		->where(array('delivery_status'=>'Delivered'));
       
        $statement = $sql->prepareStatementForSqlObject($select);
        $result=$statement->execute($statement);
        $rows = new ResultSet();
        $porders = $rows->initialize($result);
  
		$kitchenArray[] = array('Range');
		$porders->buffer(); 
		foreach($porders->toArray() as $nkey=>$nval){
			if(!in_array($nval['distinct'],$kitchenArray)) {
				array_push($kitchenArray[count($kitchenArray)-1], (int)$nval['distinct']);
			}
		}
	
			$monthArray=array("1"=>"Jan-Mar","2"=>"Apr-June","3"=>"July-Sep","4"=>"Oct-Dec");
			
			foreach($monthArray as $monthKey=>$monthVal) {
				$total=array();
					foreach($kitchenArray[0] as $key=>$val){
						if($val!='Range'){
							$sql = "SELECT COUNT(*) cnt FROM orders WHERE city='".$val."' AND delivery_status = 'Delivered' AND YEAR(order_date) = '".$selectedYear."' AND QUARTER(order_date)='".$monthKey."'";
							$porders = $adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
							foreach($porders->toArray() as $newkey=>$newval){
								$total[] = (int)$newval['cnt'];
							}
						}
					}
					
					
				if(count($total)>0){
					$kitchenArray[] = array_merge(array($monthVal),$total);
				
				}
			}
		
        return new JsonModel($kitchenArray);
    }
    
    public function setKitchenSessionAction(){
    	
    	//unset($_SESSION['adminkitchen']);
    	
    	$request = $this->getRequest();
    	$id =  $request->getPost('id');
    	if($id!=''){
    		$kitchenname =  $request->getPost('kitchenname');
    		$_SESSION['adminkitchen'] = $id;
    		$_SESSION['adminkitchenname'] = $kitchenname;
    	}
    	return new JsonModel($_SESSION);
    }
    
    public function kitchenAction(){
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
    	$sql = new QSql($sm);
    	
    	$kitchen = QSConfig::getInstance($sm);

    	//$mykitchen = $kitchen->getKitchenScreen();
    	
    	$mykitchen = array();
    	$rowKitchens = $this->layout()->loggedUser->kitchens;
    	
    	foreach($rowKitchens as $row=>$kitchen){
    		$mykitchen[$kitchen['fk_kitchen_code']] = $kitchen['kitchen_name']." (".$kitchen['location'].")";
    	}
    	
    	return new JsonModel($mykitchen);
    }
    
    public function getKitchenOverviewAction(){
    	
        
    	$id = $_POST['id'];
    	
        $userKitchens = $this->layout()->loggedUser->kitchens;
        
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
//    	$sql = new QSql($adapter);
    	
    	$fetchmenus = QSConfig::getInstance($sm);
    	
    	$menus = $fetchmenus->getSettings();
    	
    	$cols = "";
    	
    	foreach ($menus['MENU_TYPE'] as $k => $v){
    		$key = $v;
    		$cols .= 'SUM(IF(order_menu = \''.$v.'\', total_order, 0)) as '.$key.',';
    	}
    	
    	$cols = rtrim($cols,",");
    	
    	$today = date('Y-m-d');
    	
    	$select1 = "DROP VIEW IF EXISTS mykitchen";
    	
    	$result1 = $adapter->query(
    			$select1, $adapter::QUERY_MODE_EXECUTE
    	);    	

    	$select2 = "CREATE VIEW mykitchen as SELECT product_name,$cols FROM kitchen ";
        
        if($id != 'all'){
            $select2 .= "WHERE fk_kitchen_code = ".$id." ";
        }else{
            $select2 .= "WHERE fk_kitchen_code in ( ".implode(',', array_column($userKitchens, 'fk_kitchen_code'))." ) ";
        }
             
        $select2 .= "GROUP BY product_name ORDER BY total_order desc LIMIT 7";

    	$result2 = $adapter->query(
    			$select2, $adapter::QUERY_MODE_EXECUTE
    	);

    	$select = "";
    	$rows = array();
    	$data = array();
    	
    	$select = "SELECT *  FROM mykitchen";
    	$result = $adapter->query(
    		$select, $adapter::QUERY_MODE_EXECUTE
    	);
    	
    	$rs = $result->toArray();
    	
    	if(is_array($rs)){
	    	foreach($rs as $row){
	    		foreach($row as $field=>$value){
	    			$data[$field] = (isset($data[$field]))?$data[$field]:'';
	   				$data[$field] .= $value.",";
	    		}
	    	}
    	}
    	
    	foreach($menus['MENU_TYPE'] as $menu){
    		$arrTmp = array();
    		$arrTmp['name'] = $menu;
    		
    		$arrTmp['data'] = rtrim($data[$menu],",");
    		
    		array_push($rows, $arrTmp);
    	}
    	
    	return new JsonModel($data);
    	 
    }
     
    public function getTodayDeliverChartAction(){
    	$id = $_POST['id'];
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
    	$sql = new QSql($sm);
    	$today = date('Y-m-d');
    	
        $userKitchens = $this->layout()->loggedUser->kitchens;
        
    	$select2 = "select order_menu,count(*) count from orders where quantity = '1' AND DATE(order_date) = '$today' and ";
        
        if($id != 'all'){
            $select2 .= " fk_kitchen_code = ".$id." ";
        }else{
            $select2 .= " fk_kitchen_code in ( ".implode(',', array_column($userKitchens, 'fk_kitchen_code'))." ) ";
        }
             
        $select2 .= " group by order_menu";

        $result2 = $adapter->query(
    			$select2, $adapter::QUERY_MODE_EXECUTE
    	);
    	$rs = $result2->toArray();
    	return new JsonModel($rs);
    }
    
    public function getTodayOrdersrChartAction(){
    	$id = $_POST['id'];
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
    	$sql = new QSql($sm);
    	$today = date('Y-m-d');
        
        $userKitchens = $this->layout()->loggedUser->kitchens;
    	
        $select2 = "select order_menu,count(*) count from orders where DATE(created_date) = '$today' and ";
        
         if($id != 'all'){
            $select2 .= " fk_kitchen_code = ".$id." ";
        }else{
            $select2 .= " fk_kitchen_code in ( ".implode(',', array_column($userKitchens, 'fk_kitchen_code'))." ) ";
        }
             
        $select2 .= " group by order_menu";
        
    	$result2 = $adapter->query(
    			$select2, $adapter::QUERY_MODE_EXECUTE
    	);
    	$rs = $result2->toArray();
    	return new JsonModel($rs);
    }
    
    public function getKitchenDataAction(){
    	
    	$selectedMenu = $this->getRequest()->getQuery('selectedMenu','lunch');
    	$today = date('Y-m-d');
		$sm = $this->getServiceLocator();
    	$adapter = $sm->get('Write_Adapter');
		$sql=new QSql($sm);
		
		$select=  $sql->select()->from('kitchen')->where(array('products.status'=> '1','kitchen.date'=> $today,'order_menu'=>$selectedMenu));
		$select->where->notEqualTo('kitchen.total_order', 0);
		$select->join('products', 'products.pk_product_code = kitchen.fk_product_code',array('name'),$select::JOIN_LEFT);
		//$select->limit(10);
	
		$statement = $sql->prepareStatementForSqlObject($select);
		$result=$statement->execute($statement);
		$rows = new ResultSet();
		$porders = $rows->initialize($result);
		$kitchenArray = array();
		foreach($porders->toArray() as $key=>$ord)
		{
			$kitchenArray[$key]['name'] = $ord['name'];
			$kitchenArray[$key]['total_order'] = $ord['total_order'];
			$kitchenArray[$key]['prepared'] = $ord['prepared'];
		}
	
		return new JsonModel($kitchenArray);
		
    }
    
    public function locationAction(){
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libCommon = QSConfig::getInstance($sm);
    
    	$city = $this->params()->fromPost("city",null);
    	$locations = $libCommon->getLocations($city);
    	$locations = $locations->toArray();
    	 
    	return new JsonModel(array('data' => $locations));
    }
    
    public function cityAction(){
    	 
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libCommon = QSConfig::getInstance($sm);
    	 
    	$cities = $libCommon->getCity();
    
    	return new JsonModel(array('data' => $cities));
    }
    public function showactivityAction()
    {
    	$this->layout()->setVariables(array('page_title'=>"Activity Log",'description'=>"activity log",'breadcrumb'=>"Activity Log"));
    	return new ViewModel(array(
    			
    			
    	));
    }
    public function ajxshowactivityAction()
    {
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get("Write_Adapter");
    	$request = $this->getRequest();
    	$libCustomer = QSCustomer::getInstance($sm);
    	$libCommon = QSCommon::getInstance($sm);
    	
    	$setting_session = new Container('setting');
    	$setting = $setting_session->setting;
    	
    	$utility = new \Lib\Utility();
    	$utility = Utility::getInstance();
    	
    	$tab = $this->params()->fromPost("tab",'today');
    	$page = $this->params()->fromPost("page",1);
    	$itemsPerPage = $this->params()->fromPost("limit",10);

    	$select  = new QSelect();
    	if($tab=='today')
    	{
    		$select->where('date(modified_date) = "'.$this->todaysdate.'"');
    	}
    	elseif($tab=='yesterday')
    	{
    		$select->where('date(modified_date) = "'.$this->yesterdaysdate.'"');
    	}
    	elseif($tab=='last_week'){
	    	$select->where('date(modified_date) >= "'.$this->lastweekstartdate.'"');
	    	$select->where('date(modified_date) <= "'.$this->lastweekenddate.'"');
    	}
    	elseif($tab=='last_month'){
    		$select->where('(modified_date) >= "'.$this->lastmonthstartdate.'"');
    		$select->where('date(modified_date) <= "'.$this->lastmonthenddate.'"');
    	}
    	elseif($tab=='last_year'){
    		$lastyear = date("Y",strtotime("-1 year"));
    		$select->where('YEAR(modified_date) = "'.$lastyear.'"');
    	}
    	else 
    	{
    		$select->where('MONTH(modified_date) = "'.$tab.'"');
    	}
    
    	$activitydata = $libCustomer->getActivityData($select,$tab,$page);
    	$activitydata->setCurrentPageNumber($page)
    	->setItemCountPerPage($itemsPerPage)
    	->setPageRange(7);
    	
    	$arrTransactions = array();
    
    	
    	foreach($activitydata as $key=>$transaction){
    	
    		$transaction->controller = ucfirst($transaction->controller);
    		
    		$date = $transaction->modified_date;
    		$transaction->modified_date = $utility->displayDate($date,$setting['DATE_FORMAT'])." ".date('h:i:s',strtotime($date));
    		array_push($arrTransactions,$transaction);
    	}

    	return new JsonModel(
			array(
				'recordsTotal'=>$activitydata->getTotalItemCount(),
				'recordsFiltered'=>$activitydata->getTotalItemCount(),
				'page'=>$page,
				'pageCount'=>$activitydata->getPages()->pageCount,
				'data' => $arrTransactions,
				'date_format' => $setting['DATE_FORMAT']
			)
    	);
    }
    
    public function calculatedates()
    {
    	
    	$this->todaysdate=date('Y-m-d');
    	$this->yesterdaysdate = date('Y-m-d',strtotime("-1 days"));
    	
    	$previous_week = strtotime("-1 week +1 day");
    	$start_week = strtotime("last sunday midnight",$previous_week);
    	$end_week = strtotime("next saturday",$start_week);
    	$start_week = date('Y-m-d',$start_week);
    	$end_week = date('Y-m-d',$end_week);
    	$this->lastweekstartdate = $start_week;
    	$this->lastweekenddate = $end_week;
    
    	$this->thisweekstartdate = date('Y-m-d', strtotime('Last Monday'));
    	$this->thisweekendtdate = date('Y-m-d', strtotime('Next Sunday'));
    
    	$this->lastmonthstartdate = date('Y-m-d',strtotime('first day of last month'));
    	$this->lastmonthenddate = date('Y-m-d',strtotime('last day of last month'));
    
    	$this->thismonthstartdate = date("Y-m-d", strtotime(date('m').'/01/'.date('Y').' 00:00:00'));
    	$this->thismonthenddate = date("Y-m-d", strtotime('-1 second',strtotime('+1 month',strtotime(date('m').'/01/'.date('Y').' 00:00:00'))));

    	return;
    }

    public function deleteActivityAction()
    {
    	$sm = $this->getServiceLocator();
    	$adapter = $sm->get("Write_Adapter");
    	$request = $this->getRequest();
    	$libCustomer = QSCustomer::getInstance($sm);
    	
    	$date = $this->params()->fromPost("date");
    	
    	$activitydata = $libCustomer->deleteActivity($date);
    	return new JsonModel(
			array(
				'msg'=>'success',
			)
    	);
    	     	
    }
    
    public function testAction(){
    	
    	$sm = $this->getServiceLocator();
    	$libCommon = QSConfig::getInstance($sm);
    	$settings = $libCommon->getSettings();
    	$libPrintLabel = \Lib\QuickServe\PrintLabel::getInstance($settings,$sm);
    	$ordertable = $sm->get('QuickServe\Model\OrderTable');
    	
    	$locationTable = $sm->get('QuickServe\Model\LocationTable');
    	
    	$delivery_codes = $locationTable->fetchAll();
    	
    	$location_ids = array();
    	foreach ($delivery_codes as $code){
    		$location_ids[] = $code['pk_location_code'];
    	}
    	
    	$menu = "lunch";
    	
    	$print_data = $ordertable->getTodaysorder($location_ids,$menu);
    	
    	//echo "<pre>";print_r($print_data);die;
    	$config = $this->getServiceLocator()->get("config");
    	
    	// Set SMS config and application config data. 
    	$libPrintLabel->setConfig($config);
    	//$libPrintLabel->setShowBarcode('yes');
    	$libPrintLabel->setData($print_data['printData']);
    	
    	try{
    		$libPrintLabel->renderLabels();
    	}catch(\Exception $e){
    		
    		echo $e->getMessage();
    	}
    	
    	//echo "<pre>";print_r($libPrintLabel);echo "</pre>";
    	
    	die;
    	
    	return new JsonModel(array("success"));
    }
}
