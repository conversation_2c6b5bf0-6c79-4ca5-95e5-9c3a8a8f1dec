# 📋 OneFoodDialer 2025 - Primary API Specification

**Status:** ✅ CONSOLIDATED  
**Date:** December 23, 2025  
**Primary Specification:** `unified-api-specification.yaml`

## 🎯 Unified API Specification

The **primary and authoritative** API specification for OneFoodDialer 2025 is:

```
docs/openapi/unified-api-specification.yaml
```

### 📊 Coverage Summary
- **Total Endpoints:** 426 API endpoints
- **Microservices:** 11 Laravel 12 services
- **API Version:** v2 (current)
- **Authentication:** JWT Bearer tokens via Laravel Sanctum

### 🏗️ Service Coverage
| Service | Endpoints | Status |
|---------|-----------|--------|
| Authentication Service | 45 | ✅ Included |
| Customer Service | 89 | ✅ Included |
| Payment Service | 67 | ✅ Included |
| QuickServe Service | 156 | ✅ Included |
| Kitchen Service | 45 | ✅ Included |
| Delivery Service | 78 | ✅ Included |
| Analytics Service | 52 | ✅ Included |
| Admin Service | 23 | ✅ Included |
| Catalogue Service | 48 | ✅ Included |
| Notification Service | 22 | ✅ Included |
| Misscall Service | 16 | ✅ Included |

## 📁 Service-Specific Specifications (Reference Only)

The following service-specific OpenAPI files are maintained for reference and development purposes:

```
services/auth-service-v12/openapi.yaml
services/customer-service-v12/openapi.yaml
services/payment-service-v12/openapi.yaml
services/quickserve-service-v12/openapi.yaml
services/kitchen-service-v12/openapi.yaml
services/delivery-service-v12/openapi.yaml
services/analytics-service-v12/openapi.yaml
services/admin-service-v12/openapi.yaml
services/catalogue-service-v12/openapi.yaml
services/notification-service-v12/docs/openapi.yaml
services/misscall-service-v12/openapi.yaml
services/meal-service-v12/openapi.yaml
services/subscription-service-v12/openapi.yaml
```

**⚠️ Important:** These service-specific files should be considered **reference only**. All API documentation, tooling, and integration should use the unified specification.

## 🔧 Usage Guidelines

### For Developers
- **API Documentation:** Use `unified-api-specification.yaml`
- **Code Generation:** Generate from unified specification
- **Testing:** Validate against unified specification
- **Integration:** Reference unified specification for all integrations

### For Tools & Services
- **Kong API Gateway:** Configure routes based on unified specification
- **Swagger UI:** Serve unified specification
- **Postman Collections:** Generate from unified specification
- **API Testing:** Use unified specification for contract testing

### For Documentation
- **Public API Docs:** Generated from unified specification
- **Developer Portal:** Reference unified specification
- **Integration Guides:** Use unified specification examples

## 🔄 Maintenance

### Updating the Unified Specification
1. Make changes to the unified specification file
2. Validate the specification using OpenAPI tools
3. Update service-specific files if needed for development reference
4. Deploy updated specification to documentation systems

### Synchronization
- The unified specification is the **source of truth**
- Service-specific files may be updated for development convenience
- Any conflicts should be resolved in favor of the unified specification

## 📚 Related Documentation
- [Consolidation Implementation Plan](../../CONSOLIDATION_IMPLEMENTATION_PLAN.md)
- [Comprehensive Codebase Audit Report](../../COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md)
- [Kong API Gateway Configuration](../kong/)

---

**Last Updated:** December 23, 2025  
**Consolidation Status:** ✅ COMPLETED  
**Next Review:** January 2026
