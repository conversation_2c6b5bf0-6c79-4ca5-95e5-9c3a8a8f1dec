<?php

namespace App\Http\Controllers\Api\V2;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryStatusUpdateRequest;
use App\Http\Requests\OrderDeliveryRequest;
use App\Http\Resources\DeliveryLocationResource;
use App\Http\Resources\OrderResource;
use App\Services\Contracts\DeliveryServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DeliveryController extends Controller
{
    /**
     * @var \App\Services\Contracts\DeliveryServiceInterface
     */
    private DeliveryServiceInterface $deliveryService;
    
    /**
     * Create a new controller instance.
     *
     * @param \App\Services\Contracts\DeliveryServiceInterface $deliveryService
     */
    public function __construct(DeliveryServiceInterface $deliveryService)
    {
        $this->deliveryService = $deliveryService;
    }
    
    /**
     * Get orders for delivery.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection|\Illuminate\Http\JsonResponse
     */
    public function getOrders(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $locationId = $request->input('location_id');
            $date = $request->input('date', date('Y-m-d'));
            
            $orders = $this->deliveryService->getDeliveryOrders($userId, $locationId, $date);
            
            return OrderResource::collection($orders);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Search orders.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection|\Illuminate\Http\JsonResponse
     */
    public function searchOrders(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $searchTerm = $request->input('search');
            $locationId = $request->input('location_id');
            
            if (empty($searchTerm)) {
                return $this->errorResponse('Search term is required');
            }
            
            $orders = $this->deliveryService->searchOrders($userId, $searchTerm, $locationId);
            
            return OrderResource::collection($orders);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Update delivery status.
     *
     * @param \App\Http\Requests\DeliveryStatusUpdateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateDeliveryStatus(DeliveryStatusUpdateRequest $request): JsonResponse
    {
        try {
            $dto = new DeliveryStatusUpdateDTO(
                $request->input('order_id'),
                $request->user()->id,
                $request->input('order_completed', false)
            );
            
            $result = $this->deliveryService->updateDeliveryStatus($dto);
            
            if ($result) {
                return $this->successResponse('Delivery status updated successfully');
            } else {
                return $this->errorResponse('Failed to update delivery status');
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Get delivery locations for the authenticated user.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection|\Illuminate\Http\JsonResponse
     */
    public function getLocations(Request $request): AnonymousResourceCollection|JsonResponse
    {
        try {
            $userId = $request->user()->id;
            
            $locations = $this->deliveryService->getDeliveryLocations($userId);
            
            return DeliveryLocationResource::collection($locations);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Book third-party delivery.
     *
     * @param \App\Http\Requests\OrderDeliveryRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bookThirdPartyDelivery(OrderDeliveryRequest $request): JsonResponse
    {
        try {
            $dto = new OrderDeliveryDTO(
                $request->input('order_id')
            );
            
            $result = $this->deliveryService->bookThirdPartyDelivery($dto);
            
            if (isset($result['code']) && $result['code'] == 201) {
                return $this->successResponse('Third-party delivery booked successfully', $result);
            } else {
                return $this->errorResponse('Failed to book third-party delivery', $result);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Cancel third-party delivery.
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelThirdPartyDelivery(int $orderId): JsonResponse
    {
        try {
            $result = $this->deliveryService->cancelThirdPartyDelivery($orderId);
            
            if ($result) {
                return $this->successResponse('Third-party delivery cancelled successfully');
            } else {
                return $this->errorResponse('Failed to cancel third-party delivery');
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Get third-party delivery status.
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getThirdPartyDeliveryStatus(int $orderId): JsonResponse
    {
        try {
            $result = $this->deliveryService->getThirdPartyDeliveryStatus($orderId);
            
            return $this->successResponse('Third-party delivery status retrieved successfully', $result);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Return success response.
     *
     * @param string $message
     * @param array $data
     * @return \Illuminate\Http\JsonResponse
     */
    private function successResponse(string $message, array $data = []): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    /**
     * Return error response.
     *
     * @param string $message
     * @param array $data
     * @param int $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    private function errorResponse(string $message, array $data = [], int $statusCode = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'data' => $data
        ], $statusCode);
    }
}
