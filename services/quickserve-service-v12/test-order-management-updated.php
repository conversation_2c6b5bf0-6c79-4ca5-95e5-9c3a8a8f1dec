<?php
/**
 * Test script for updated order-management/create API
 * Tests the new start_date and selected_days functionality
 */

require_once __DIR__ . '/vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

$client = new Client([
    'base_uri' => 'http://*************:8003/api/',
    'timeout' => 30,
]);

echo "=== Testing Updated Order Management API ===\n\n";

// Test data with new structure
$testData = [
    'customer_id' => 3800,
    'customer_name' => 'Customer User',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '919998887779',
    'customer_address' => '123 Test Street, Test Area, Test City - 400001',
    'location_code' => 1001,
    'location_name' => 'Test Location',
    'city' => 1,
    'city_name' => 'Mumbai',
    'product_code' => 342,
    'product_name' => 'International Breakfast Subscription',
    'product_type' => 'Meal',
    'quantity' => 1,
    'amount' => 200.00,
    'start_date' => '2025-01-27', // Monday
    'selected_days' => [1, 2, 3, 4, 5], // Monday to Friday
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'subscription_days' => 15
];

echo "Test Data:\n";
echo json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

try {
    echo "Sending POST request to v2/order-management/create...\n";
    
    $response = $client->post('v2/order-management/create', [
        'json' => $testData,
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ]
    ]);
    
    $statusCode = $response->getStatusCode();
    $responseData = json_decode($response->getBody(), true);
    
    echo "Status Code: $statusCode\n";
    echo "Response:\n";
    echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
    
    if ($statusCode === 201 && $responseData['success']) {
        echo "✅ Test PASSED: Order created successfully\n";
        
        // Extract order details for further testing
        $orderNo = $responseData['data']['order_no'] ?? null;
        $tempPreOrderId = $responseData['data']['temp_pre_order_id'] ?? null;
        
        if ($orderNo) {
            echo "Order Number: $orderNo\n";
            echo "Temp Pre-Order ID: $tempPreOrderId\n";
            
            // Test getting order details
            echo "\n--- Testing Order Details API ---\n";
            try {
                $detailsResponse = $client->get("v2/order-management/details/$orderNo");
                $detailsData = json_decode($detailsResponse->getBody(), true);
                
                echo "Order Details Response:\n";
                echo json_encode($detailsData, JSON_PRETTY_PRINT) . "\n";
                
                if ($detailsData['success']) {
                    echo "✅ Order details retrieved successfully\n";
                } else {
                    echo "❌ Failed to retrieve order details\n";
                }
            } catch (RequestException $e) {
                echo "❌ Order details request failed: " . $e->getMessage() . "\n";
            }
            
            // Test pre-order status
            echo "\n--- Testing Pre-Order Status API ---\n";
            try {
                $statusResponse = $client->get("v2/order-management/pre-order-status/$orderNo");
                $statusData = json_decode($statusResponse->getBody(), true);
                
                echo "Pre-Order Status Response:\n";
                echo json_encode($statusData, JSON_PRETTY_PRINT) . "\n";
                
                if ($statusData['success']) {
                    echo "✅ Pre-order status retrieved successfully\n";
                } else {
                    echo "❌ Failed to retrieve pre-order status\n";
                }
            } catch (RequestException $e) {
                echo "❌ Pre-order status request failed: " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "❌ Test FAILED: " . ($responseData['message'] ?? 'Unknown error') . "\n";
    }
    
} catch (RequestException $e) {
    echo "❌ Test FAILED: Request exception - " . $e->getMessage() . "\n";
    
    if ($e->hasResponse()) {
        $errorResponse = $e->getResponse();
        $errorBody = json_decode($errorResponse->getBody(), true);
        echo "Error Response:\n";
        echo json_encode($errorBody, JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n=== Test Completed ===\n";
