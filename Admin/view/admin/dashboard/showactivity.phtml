<?php 

$lastsixmnthdate = date("Y-m-d",strtotime("-6 Months"));	

$arrMonth = array('1'=>'Jan','2'=>'Feb','3'=>'March','4'=>'April','5'=>'May','6'=>'June','7'=>'July','8'=>'Aug','9'=>'Sep','10'=>'Oct','11'=>'Nov','12'=>'Dec');
													$date =  date('m',strtotime("first day of last month"));
												
													//echo $arrMonth[$date-1];
													$str = '';
													for($i=$date-1; $i>='01';$i--){
													$str .='<tr>';
														$str .='<td>';
														$str .='<a href="javascript:void(0)" class="showdata" data-name='.$arrMonth[$i].' data-show='.$i.'>- '.$arrMonth[$i].'</a>';
														$str .='</td>';
													$str .='</tr>';
													}
											?>
							<div class="large-12 columns order_alert dn" id="msgdiv">
								<div  data-alert="" id= "msg" class="alert-box success round ">
					 			
								</div>
							</div>
							<div class="large-12 columns">
							
							<div class="portlet-title">
									<h4><i class="fa fa-table"></i> Show History</h4>
									<ul class="toolOption">
										<li>
											<div class="print">
												<button class="btn" id="deleteactivity" data-val="<?php echo $lastsixmnthdate;?>" data-name="deleteactivity" data-show="delete">
													<i class="fa fa-trash-o"></i> &nbsp;Logs older than six months
												</button>
											</div>
										</li>

									</ul>
								</div>
							</div>
							<div id="content" class="clearfix">
						
								<div class="row">
								
									<div class="large-10 columns">
										<table>
											<tr>
												<th>Modules</th>
												<th style="width: 60%">Activity</th>
												<th>User</th>
												<th>Date Time</th>
											</tr>
										
											<tbody class="activitydata">
                                            </tbody>
										</table>
										<div class="mb30 right" id="showmoreContainer">
									<button data-page="" data-view="" data-tab="" id="showmore" class="text-uppercase btn common-btn show-btn btn-block ob" type="button" style="display: none; margin-right:67px"> Show More &gt;&gt; </button>
									<input type="hidden" id="page" name="page" value="">
								</div>
								<div class="mb15" style="visibility: hidden;">
									
								&nbsp;
								</div>
									</div>
									<div class="large-2 columns">
										<div class="tbl_scroll">
										<table class="hishtory_table" style="width: 100%; text-align: center">
											<tr>
												<th style="width: 100%">History</th>
											</tr>
											<tr>
												<td>
													<a href="javascript:void(0)" class="showdata" data-name="Today" data-show="today">- Today</a>
												</td>
											</tr>
											<tr>
												<td>
													<a href="javascript:void(0)" class="showdata" data-name="Yesterday" data-show="Yesterday">- Yesterday</a>
												</td>
											</tr>
											<tr>
												<td>
													<a href="javascript:void(0)" class="showdata" data-name="Last Week" data-show="Last_Week">- Last Week</a>
												</td>
											</tr>
											<tr>
												<td>
													<a href="javascript:void(0)" class="showdata" data-name="Last Month" data-show="Last_Month">- Last Month</a>
												</td>
											</tr>
											
											<?php echo $str;?>
											<tr>
												<td>
													<a href="javascript:void(0)" class="showdata" data-name="Last Year" data-show="Last_Year">- Last Year</a>
												</td>
											</tr>
									<!-- 		<tr>
												<td>
													<a href="javascript:void(0)" id="deleteactivity" data-val="<?php //echo $lastsixmnthdate;?>" data-name="deleteactivity" data-show="delete">- Delete last 6 months log</a>
												</td>
											</tr> -->
											
										</table>
										</div>
									</div>
								</div>
								
								
								
						</div>
						
						<!-- END PAGE CONTAINER-->
				
				<!-- END CONTAINER -->
			</div>
	<style>
		.tbl_scroll{
		height: 430px;
   	   overflow-y: scroll;
    }
	</style>
			
			<script type="text/javascript">

			function getActivityData(tab){ 
			var page = $("#showmore").attr('data-page');
		
			$.ajax({
		        url: "<?php echo $this->url('dashboard',array('action' => 'ajxshowactivity')); ?>",
		        type:"POST",
		        async:false,
		        data:{tab:tab,page:page},
		        success:function(data){
		        	
		        	if(data.pageCount==0 || data.pageCount==page || data.pageCount<page){
						$("#showmore").hide();
						
					}else{
						$("#showmore").show();
						$("body").css("overflow:scroll");
					}
		        	//console.log(data.data); return false;
		        	if(data.data.length == 0){	
						var strHtml = '<tr><td style="text-align:center" colspan="6">No Data Found</td></tr>';
					}else{
						var strHtml = '';
					}

		        	$.each( data.data, function( key, value ) {
		        		 strHtml+='<tr>'+
		        		 '<td>'+value.controller+'</td>'+
                         '<td>'+value.description+'</td>'+
                         '<td>'+value.context_name+'</td>'+
                         '<td>'+value.modified_date+'</td>'+
                     '</tr>';
			        	
			        });

		        	if(page==1){							
						$(".activitydata").html(strHtml);
					}else{
						$(".activitydata").append(strHtml);
					}
					
		        },
		        error:function(){
		        }
		      });
			}
			var nextpage;
			$(document).ready(function() {
				
					$("#showmore").attr('data-page',1);
					
					var lastshow = "today";
					var tabname =  "Today";
					$("#showmore").attr('data-tab',lastshow);
					
					$(".activitylogcls").attr('display','block');
					
					$(".activitylogcls").html(tabname);
					getActivityData(lastshow);

					$(".showdata").click(function(){
						$("#msgdiv").addClass("dn");
					$("#showmore").attr('data-page',1);
					var changedtab = $(this).attr('data-show').toLowerCase();
					$("#showmore").attr('data-tab',changedtab);
					var tabname = $(this).attr('data-name').toUpperCase();
					$(".activitylogcls").html(tabname);
				
					getActivityData(changedtab);
				
				});
				
					/* $(".hishtory_table tr td a").click(function() {
						$("."+lastshow).hide();
						$("."+$(this).attr('data-show')).show();
						lastshow=$(this).attr('data-show');
						
					}); */

			    $("#showmore").click(function(){
				    
			    	$("#msgdiv").addClass("dn");
			    	var changedtab = $("#showmore").attr('data-tab');
			    
			    	//alert(changedtab);
			    	page = $("#showmore").attr('data-page');
			        nextpage = parseInt(page) + parseInt(1);
			        var amount_type = $("#amount_type").val();
			        $("#showmore").attr('data-page',nextpage);
			        
			        getActivityData(changedtab);
			    });

			    $("#deleteactivity").click(function(){

			    	var checkstr =  confirm('This will delete activity logs which are older than six months. \nAre you sure you want to continue?');
			   		 if(checkstr == true){

				    	var date = $(this).attr('data-val');
				    	$.ajax({
					        url: "<?php echo $this->url('dashboard',array('action' => 'delete-activity')); ?>",
					        type:"POST",
					        async:false,
					        data:{date:date},
					        success:function(data){ 
								if(data.msg=="success")
								{
									$("#msgdiv").removeClass("dn");
									$("#msg").html('Last six month log deleted successfully');
								}
						     }
				    	});
			   		 }
				 });
			});
			</script>