# 🔄 ORDER SWAP API DOCUMENTATION

## 📋 **Overview**

The Order Swap API allows customers to swap their meal with another product from the same category. This functionality updates both the `orders` and `order_details` tables while maintaining data integrity and proper financial calculations.

## 🎯 **API Endpoint**

```
POST /api/v2/order-management/swap/{orderNo}
```

## 📊 **Request Format**

### **Headers**
```
Content-Type: application/json
Accept: application/json
```

### **URL Parameters**
- `orderNo` (string, required): The order number to swap

### **Request Body**
```json
{
  "order_date": "2025-09-03",
  "new_product_code": 342,
  "reason": "Customer wants to change from Poha to Upma",
  "meal_type": "breakfast"
}
```

### **Request Fields**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `order_date` | string (date) | ✅ Yes | Date of the order to swap (YYYY-MM-DD) |
| `new_product_code` | integer | ✅ Yes | Product code of the new product |
| `reason` | string | ❌ No | Reason for the swap (max 255 chars) |
| `meal_type` | string | ❌ No | Filter by meal type (breakfast/lunch/dinner) |

## 📋 **Response Format**

### **Success Response (200)**
```json
{
  "success": true,
  "message": "Order swapped successfully",
  "data": {
    "order_id": 127810,
    "order_no": "QA93250725",
    "order_date": "2025-09-03",
    "swap_details": {
      "old_product": {
        "code": 341,
        "name": "Poha",
        "price": 150.00
      },
      "new_product": {
        "code": 342,
        "name": "Upma",
        "price": 200.00
      },
      "price_difference": 50.00,
      "swap_charges": 25.00,
      "total_amount_change": 75.00,
      "new_order_amount": 225.00
    },
    "reason": "Customer wants to change from Poha to Upma"
  }
}
```

### **Error Responses**

#### **404 - Order Not Found**
```json
{
  "success": false,
  "message": "Order not found for the specified date",
  "data": {
    "order_no": "QA93250725",
    "order_date": "2025-09-03",
    "meal_type": null
  }
}
```

#### **400 - Order Not Swappable**
```json
{
  "success": false,
  "message": "Order cannot be swapped due to its current status",
  "data": {
    "order_id": 127810,
    "order_status": "Delivered",
    "delivery_status": "Delivered"
  }
}
```

#### **400 - Products Not Swappable**
```json
{
  "success": false,
  "message": "Products are not in the same category or not swappable",
  "data": {
    "current_product": {
      "code": 341,
      "name": "Poha",
      "category": "Breakfast",
      "type": "Meal"
    },
    "new_product": {
      "code": 355,
      "name": "Biryani",
      "category": "Lunch",
      "type": "Meal"
    }
  }
}
```

#### **422 - Validation Error**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "order_date": ["The order date field is required."],
    "new_product_code": ["The new product code field is required."]
  }
}
```

## 🔍 **Validation Rules**

### **Order Validation**
- ✅ Order must exist for the specified date
- ✅ Order status must not be: `Cancelled`, `Delivered`, `Complete`
- ✅ Delivery status must not be: `Delivered`, `Failed`, `Dispatched`
- ✅ Order date must not be in the past

### **Product Validation**
- ✅ Both current and new products must exist and be active
- ✅ Products must belong to the same category
- ✅ Products must have the same product type
- ✅ Products must have the same food type (veg/non-veg)
- ✅ New product must be swappable (`is_swappable = true`)

## 💰 **Price Calculation**

### **Formula**
```
New Order Amount = Old Amount + Price Difference + Swap Charges
```

### **Example Calculation**
```
Old Product Price: ₹150.00
New Product Price: ₹200.00
Swap Charges: ₹25.00

Price Difference = ₹200.00 - ₹150.00 = ₹50.00
Total Amount Change = ₹50.00 + ₹25.00 = ₹75.00
New Order Amount = ₹150.00 + ₹75.00 = ₹225.00
```

### **Tax Recalculation**
- Tax is automatically recalculated based on the new order amount
- Uses the same tax calculation logic as order creation

## 📊 **Database Updates**

### **Orders Table**
| Column | Update |
|--------|--------|
| `product_code` | Updated to new product code |
| `product_name` | Updated to new product name |
| `product_description` | Updated to new product description |
| `amount` | Adjusted for price difference + swap charges |
| `tax` | Recalculated based on new amount |
| `last_modified` | Updated to current timestamp |
| `remark` | Appended with swap details and reason |

### **Order Details Table**
| Column | Update |
|--------|--------|
| `product_code` | Updated to new product code |
| `product_name` | Updated to new product name |
| `product_amount` | Proportionally adjusted |
| `product_tax` | Recalculated for new amount |
| `product_generic_code` | Updated to new product code |
| `product_generic_name` | Updated to new product name |

## 🧪 **Testing Examples**

### **1. Valid Breakfast Swap**
```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 342,
    "reason": "Customer wants to change from Poha to Upma",
    "meal_type": "breakfast"
  }'
```

### **2. Cross-Category Swap (Should Fail)**
```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 355,
    "reason": "Try to change breakfast to lunch item"
  }'
```

## 📋 **Database Verification**

### **Check Updated Order**
```sql
SELECT pk_order_no, order_no, product_code, product_name, amount, tax, remark, last_modified
FROM orders 
WHERE order_no = 'QA93250725' AND order_date = '2025-09-03';
```

### **Check Updated Order Details**
```sql
SELECT pk_order_detail_id, ref_order_no, product_code, product_name, product_amount, product_tax
FROM order_details 
WHERE ref_order_no = 'QA93250725' AND order_date = '2025-09-03';
```

### **Check Product Categories**
```sql
SELECT pk_product_code, name, product_category, category, product_type, food_type, is_swappable, swap_charges
FROM products 
WHERE pk_product_code IN (341, 342, 355);
```

## 🔒 **Security & Audit**

### **Audit Logging**
- Complete swap history is logged with timestamps
- Includes old and new product details
- Records financial impact and reasons
- Tracks IP address and user agent

### **Transaction Safety**
- All database operations are wrapped in transactions
- Automatic rollback on any error
- Ensures data consistency

## 🎯 **Use Cases**

1. **Dietary Preference Change**: Customer switches from non-veg to veg option
2. **Taste Preference**: Customer prefers different breakfast item
3. **Availability Issues**: Swap to available alternative in same category
4. **Premium Upgrade**: Customer upgrades to premium option with additional charges

## ⚠️ **Limitations**

1. **Same Category Only**: Cannot swap across different meal categories
2. **Future Orders Only**: Cannot swap orders for past dates
3. **Active Orders Only**: Cannot swap cancelled or delivered orders
4. **Product Availability**: New product must be active and swappable

## 🎉 **Status: READY FOR PRODUCTION**

The Order Swap API is fully implemented with comprehensive validation, error handling, and audit logging. It provides customers with flexibility while maintaining data integrity and proper financial calculations.
