# QuickServe Frontend Documentation

Welcome to the QuickServe Frontend documentation. This documentation provides comprehensive information about the frontend architecture, components, APIs, and user guides.

## Table of Contents

- [Architecture Documentation](architecture/README.md)
- [API Documentation](api/README.md)
- [Component Documentation](components/README.md)
- [User Guides](guides/README.md)

## Quick Links

- [Getting Started](guides/getting-started.md)
- [Development Guidelines](guides/development-guidelines.md)
- [Microfrontend Architecture](architecture/microfrontend-architecture.md)
- [Feature Flags](architecture/feature-flags.md)
- [API Integration](api/api-integration.md)
- [Component Library](components/ui-components.md)

## Overview

QuickServe is a comprehensive food service management platform that handles customer management, order processing, payment processing, kitchen operations, and delivery management. This frontend application provides a unified interface for all these operations.

The application follows a microfrontend architecture pattern, with each business domain implemented as a separate microfrontend:

- **Customer**: Customer management and profiles
- **Payment**: Payment processing and management
- **Order**: Order management and processing
- **Kitchen**: Kitchen operations and order preparation
- **Delivery**: Delivery management and tracking

Each microfrontend is self-contained with its own components, services, and state management, but they share common UI components, utilities, and infrastructure.

## Technology Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn UI
- **State Management**: Zustand
- **API Client**: Custom API client with Axios
- **Feature Flags**: Flagsmith
- **Internationalization**: Custom i18n solution
- **Testing**: Jest, React Testing Library
- **CI/CD**: GitLab CI/CD

## Contributing to Documentation

We encourage all team members to contribute to this documentation. If you find any issues or have suggestions for improvement, please create a merge request with your changes.

To contribute:

1. Create a feature branch from `develop`
2. Make your changes to the documentation
3. Submit a merge request to `develop`
4. Ensure CI/CD pipeline passes
5. Request a review from a team member

## Documentation Structure

- **Architecture Documentation**: Detailed information about the frontend architecture, design patterns, and technical decisions.
- **API Documentation**: Information about the API endpoints, request/response formats, and integration points.
- **Component Documentation**: Documentation for the UI components, including usage examples and props.
- **User Guides**: Step-by-step guides for common tasks and workflows.

## Contact

For questions or support, please contact the frontend team.
