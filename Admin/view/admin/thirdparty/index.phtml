<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                     <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addthird" class="common-orange-btn-on-hover"> Add Third Party</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updthird" class="common-orange-btn-on-hover">Update Third Party details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactthird" class="common-orange-btn-on-hover">Deactivate Third Party</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<?php
$utility = \Lib\Utility::getInstance ();
?>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Third Party</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'thirdparty','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('thirdparty', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Third Party</button>
                    </div>
                  <?php } ?>
                </li>
               
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Commission Rate <!-- <i class="fa fa-rupee"></i> --></th>
                            <th>Commission Type</th>
                            <!--<th>Location</th>-->
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                    <?php // dd($paginator);?>
                     <?php foreach ($paginator as $thirdparty) { ?>
                        <tr>
                            <td><?php echo $this->escapeHtml($thirdparty['name']);?></td>
                          
                            <td><?php echo ($this->escapeHtml($thirdparty['phone']));?></td>
                            <td><?php echo $this->escapeHtml($thirdparty['email']);?></td>
                            <td><?php echo $x = ( $this->escapeHtml($thirdparty['commission_type']) == 'fixed' ? $utility->getLocalCurrency($this->escapeHtml($thirdparty['comission_rate'])) : $this->escapeHtml($thirdparty['comission_rate']) ); ?></td>
                            <td><?php echo $this->escapeHtml($thirdparty['commission_type']);?></td>
                            <!--<td><?php // echo $this->escapeHtml($thirdparty['location']);?></td>-->
                            <td><span class="<?php echo ($this->escapeHtml($thirdparty['status']))?'active':'inactive';?>"><?php echo ($this->escapeHtml($thirdparty['status']))?'Active':'Inactive';?></span></td>
                            <td>
                            <a href="<?php echo $this->url('thirdparty', array('action' => 'edit', 'id' => $thirdparty['third_party_id'])); ?>" class="btn btn5 btn_pencil5">
						             <?php $textadd = ($thirdparty['status'])=="1"? 'Deactive' :'Activate'; ?>
                            <button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
                            </a>
                             <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this third party ?')" href="<?php echo $this->url('thirdparty', array('action' => 'delete', 'id' => $thirdparty['third_party_id']));
						        	?>" class="btn btn5 btn_trash5">
                            
                           <?php if($textadd == 'Deactive') {?>
                            <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"><i class="fa fa-ban"></i></button>
                            <?php } else if($textadd == 'Activate') {?>
                            <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>
                            <?php } ?>
                            </a></td>
                        </tr>
                       <?php } ?>
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();
	$("#customer").dataTable().fnDestroy();

	$('#customer').dataTable( {

        "aaSorting": [],
        "aoColumnDefs": [
          { 'bSortable': false, 'aTargets': [ -1 ] }
       ]
    });	
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addthird",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add third party delivery/aggregator");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updthird",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-intro", "Click here to edit third party details");
      $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactthird",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-intro", "Click here to deactivate third party.");
      $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').removeAttr("data-intro");
    });
</script>  