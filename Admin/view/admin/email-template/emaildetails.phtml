 <div id="content">
      	
      <?php 
      $cnt = 1; 
      $total = count($emailtemplate);
      	
      foreach ($emailtemplate as  $key => $val){
      	
      	$colStyle = ($cnt == $total) ? 'style="float:left"' : '';
      ?>
      
      <div class="large-6 columns mt10" <?php echo $colStyle; ?>>
      
      	<div class="portlet box light-grey">        
        	<div class="portlet-title">
	            <h4 class="white"><i class="fa fa-table"></i><?php echo str_replace('_', " ", $val['template_key']);?><?php //echo $cnt;?></h4>  
	            <ul class="toolOption">            	
	     
	                <li>
	                    <div class="tools">
	                        <a href="javascript:;" class="collapse"></a>
	                    </div>
	                </li>
	            </ul>
        	</div>        
        	<div class="portlet-body">        
        		<table id="customerInfo" class="display">                   
 
                    <tbody>
                        <tr>
                            <td style="width:20%">Title</td>
                            <td style="width:1%">:</td>
                            <td style="width:79%"><strong><input type="hidden" name="templateid" id="templateid" value="<?php echo $val['pk_template_id'];?>" ><?php echo str_replace('_', " ", $val['template_key']);?></strong></td>
                        </tr>
                        <tr>
                            <td>Purpose</td>
                            <td>:</td>
                            <td><?php echo $val['purpose'];?></td>
                        </tr>
                        <tr>
                            <td>Type</td>
                            <td>:</td>
                            <td><?php echo $val['type'];?></td>
                        </tr>
                
                        <tr>
                        	<td>Action</td>
                        	<td>:</td>
                        	<td>
                        		<button class="btn mb0" onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'editemaildetail','id'=>$val['pk_template_id'],'setid'=>$val['fk_set_id']));?>'"><i class="fa fa-edit"></i> Modify</button>
                        		<button class="btn mb0" onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'emailview','id'=>$val['pk_template_id']));?>'" ><i class="fa fa-eye"></i> Preview</button>
                        		
                        		<!-- <input name="status" type="checkbox" id="<?php //echo $this->escapeHtml($val['pk_template_id']);?>"  <?php //if($val['is_active'] == 1){ echo 'checked';} ?>  name="check-1" class="lcs_check btn mb0 chkbox" /> -->                      		
                        		
                        	</td>
                        </tr>  
                        <script type="text/javascript">
                        $(document).ready(function(e) {
                            var sendtoadmin = '<?php echo $val['send_to_admin'] ?>';
                            if(sendtoadmin == 'yes'){
                              $('.sendtoadminyes_'+<?php echo $val['pk_template_id'] ?>+' .radio span').attr("class", "checked");return false;
                            }else {
                              $('.sendtoadminno_'+<?php echo $val['pk_template_id'] ?>+' .radio span').attr("class", "checked"); return false;
                            }
                            
                        });    
                        </script>
                        <tr> 
                          <td>Admin Notification</td> 
                          <td>:</td> 
                            <td style="display: flex;border-bottom: 0; margin-top: 7px;">
                            <div style="margin-right: 15px;" class='sendtoadminyes_<?php echo $val['pk_template_id']?>'><input name="<?php echo $val['pk_template_id']?>" type="radio" value="yes" class='sendadmin' />Yes</div>
                            <div style="margin-right: 15px;" class="sendtoadminno_<?php echo $val['pk_template_id']?>"><input name="<?php echo $val['pk_template_id']?>" type="radio" value="no" class='sendadmin' />No</td></div>
                         </tr> 
                        <tr> 
                         	<td>Status</td> 
                         	<td>:</td> 
                          	<td><input name="status" type="checkbox" id="<?php echo $this->escapeHtml($val['pk_template_id']);?>"  <?php if($val['is_active'] == 1){ echo 'checked';} ?>  name="check-1" class="lcs_check btn mb0 chkbox" /></td>
                         </tr>   
                        
                    </tbody>
                </table>         
          	</div>
        </div>   
      		
      	</div>
      	
      	<?php $cnt++ ; }?>
 </div>
 
 <script type="text/javascript">

$(document).ready(function(e) {

	$('input[type=checkbox]').lc_switch();

    $(document).delegate('.lcs_switch:not(.lcs_disabled)', 'click tap', function(e) {

		//var status = ($(this).is(':checked')) ? 'checked' : 'unchecked';
		var status = ($(this).parent().find('input').is(':checked')) ? 'checked' : 'unchecked';
		var id = $(this).parent().find('input').attr("id");
 		var Obj = $(this);
		
		if(confirm("Do you really want to change status ?")){
		
			 $.ajax({
				 url:"/emailtemplate/updateemailstatus",
				 type: "POST",
				 data: { status:status,id:id },
				 beforeSend : function(){
					
				 },
				 success:function(result)
				 {
					//console.log(Obj.attr('class'));
					
					 if(result == 1)
					 { 
						 if( Obj.hasClass('lcs_on') ) {
							 
								if( !Obj.hasClass('lcs_radio_switch') ) { // not for radio
									Obj.lcs_on();
								}
							} else {
								Obj.lcs_off();	
						}
							
					 }
					
				 }
				 
			 });
		}
    });
		
  $(document).on('click','.sendadmin',function(){

    var value = $(this).parent().find('input').attr("value");
    var id = $(this).parent().find('input').attr("name");

    if(confirm("Do you really want to send email to admin?")){
      $.ajax({
           url:"/emailtemplate/updatesendtoadmin",
           type: "POST",
           data: { value:value,id:id },
           success:function(result)
           {
            
           }
           
         });
     } 
  });

});

</script>