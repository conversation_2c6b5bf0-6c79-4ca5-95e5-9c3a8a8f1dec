<?php
/**
 * This file manages the kitchen screen on fooddialer system
 * The activity includes add, update and delete kitchen
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <PERSON><PERSON><PERSON> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Session\Container;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;

use Admin\Form\KitchenForm;

class KitchenController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\KitchenMasterTable model
	 *
	 * @var QuickServe\Model\KitchenMasterTable $kitchenmasterTable
	 */
	protected $kitchenmasterTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of product categories
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice) {
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'pk_kitchen_code';
		$order = $this->params()->fromRoute('order')?
				 $this->params()->fromRoute('order'): QSelect::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$returnvar = $this->getKitchenMasterTable()->fetchAll($select->order($order_by . ' ' . $order));
		
		$itemsPerPage = 2;
		
		//$kitchens->current();
		$paginator = new Paginator(new paginatorIterator($returnvar));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Kitchens",'description'=>"Kitchen information",'breadcrumb'=>"Kitchens"));
		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	/**
	 * To add new kitchen screen
	 *
	 * @return \Admin\Form\KitchenForm
	 */
	public function addAction()
	{
        $utility = Utility::getInstance();
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$sm = $this->getServiceLocator();
		//$adapt = $sm->get('Write_Adapter');
		$form = new KitchenForm($sm);
      
		$libCommon = Qscommon::getInstance($sm);
		
		$form->get('submit')->setAttribute('value', 'Add');

		$request = $this->getRequest();
		if ($request->isPost()) {
			$kitchen = new \QuickServe\Model\KitchenMaster();
			$kitchen->getInputFilter()->get('kitchen_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(
				new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'kitchen_master',
					'field'     => 'kitchen_name',
					'adapter'   => $sm->get('Read_Adapter'),
					'message'   => 'Kitchen screen already exists',
				))
			);

			$form->setInputFilter($kitchen->getInputFilter());
			$form->setData($request->getPost());

			if ($form->isValid()) {
				
				$data = $form->getData();
           
				$data['created_by'] = $this->layout()->loggedUser->pk_user_code;
				
				$kitchen->exchangeArray($data);
				$authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
				$kitchen->updated_by = $authservice->pk_user_code;
				
                		if($utility->checkSubscription('multiple_kitchen','allowed')){
                  
				$count = $libCommon->getKitchenCount();

				$subscriptionCheck = $utility->checkSubscription('kitchen_screen','count',$count[0]['count']);

					if(!$subscriptionCheck){

						$errStr = "Maximum no. of kitchen limit has reached";
						goto SHOWDETAILS;
					}

				$data_kitchen = $this->getKitchenMasterTable()->saveKitchen($kitchen);
				}else{
        		            $errStr ="Multiple kitchen not allowed";
        		            goto SHOWDETAILS;
        		        }  
				if($data_kitchen){
                    
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$kitachen_name=$data_kitchen['kitchen_name'];
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'kitchen';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "Kitchen : New Kitchen '$kitachen_name' created.";
		
					$libCommon->saveActivityLog($activity_log_data);
					
					//print_r($_POST);exit;
					$settingsArr = array_intersect_key($_POST, array_flip(preg_grep('/^settings_/', array_keys($_POST))));
					
					
					$lastId = $data_kitchen['last_id'];
//                   
					$created_date = date('Y:m:d H:i:s');
                    
                    $sql = new QSql($sm);
                    $insert= $sql->insert('settings');
					/*For adding into setting table*/
                    if(!empty($kitchen->CUSTOMER_PAYMENT_MODE)){
                        $newData= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'CUSTOMER_PAYMENT_MODE',
                            'value'=>$kitchen->CUSTOMER_PAYMENT_MODE,
                            'created_date'=>$created_date
                        );
                        $insert->values($newData);
                        $result1 = $sql->execQuery($insert);
                    }	
                    
                    $insert2 = $sql->insert('settings');
                    if(!empty($kitchen->MIN_ORDER_PRICE)){
                        $newData2 = array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MIN_ORDER_PRICE',
                            'value'=>$kitchen->MIN_ORDER_PRICE,
                            'created_date'=>$created_date
                        );
                        $insert2->values($newData2);
						$result2 = $sql->execQuery($insert2);
                    }	
                    
                    $insert3= $sql->insert('settings');
                    if(!empty($kitchen->MAX_ORDER_PRICE)){
                        $newData3= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MAX_ORDER_PRICE',
                            'value'=>$kitchen->MAX_ORDER_PRICE,
                            'created_date'=>$created_date
                        );
                        $insert3->values($newData3);
						$result3 = $sql->execQuery($insert3);
                    }
                    
					$insert4= $sql->insert('settings');
                    if(!empty($kitchen->MENU_TYPE)){
                        $newData4= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MENU_TYPE',
                            'value'=>$kitchen->MENU_TYPE,
                            'created_date'=>$created_date
                        );
                        $insert4->values($newData4);
						$result4 = $sql->execQuery($insert4);
                    }	
                    
					$insert5= $sql->insert('settings');
                    if(!empty($kitchen->ORDER_NOTIFICATION_EMAIL)){
                        $newData5= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'ORDER_NOTIFICATION_EMAIL',
                            'value'=>$kitchen->ORDER_NOTIFICATION_EMAIL,
                            'created_date'=>$created_date
                        );	
                        $insert5->values($newData5);
						$result5 = $sql->execQuery($insert5);
                    }
					/**Ends Here**/
					
					foreach($settingsArr as $t=>$r){
						
						$key = strtoupper(explode('settings_',$t)[1]);
						$arrKey = explode('_', $key);	
						if(end($arrKey)=='TIME'){
							$r = date('H:i:s',strtotime($r));
						}
                        
						if(end($arrKey)=='DELIVERY'){
                            $menu = explode('_', $key)[0];
							$r = $_POST[$menu.'_AUTO_DELIVERY_DAY'].','.date('H:i:s',strtotime($r));
						}
                        
                        	
                        if (strpos($key, 'WEEKOFFS') !== false) {
                            $r = implode(',',$r);
                        }
                        
						$key = strtoupper('K'.$lastId).'_'.$key;
						
                        if (strpos($key, 'PICKUPTIME') !== false) {
                            $r = date('H:i:s',strtotime($r));
                        }

                        $insert= $sql->insert('settings');
                        $newData= array(
							'key'=>$key,
							'value'=>$r,
							'created_date'=>$created_date
                        );	
                        $insert->values($newData);
						$result = $sql->execQuery($insert);
					}
					
				}
				
				($data_kitchen) ?$this->flashMessenger()->addSuccessMessage("Kitchen added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Kitchen screen.");

				return $this->redirect()->toRoute('kitchen_master');
			}

		}
		
        $unique = array(
			1=>"Monday",
			2=>"Tuesday",
			3=>"Wednesday",
			4=>"Thursday",
			5=>"Friday",
			6=>"Saturday",
			0=>"Sunday",
        );
        
        SHOWDETAILS:
            
		$this->layout()->setVariables(array('page_title'=>"Add Kitchen Screen",'breadcrumb'=>"Add Kitchen Screen"));
		return array('form' => $form,
					'menu_type' => $setting['MENU_TYPE'],
					'allDays'  => $unique,
                    'errStr'=>$errStr,
					'setting' => $setting
                );

	}

	/**
	 * To update the kitchen screen of given kitchen id
	 *
	 * @return \Admin\Form\KitchenForm
	 */
	public function editAction()
	{
        $utility = Utility::getInstance();
        $id = (int) $this->params('id');
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		
		if (!$id) {
			return $this->redirect()->toRoute('kitchen_master', array('action' => 'add'));
		}

		$kitchen = $this->getKitchenMasterTable()->getKitchen($id);
		/* Ashwini - 9may*/
        $kitchen->location_id = $kitchen->location_id."#".$kitchen->location."#".$kitchen->city_id;
		/* ends */
        $sm = $this->getServiceLocator();
        
		$adapt = $sm->get('Write_Adapter');
        $sql = new QSql($sm);
		$select = new QSelect();
		$select->from( "settings");
        $select->where->like('key', '%'.$id.'%');
        $result3 =$sql->execQuery($select);

        $kitchen_settings = $result3->toArray();
		$layoutviewModel = $this->layout();

        $loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		
		$form = new KitchenForm($sm);
		$form->bind($kitchen);
		
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $kitchen->pk_kitchen_code;

		$request = $this->getRequest();

		if ($request->isPost())
		{
		 
			$kitchen = new \QuickServe\Model\KitchenMaster();
			
			$kitchen->getInputFilter()->get('kitchen_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(
				new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'kitchen_master',
					'field'     => 'kitchen_name',
					'adapter'   => $adapt,
					'message'   => 'Kitchen screen already exists',
					'exclude' => array(
						'field' => 'pk_kitchen_code',
						'value' => $val
					)
				))
			);

			//$kitchen->setAdapter($adapt);
                        
			$form->setInputFilter($kitchen->getInputFilter());
            
			$form->setData($request->getPost());
			 
			if ($form->isValid()) {
				
				$data = (array) $form->getData();
				
				$settingsArr = array_intersect_key($_POST, array_flip(preg_grep('/^settings_/', array_keys($_POST))));

				$kitchen->exchangeArray($data);

                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                $kitchen->updated_by = $authservice->pk_user_code;
               
				$data_kitchen = $this->getKitchenMasterTable()->saveKitchen($kitchen);
				
				if($data_kitchen){

					$settingsArr = array_intersect_key($_POST, array_flip(preg_grep('/^settings_/', array_keys($_POST))));
					
					$lastId = $id;
					
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$kitachen_name=$kitchen->kitchen_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'kitchen';
					$activity_log_data['action']= 'edit';
					$activity_log_data['description']= "Kitchen : Kitchen '$kitachen_name' updated.";
                    
					$libCommon->saveActivityLog($activity_log_data);
					
                    $adapt = $sm->get('Write_Adapter');
                    
					$sql1 = "DELETE FROM settings WHERE `key` LIKE 'K".$id."\_%"."'";
					
                    $created_date = date('Y-m-d H:i:s');
                    
					$result3 = $adapt->query(
							$sql1, $adapt::QUERY_MODE_EXECUTE
					);
					
					/*For adding into setting table*/
					$sql = new QSql($sm);
                    $insert= $sql->insert('settings');
					/*For adding into setting table*/
                    if(!empty($kitchen->CUSTOMER_PAYMENT_MODE)){
                        $newData= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'CUSTOMER_PAYMENT_MODE',
                            'value'=>$kitchen->CUSTOMER_PAYMENT_MODE,
                            'created_date'=>$created_date
                        );

                        $insert->values($newData);
						$result = $sql->execQuery($insert);
                    }	
                    
                    $insert2= $sql->insert('settings');

                    if(!empty($kitchen->MIN_ORDER_PRICE)){
                        $newData2= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MIN_ORDER_PRICE',
                            'value'=>$kitchen->MIN_ORDER_PRICE,
                            'created_date'=>$created_date
                        );
                        $insert2->values($newData2);
						$result2 = $sql->execQuery($insert2);
                    }	
                    
                    $insert3= $sql->insert('settings');
                    if(!empty($kitchen->MAX_ORDER_PRICE)){
                        $newData3= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MAX_ORDER_PRICE',
                            'value'=>$kitchen->MAX_ORDER_PRICE,
                            'created_date'=>$created_date
                        );
                        $insert3->values($newData3);
						$result3 = $sql->execQuery($insert3);
                    }
                    
					$insert4= $sql->insert('settings');
                    if(!empty($kitchen->MENU_TYPE)){
                        $newData4= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'MENU_TYPE',
                            'value'=>$kitchen->MENU_TYPE,
                            'created_date'=>$created_date
                        );
                        $insert4->values($newData4);
						$result4 = $sql->execQuery($insert4);
                    }	
                    
					$insert5= $sql->insert('settings');
                    if(!empty($kitchen->ORDER_NOTIFICATION_EMAIL)){
                        $newData5= array(
                            'key'=>strtoupper('K'.$lastId).'_'.'ORDER_NOTIFICATION_EMAIL',
                            'value'=>$kitchen->ORDER_NOTIFICATION_EMAIL,
                            'created_date'=>$created_date
                        );	
                        $insert5->values($newData5);
						$result5 = $sql->execQuery($insert5);
                    }  
					/**Ends Here**/
					
					foreach($settingsArr as $t=>$r){
						$created_date = date('Y:m:d H:i:s');
						$key = strtoupper(explode('settings_',$t)[1]);
						$arrKey = explode('_', $key);

						if(preg_match("/\_TIME/",$key)){
							$r = date('H:i:s',strtotime($r));
						}
                        
						if(preg_match("/\_DELIVERY/",$key)){
                            $menu = explode('_', $key)[0];
							$r = $_POST[$menu.'_AUTO_DELIVERY_DAY'].','.date('H:i:s',strtotime($r));
						}
						
						if(preg_match("/\_DISPATCH/",$key)){
							$menu = $arrKey[0];
							$r = $_POST[$menu.'_AUTO_DISPATCH_DAY'].','.date('H:i:s',strtotime($r));
						}
                        
						$key = strtoupper('K'.$lastId).'_'.$key;
	
                        if (strpos($key, 'WEEKOFFS') !== false) {
                            $r = implode(',',$r);
                        }
                        
                        if (strpos($key, 'PICKUPTIME') !== false) {
                            $r = date('H:i:s',strtotime($r));
                        }

                        $insert5= $sql->insert('settings');
                        $newData5= array(
                            'key'=>$key,
                            'value'=>$r,
                            'created_date'=>$created_date
                        );	
                        $insert5->values($newData5);
                        $insertString=$sql->getSqlStringForSqlObject($insert5);	
                        $result5 = $adapt->query(
                                $insertString, $adapt::QUERY_MODE_EXECUTE
                        );
                       
					}
					
				}
				$this->flashMessenger()->addSuccessMessage("Kitchen screen updated successfully");
				// Redirect to list of albums
				return $this->redirect()->toRoute('kitchen_master');
			}
		}
                
        $unique=array(
            1=>"Monday",
            2=>"Tuesday",
            3=>"Wednesday",
            4=>"Thursday",
            5=>"Friday",
            6=>"Saturday",
            0=>"Sunday",
        );

        $this->layout()->setVariables(array('page_title'=>"Edit Kitchen Screen",'breadcrumb'=>"Edit Kitchen Screen"));

        return array(
            'id' => $id,
            'form' => $form,
            'kitchen' => $kitchen,
            'errStr'=>$errStr,
            'menu_type' => $setting['MENU_TYPE'],
            'kitchen_settings' => json_encode($kitchen_settings),
            'allDays'  => $unique,
            'setting' => $setting

        );
	}
	/**
	 * To delete the kitchen of given kitchen id
	 *
	 * @param int id
	 * @return route kitchen master
	 */
	public function deleteAction() {
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('kitchen_master');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		
		$data_productCategory=$this->getKitchenMasterTable()->deleteKitchen($id);
		
		if($data_productCategory) {
			
			$kitchen = $this->getKitchenMasterTable()->getKitchen($id);
			
			if( $kitchen->status == '1' ) {
				$this->flashMessenger()->addSuccessMessage('Kitchen screen "'.($kitchen->kitchen_name).'" is activated successfully');
				
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$kitachen_name=$kitchen->kitchen_name;
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'kitchen';
				$activity_log_data['action']= 'Activate';
				$activity_log_data['description']= "Kitchen : Kitchen '$kitachen_name' activated.";
				
				//$activity_log_data['description']= "'$kitachen_name' kitchen screen updated by $full_name";
				$libCommon->saveActivityLog($activity_log_data);
				
			}
			else {
				$this->flashMessenger()->addInfoMessage('Kitchen screen "'.($kitchen->kitchen_name).'" has been deactivated successfully');
				
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$kitachen_name=$kitchen->kitchen_name;
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'kitchen';
				$activity_log_data['action']= 'Deactivate';
				$activity_log_data['description']= "Kitchen : Kitchen '$kitachen_name' deactivated.";
				
				//$activity_log_data['description']= "'$kitachen_name' kitchen screen updated by $full_name";
				$libCommon->saveActivityLog($activity_log_data);
			}
		}
		else {
			$this->flashMessenger()->addErrorMessage("Error updating kitchen screen.");
		}
		return $this->redirect()->toRoute('kitchen_master');
	}
	/**
	 * Get instance of QuickServe\Model\KitchenMasterTable
	 *
	 * @return QuickServe\Model\KitchenMasterTable
	 */
	public function getKitchenMasterTable()
	{
		if (!$this->kitchenmasterTable) {
			$sm = $this->getServiceLocator();
			$this->kitchenmasterTable = $sm->get('QuickServe\Model\KitchenMasterTable');
		}
		return $this->kitchenmasterTable;
	}
}
