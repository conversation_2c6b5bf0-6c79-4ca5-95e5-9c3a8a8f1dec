import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Deactivate } from '@/components/customer-service-v12/[id]/deactivate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Deactivate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Deactivate />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Deactivate')).toBeInTheDocument();
  });
});