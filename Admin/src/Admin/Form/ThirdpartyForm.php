<?php
/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;
use Lib\Utility;

use Lib\QuickServe\Db\Sql\QSql;

class ThirdpartyForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->adapter = $adapter;
        $this->service_locator = $sm;
        $utility = Utility::getInstance ();
		$setting_session = new Container( "setting" );
		$setting = $setting_session->setting;
		$currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);        

        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'third_party_id',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'third_party_id',
        		),
        		'options' => array(
      		),
        ));

        $this->add(array(
            'name' => 'name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            		'class'=> 'smallinput',
                'id' => 'name',
                'placeholder' => 'Enter Name',
            ),
            'options' => array(
                'label' => 'Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'phone',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'phone',
            	'placeholder' => 'Enter Phone',
            ),
            'options' => array(
            		'label' => 'Phone<span class="red">*</span',
            		'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'email',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'email',
                'placeholder' => 'Enter Email Address',
            ),
            'options' => array(
                'label' => 'Email Id<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        $this->add(array(
        		'name' => 'comission_rate',
        		'type' => 'Zend\Form\Element\Text',
        		'required' => false,
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'comission_rate',
        				'placeholder' => 'Enter Commission Rate',
        		),
        		'options' => array(
        				'label' => 'Commission Rate ('.$currencySymbol.')<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name'=>'city',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'id'=>'city',
        				'class'=>'selectpicker small',
        				//'required' => 'required',
        		),
        		'options'=>array(
        				'label'=>'City<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        				'value_options' => $this->getCities()
        		)
        ));
        
        $this->add(array(
        		'name' => 'location',
        		'type' => 'Zend\Form\Element\Select',
        		'required' => false,
        		'attributes' => array(
        				'class'=> 'smallinput form-control locationcodecopycls locationcodes',
        				'id' => 'location',
        				'placeholder' => 'Enter Location',
        		),
        		'options' => array(
        				'label' => 'Location<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
                                        'value_options' => $this->getLocation() 
        		),
        ));
        
        $this->add(array(
        		'name' => 'commission_type',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'commission_type',
        		),
        		'options' => array(
                                'label' => 'Commission Type',
                                'label_options' => array('disable_html_escape' => true),
                                'value_options' => array(
                                                'fixed' => 'Fixed',
                                                'percentage' => 'Percentage',
                                ),
        		)
        ));
        
        $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
				),
		));
        
        
        $this->add(array(
        		'name' => 'create_account',
        		'type' => 'Zend\Form\Element\Checkbox',
                        'attributes'=>array(
       				'id' => 'create_account',
                                 'class' => 'create_account',
                        ),
        		'options' => array(
        				'label' => 'Create Account',
        				'checked_value' => 1,
        				'unchecked_value' => 0
        		),
                        'attributes'    => array(
//                            'disabled' => 'disabled'
                            )
        ));
        
        $this->add(array(
       		'name'=>'thirdparty_type',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'id' => 'thirdparty_type',
       		),
       		'options'=>array(
       				'label'=>'Third Party Type',
       				
       				'value_options' => $this->getTPRoles(),
       				'label_options' => array('disable_html_escape' => true),
                                'label_attributes' => array(
                                    'class'  => 'left mr10'
                                ),
       		),
                'attributes' => [
                    'value' => current(array_keys($this->getTPRoles()))
                ],
        ));
        
        $this->add(array(
            'name'=>'thirdparty_system',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                            'id' => 'thirdparty_system',
            ),
            'options'=>array(
                            'label'=>'Third Party System',

                            'value_options' => $this->thirdPartySystem(),
                            'label_options' => array('disable_html_escape' => true),
                            'label_attributes' => array(
                                       'class'  => 'left mr10'
                                   ),
            ),
            'attributes' => [
                'value' => end(array_keys($this->thirdPartySystem()))
            ],
       ));
        
        $this->add(array(
       		'name'=>'charges_type',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'id' => 'charges_type',
       		),
       		'options'=>array(
       				'label'=>'Charges Type',
       				
       				'value_options' => array('inclusive' => 'inclusive', 'exclusive'=> 'exclusive'),
       				'label_options' => array('disable_html_escape' => true),
                                'label_attributes' => array(
                                       'class'  => 'left mr10'
                                   ),
       		),
                'attributes' => [
                    'value' => 'inclusive'
                ],
       ));
        
        $this->add(array(
       		'name' => 'password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'id' => 'password',
       				'placeholder' => 'Password Here...',
       		),
       		'options' => array(
       				'label' => 'Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		),                
       ));

       $this->add(array(
       		'name' => 'confirm_password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'id' => 'confirm_password',
       				'placeholder' => 'Confirm Password Here...',
       				//'required' => 'required',
       		),
       		'options' => array(
       				'label' => 'Confirm Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		)
       ));
       
       $this->add(array(
        		'name' => 'address',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'class' => 'form-control clstextareaaddress locationaddrcopycls',
        				'placeholder' => 'Address Here...',
        		),
        		'options' => array(
        				'label' => 'Address<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Go',
        				'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/thirdparty',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
    }
    
//    public function getCities()
//    {
//    	$sql = "SELECT pk_city_id,city FROM `city` WHERE status=1";
//    	//echo $sql;exit;
//    	$vendors = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
//    	$returnData = array();
//    	//$returnData['default'] = "Select any category";
//    	foreach($vendors->toArray() as $vendor){
//    		$returnData[$vendor['pk_city_id']] = $vendor['city'];
//    	}
//    	return $returnData;
//    }
    
    public function getCities()
    {
        $sql = new QSql($this->service_locator);
        $select = $sql->select();
        $select->from('city');
        $select->where('status',1);
        //$statement = $sql->prepareStatementForSqlObject($select);
        //$results = $statement->execute();
        
        $results = $sql->execQuery($select);
        $selectData[''] = "Select Your City";
        foreach ($results as $res) {
                $selectData[$res['pk_city_id']] = $res['city'];
        }

        return $selectData;

    }
    
    public function getLocation()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('delivery_locations');
    	$select->where('status',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
    	$selectData[""]="Select Your Location";
    	foreach ($results as $res) {
    		$selectData[$res['pk_location_code']] = $res['location'];
    	}
        
    	return $selectData;

    }
    
    public function getTPRoles()
    {
       $sql = "SELECT role_name,pk_role_id FROM `roles` WHERE role_name in('Third-Party Delivery', 'Third-Party Aggregator')";
    	//echo $sql;exit;
    	//$vendors = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
    	//$returnData = array();
    	//$returnData['default'] = "Select any category";
       
        $vendors = $sql->execQuery($select);
    	foreach($vendors->toArray() as $vendor){
    		$returnData[$vendor['pk_role_id']] = $vendor['role_name'];
    	}
        
    	return $returnData; 
    }
    
    public function thirdPartySystem(){
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
        
        $third_party_system = explode(',',$setting['GLOBAL_THIRDPARTY_DELIVERY']);
        $third_party_system = array_combine($third_party_system, $third_party_system);
        $third_party_system['other'] = 'other';
        
        return $third_party_system;
    }
    
}