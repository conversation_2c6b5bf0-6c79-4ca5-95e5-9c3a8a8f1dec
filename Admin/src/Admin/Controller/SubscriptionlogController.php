<?php
/**
 * This file manages the kitchen screen on fooddialer system
 * The activity includes add, update and delete kitchen
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Session\Container;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;

class SubscriptionlogController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\SubscriptionlogTable model
	 *
	 * @var QuickServe\Model\KitchenMasterTable $kitchenmasterTable
	 */
	protected $subscriptionlogtable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of product categories
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		
	/* 	getcurrentmonthcount($date) */
		$date = $this->params('date','');
	
		$cusrrentmnthdata = $this->getSubscriptionlogTable()->getcurrentmonthcount($date);
		
		$this->layout()->setVariables(array('page_title'=>"Subscribe",'description'=>"Subscription Log",'breadcrumb'=>"Subscribe"));
		
		return new ViewModel(array( 
			'data' =>	$cusrrentmnthdata,
			'date' => $date
				
		));
	}
	
	public function ajxCustomerAction()
	{
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;
		
		$viewModel = new ViewModel();
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'id','1'=>'total_orders','2'=>'sms_sent','3'=>'email_sent',"4"=>"active_customer","5"=>"admin_account","6"=>"user_account","7"=>"kitchen_count","8"=>"date");
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		 
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		 
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		 
		$status = $this->params()->fromQuery('status');
		 
		$columns = $this->params()->fromQuery('columns');
		
		$select->where(
		
				new \Zend\Db\Sql\Predicate\PredicateSet(
						array(
								new \Zend\Db\Sql\Predicate\Operator('id', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('total_orders', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('sms_sent', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('email_sent', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('active_customer', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('admin_account', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('user_account', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('kitchen_count', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('date', 'LIKE', '%'.$search.'%'),
									
						),
						// optional; OP_AND is default
						\Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
		
		);
		
		$select->order($order_by . ' ' . $order);
		
		$subscriptionlog = $this->getSubscriptionlogTable()->fetchAll($select,$page);
		
		//echo "<pre>";print_r($subscriptionlog);die;
		$subscriptionlog->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $subscriptionlog->getTotalItemCount();
		$returnVar['recordsFiltered'] = $subscriptionlog->getTotalItemCount();
		$returnVar['data'] = array();
		
		
		
		foreach($subscriptionlog as $subscriptionlogdata){
			$arrTmp = array();
			
			$date = date('M-Y', strtotime($subscriptionlogdata['date']));
			array_push($arrTmp,$date);
			array_push($arrTmp,$subscriptionlogdata['total_orders']);
			array_push($arrTmp,$subscriptionlogdata['sms_sent']);
			array_push($arrTmp,$subscriptionlogdata['email_sent']);
			array_push($arrTmp,$subscriptionlogdata['active_customer']);
			array_push($arrTmp,$subscriptionlogdata['admin_account']);
			array_push($arrTmp,$subscriptionlogdata['user_account']);
			array_push($arrTmp,$subscriptionlogdata['kitchen_count']);
			
			$str ='<button class="smBtn blueBg" data-tooltip="" onClick="location.href=\''.$this->url()->fromRoute('subscriptionlog', array('action'=>'index', 'date' => $subscriptionlogdata['date'])).'\'"  data-selector="tooltip-ihxedocq3" title="view"><i class="fa fa-eye"></i></button>';
			//echo $str; exit();
			array_push($arrTmp,$str);
			
            array_push($returnVar['data'],$arrTmp);
		}
		
		
		//echo "<pre>";print_r($returnVar);die;
		return new JsonModel($returnVar);
		
	}
    /**
     * Get instance of QuickServe\Model\getSubscriptionlogTable
     * 
     * @return QuickServe\Model\getSubscriptionlogTable
     * 
     */
	public function getSubscriptionlogTable()
	{
		if(!$this->subscriptionlogtable)
		{
			$sm = $this->getServiceLocator();
			$this->subscriptionlogtable = $sm->get('QuickServe\Model\SubscriptionlogTable');
		}
		return $this->subscriptionlogtable;
	}
	
}