<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\CommonConfig as QSCommon;

class SystemSettingForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	
	private $service_locator;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('setting');
        $adapt = $sm->get('Write_Adapter');
        $this->adapter = $adapt;
        $this->service_locator = $sm;

        $this->setAttribute('method', 'post');
        
        $this->add(array(
        		'name' => 'id',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'id',
        		),
        		'options' => array(
        				'label' => 'undefined',
        		),
        ));
        
       $this->add(array(
        		'name'=>'TIME_ZONE',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'class'=>'small',
        		),
        		'options'=>array(
        				'label'=>'Time Zone<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right',
        				),
        				'value_options' => $this->getTimeZones(),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
       
       
       // menu type
       
       /*$this->add(array(
       		'name' => 'MENU_TYPE',
       		'type' => 'Zend\Form\Element\MultiCheckbox',
       		'options' => array(
       				'label' => 'Menu Type<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'attributes' => array(
       						'id' => 'MENU_TYPE',
       				),
       				'value_options' => array(
       						array(
       								'value' => 'breakfast',
       								'label' => 'Breakfast',
       								'attributes' => array(
       										'class'=>"cashcheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'lunch',
       								'label' => 'Lunch',
       								'attributes' => array(
       										'class'=>"neftcheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'dinner',
       								'label' => 'Dinner',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'snacks',
       								'label' => 'Snacks',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'fastfood',
       								'label' => 'Fastfood',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       				        array(
       								'value' => 'instantorder',
       								'label' => 'Instant Order',
       								'attributes' => array(
       										'class'=>"chequecheck" ,
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		),
       ));


       $this->add(array(
       		'name' => 'MENU_TYPE',
       		'type' => 'Zend\Form\Element\Text',
       		'options' => array(
				'label' => 'Menu Type<span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),
				'attributes' => array(
					'id' => 'MENU_TYPE',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		),
       ));*/


       $this->add(array(
       		'name'=>'MENU_TYPE',
       		'type' => 'Zend\Form\Element\Text',
       		'attributes'=>array(
				'class'=>'small',
				'placeholder' => 'Enter Menu Type',
				'id' => 'MENU_TYPE',
       		),
       		'options'=>array(
				'label'=>'Menu Type<span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),
				'label_options' => array('disable_html_escape' => true),
       		)
       ));

       
       $this->add(array(
       		'name'=>'GLOBAL_APPLY_TAX',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'GLOBAL_APPLY_TAX',
       		),
       		'options'=>array(
				'label'=>'Apply tax<span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline right  mr5',
				),
				'value_options' => array(
					'yes' => 'Yes',
					'no' => 'No',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		)
       ));
       
       $this->add(array(
       		'name'=>'ENABLE_AUTO_DELIVERY',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'ENABLE_AUTO_DELIVERY',
       		),
       		'options'=>array(
				'label'=>'Auto Delivery <span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline right mr5',
				),
				'value_options' => array(
					'yes' => 'Yes',
					'no' => 'No',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		)
       ));
       
       $this->add(array(
       		'name'=>'ENABLE_AUTO_DISPATCH',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'ENABLE_AUTO_DISPATCH',
       		),
       		'options'=>array(
       				'label'=>'Auto Dispatch <span class="red">*</span>',
       				'label_attributes' => array(
       					'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       					'yes' => 'Yes',
       					'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));

       $this->add(array(
          'name'=>'GLOBAL_ENABLE_RECURRING_ORDER',
          'type' => 'Zend\Form\Element\Radio',
          'attributes'=>array(
              'class'=>'selectpicker small',
              'id' => 'GLOBAL_ENABLE_RECURRING_ORDER',
          ),
          'options'=>array(
              'label'=>'Enable Recurring Order <span class="red">*</span>',
              'label_attributes' => array(
                'class' => 'inline right  mr5',
              ),
              'value_options' => array(
                'yes' => 'Yes',
                'no' => 'No',
              ),
              'label_options' => array('disable_html_escape' => true),
              'disable_inarray_validator' => true,
          )
       ));       
       
       
       $this->add(array(
       		'name'=>'WEBSITE_MAINTENANCE',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'WEBSITE_MAINTENANCE',
       		),
       		'options'=>array(
       				'label'=>'Website Maintenance<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));
       
       
       $this->add(array(
       		'name'=>'GLOBAL_TAX_METHOD',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes'=>array(
       				'class'=>'small timePicker',
       				'id' =>'GLOBAL_TAX_METHOD',
       				//	  'multiple' => 'multiple',
       		),
       		'options'=>array(
       				'label'=>'Tax method<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'value_options' => array(
       						'inclusive' => 'Inclusive',
       						'exclusive' => 'Exclusive',
       				),
       				'label_options' => array('disable_html_escape' => true),
       
       		)
       ));
       
       
       
       $this->add(array(
       		'name'=>'GLOBAL_APPLY_DELIVERY_CHARGES',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_APPLY_DELIVERY_CHARGES',
       		),
       		'options'=>array(
       				'label'=>'Apply delivery charges<spaSn class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));
       
       $this->add(array(
       		'name'=>'APPLY_DELIVERY_CHARGES',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes'=>array(
       				'class'=>'small timePicker',
       				'id' =>'APPLY_DELIVERY_CHARGES',
       				//	  'multiple' => 'multiple',
       		),
       		'options'=>array(
       				'label'=>'Delivery charges calculations<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'value_options' => array(
       						'mealwise' => 'Mealwise',
       						'orderwise' => 'Orderwise',
       				),
       				'label_options' => array('disable_html_escape' => true),
       
       		)
       ));
       
       $this->add(array(
       		'name'=>'GLOBAL_ALLOW_SMS_QUOTA_EXCEED',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_ALLOW_SMS_QUOTA_EXCEED',
       		),
       		'options'=>array(
       				'label'=>'Allow SMS quota to exceed<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));
       
       $this->add(array(
       		'name'=>'PHONE_VERIFICATION_METHOD',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes'=>array(
       				'class'=>'small timePicker',
       				'id' =>'PHONE_VERIFICATION_METHOD',
       				//	  'multiple' => 'multiple',
       		),
       		'options'=>array(
       				'label'=>'Customer phone verification method<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'value_options' => array(
       						'otp' => 'OTP',
       						'dail2verify' => 'Dail2verify',
       				),
       				'label_options' => array('disable_html_escape' => true),
       
       		)
       ));
       
       
       
       $this->add(array(
       		'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'SHOW_PRODUCT_AND_MEAL_CALENDAR',
       		),
       		'options'=>array(
       				'label'=>'Show product meal calender<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right mr5',
       				),
       				'value_options' => array(
       						1 => 'Yes',
       						0 => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));
       
      
       
       $this->add(array(
       		'name'=>'GLOBAL_SHOW_CATALOG_VIEW',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_SHOW_CATALOG_VIEW',
       		),
       		'options'=>array(
       				'label'=>'Show catalog view<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right mr5',
       				),
       				'value_options' => array(
       						'tiffin' => 'Tiffin',
       						'restaurant' => 'Restaurant',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));
       
       
       
       $this->add(array(
       		'name'=>'GLOBAL_CATALOG_CART_PLAN',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes'=>array(
       				'class'=>'small timePicker',
       				'id' =>'GLOBAL_CATALOG_CART_PLAN',
       				//	  'multiple' => 'multiple',
       		),
       		'options'=>array(
       				'label'=>'Catalog cart plan<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'value_options' => array(
       						'all' => 'All',
       						'period' => 'Period',
       						'datewise' => 'Datewise',
       						'perday' => 'Per day',
       				),
       				'label_options' => array('disable_html_escape' => true),
       
       		)
       ));
       
       $this->add(array(
       		'name'=>'S3_BUCKET_URL',
       		'type' => 'Zend\Form\Element\Text',
       		'attributes'=>array(
       				'class'=>'small timePicker',
       				'placeholder' => 'S3 Bucket URL',
       				'id' => 'S3_BUCKET_URL',
       		),
       		'options'=>array(
       				'label'=>'S3 Bucket URL<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'label_options' => array('disable_html_escape' => true),
       		)
       ));
       
       $this->add(array(
        		'name'=>'SMTP_FROM_NAME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'From Name',
        				'id' => 'SMTP_FROM_NAME',
        		),
        		'options'=>array(
        				'label'=>'From name<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'SMTP_FROM_EMAIL',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'From email',
        				'id' => 'SMTP_FROM_EMAIL',
        		),
        		'options'=>array(
        				'label'=>'From email<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'SMTP_HOST',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'SMTP hosts',
        				'id' => 'SMTP_HOST',
        		),
        		'options'=>array(
        				'label'=>'SMTP hosts<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'SMTP_PORT',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'SMTP port',
        				'id' => 'SMTP_PORT',
        		),
        		'options'=>array(
        				'label'=>'SMTP port<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'SMTP_USERNAME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'SMTP username',
        				'id' => 'SMTP_USERNAME',
        		),
        		'options'=>array(
        				'label'=>'SMTP username<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'SMTP_PASSWORD',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
    				'class'=>'small timePicker',
    				'placeholder' => 'SMTP password',
    				'id' => 'SMTP_PASSWORD',
        		),
        		'options'=>array(
    				'label'=>'SMTP password<span class="red">*</span>',
    				'label_attributes' => array(
    						'class'  => 'inline right'
    				),
    				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        
        $this->add(array(
                'name'=>'GLOBAL_PAYMENT_ENV',
                'type' => 'Zend\Form\Element\Text',
                'attributes'=>array(
                    'class'=>'small timePicker',
                    'placeholder' => 'GLOBAL PAYMENT ENV',
                    'id' => 'GLOBAL_PAYMENT_ENV',
                ),
                'options'=>array(
                    'label'=>'Global Payment Environment<span class="red">*</span>',
                    'label_attributes' => array(
                        'class'  => 'inline right'
                    ),
                    'label_options' => array('disable_html_escape' => true),
                )
        ));
        
        /*
         * payment changed from radio to checkbox
         */
        
        $this->add(array(
       		'name' => 'ONLINE_PAYMENT_GATEWAY',
       		'type' => 'Zend\Form\Element\MultiCheckbox',
       		'options' => array(
				'label' => 'Online payment gateway<span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),
				'attributes' => array(
					'id' => 'ONLINE_PAYMENT_GATEWAY',
				),
				'value_options' => array(
					array(
						'value' => 'payu',
						'label' => 'PayU',
						'attributes' => array(
							'id' => 'payu-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
					array(
						'value' => 'instamojo',
						'label' => 'Instamojo',
						'attributes' => array(
							'id' => 'instamojo-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
					array(
						'value' => 'paytm',
						'label' => 'Paytm',
						'attributes' => array(
							'id' => 'paytm-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
					array(
						'value' => 'payeezy',
						'label' => 'Payeezy',
						'attributes' => array(
							'id' => 'payeezy-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
					array(
						'value' => 'mobikwik',
						'label' => 'Mobikwik',
						'attributes' => array(
							'id' => 'mobikwik-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
                    array(
                      'value' => 'paypal',
                      'label' => 'Paypal',
                      'attributes' => array(
                        'id' => 'paypal-gateway',
                        'data-close'=>".onlinerow",
                        'class'=>'gateway'
                      ),
                      'label_attributes' => array(
                        'class'=>"left mr5"
                      ),
                    ),  
                    array(
						'value' => 'converge',
						'label' => 'converge',
						'attributes' => array(
							'id' => 'converge-gateway',
							'data-close'=>".onlinerow",
							'class'=>'gateway'
						),
						'label_attributes' => array(
							'class'=>"left mr5"
						),
					),
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		),
       ));

		    $this->add(array(
        		'name'=>'APPLY_GATEWAY_TRANSACTION_CHARGES',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        				'id' => 'APPLY_GATEWAY_TRANSACTION_CHARGES',
        		),
        		'options'=>array(
        				'label'=>'Apply gatway transaction charges<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right mr5',
        				),
        				'value_options' => array(
        						'yes' => 'Yes',
        						'no' => 'No',
        				),
        				'label_options' => array('disable_html_escape' => true),
        				'disable_inarray_validator' => true,
        		)
        ));
        
    		$this->add(array(
    				'name'=>'GATEWAY_TRANSACTION_CHARGES_AMOUNT',
    				'type' => 'Zend\Form\Element\Text',
    				'attributes'=>array(
    						'class'=>'small timePicker',
    						'placeholder' => 'Gatway transaction charges amount',
    						'id' => 'GATEWAY_TRANSACTION_CHARGES_AMOUNT',
    				),
    				'options'=>array(
    						'label'=>'Gatway transaction charges amount<span class="red">*</span>',
    						'label_attributes' => array(
    								'class'  => 'inline right'
    						),
    						'label_options' => array('disable_html_escape' => true),
    				)
    		));
        
        $this->add(array(
        		'name'=>'THIRD_PARTY_CHARGES',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        				'id' => 'THIRD_PARTY_CHARGES',
        		),
        		'options'=>array(
        				'label'=>'Third party charges<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right mr5',
        				),
        				'value_options' => array(
        						'inclusive' => 'Inclusive',
        						'exclusive' => 'Exclusive',
        				),
        				'label_options' => array('disable_html_escape' => true),
        				'disable_inarray_validator' => true,
        		)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_PAYU_MERCHANT_ID',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'PAYU MERCHANT ID',
						'id' => 'GATEWAY_PAYU_MERCHANT_ID',
				),
				'options'=>array(
						'label'=>'PayU Merchant Id<span class="red">*</span>',
						'label_attributes' => array(
								'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));

        $this->add(array(
				'name'=>'GATEWAY_PAYU_MERCHANT_KEY',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'PAYU MERCHANT KEY',
						'id' => 'GATEWAY_PAYU_MERCHANT_KEY',
				),
				'options'=>array(
						'label'=>'PayU Merchant Key<span class="red">*</span>',
						'label_attributes' => array(
								'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));
                        
        $this->add(array(
				'name'=>'GATEWAY_PAYU_MERCHANT_SALT',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'PAYU MERCHANT SALT',
						'id' => 'GATEWAY_PAYU_MERCHANT_SALT',
				),
				'options'=>array(
						'label'=>'PayU Merchant Salt<span class="red">*</span>',
						'label_attributes' => array(
								'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_INSTAMOJO_MERCHANT_KEY',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'INSTAMOJO MERCHANT KEY',
						'id' => 'GATEWAY_INSTAMOJO_MERCHANT_KEY',
				),
				'options'=>array(
						'label'=>'Instamojo Merchant Key<span class="red">*</span>',
						'label_attributes' => array(
								'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_INSTAMOJO_MERCHANT_TOKEN',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'INSTAMOJO MERCHANT TOKEN',
						'id' => 'GATEWAY_INSTAMOJO_MERCHANT_TOKEN',
				),
				'options'=>array(
						'label'=>'Instamojo Merchant Token<span class="red">*</span>',
						'label_attributes' => array(
								'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));
        /////////////////////// PAYTM ///////////////////////////////
        
        $this->add(array(
				'name'=>'GATEWAY_PAYTM_MERCHANT_MID',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
					'class'=>'small',
					'placeholder' => 'PAYTM MERCHANT ID',
					'id' => 'GATEWAY_PAYTM_MERCHANT_MID',
				),
				'options'=>array(
					'label'=>'Paytm Merchant ID<span class="red">*</span>',
					'label_attributes' => array(
							'class'  => 'inline right'
					),
					'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_PAYTM_MERCHANT_KEY',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
					'class'=>'small',
					'placeholder' => 'PAYTM MERCHANT KEY',
					'id' => 'GATEWAY_PAYTM_MERCHANT_KEY',
				),
				'options'=>array(
					'label'=>'Paytm Merchant Key<span class="red">*</span>',
					'label_attributes' => array(
						'class'  => 'inline right'
					),
					'label_options' => array('disable_html_escape' => true),
				)
        ));

        $this->add(array(
				'name'=>'GATEWAY_PAYTM_MERCHANT_INDUSTRY',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
					'class'=>'small timePicker',
					'placeholder' => 'PAYTM MERCHANT INDUSTRY ID',
					'id' => 'GATEWAY_PAYTM_MERCHANT_INDUSTRY',
				),
				'options'=>array(
					'label'=>'Paytm Merchant Industry ID <span class="red">*</span>',
					'label_attributes' => array(
							'class'  => 'inline right'
					),
					'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_PAYTM_MERCHANT_CHANNEL',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
					'class'=>'small timePicker',
					'placeholder' => 'PAYTM MERCHANT CHANNEL ID',
					'id' => 'GATEWAY_PAYTM_MERCHANT_CHANNEL',
				),
				'options'=>array(
					'label'=>'Paytm Merchant Channel ID<span class="red">*</span>',
					'label_attributes' => array(
							'class'  => 'inline right'
					),
					'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        $this->add(array(
				'name'=>'GATEWAY_PAYTM_MERCHANT_WEBSITE',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'small timePicker',
						'placeholder' => 'PAYTM MERCHANT WEBSITE',
						'id' => 'GATEWAY_PAYTM_MERCHANT_WEBSITE',
				),
				'options'=>array(
						'label'=>'Paytm Merchant Website<span class="red">*</span>',
						'label_attributes' => array(
							'class'  => 'inline right'
						),
						'label_options' => array('disable_html_escape' => true),
				)
        ));
        
        /////////////////////////////////////////////////////////

        /////////////////////// PAYEEZY ///////////////////////////////
        
        $this->add(array(
          'name'=>'PAYEEZY_HCO_LOGIN',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'PAYEEZY LOGIN ID',
            'id' => 'PAYEEZY_HCO_LOGIN',
          ),
          'options'=>array(
            'label'=>'Payeezy Login ID<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));
        
        $this->add(array(
          'name'=>'PAYEEZY_HCO_TRANSACTION_KEY',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'PAYEEZY TRANSACTION KEY',
            'id' => 'PAYEEZY_HCO_TRANSACTION_KEY',
          ),
          'options'=>array(
            'label'=>'Payeezy Transaction Key<span class="red">*</span>',
            'label_attributes' => array(
              'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));

        $this->add(array(
          'name'=>'GATEWAY_PAYEEZY_ID',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'PAYEEZY GATEWAY ID',
            'id' => 'GATEWAY_PAYEEZY_ID',
          ),
          'options'=>array(
            'label'=>'Payeezy Gateway ID <span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));
        
        $this->add(array(
          'name'=>'GATEWAY_PAYEEZY_KEY',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'PAYEEZY GATEWAY KEY',
            'id' => 'GATEWAY_PAYEEZY_KEY',
          ),
          'options'=>array(
            'label'=>'Payeezy Gateway Key<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));
        
        $this->add(array(
          'name'=>'GATEWAY_PAYEEZY_SECRET',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'PAYEEZY GATEWAY SECRET',
            'id' => 'GATEWAY_PAYEEZY_SECRET',
          ),
          'options'=>array(
            'label'=>'Payeezy Gateway Secret<span class="red">*</span>',
            'label_attributes' => array(
            'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));

        $this->add(array(
          'name'=>'GATEWAY_PAYEEZY_HMAC_KEY',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'PAYEEZY GATEWAY HMAC KEY',
            'id' => 'GATEWAY_PAYEEZY_HMAC_KEY',
          ),
          'options'=>array(
            'label'=>'Payeezy Gateway Hmac Key<span class="red">*</span>',
            'label_attributes' => array(
            'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));

        ///////////////////////////////////////////////////////////////////////////

        /////////////////////////Paypal////////////////////////////////////////////

        $this->add(array(
          'name'=>'GATEWAY_PAYPAL_USER',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'PAYPAL LOGIN ID',
            'id' => 'GATEWAY_PAYPAL_USER',
          ),
          'options'=>array(
            'label'=>'Paypal Login ID<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));    

        $this->add(array(
          'name'=>'GATEWAY_PAYPAL_SECRET',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'PAYPAL SECRET',
            'id' => 'GATEWAY_PAYPAL_SECRET',
          ),
          'options'=>array(
            'label'=>'Paypal Secret<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        )); 

        $this->add(array(
          'name'=>'GATEWAY_PAYPAL_SIGNATURE',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'PAYPAL SIGNATURE',
            'id' => 'GATEWAY_PAYPAL_SIGNATURE',
          ),
          'options'=>array(
            'label'=>'Paypal Signature<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));                     

        ///////////////////////////////////////////////////////////////////////////

        $this->add(array(
          'name'=>'GATEWAY_MOBIKWIK_MERCHANT_NAME',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'GATEWAY MOBIKWIK MERCHANT NAME',
            'id' => 'GATEWAY_MOBIKWIK_MERCHANT_NAME',
          ),
          'options'=>array(
            'label'=>'Mobikwik Merchant Name <span class="red">*</span>',
            'label_attributes' => array(
            'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));

        $this->add(array(
          'name'=>'GATEWAY_MOBIKWIK_MERCHANT_ID',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'GATEWAY MOBIKWIK MERCHANT ID',
            'id' => 'GATEWAY_MOBIKWIK_MERCHANT_ID',
          ),
          'options'=>array(
            'label'=>'Mobikwik Merchant ID<span class="red">*</span>',
            'label_attributes' => array(
            'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));

        $this->add(array(
          'name'=>'GATEWAY_MOBIKWIK_MERCHANT_KEY',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small timePicker',
            'placeholder' => 'GATEWAY MOBIKWIK MERCHANT KEY',
            'id' => 'GATEWAY_MOBIKWIK_MERCHANT_KEY',
          ),
          'options'=>array(
            'label'=>'Mobikwik Secret Key<span class="red">*</span>',
            'label_attributes' => array(
            'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));
        
        ///////////////////////Converge Payment///////////////////////////////////
        
        $this->add(array(
          'name'=>'GATEWAY_CONVERGE_MERCHANT_ID',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'CONVERGE_MERCHANT_ID',
            'id' => 'GATEWAY_CONVERGE_MERCHANT_ID',
          ),
          'options'=>array(
            'label'=>'Converge Merchant ID<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));    

        $this->add(array(
          'name'=>'GATEWAY_CONVERGE_USER_ID',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'CONVERGE_USER_ID',
            'id' => 'GATEWAY_CONVERGE_USER_ID',
          ),
          'options'=>array(
            'label'=>'Converge User Id<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        )); 

        $this->add(array(
          'name'=>'GATEWAY_CONVERGE_PIN',
          'type' => 'Zend\Form\Element\Text',
          'attributes'=>array(
            'class'=>'small',
            'placeholder' => 'CONVERGE_PIN',
            'id' => 'GATEWAY_CONVERGE_PIN',
          ),
          'options'=>array(
            'label'=>'Converge Pin<span class="red">*</span>',
            'label_attributes' => array(
                'class'  => 'inline right'
            ),
            'label_options' => array('disable_html_escape' => true),
          )
        ));   

        /////////////////////////////////////////////////////////        
        
        $this->add(array(
        		'name'=>'CATALOGUE_MOBILE_APP_PLAYSTORE_URL',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => '',
        				'id' => 'CATALOGUE_MOBILE_APP_PLAYSTORE_URL',
        		),
        		'options'=>array(
        				'label'=>'Catalogue Mobile App Playstore Url',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'CATALOGUE_MOBILE_APP_VERSION',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        			'class'=>'small timePicker',
        			'placeholder' => 'eg: v1.2',
        			'id' => 'CATALOGUE_MOBILE_APP_VERSION',
        		),
        		'options'=>array(
        			'label'=>'Catalogue Mobile App Version<span class="red">*</span>',
        			'label_attributes' => array(
        				'class'  => 'inline right'
        			),
        			'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'RESTAURANT_MOBILE_APP_VERSION',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        			'class'=>'small timePicker',
        			'placeholder' => 'eg: v1.2',
        			'id' => 'RESTAURANT_MOBILE_APP_VERSION',
        		),
        		'options'=>array(
        			'label'=>'Restaurant Mobile App Version<span class="red">*</span>',
        			'label_attributes' => array(
        				'class'  => 'inline right'
        			),
        			'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
       		'name'=>'GLOBAL_AUTO_CONFIRM_ORDER_COD',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'selectpicker small',
       			'id' => 'GLOBAL_AUTO_CONFIRM_ORDER_COD',
       		),
       		'options'=>array(
       			'label'=>'Auto Confirm Order COD <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'inline right  mr5',
       			),
       			'value_options' => array(
       				'yes' => 'Yes',
       				'no' => 'No',
       			),
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		)
        ));
        
        $this->add(array(
       		'name'=>'GLOBAL_ALLOW_MEAL_SWAP',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'selectpicker small',
       			'id' => 'GLOBAL_ALLOW_MEAL_SWAP',
       		),
       		'options'=>array(
       			'label'=>'Allow Meal Swap <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'inline right  mr5',
       			),
       			'value_options' => array(
       				'yes' => 'Yes',
       				'no' => 'No',
       			),
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		)
        ));
        $this->add(array(
       		'name'=>'GLOBAL_ALLOW_ADMIN_MEAL_SWAP',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'selectpicker small',
       			'id' => 'GLOBAL_ALLOW_ADMIN_MEAL_SWAP',
       		),
       		'options'=>array(
       			'label'=>'Show to customer meal swap <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'inline right  mr5',
       			),
       			'value_options' => array(
       				'yes' => 'Yes',
       				'no' => 'No',
       			),
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		)
        ));
        
        
        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
        		'name'=>'submit',
        		'attributes'=>array(
        				'type'=>'submit',
        				'value'=>'Save',
        				'class'=>'dark-greenBg',
        				'id' => 'submitbutton',
        		)
        ));
        
        
         $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Cancel',
        				'id' => 'button',
        				'class'=>'button left tiny left5 redBg'
        
        		),
        ));
        
        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/setting/view-system-setting',
        		),
        )); 

        $locales = $this->getLocale();
        
        $this->add(array(
        		'name' => 'GLOBAL_LOCALE',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'class'=> 'smallinput chosen-select',
        				'id' => 'GLOBAL_LOCALE',
        				'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Country (locale) <span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),   
        				'value_options' => $locales,
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $currency = $this->getLocaleCurrency();
        
        //echo "<pre>"; print_r($locales); die;
        
        $this->add(array(
        		'name' => 'GLOBAL_CURRENCY',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'class'=> 'smallinput chosen-select',
        				'id' => 'GLOBAL_CURRENCY',
        				'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Currency <span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),        				
        				'value_options' => $currency,
        				'label_options' => array('disable_html_escape' => true),
        		),
        )); 
        
       $this->add(array(
       		'name'=>'GLOBAL_ALLOW_MENU_PLANNER',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_ALLOW_MENU_PLANNER',
       		),
       		'options'=>array(
       				'label'=>'Enable Menu Planner<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));    

       $this->add(array(
       		'name'=>'GLOBAL_PUBLISH_MENU_PLANNER',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_PUBLISH_MENU_PLANNER',
       		),
       		'options'=>array(
       				'label'=>'Publish Planned Menu<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       )); 

       $this->add(array(
       		'name'=>'GLOBAL_ALLOW_MEAL_ITEM_SWAP',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_ALLOW_MEAL_ITEM_SWAP',
       		),
       		'options'=>array(
       				'label'=>'Allow Customer To Set Item Preference<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       ));       
       
       $this->add(array(
       		'name' => 'GLOBAL_DELIVERY_TYPE',
       		'type' => 'Zend\Form\Element\MultiCheckbox',
       		'options' => array(
       				'label' => 'Global Delivery Type<span class="red">*</span>',
       				'label_attributes' => array(
       						'class'  => 'inline right'
       				),
       				'attributes' => array(
       						'id' => 'GLOBAL_DELIVERY_TYPE',
       				),
       				'value_options' => array(
       						array(
       								'value' => 'delivery',
       								'label' => 'Delivery',
       								'attributes' => array(
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						),
       						array(
       								'value' => 'pickup',
       								'label' => 'Pickup',
       								'attributes' => array(
       										'data-close'=>".onlinerow"
       								),
       								'label_attributes' => array(
       										'class'  => 'inline left mr5',
        									'style'=>'line-height: 0.9em;'
       								),
       						)
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		),
       ));

       $this->add(array(
       		'name'=>'GLOBAL_ALLOW_PARTIAL_PAYMENT',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_ALLOW_PARTIAL_PAYMENT',
       		),
       		'options'=>array(
       				'label'=>'Allow Partial Payment<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       )); 
       $this->add(array(
       		'name'=>'GLOBAL_SKIP_KITCHEN',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'small',
       				'id' => 'GLOBAL_SKIP_KITCHEN',
       		),
       		'options'=>array(
       				'label'=>'Skip KOT<span class="red">*</span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
                      
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       )); 
	   $this->add(array(
       		'name'=>'GLOBAL_ALLOW_INSTANT_ORDER',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
				'class'=>'small',
				'id' => 'GLOBAL_ALLOW_INSTANT_ORDER',
       		),
       		'options'=>array(
				'label'=>'Allow Instant Order <span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline right  mr5',
				),
				'value_options' => array(
					'yes' => 'Yes',
					'no' => 'No',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		)
       )); 
       $this->add(array(
       		'name'=>'GLOBAL_ENABLE_INSTANT_ORDER_IMAGE',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'GLOBAL_ENABLE_INSTANT_ORDER_IMAGE',
       		),
       		'options'=>array(
				'label'=>'Instant Order Image',
				'label_attributes' => array(
					'class' => 'inline right  mr5',
				),
				'value_options' => array(
					'yes' => 'Yes',
					'no' => 'No',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		)
       ));
       $this->add(array(
       		'name'=>'GLOBAL_ENABLE_WEBSITE',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       				'class'=>'selectpicker small',
       				'id' => 'GLOBAL_ENABLE_WEBSITE',
       		),
       		'options'=>array(
       				'label'=>'Enable Website<span class="red"></span>',
       				'label_attributes' => array(
       						'class' => 'inline right  mr5',
       				),
       				'value_options' => array(
       						'yes' => 'Yes',
       						'no' => 'No',
       				),
       				'label_options' => array('disable_html_escape' => true),
       				'disable_inarray_validator' => true,
       		)
       )); 
       $this->add(array(
       		'name'=>'GLOBAL_ENABLE_MEAL_PLANS',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
				'class'=>'selectpicker small',
				'id' => 'GLOBAL_ENABLE_MEAL_PLANS',
       		),
       		'options'=>array(
				'label'=>'Enable Meal Plans<span class="red">*</span>',
				'label_attributes' => array(
					'class' => 'inline right  mr5',
				),
				'value_options' => array(
					'yes' => 'Yes',
					'no' => 'No',
				),
				'label_options' => array('disable_html_escape' => true),
				'disable_inarray_validator' => true,
       		)
       ));  
       $this->add(array(
          'name'=>'GLOBAL_ALLOW_TIMESLOT',
          'type' => 'Zend\Form\Element\Radio',
          'attributes'=>array(
        'class'=>'selectpicker small',
        'id' => 'GLOBAL_ALLOW_TIMESLOT',
          ),
          'options'=>array(
        'label'=>'Enable Timeslot Manager<span class="red">*</span>',
        'label_attributes' => array(
          'class' => 'inline right  mr5',
        ),
        'value_options' => array(
          'yes' => 'Yes',
          'no' => 'No',
        ),
        'label_options' => array('disable_html_escape' => true),
        'disable_inarray_validator' => true,
          )
       ));             
    }
    

    /**
     * to get list of timezone
     * @method getTimeZones()
     * @return array $returndata
     */
    public function getTimeZones(){
    
    	$returndata =array();
    	$zones= timezone_identifiers_list();
        //$others = array('Canada/Eastern' => 'Canada/Eastern');
    	foreach ($zones as $zone){
    		$returndata[$zone] = $zone;
    	}
    	
        $returndata['Canada/Eastern'] = 'Canada/Eastern';
    	return  $returndata;
    }
    
    private function getLocale() {
    	 
    	$libCommon = QSCommon::getInstance($this->service_locator);
    	 
    	$locales = $libCommon->getLocale();
    	
    	$arrLocale = array(""=>"Select Country");
    	
    	foreach($locales as $locale) {
    		//$arrLocale[$locale['language_code'].'#'.$locale['currency_code']] = $locale['country_name'].' ('.$locale['language_code'].')';
        $arrLocale[$locale['language_code']] = $locale['country_name'].' ('.$locale['language_code'].')';
    	}
    	return $arrLocale;
    }

    private function getLocaleCurrency() {
    	
    	$libCommon = QSCommon::getInstance($this->service_locator);
    
    	$locale_currencies = $libCommon->getLocaleCurrency();
    	 
    	$arrCurrency = array(""=>"Select Currency");
    	 
    	foreach($locale_currencies as $currency) {
    		$arrCurrency[$currency['currency_code']] = $currency['currency_code'];
    	}
    	
    	return $arrCurrency;
    }
    
    
}

