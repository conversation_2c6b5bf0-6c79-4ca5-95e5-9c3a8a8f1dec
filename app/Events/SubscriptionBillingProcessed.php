<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\SchoolMealSubscription;
use App\DTOs\Payment\PaymentResponseDTO;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Event fired when subscription billing is successfully processed
 */
class SubscriptionBillingProcessed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly SchoolMealSubscription $subscription,
        public readonly PaymentResponseDTO $paymentResponse
    ) {}

    /**
     * Get event data for logging/monitoring
     */
    public function toArray(): array
    {
        return [
            'event' => 'subscription_billing_processed',
            'subscription_id' => $this->subscription->id,
            'customer_id' => $this->subscription->customer_id,
            'school_id' => $this->subscription->school_id,
            'payment_id' => $this->paymentResponse->paymentId,
            'amount' => $this->paymentResponse->amount,
            'currency' => $this->paymentResponse->currency,
            'payment_status' => $this->paymentResponse->status,
            'billing_cycle' => $this->subscription->billing_cycle,
            'processed_at' => now()->toISOString(),
        ];
    }
}
