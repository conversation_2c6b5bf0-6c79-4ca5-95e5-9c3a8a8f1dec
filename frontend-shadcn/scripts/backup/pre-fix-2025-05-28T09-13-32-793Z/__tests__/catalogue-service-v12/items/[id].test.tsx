import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueItems[id] } from '@/components/catalogue-service-v12/items/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueItems[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueItems[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueItems[id]')).toBeInTheDocument();
  });
});