import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletTransfer } from '@/components/customer-service-v12/[id]/wallet/transfer';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletTransfer', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletTransfer />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletTransfer')).toBeInTheDocument();
  });
});