openapi: 3.1.0
info:
  title: Food Delivery API
  description: API for the Food Delivery Service
  version: 2.0.0
  contact:
    name: API Support
    email: <EMAIL>
servers:
  - url: https://api.example.com/v2
    description: Production server
  - url: https://staging-api.example.com/v2
    description: Staging server
  - url: http://localhost:8000/api/v2
    description: Local development server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: An error occurred
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
    
    # Customer schemas
    Customer:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: <PERSON>
        email:
          type: string
          format: email
          example: <EMAIL>
        phone:
          type: string
          example: "+1234567890"
        address:
          type: string
          example: "123 Main St, City, Country"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    
    # Product schemas
    Product:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Veggie Burger
        description:
          type: string
          example: Delicious vegetarian burger
        price:
          type: number
          format: float
          example: 9.99
        category_id:
          type: integer
          format: int64
          example: 2
        image_url:
          type: string
          format: uri
          example: https://example.com/images/veggie-burger.jpg
        is_available:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    
    # Order schemas
    Order:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        customer_id:
          type: integer
          format: int64
          example: 1
        total_amount:
          type: number
          format: float
          example: 29.99
        status:
          type: string
          enum: [pending, confirmed, preparing, ready, delivered, cancelled]
          example: confirmed
        payment_status:
          type: string
          enum: [pending, paid, failed, refunded]
          example: paid
        delivery_address:
          type: string
          example: "123 Main St, City, Country"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
    
    OrderItem:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        order_id:
          type: integer
          format: int64
          example: 1
        product_id:
          type: integer
          format: int64
          example: 1
        quantity:
          type: integer
          example: 2
        price:
          type: number
          format: float
          example: 9.99
        subtotal:
          type: number
          format: float
          example: 19.98
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

paths:
  # Customer endpoints
  /customers:
    get:
      summary: Get all customers
      description: Returns a list of all customers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of customers
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create a new customer
      description: Creates a new customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - email
                - phone
              properties:
                name:
                  type: string
                  example: John Doe
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                phone:
                  type: string
                  example: "+1234567890"
                address:
                  type: string
                  example: "123 Main St, City, Country"
      responses:
        '201':
          description: Customer created
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Customer'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Product endpoints
  /products:
    get:
      summary: Get all products
      description: Returns a list of all products
      responses:
        '200':
          description: A list of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'

  # Order endpoints
  /orders:
    get:
      summary: Get all orders
      description: Returns a list of all orders
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
