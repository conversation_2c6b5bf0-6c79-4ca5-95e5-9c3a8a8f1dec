{"success": true, "message": "Active subscriptions retrieved successfully", "data": [{"id": 1, "parent_customer_id": 1, "child_profile_id": 1, "child_name": "<PERSON>", "child_profile_photo": "https://example.com/photos/alice.jpg", "meal_plan_id": 1, "meal_plan_name": "Healthy Lunch Plan", "school_id": 1, "school_name": "Delhi Public School", "start_date": "2024-01-01", "end_date": "2024-12-31", "status": "active", "subscription_type": "monthly", "billing_cycle": "monthly", "auto_renew": true, "delivery_days": ["monday", "tuesday", "wednesday", "thursday", "friday"], "preferred_break_time": "lunch_break", "delivery_time_preference": "12:00 PM", "meal_customizations": {"portion_size": "regular", "extra_items": ["fruit"], "special_requests": ["less spicy"]}, "dietary_accommodations": ["nut-free"], "spice_level": "mild", "special_notes": "Please ensure nut-free preparation", "requires_special_handling": true, "daily_rate": 40, "total_amount": 1200, "monthly_amount": 1200, "next_billing_date": "2024-02-01", "last_billing_date": "2024-01-01", "billing_status": "current", "consumption_stats": {"total_meals_delivered": 20, "meals_consumed": 18, "consumption_rate": 90, "feedback_average": 4.5}, "created_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-20T14:45:00Z"}, {"id": 2, "parent_customer_id": 1, "child_profile_id": 1, "child_name": "<PERSON>", "child_profile_photo": "https://example.com/photos/alice.jpg", "meal_plan_id": 2, "meal_plan_name": "Morning Snack Plan", "school_id": 1, "school_name": "Delhi Public School", "start_date": "2024-01-15", "end_date": "2024-06-15", "status": "active", "subscription_type": "monthly", "billing_cycle": "monthly", "auto_renew": true, "delivery_days": ["monday", "tuesday", "wednesday", "thursday", "friday"], "preferred_break_time": "morning_break", "delivery_time_preference": "10:30 AM", "meal_customizations": {"portion_size": "small", "extra_items": [], "special_requests": []}, "dietary_accommodations": ["nut-free"], "spice_level": "no_spice", "special_notes": "", "requires_special_handling": false, "daily_rate": 25, "total_amount": 750, "monthly_amount": 750, "next_billing_date": "2024-02-15", "last_billing_date": "2024-01-15", "billing_status": "current", "consumption_stats": {"total_meals_delivered": 10, "meals_consumed": 10, "consumption_rate": 100, "feedback_average": 4.8}, "created_at": "2024-01-15T09:00:00Z", "updated_at": "2024-01-25T16:30:00Z"}, {"id": 3, "parent_customer_id": 1, "child_profile_id": 2, "child_name": "<PERSON>", "child_profile_photo": null, "meal_plan_id": 3, "meal_plan_name": "Lunch Combo Plan", "school_id": 2, "school_name": "Modern School", "start_date": "2024-01-10", "end_date": "2024-07-10", "status": "active", "subscription_type": "monthly", "billing_cycle": "monthly", "auto_renew": true, "delivery_days": ["monday", "tuesday", "wednesday", "thursday", "friday"], "preferred_break_time": "lunch_break", "delivery_time_preference": "12:30 PM", "meal_customizations": {"portion_size": "regular", "extra_items": ["yogurt"], "special_requests": ["extra vegetables"]}, "dietary_accommodations": [], "spice_level": "medium", "special_notes": "Child loves vegetables", "requires_special_handling": false, "daily_rate": 35, "total_amount": 1050, "monthly_amount": 1050, "next_billing_date": "2024-02-10", "last_billing_date": "2024-01-10", "billing_status": "current", "consumption_stats": {"total_meals_delivered": 15, "meals_consumed": 14, "consumption_rate": 93, "feedback_average": 4.2}, "created_at": "2024-01-10T11:00:00Z", "updated_at": "2024-01-22T13:15:00Z"}], "meta": {"timestamp": "2024-01-28T10:30:00Z", "api_version": "v2", "correlation_id": "test-correlation-id-003"}}