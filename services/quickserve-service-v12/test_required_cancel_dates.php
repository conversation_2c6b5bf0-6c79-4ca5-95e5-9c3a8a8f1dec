<?php

echo "🧪 Testing Updated Order Cancel API - Required cancel_dates\n";
echo "=" . str_repeat("=", 70) . "\n\n";

echo "📋 IMPORTANT UPDATE: cancel_dates is now REQUIRED\n";
echo "-" . str_repeat("-", 70) . "\n";
echo "Since one order_no can span multiple days (5 days, 20 days, etc.),\n";
echo "you MUST specify which specific delivery dates to cancel.\n";
echo "This prevents accidental cancellation of all orders.\n\n";

// Test scenarios
$testScenarios = [
    [
        'name' => 'Valid Request - Single Date',
        'data' => [
            'reason' => 'Customer requested cancellation',
            'cancel_dates' => ['2025-08-06']
        ],
        'expected' => 'Should be accepted - valid single date cancellation',
        'status' => 'Valid'
    ],
    [
        'name' => 'Valid Request - Multiple Dates',
        'data' => [
            'reason' => 'Cancel specific delivery dates',
            'cancel_dates' => ['2025-08-06', '2025-08-13', '2025-08-20']
        ],
        'expected' => 'Should be accepted - valid multiple dates cancellation',
        'status' => 'Valid'
    ],
    [
        'name' => 'Valid Request - With Meal Type Filter',
        'data' => [
            'reason' => 'Cancel only lunch orders for these dates',
            'cancel_dates' => ['2025-08-06', '2025-08-13'],
            'meal_type' => 'lunch'
        ],
        'expected' => 'Should be accepted - valid with meal type filter',
        'status' => 'Valid'
    ],
    [
        'name' => 'Valid Request - With Request Timestamp (Edge Case)',
        'data' => [
            'reason' => 'Testing edge case timing',
            'cancel_dates' => ['2025-08-06'],
            'request_timestamp' => '2025-01-28 07:59:59'
        ],
        'expected' => 'Should be accepted - valid with timing validation',
        'status' => 'Valid'
    ],
    [
        'name' => 'Invalid Request - Missing cancel_dates',
        'data' => [
            'reason' => 'Testing invalid request'
        ],
        'expected' => 'Should be rejected - 422 Validation Error',
        'status' => 'Invalid'
    ],
    [
        'name' => 'Invalid Request - Empty cancel_dates Array',
        'data' => [
            'reason' => 'Testing empty array',
            'cancel_dates' => []
        ],
        'expected' => 'Should be rejected - 422 Validation Error (min 1 item)',
        'status' => 'Invalid'
    ]
];

echo "📋 Test Scenarios Overview\n";
echo "-" . str_repeat("-", 70) . "\n";
foreach ($testScenarios as $index => $scenario) {
    echo ($index + 1) . ". {$scenario['name']} ({$scenario['status']})\n";
    echo "   Data: " . json_encode($scenario['data']) . "\n";
    echo "   Expected: {$scenario['expected']}\n\n";
}

// Test 1: Valid single date cancellation
echo "📋 Test 1: Valid Single Date Cancellation\n";
echo "-" . str_repeat("-", 70) . "\n";

$validSingleDate = [
    'reason' => 'Customer requested cancellation',
    'cancel_dates' => ['2025-08-06']
];

echo "Request Body:\n";
echo json_encode($validSingleDate, JSON_PRETTY_PRINT) . "\n\n";

echo "cURL Command:\n";
echo "curl -X POST 'http://************:8003/api/v2/order-management/cancel/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($validSingleDate) . "'\n\n";

// Test 2: Valid multiple dates cancellation
echo "📋 Test 2: Valid Multiple Dates Cancellation\n";
echo "-" . str_repeat("-", 70) . "\n";

$validMultipleDates = [
    'reason' => 'Cancel specific delivery dates',
    'cancel_dates' => ['2025-08-06', '2025-08-13', '2025-08-20']
];

echo "Request Body:\n";
echo json_encode($validMultipleDates, JSON_PRETTY_PRINT) . "\n\n";

echo "cURL Command:\n";
echo "curl -X POST 'http://************:8003/api/v2/order-management/cancel/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($validMultipleDates) . "'\n\n";

// Test 3: Valid with meal type filter
echo "📋 Test 3: Valid with Meal Type Filter\n";
echo "-" . str_repeat("-", 70) . "\n";

$validWithMealType = [
    'reason' => 'Cancel only lunch orders for these dates',
    'cancel_dates' => ['2025-08-06', '2025-08-13'],
    'meal_type' => 'lunch'
];

echo "Request Body:\n";
echo json_encode($validWithMealType, JSON_PRETTY_PRINT) . "\n\n";

echo "cURL Command:\n";
echo "curl -X POST 'http://************:8003/api/v2/order-management/cancel/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($validWithMealType) . "'\n\n";

// Test 4: Edge case with request timestamp
echo "📋 Test 4: Edge Case with Request Timestamp\n";
echo "-" . str_repeat("-", 70) . "\n";

$edgeCaseWithTimestamp = [
    'reason' => 'Testing edge case timing',
    'cancel_dates' => ['2025-08-06'],
    'request_timestamp' => '2025-01-28 07:59:59'
];

echo "Request Body:\n";
echo json_encode($edgeCaseWithTimestamp, JSON_PRETTY_PRINT) . "\n\n";

echo "cURL Command:\n";
echo "curl -X POST 'http://************:8003/api/v2/order-management/cancel/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($edgeCaseWithTimestamp) . "'\n\n";

// Test 5: Invalid - missing cancel_dates
echo "📋 Test 5: Invalid Request - Missing cancel_dates\n";
echo "-" . str_repeat("-", 70) . "\n";

$invalidMissingDates = [
    'reason' => 'Testing invalid request'
];

echo "Request Body:\n";
echo json_encode($invalidMissingDates, JSON_PRETTY_PRINT) . "\n\n";

echo "Expected Response (422 Validation Error):\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Validation failed\",\n";
echo "  \"errors\": {\n";
echo "    \"cancel_dates\": [\"The cancel dates field is required.\"]\n";
echo "  }\n";
echo "}\n\n";

// Test 6: Invalid - empty cancel_dates array
echo "📋 Test 6: Invalid Request - Empty cancel_dates Array\n";
echo "-" . str_repeat("-", 70) . "\n";

$invalidEmptyArray = [
    'reason' => 'Testing empty array',
    'cancel_dates' => []
];

echo "Request Body:\n";
echo json_encode($invalidEmptyArray, JSON_PRETTY_PRINT) . "\n\n";

echo "Expected Response (422 Validation Error):\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Validation failed\",\n";
echo "  \"errors\": {\n";
echo "    \"cancel_dates\": [\"The cancel dates must have at least 1 items.\"]\n";
echo "  }\n";
echo "}\n\n";

// Expected success response format
echo "📋 Expected Success Response Format\n";
echo "-" . str_repeat("-", 70) . "\n";

echo "{\n";
echo "  \"success\": true,\n";
echo "  \"message\": \"Orders cancelled successfully with time-based refund policy\",\n";
echo "  \"data\": {\n";
echo "    \"cancelled_orders\": 2,\n";
echo "    \"cancelled_order_ids\": [127810, 127811],\n";
echo "    \"total_refund_amount\": 125.50,\n";
echo "    \"wallet_credited\": true,\n";
echo "    \"wallet_unlocked\": 200.00,\n";
echo "    \"customer_code\": 1,\n";
echo "    \"order_no\": \"QA93250725\",\n";
echo "    \"refund_breakdown\": [\n";
echo "      {\n";
echo "        \"order_id\": 127810,\n";
echo "        \"order_date\": \"2025-08-06\",\n";
echo "        \"meal_type\": \"lunch\",\n";
echo "        \"base_amount\": 125.00,\n";
echo "        \"refund_percentage\": 50,\n";
echo "        \"refund_amount\": 62.50,\n";
echo "        \"wallet_unlocked\": 125.00,\n";
echo "        \"policy_type\": \"partial_refund_window\",\n";
echo "        \"cutoff_time\": \"00:01:00\"\n";
echo "      }\n";
echo "    ]\n";
echo "  }\n";
echo "}\n\n";

// Implementation benefits
echo "📋 Benefits of Required cancel_dates\n";
echo "-" . str_repeat("-", 70) . "\n";

$benefits = [
    '🎯 Prevents accidental cancellation of all orders under same order_no',
    '🎯 Explicit control over which delivery dates to cancel',
    '🎯 Supports partial cancellation for multi-day orders',
    '🎯 Clear validation errors for missing or empty dates',
    '🎯 Better user experience with specific date selection',
    '🎯 Prevents confusion in 5-day, 20-day order scenarios',
    '🎯 Maintains backward compatibility with enhanced validation',
    '🎯 Supports both single and multiple date cancellations'
];

echo "Benefits of making cancel_dates required:\n";
foreach ($benefits as $benefit) {
    echo "  {$benefit}\n";
}

echo "\n🎉 Updated Order Cancel API Test Complete!\n";
echo "Key Changes:\n";
echo "  ✅ cancel_dates is now REQUIRED (not optional)\n";
echo "  ✅ Must specify at least 1 date (min:1 validation)\n";
echo "  ✅ Prevents accidental cancellation of all orders\n";
echo "  ✅ Clear validation errors for missing dates\n";
echo "  ✅ Supports both single and multiple date cancellations\n";
echo "  ✅ Enhanced OpenAPI documentation updated\n";

function makeCancellationRequest($requestData) {
    $url = 'http://************:8003/api/v2/order-management/cancel/QA93250725';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return "Failed to make request";
    }
    
    return "HTTP {$httpCode}: " . $response;
}
