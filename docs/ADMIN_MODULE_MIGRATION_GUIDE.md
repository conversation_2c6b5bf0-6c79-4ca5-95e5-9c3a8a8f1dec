# Admin Module Migration Guide

This guide provides step-by-step instructions for migrating from the legacy Zend Admin Module to the new Laravel 12 Admin Service.

## Prerequisites

- PHP 8.2 or higher
- Composer
- Docker (optional)
- MySQL 8.0 or higher

## Migration Steps

### 1. Set Up the New Admin Service

1. Clone the repository:

```bash
git clone <repository-url>
cd services/admin-service-v12
```

2. Install dependencies:

```bash
composer install
```

3. Copy the environment file:

```bash
cp .env.example .env
```

4. Configure the environment variables in the `.env` file:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=admin_service
DB_USERNAME=root
DB_PASSWORD=

AUTH_SERVICE_URL=http://auth-service-v12:8000/api
CUSTOMER_SERVICE_URL=http://customer-service-v12:8000/api
ORDER_SERVICE_URL=http://order-service-v12:8000/api
PAYMENT_SERVICE_URL=http://payment-service-v12:8000/api
```

5. Generate application key:

```bash
php artisan key:generate
```

### 2. Migrate the Database

1. Create a new database for the Admin Service:

```sql
CREATE DATABASE admin_service;
```

2. Run the migrations:

```bash
php artisan migrate
```

3. Migrate data from the Zend database:

```bash
php artisan migrate:zend-data
```

### 3. Update API Clients

#### Legacy API Endpoints (Zend)

```
GET /admin/dashboard
GET /admin/users
POST /admin/users
PUT /admin/users/{id}
DELETE /admin/users/{id}
```

#### New API Endpoints (Laravel)

```
GET /api/v2/admin/dashboard
GET /api/v2/admin/users
POST /api/v2/admin/users
PUT /api/v2/admin/users/{id}
DELETE /api/v2/admin/users/{id}
```

#### Example: Updating JavaScript Client

```javascript
// Old
const response = await fetch('/admin/users');

// New
const response = await fetch('/api/v2/admin/users');
```

### 4. Update Authentication

#### Legacy Authentication (Zend)

```php
// Zend
$auth = new \Zend\Authentication\AuthenticationService();
$identity = $auth->getIdentity();
```

#### New Authentication (Laravel)

```php
// Laravel
$token = $request->bearerToken();
$user = $authService->getUserFromToken($token);
```

#### Example: Updating JavaScript Client

```javascript
// Old
const response = await fetch('/admin/users', {
  headers: {
    'X-Auth-Token': sessionStorage.getItem('authToken')
  }
});

// New
const response = await fetch('/api/v2/admin/users', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('jwtToken')}`
  }
});
```

### 5. Configure Kong API Gateway

1. Create a Kong API Gateway configuration file:

```yaml
# admin-service-kong.yaml
_format_version: "2.1"
_transform: true

services:
  - name: admin-service
    url: http://admin-service-v12:8000
    routes:
      - name: admin-service-route
        paths:
          - /v2/admin
        strip_path: false
    plugins:
      - name: cors
      - name: rate-limiting
      - name: jwt
      - name: request-transformer
```

2. Deploy the Kong API Gateway configuration:

```bash
./deploy-kong-config.sh
```

### 6. Update Frontend Applications

1. Update API endpoints in frontend applications:

```javascript
// Old
const API_BASE_URL = '/admin';

// New
const API_BASE_URL = '/api/v2/admin';
```

2. Update authentication in frontend applications:

```javascript
// Old
const headers = {
  'X-Auth-Token': sessionStorage.getItem('authToken')
};

// New
const headers = {
  'Authorization': `Bearer ${localStorage.getItem('jwtToken')}`
};
```

3. Update response handling in frontend applications:

```javascript
// Old
const response = await fetch('/admin/users');
const data = await response.json();
const users = data.users;

// New
const response = await fetch('/api/v2/admin/users');
const data = await response.json();
const users = data.data;
```

## API Changes

### Response Format Changes

#### Legacy Response Format (Zend)

```json
{
  "status": "success",
  "users": [
    {
      "pk_user_code": 1,
      "first_name": "John",
      "last_name": "Doe",
      "email_id": "<EMAIL>",
      "phone": "1234567890",
      "status": 1,
      "created_date": "2023-01-01 12:00:00"
    }
  ]
}
```

#### New Response Format (Laravel)

```json
{
  "data": [
    {
      "id": 1,
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "1234567890",
      "status": true,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    }
  ],
  "meta": {
    "total": 1
  }
}
```

### Request Format Changes

#### Legacy Request Format (Zend)

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email_id": "<EMAIL>",
  "phone": "1234567890",
  "status": 1,
  "role_id": 1
}
```

#### New Request Format (Laravel)

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "1234567890",
  "status": true,
  "role_ids": [1]
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Make sure the Auth Service is running
   - Check that the JWT token is valid
   - Verify that the user has the required roles and permissions

2. **Database Migration Errors**:
   - Check that the database exists and is accessible
   - Verify that the database user has the required permissions
   - Make sure the Zend database is accessible for data migration

3. **API Gateway Errors**:
   - Check that Kong is running
   - Verify that the Kong configuration is correct
   - Make sure the Admin Service is accessible from Kong

## Support

For support, please contact the development team at [<EMAIL>](mailto:<EMAIL>).
