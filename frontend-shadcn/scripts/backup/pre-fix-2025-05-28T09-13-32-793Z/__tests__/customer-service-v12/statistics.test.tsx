import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomerStatistics } from '@/components/customer-service-v12/statistics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerStatistics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerStatistics />
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerStatistics')).toBeInTheDocument();
  });
});