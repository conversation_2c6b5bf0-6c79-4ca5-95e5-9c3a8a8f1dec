<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Storage</title>
</head>
<body>
    <h1>Clear Kanban Storage</h1>
    <button onclick="clearStorage()">Clear Storage</button>
    <p id="message"></p>

    <script>
        function clearStorage() {
            try {
                // Clear the specific kanban store
                localStorage.removeItem('task-store');
                
                // Clear all localStorage (optional)
                // localStorage.clear();
                
                document.getElementById('message').innerHTML = 'Storage cleared! Please refresh the kanban page.';
                document.getElementById('message').style.color = 'green';
            } catch (error) {
                document.getElementById('message').innerHTML = 'Error clearing storage: ' + error.message;
                document.getElementById('message').style.color = 'red';
            }
        }

        // Auto-clear on page load
        window.onload = function() {
            clearStorage();
        }
    </script>
</body>
</html> 