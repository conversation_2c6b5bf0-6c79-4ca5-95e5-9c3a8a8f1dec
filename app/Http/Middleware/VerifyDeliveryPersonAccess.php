<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class VerifyDeliveryPersonAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }
        
        // Check if user has delivery person role
        // This could be done by checking a local role or by calling the Auth Service
        try {
            $response = Http::withToken($request->bearerToken())
                ->get(config('services.auth.url') . '/api/v2/user/permissions');
            
            $permissions = $response->json();
            
            if (!isset($permissions['data']['roles']) || !in_array('delivery', $permissions['data']['roles'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to access this resource'
                ], 403);
            }
        } catch (\Exception $e) {
            // For development purposes, allow access if Auth Service is not available
            if (app()->environment('local', 'development')) {
                return $next($request);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Error verifying permissions: ' . $e->getMessage()
            ], 500);
        }
        
        return $next($request);
    }
}
