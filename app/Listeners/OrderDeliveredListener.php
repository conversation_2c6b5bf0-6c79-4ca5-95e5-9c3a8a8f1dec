<?php

namespace App\Listeners;

use App\Events\OrderDeliveredEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class OrderDeliveredListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrderDeliveredEvent $event): void
    {
        try {
            $data = [
                'order_id' => $event->orderId,
                'delivery_person_id' => $event->deliveryPersonId,
                'timestamp' => now()->toIso8601String(),
                'status' => 'delivered'
            ];
            
            $this->publishToRabbitMQ('order.delivered', $data);
            
            Log::info('Order delivered event published', [
                'order_id' => $event->orderId,
                'delivery_person_id' => $event->deliveryPersonId
            ]);
        } catch (\Exception $e) {
            Log::error('Error publishing order delivered event: ' . $e->getMessage(), [
                'order_id' => $event->orderId,
                'delivery_person_id' => $event->deliveryPersonId
            ]);
        }
    }
    
    /**
     * Publish message to RabbitMQ.
     *
     * @param string $routingKey
     * @param array $data
     * @return void
     */
    private function publishToRabbitMQ(string $routingKey, array $data): void
    {
        $connection = new AMQPStreamConnection(
            config('rabbitmq.host', 'localhost'),
            config('rabbitmq.port', 5672),
            config('rabbitmq.user', 'guest'),
            config('rabbitmq.password', 'guest'),
            config('rabbitmq.vhost', '/')
        );
        
        $channel = $connection->channel();
        
        $exchange = config('rabbitmq.exchange', 'fooddialer');
        
        // Declare exchange
        $channel->exchange_declare(
            $exchange,
            config('rabbitmq.exchange_type', 'topic'),
            false,
            true,
            false
        );
        
        $message = new AMQPMessage(
            json_encode($data),
            [
                'content_type' => 'application/json',
                'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
            ]
        );
        
        $channel->basic_publish($message, $exchange, $routingKey);
        
        $channel->close();
        $connection->close();
    }
}
