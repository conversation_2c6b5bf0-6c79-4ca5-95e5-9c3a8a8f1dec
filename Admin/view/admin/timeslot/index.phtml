<div id="content" class="clearfix">
	<div class="large-12 columns">

          <div class="filter">
                <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/timeslot" method="post"/>
                
                    <div class="row">
                        <div class="medium-12 columns">
                            <div class="type left">
                                <select name="day" id="day" class="left filterSelect">
                                    <option value="">Select Day</option>
                                    <option value="all">All</option>
                                    <option value="Monday">Monday</option>
                                    <option value="Tuesday">Tuesday</option>
                                    <option value="Wednesday">Wednesday</option>
                                    <option value="Thursday">Thursday</option>
                                    <option value="Friday">Friday</option>
                                    <option value="Saturday">Saturday</option>
                                    <option value="Sunday">Sunday</option>
                                </select>

                                <select name="menu" id="menu" class="left filterSelect">
                                    <option value="">Select Menu</option>
                                    <option value="all">All</option>                                    
                                <?php
                                    foreach ($menus as $menu) {
                                ?>
                                    <option value="<?php echo $menu; ?>"><?php echo $menu; ?></option>
                                <?php        
                                    }
                                ?>
                                </select>

                                <select name="status" id="status" class="left filterSelect">
                                    <option value="">Select Status</option>
                                    <option value="all">All</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                                <button id="selectedloc" name="selectedloc" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                            </div>
                        </div>
                    </div>
                </form>
        </div>
        <div class="clearBoth10"></div>

        <div class="portlet box yellow">        
            <div class="portlet-title">
                <h4><i class="fa fa-table"></i>Timeslot</h4>  
                <ul class="toolOption">
                    <li>
                    <?php if($acl->isAllowed($loggedUser->rolename,'timeslot','add')){  ?>
                    <div class="addRecord">
                        <button class="btn" onClick="location.href='<?php echo $this->url('timeslot', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Timeslot</button>
                        </div>
                      <?php } ?>
                    </li>
                </ul>
            </div>        
            <div class="portlet-body sales_data_table">   
                <div class="filter">
                    <div>
                        <a class="advance_search_click">Hide advance Search </a>
                    </div>
                </div>                 
                <table id="timeslot" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Slot Id</th>
                            <th>Start Time</th>
                            <th>End Time</th>
                            <th>Day</th>
                            <th>Menu</th>
                            <th>Kitchen</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>          
            </div>
        </div>
        <div class="clearBoth20"></div>
	</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {

        var timeslotTable = $('#timeslot').dataTable( {
            "processing": true,
            "serverSide": true,
            "bDestroy" :true,
            "stateSave": true,
            //"aoColumns":aoColumns,
            "ajax": {
                "url":"/timeslot/ajx-timeslot",
                "data": function ( d ) {
                    d.day = $("#day option:selected").val();
                    d.menu = $("#menu option:selected").val();
                    d.kitchen = $("#kitchen option:selected").val();
                    d.status = $("#status option:selected").val();
                } 
             },
            "aoColumnDefs": [
              {
                bSortable: false,
                aTargets: [ -1,-2 ]
              }
            ],
        });
    
        $(document).on('click','#selectedloc',function(){
            timeslotTable.api().ajax.reload();
        });

        $(".smBtn").on('click', function() {
            alert('hhgghh');
            var id =  $(this).data('id');
            return false;
            var url = "/timeslot/updateslot";

            $.ajax({
                url     : url,
                type    :'POST',
                data    : {id:id},
                dataType: 'json',
                async: false,            
                beforeSend : function() {
                    //$('#'+ordno).append('processing..');
                },
                success : function(response) {
                    if(response.status=="success") {
                        document.location.href = '/timeslot',true;
                    }
                },
                error: function(xhr, status, errorThrown) {
                    console.log('Error: '+xhr.status+' '+xhr.responseText);
                }
            });         
            return false;            
        });
    });
</script>