******************added delivery_person_id to temp_pre_orders**********************

ALTER TABLE `temp_pre_orders`
ADD COLUMN `delivery_person` INT(11) NULL DEFAULT NULL AFTER `delivery_type`;


******************added by Neelam 10-02-16**********************

ALTER TABLE `customers`
ADD COLUMN `source` VARCHAR(45) NULL DEFAULT NULL AFTER `email_verified`,
ADD COLUMN `referer` VARCHAR(45) NULL DEFAULT NULL AFTER `source`;

******************added by Neelam 11-02-16**********************

ALTER TABLE `orders`
ADD COLUMN `source` VARCHAR(45) NULL DEFAULT NULL AFTER `created_date`;


Third-Party Delivery
Third-Party Aggregator
*********************added by ritesh 15-2-16 *************************


INSERT INTO `email_template` (`pk_template_id`, `fk_set_id`, `template_variable_id`, `template_key`, `purpose`, `type`, `subject`, `body`, `is_active`, `created_date`, `created_by`, `modified_date`, `modified_by`)

VALUES (167, 1, '1,11,36,35,4,3,25,21', 'order_payment_receipt', 'Order Payment', 'html', 'Food Dialer-Your Order payment receipt', '<html>\r\n   <head></head>\r\n <body>\r\n      <table style="width:600px" class=" cke_show_border" border="0" cellpadding="0" cellspacing="0">\r\n         <tbody>\r\n <tr>\r\n               <td>\r\n <table style="width:100%" class=" cke_show_border" border="0" cellpadding="0" cellspacing="0">\r\n <tbody>\r\n <tr>\r\n <td>\r\n                              <table style="width:600px" class=" cke_show_border" border="0" cellpadding="0" cellspacing="0">\r\n <tbody>\r\n <tr>\r\n <td>\r\n                                          <table style="width:100%" class=" cke_show_border" border="0" cellspacing="0">\r\n <tbody>\r\n <tr>\r\n <td style="text-align:left">\r\n <h1>Dear #towards# </h1>\r\n <h1><b>Thank you for the payment of Rs. #total# .</b></h1>\r\n </td>\r\n </tr>\r\n </tbody>\r\n </table>\r\n <table style="width:100%" class=" cke_show_border">\r\n <tbody>\r\n <tr>\r\n <td style="width:50%">Billed to Account/Owner: #towards# </td>\r\n <td style="width:50%">Order number: #order_no# </td>\r\n </tr>\r\n <tr>\r\n <td style="width:50%">Order date: #due_date# </td>\r\n </tr>\r\n </tbody>\r\n </table>\r\n <table style="width:600px" class=" cke_show_border" border="0" cellpadding="0" cellspacing="0">\r\n \r\n <tbody>\r\n <tr>\r\n <td style="text-align:left">\r\n <p><b>Due Date: #due_date# </b></p>\r\n </td>\r\n </tr>\r\n <tr>\r\n <td style="text-align:left">\r\n <p>Thanks for ordering to our services. &nbsp;</p>\r\n </td>\r\n </tr>\r\n <tr>\r\n <td style="text-align:left">\r\n <p>In case of any query, feel free to drop a mail at: #support_email# &nbsp;</p>\r\n </td>\r\n </tr>\r\n </tbody>\r\n </table>\r\n\r\n </td>\r\n </tr>\r\n </tbody>\r\n </table>\r\n </td>\r\n </tr>\r\n </tbody>\r\n                  </table>\r\n \r\n               </td>\r\n </tr>\r\n         </tbody>\r\n </table>\r\n   </body>\r\n</html>', '1', '0000-00-00', '', '2016-02-10 16:25:12', '');



ALTER TABLE `invoice_details`
ADD COLUMN `order_bill_no` VARCHAR(45) NULL DEFAULT NULL AFTER `discount`;


ALTER TABLE `invoice_details`
ADD COLUMN `order_date` DATE NULL AFTER `order_bill_no`;


ALTER TABLE `invoice`
ADD COLUMN `order_bill_no` VARCHAR(45) NULL DEFAULT NULL AFTER `order_dates`;

/**Changes by Neelam 24-Feb-16 **/

ALTER TABLE `orders` 
ADD COLUMN `service_charge` INT(11) NULL DEFAULT NULL AFTER `delivery_charges`,


/******************************** Added by shilbhushan on 17-02-2016 *******************/

ALTER TABLE `payment_transaction`
    ALTER `created_date` DROP DEFAULT;

ALTER TABLE `payment_transaction`
    CHANGE COLUMN `customer_id` `customer_id` INT(11) NULL DEFAULT NULL AFTER `pk_transaction_id`,
    CHANGE COLUMN `customer_email` `customer_email` VARCHAR(100) NULL DEFAULT NULL AFTER `customer_id`,
    CHANGE COLUMN `customer_name` `customer_name` VARCHAR(100) NULL DEFAULT NULL AFTER `customer_phone`,
    ADD COLUMN `transaction_charges` VARCHAR(45) NULL DEFAULT NULL AFTER `payment_amount`,
    ADD COLUMN `pre_order_id` VARCHAR(45) NULL DEFAULT NULL AFTER `transaction_charges`,
    CHANGE COLUMN `payment_transaction_id` `gateway_transaction_id` VARCHAR(45) NULL DEFAULT NULL AFTER `status`,
    CHANGE COLUMN `created_date` `created_date` DATETIME NOT NULL AFTER `description`,
    ADD COLUMN `modified_date` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_date`,
    CHANGE COLUMN `transaction_by` `transaction_by` VARCHAR(50) NULL DEFAULT 'gateway' COMMENT 'gateway / admin' AFTER `modified_date`,
    ADD COLUMN `referer` VARCHAR(50) NULL DEFAULT 'gateway' COMMENT 'website / mobileapp / desktopapp' AFTER `transaction_by`;d


ALTER TABLE `payment_transaction`
    ADD COLUMN `success_url` VARCHAR(255) NULL AFTER `referer`,
    ADD COLUMN `failure_url` VARCHAR(255) NULL AFTER `success_url`;


ALTER TABLE `orders`
    ADD COLUMN `tax_method` VARCHAR(20) NULL COMMENT 'inclusive / exclusive' AFTER `food_type`;

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `tax_method` VARCHAR(20) NULL COMMENT 'inclusive/exclusive' AFTER `delivery_type`;

ALTER TABLE `order_tax_details`
    ADD COLUMN `bill_no` INT(11) NULL AFTER `ord_ref_id`;


ALTER TABLE `temp_order_tax_details`
    ADD COLUMN `order_date` DATE NOT NULL AFTER `tax_amount`;


ALTER TABLE `temp_pre_orders`
    ADD COLUMN `apply_tax` VARCHAR(20) NULL AFTER `tax_method`;


CREATE TABLE `tax` (
    `tax_id` INT(11) NOT NULL AUTO_INCREMENT,
    `tax_name` VARCHAR(45) NULL DEFAULT NULL,
    `tax_type` ENUM('percent','fixed') NULL DEFAULT NULL COMMENT 'Per & Fix',
    `tax` FLOAT NULL DEFAULT NULL,
    `country` VARCHAR(50) NULL DEFAULT 'all',
    `city` VARCHAR(50) NULL DEFAULT 'all',
    `priority` INT(5) NOT NULL,
    `apply_all_product` ENUM('yes','no') NULL DEFAULT NULL COMMENT 'If no, then tax rates will not apply to cart on tax calculation. If set to yes then considered as global tax rate.',
    `created_date` DATETIME NULL DEFAULT NULL,
    `updated_date` DATETIME NULL DEFAULT NULL,
    `date_effective_from` DATETIME NULL DEFAULT NULL,
    `date_effective_till` DATETIME NULL DEFAULT NULL,
    `status` INT(1) NULL DEFAULT NULL COMMENT '1 =Active & 0 = Deactive',
    PRIMARY KEY (`tax_id`)
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;


CREATE TABLE `tax_classes` (
    `tax_class_id` BIGINT(20) NOT NULL,
    `tax_title` VARCHAR(45) NOT NULL,
    `tax_desc` VARCHAR(255) NULL DEFAULT NULL,
    `created_by` BIGINT(20) NOT NULL,
    `created_on` DATETIME NOT NULL,
    `updated_by` BIGINT(20) NULL DEFAULT NULL,
    `updated_on` DATETIME NOT NULL,
    PRIMARY KEY (`tax_class_id`)
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
;

CREATE TABLE `tax_rules` (
    `tax_rule_id` BIGINT(20) NOT NULL,
    `tax_class_id` BIGINT(20) NOT NULL,
    `tax_id` INT(11) NOT NULL,
    `priority` INT(5) NOT NULL,
    PRIMARY KEY (`tax_rule_id`),
    INDEX `fk_tax_rules_class_idx` (`tax_id`),
    INDEX `fk_tax_rules_class_idx1` (`tax_class_id`),
    CONSTRAINT `fk_tax_rules_class` FOREIGN KEY (`tax_class_id`) REFERENCES `tax_classes` (`tax_class_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT `fk_tax_rules_rate` FOREIGN KEY (`tax_id`) REFERENCES `tax` (`tax_id`) ON UPDATE NO ACTION ON DELETE NO ACTION
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
;

ALTER TABLE `customer_wallet`
    CHANGE COLUMN `payment_type` `payment_type` ENUM('neft','cash','cheque','online','wallet') NOT NULL DEFAULT 'cash' AFTER `context`;


========================================================

ALTER TABLE `orders`
    CHANGE COLUMN `delivery_charges` `delivery_charges` DECIMAL(10,2) NULL DEFAULT '0.00' AFTER `tax`,
    ADD COLUMN `service_charges` DECIMAL(10,2) NULL DEFAULT '0.00' AFTER `delivery_charges`;

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `service_charges` DECIMAL(10,2) NULL DEFAULT '0.00' AFTER `delivery_charges`;

ALTER TABLE `product_category`
    ADD COLUMN `sequence` TINYINT(1) UNSIGNED NULL DEFAULT '1' AFTER `status`;

/************* added by sankalp march 17  ******************/
INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `status`, `resource_type`) VALUES ('order', 'Admin\\Controller\\Order', 'printpackaging', 'read', '1', 'public');
INSERT INTO `products` (`name`, `description`, `unit_price`) VALUES ('Corporate Meal', 'Corporate Meal', 10);

////////////////////// Payment history ///////////////////////////////////////////

ALTER TABLE `invoice_payments`
    ADD COLUMN `current_amount_paid` DECIMAL(10,2) NULL DEFAULT NULL AFTER `date`;

CREATE TABLE `invoice_payments_history` (
    `invoice_payment_id` INT(11) NULL DEFAULT NULL,
    `invoice_ref_id` INT(11) NULL DEFAULT NULL,
    `actual_invoice_amount` DECIMAL(10,2) NULL DEFAULT '0.00',
    `invoice_amount` DECIMAL(10,2) NULL DEFAULT NULL,
    `discounted_amount` DECIMAL(10,2) NULL DEFAULT '0.00',
    `tax` DECIMAL(10,2) NULL DEFAULT '0.00',
    `delivery_charges` DECIMAL(10,2) NULL DEFAULT '0.00',
    `amount_paid` DECIMAL(10,2) NULL DEFAULT NULL,
    `amount_due` DECIMAL(10,2) NULL DEFAULT NULL,
    `mode_of_payment` VARCHAR(10) NULL DEFAULT NULL COMMENT 'cheque/cc/cash/voucher',
    `date` DATE NULL DEFAULT NULL,
    `current_amount_paid` DECIMAL(10,2) NULL DEFAULT NULL
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB;


SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='';
DELIMITER //
CREATE TRIGGER `insert_invoice_history` AFTER INSERT ON `invoice_payments` FOR EACH ROW BEGIN
INSERT INTO `invoice_payments_history` (
  `invoice_payment_id`,
  `invoice_ref_id`,
  `actual_invoice_amount`,
  `invoice_amount`,
  `discounted_amount`,
  `tax`,
  `delivery_charges`,
  `amount_paid`,
  `amount_due`,
  `mode_of_payment`,
  `date`,
  `current_amount_paid`)
VALUES (
  NEW.invoice_payment_id,
  NEW.invoice_ref_id,
  NEW.actual_invoice_amount,
  NEW.invoice_amount,
  NEW.discounted_amount,
  NEW.tax,
  NEW.delivery_charges,
  NEW.amount_paid,
  NEW.amount_due,
  NEW.mode_of_payment,
  NEW.date,
  NEW.current_amount_paid );
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;


SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='';
DELIMITER //
CREATE TRIGGER `update_invoice_history` AFTER UPDATE ON `invoice_payments` FOR EACH ROW BEGIN
INSERT INTO `invoice_payments_history` (
  `invoice_payment_id`,
  `invoice_ref_id`,
  `actual_invoice_amount`,
  `invoice_amount`,
  `discounted_amount`,
  `tax`,
  `delivery_charges`,
  `amount_paid`,
  `amount_due`,
  `mode_of_payment`,
  `date`,
  `current_amount_paid`)
VALUES (
  NEW.invoice_payment_id,
  NEW.invoice_ref_id,
  NEW.actual_invoice_amount,
  NEW.invoice_amount,
  NEW.discounted_amount,
  NEW.tax,
  NEW.delivery_charges,
  NEW.amount_paid,
  NEW.amount_due,
  NEW.mode_of_payment,
  NEW.date,
  NEW.current_amount_paid);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

08/03/16

ALTER TABLE `orders`
ADD COLUMN `prefered_delivery_person_id` INT(11) NULL DEFAULT NULL AFTER `source`;

/**
* tax recording -- shilbhushan
**/

INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('order', 'Admin\\Controller\\Order', 'printpackaging', 'read', '2016-01-05 16:39:08', '2016-01-05 16:39:08', 1, 'public');

INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('order', 'Admin\\Controller\\Order', 'ajx-update-qty', 'write', '2016-01-05 16:39:08', '2016-03-14 12:12:56', 1, 'public');

ALTER TABLE `order_tax_details`
    ADD COLUMN `tax_type` VARCHAR(50) NULL AFTER `tax_amount`,
    ADD COLUMN `tax_rate` VARCHAR(50) NULL AFTER `tax_type`,
    ADD COLUMN `tax_priority` VARCHAR(50) NULL AFTER `tax_rate`;

ALTER TABLE `temp_order_tax_details`
    ADD COLUMN `tax_type` VARCHAR(50) NULL AFTER `tax_amount`,
    ADD COLUMN `tax_rate` VARCHAR(50) NULL AFTER `tax_type`,
    ADD COLUMN `tax_priority` VARCHAR(50) NULL AFTER `tax_rate`;

INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('PRINT_LABEL_SHOW_NONVEG_DAY_COLOR', 'THU:#ff0000', '2016-03-17 17:23:12', '2016-03-17 17:23:14');


INSERT INTO `label_templates` (`name`, `attributes`, `label_per_page`, `layout`, `content`, `display_labels`, `created_date`, `modified_date`) VALUES ('10_label_half_dynamic', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_barcode":"yes","show_merchant_phone":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '10', 'colordaylayout', '<table class="outertable" cellspacing="0" cellpadding="0">\r\n    <tbody>\r\n     <tr>\r\n            <td class="left out">\r\n             <table class="innertable">\r\n     <thead>\r\n                        <tr>\r\n                     <th style="font-weight:bolder; font-size:11px;">\r\n                                <span class="left" style="width: 100px; text-align: left;">#location_name#</span>\r\n         {{<span class="left" style="width: 125px; text-align: right;">#delivery_person#</span>}}\r\n             {{<span class="left" style="width: 65px; float: right; text-align: center;">#dabbawala_code#</span>}}\r\n         </th>\r\n                        \r\n         </tr>\r\n                    </thead>\r\n                 <tbody>\r\n                        <tr class="name">\r\n                            <td class="left" ><strong>Name : #customer_name#</strong></td>\r\n     </tr>\r\n                        {{<tr class="rm_customer_phone mobile">\r\n     <td class="left" ><strong>Mobile No. :</strong>#phone#</td>\r\n </tr>}}\r\n        \r\n                        <tr class="products onlyproducts">\r\n <td class="left">\r\n <span style="font-size: 10px;"> <strong>Meal</strong> </span>\r\n                 <span class="diet">\r\n                 <font style="width:200px;color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 16px;">#products#</font>\r\n     </span>    \r\n                                <span class="nonveg">\r\n                                    \r\n                                 <font style="color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 16px;">#nonvegtext#</font>\r\n     </span>\r\n </td>\r\n                        </tr>\r\n             {{<tr class="rm_item_details products ">\r\n                         <td class="left" >\r\n                     <span style="font-size: 14px;">#items#</span>\r\n </td>\r\n                        </tr>}}\r\n                 \r\n                        <tr class="location">\r\n                            <td class="left" ><strong>Address :</strong>#ship_address#</td>\r\n     </tr>\r\n                    \r\n <tr>\r\n                            <td style="font-weight:bolder; font-size:11px;" >#company_name#\r\n                            {{<span style="font-weight:normal;font-style:italic;"> #website#</span>}}\r\n                            <span style="float:right; text-align: center; width: 92px;">#order_date#</span></td>\r\n         </tr>\r\n                    </tbody>\r\n             </table>\r\n            </td>\r\n </tr>\r\n    </tbody>\r\n</table>', 'arrange', '2015-12-22 17:20:36', '2016-03-17 18:28:34');

// Following is example key , this key type should add for each kitchen and menu.
INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('K2_BREAKFAST_AUTO_DELIVERY', '1,10:40:18', '2016-03-17 15:19:17', '2016-03-17 15:19:18');

////////////////// Added by Shilbhushan on 21-03-2016 to calculate tax on base price /////////////////////////////

ALTER TABLE `tax`
    ADD COLUMN `apply_for_catalog` INT(1) NULL DEFAULT NULL COMMENT '1 =Active & 0 = Deactive' AFTER `apply_all_product`;

ALTER TABLE `tax`
    ADD COLUMN `base_amount` VARCHAR(50) NULL DEFAULT '100' COMMENT 'Base amount to be calculated ' AFTER `apply_all_product`;


ALTER TABLE `temp_order_tax_details`
    ADD COLUMN `tax_base_amount` VARCHAR(50) NULL DEFAULT '100' AFTER `tax_priority`;


ALTER TABLE `order_tax_details`
    ADD COLUMN `tax_base_amount` VARCHAR(50) NULL DEFAULT '100' AFTER `tax_rate`;

//////////////////////// Added by Hemant on 15-04-2016 for COD Calculation /////////////////////////////////////////////////////

alter table `tax`
    add column `tax_on` enum('food', 'service') not null default 'food' after `base_amount`;

alter table `temp_order_tax_details`
    add column `tax_on` varchar(10) null default null after `tax_type`;

alter table `order_tax_details`
    add column `tax_on` varchar(10) null default null after `tax_type`;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//payment mode added in tepm_pre_orders and orders on 29 March 2016 added by Neelam

ALTER TABLE `temp_pre_orders`
ADD COLUMN `payment_mode` VARCHAR(45) NULL DEFAULT NULL AFTER `delivery_person`;

ALTER TABLE `orders`
ADD COLUMN `payment_mode` VARCHAR(45) NULL DEFAULT NULL AFTER `prefered_delivery_person_id`;

///////////////////////////// added by sankalp -  29march16 /////////////////////////////////////////////////////////////////////////////////////


INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (280,'sales','Analytics\\Controller\\Sales','avgMealPerCustomer','read','0000-00-00 00:00:00','2016-03-29 10:41:09',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (281,'sales','Analytics\\Controller\\Sales','commonPaymentMode','read','0000-00-00 00:00:00','2016-03-29 10:41:09',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (282,'sales','Analytics\\Controller\\Sales','revenueShare','read','0000-00-00 00:00:00','2016-03-29 10:41:09',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (283,'sales','Analytics\\Controller\\Sales','salesComparison','read','0000-00-00 00:00:00','2016-03-29 10:41:09',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (284,'consumer','Analytics\\Controller\\Customer','index','export','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (285,'food','Analytics\\Controller\\Food','index','read','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (286,'food','Analytics\\Controller\\Food','bestWorstMeal','read','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (287,'sales','Analytics\\Controller\\Sales','index','read','0000-00-00 00:00:00','2016-03-29 10:41:09',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (292,'consumer','Analytics\\Controller\\Customer','index','export','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (293,'food','Analytics\\Controller\\Food','index','read','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (294,'food','Analytics\\Controller\\Food','bestWorstMeal','read','0000-00-00 00:00:00','2016-03-29 11:01:45',1,'public');


INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `status`, `resource_type`) VALUES ('sales', 'Analytics\\Controller\\Sales', 'avgMealGetMonths', 'read', '', '1', 'public');
INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (279,'order','Admin\\Controller\\Order','printpackaging','read','0000-00-00 00:00:00','0000-00-00 00:00:00',1,'public');

///////////////////////////////// invoice payments ///////////////////////////

ALTER TABLE `invoice_payments`
    ADD COLUMN `service_charges` DECIMAL(10,2) NULL DEFAULT '0.00' AFTER `delivery_charges`;

INSERT INTO `acl_tpl` (`acl_tpl_id`,`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES (279,'order','Admin\\Controller\\Order','printpackaging','read','0000-00-00 00:00:00','0000-00-00 00:00:00',1,'public');

///////////////////////////// added by sankalp -  1april /////////////////////////////////////////////////////////////////////////////////////

ALTER TABLE `standard_fooddialer`.`customers`
ADD COLUMN `thirdparty_delivery_flag` TINYINT NOT NULL DEFAULT 0 AFTER `referer`;
ALTER TABLE `standard_fooddialer`.`customers`
CHANGE COLUMN `thirdparty_delivery_flag` `thirdparty_delivery_flag` ENUM('yourguy', 'roadrunner') NULL DEFAULT NULL ;

INSERT INTO `settings` (`key`, `value`) VALUES ('PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE', 'yes');


// stored procedure added by ritesh bhanushali - 4 April 2016 ////////////////////////////////////////////////////////////////

DELIMITER $$

CREATE DEFINER=`root`@`127.0.0.1` PROCEDURE `CustomerDetailReport`(IN `sdate` varchar(20), IN `edate` varchar(20))
    LANGUAGE SQL
    NOT DETERMINISTIC
    CONTAINS SQL
    SQL SECURITY DEFINER
    COMMENT ''
BEGIN
DECLARE firstDone INT DEFAULT FALSE;

DECLARE cId INT(10);
DECLARE cName varchar(45);
DECLARE oAmt DECIMAL(10,2);
DECLARE dAmt DECIMAL(10,2);
DECLARE taxAmt DECIMAL(10,2);
DECLARE del_charge_Amt DECIMAL(10,2);



DECLARE orders_summary CURSOR FOR select customer_code,customer_name,sum(tax) as taxAmt, sum(delivery_charges) as delivery_charges_amt , sum(if(tax_method='exclusive',(amount+delivery_charges+service_charges+tax-applied_discount),(amount+delivery_charges+service_charges-applied_discount))) as orderAmt ,
sum(if(order_status in ('Complete'),(if(tax_method='exclusive',(amount+delivery_charges+service_charges+tax-applied_discount),(amount+delivery_charges+service_charges-applied_discount))),0)) as DeliveredAmt
from orders
where order_status in('New','Complete') and IF(sdate != '', created_date , sdate) >= sdate and IF(edate != '', created_date , edate) <= edate group by customer_code  ;

DECLARE CONTINUE HANDLER FOR NOT FOUND SET firstDone = 1;

truncate temp_customer_account_report;

OPEN orders_summary;

firstloop: LOOP

FETCH orders_summary INTO cId,cName,taxAmt,del_charge_Amt,oAmt,dAmt;
IF firstDone THEN
close orders_summary;
LEAVE firstloop;
END IF;
INSERT INTO temp_customer_account_report
(pk_customer_code, customer_name, order_amt, delivered_amt,delivery_charges_amt,tax_amount)
VALUES
(cId, cName,oAmt, dAmt, del_charge_Amt,taxAmt);
END LOOP;

        block2: BEGIN
                DECLARE secondDone INT DEFAULT FALSE;
                DECLARE pAmt double(10,2);

                DECLARE payment_summary CURSOR FOR select fk_customer_code as customer_code,
                sum(wallet_amount) as payment_received
                from customer_wallet
                where amount_type='cr' and IF(sdate != '', payment_date , sdate) >= sdate and IF(edate != '', payment_date , edate) <= edate group by fk_customer_code ;

                DECLARE CONTINUE HANDLER FOR NOT FOUND SET secondDone = 1;

                OPEN payment_summary;

                secondloop: LOOP

                FETCH payment_summary INTO cId,pAmt;
                IF secondDone THEN
                close payment_summary;
                LEAVE secondloop;
                END IF;
                UPDATE  temp_customer_account_report SET received_amt = pAmt , ownbycompany_amt = (pAmt - delivered_amt) WHERE pk_customer_code = cId;
                END LOOP;

            END block2;

            block3: BEGIN
                DECLARE ThirdDone INT DEFAULT FALSE;
                DECLARE due DECIMAL(10,2);

                DECLARE duesummary CURSOR FOR select sum(ip.amount_due) as dueAmt , cust_ref_id
                from invoice as i
                inner join invoice_payments as ip
                on ip.invoice_ref_id = i.invoice_id
                group by cust_ref_id;

                DECLARE CONTINUE HANDLER FOR NOT FOUND SET ThirdDone = 1;

                OPEN duesummary;

                Thirdloop: LOOP

                IF ThirdDone THEN
                close duesummary;
                LEAVE Thirdloop;
                END IF;

                FETCH duesummary INTO due,cId;

                UPDATE  temp_customer_account_report SET due_amt = due  WHERE pk_customer_code = cId;
                END LOOP;

            END block3;

END


/// table for customer account report by ritesh bhanushali

CREATE TABLE `temp_customer_account_report` (
    `pk_customer_code` INT(11) NOT NULL,
    `customer_name` VARCHAR(45) NULL DEFAULT NULL,
    `order_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    `delivered_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    `delivery_charges_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    `tax_amount` DECIMAL(10,2) NULL DEFAULT '0.00',
    `received_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    `ownbycompany_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    `due_amt` DECIMAL(10,2) NULL DEFAULT '0.00',
    PRIMARY KEY (`pk_customer_code`)
)
COLLATE='utf8_general_ci'
ENGINE=MyISAM;

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


//  added by sankalp - 11th april

ALTER TABLE `orders`
ADD COLUMN `days_preference` VARCHAR(80) NULL DEFAULT NULL AFTER `payment_mode`;

ALTER TABLE `temp_pre_orders`
ADD COLUMN `days_preference` VARCHAR(80) NULL DEFAULT NULL AFTER `payment_mode`;

INSERT INTO `settings` (`key`, `value`) VALUES ('KITCHEN_WISE_WEEKOFFS', 'yes');

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `service_charges` DECIMAL(10,2) NULL DEFAULT '0.00' AFTER `delivery_charges`;
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

ALTER TABLE `temp_order_tax_details`
    ADD COLUMN `tax_on` VARCHAR(10) NULL DEFAULT 'food' AFTER `tax_type`;

ALTER TABLE `order_tax_details`
    ADD COLUMN `tax_on` VARCHAR(10) NULL DEFAULT 'food' AFTER `tax_type`;

ALTER TABLE `tax`
    ADD COLUMN `tax_on` ENUM('food','service') NOT NULL DEFAULT 'food' AFTER `base_amount`;


ALTER TABLE `third_party`
ADD COLUMN `third_party_flag` TINYINT(3) NOT NULL DEFAULT 0 AFTER `status`;

ALTER TABLE `orders`
ADD COLUMN `tp_delivery` INT NOT NULL AFTER `days_preference`,
ADD COLUMN `tp_aggregator` INT NOT NULL AFTER `tp_delivery`;

ALTER TABLE `orders`
CHANGE COLUMN `tp_delivery` `tp_delivery` INT(11) NULL DEFAULT NULL ,
CHANGE COLUMN `tp_aggregator` `tp_aggregator` INT(11) NULL DEFAULT NULL;


////////////////  29 april yourguy changes     ///////////////

ALTER TABLE `roles`
ADD COLUMN `role_type` ENUM('application','system') NULL DEFAULT 'application' AFTER `status`;

ALTER TABLE `orders`
DROP COLUMN `tp_system_order_id`,
ADD COLUMN `tp_delivery_order_id` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_delivery`,
ADD COLUMN `tp_delivery_charges` DECIMAL(10,2) NULL DEFAULT 0.00 AFTER `tp_delivery_order_id`,
ADD COLUMN `tp_delivery_charges_type` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_delivery_charges`,
ADD COLUMN `tp_aggregator_order_id` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_aggregator`,
ADD COLUMN `tp_aggregator_charges` DECIMAL(10,2) NULL DEFAULT 0.00 AFTER `tp_aggregator_order_id`,
ADD COLUMN `tp_aggregator_charges_type` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_aggregator_charges`;

ALTER TABLE `temp_pre_orders`
ADD COLUMN `tp_aggregator` VARCHAR(45) NULL DEFAULT NULL AFTER `days_preference`,
ADD COLUMN `tp_aggregator_charges` DECIMAL(10,2) NULL DEFAULT NULL AFTER `tp_aggregator`,
ADD COLUMN `tp_aggregator_charges_type` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_aggregator_charges`;

ALTER TABLE `temp_pre_orders`
DROP COLUMN `third_party_type`,
DROP COLUMN `third_party_id`,
DROP COLUMN `third_party_charges`;

ALTER TABLE `orders`
DROP COLUMN `third_party_type`,
DROP COLUMN `third_party_id`,
DROP COLUMN `third_party_charges`;

ALTER TABLE `third_party`
CHANGE COLUMN `third_party_flag` `is_aggregator` TINYINT(3) NOT NULL DEFAULT '0' COMMENT ' 0 => delivery, 1 => aggregator' ;

ALTER TABLE `third_party`
ADD COLUMN `charges_type` VARCHAR(45) NULL DEFAULT 'inclusive' AFTER `is_aggregator`;

ALTER TABLE `users`
ADD COLUMN `third_party_id` INT NULL DEFAULT NULL AFTER `auth_token`;

INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_THIRDPARTY_DELIVERY', 'yourguy');
INSERT INTO `settings` (`key`, `value`) VALUES ('YOURGUY_AUTH_TOKEN', 'MTIzNDU1NDMyMTp2ZW5kb3I=');
INSERT INTO `settings` (`key`, `value`) VALUES ('YOURGUY_API_BASE_URL', 'http://yourguytestserver.herokuapp.com/api/');
INSERT INTO `settings` (`key`, `value`) VALUES ('K1_LUNCH_YOURGUY_PICKUPTIME', '12:30:00');
INSERT INTO `settings` (`key`, `value`) VALUES ('K1_DINNER_YOURGUY_PICKUPTIME', '19:30:00');
INSERT INTO `settings` (`key`, `value`) VALUES ('YOURGUY_API_VERSION', 'v2');
INSERT INTO `settings` (`key`, `value`) VALUES ('K1_MERCHANT_ADDRESS', 'Futurescape Tech pvt.ltd. 19th floor, Cyber One, Vashi');
INSERT INTO `settings` (`key`, `value`) VALUES ('K1_MERCHANT_PINCODE', '400057');


INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('S3_BUCKET', 'development', '2016-05-03 17:13:37', '2016-05-03 17:13:38');

//////////////////////// Shilbhushan ///////////////////////////////////

ALTER TABLE `temp_order_payment`
    ADD COLUMN `bank_name` VARCHAR(255) NULL DEFAULT NULL AFTER `reference_no`;

ALTER TABLE `customers`
    ADD COLUMN `gcm_id` TEXT NULL DEFAULT NULL COMMENT 'Device Registration id on Google Cloud Messaging ' AFTER `referer`;

/////////////////////////////////

ALTER TABLE `plan_master`
ADD COLUMN `fk_kitchen_code` INT NULL DEFAULT 0 AFTER `show_to_customer`;

///////////////////////////////// Shilbhushan ////////////////////////

ALTER TABLE `customer_address`
    CHANGE COLUMN `dabbawala_code_type` `dabbawala_code_type` ENUM('text','image') NOT NULL DEFAULT 'text' AFTER `default`;

update customer_address set dabbawala_code_type = 'text' where dabbawala_code_type = '' or dabbawala_code_type is null;

INSERT INTO `acl_tpl` VALUES (297,'barcodedispatch', 'Admin\\Controller\\BarcodeDispatch', 'index', 'read', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 1, 'public');

//////////////Update by Hemant on 07/06/2016////////////////////////////////////////

ALTER TABLE `email_template`
    ADD COLUMN `send_to_admin` ENUM('yes','no') NOT NULL DEFAULT 'no' AFTER `modified_by`;


//////////////////// shilbhushan 10/06/2016 ///////////

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `source` VARCHAR(20) NULL DEFAULT 'web' COMMENT 'web/app' AFTER `apply_tax`;


//////////////////// hemant 18/06/2016 ///////////
INSERT INTO `email_template_variables` (`variable`, `content`, `type`, `created_date`) VALUES ('#order_status#', 'order status', 'other', '2016-06-17');

ALTER TABLE `third_party`
CHANGE COLUMN `email` `email` VARCHAR(50) NOT NULL ;

ALTER TABLE `third_party`
ADD COLUMN `address` VARCHAR(255) NULL DEFAULT NULL AFTER `charges_type`;

ALTER TABLE `third_party`
ADD COLUMN `thirdparty_system` VARCHAR(45) NULL DEFAULT NULL AFTER `address`;

INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_DELIVERY_TYPE', 'delivery,pickup');

ALTER TABLE `orders`
ADD COLUMN `delivery_type` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_aggregator_charges_type`;


ALTER TABLE `third_party`
ADD COLUMN `location` INT NULL DEFAULT NULL AFTER `thirdparty_system`;

ALTER TABLE `kitchen_master`
ADD COLUMN `kitchen_address` VARCHAR(255) NULL DEFAULT NULL AFTER `updated_on`;


//////////////////// Poojita 11 Jul 2016 //////////////////////////////////

REPLACE INTO `label_templates` (`pk_template_id`, `name`, `attributes`, `label_per_page`, `layout`, `content`, `display_labels`, `created_date`, `modified_date`) VALUES
    (1, '5_label_full_dynamic', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_delivery_type":"yes","show_barcode":"yes","show_merchant_phone":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '5', 'fulllayout', '<table class="outertable">\r\n <tbody>\r\n        <tr>\r\n            <td class="left out">\r\n            <table class="innertable">\r\n                <thead>\r\n             <tr class="name">\r\n <td class=""><strong>Name : </strong>#customer_name# \r\n {{<span class="rm_customer_phone" style="float:none;">(#phone#)</span>}} </td>\r\n                     <td class=""><strong></strong><span style="font-weight:Bold; font-size:18px;">#customer_code#</span></td>\r\n                 </tr>\r\n                    <tr class="name">\r\n                        <td class="" style=" vertical-align: top;"><strong>Location : </strong>#location_name#</td>\r\n </tr>\r\n                    <tr class="location">\r\n                        <td class="" style=" vertical-align: top;"><strong>Address : </strong>#ship_address#</td>\r\n </tr>\r\n                    {{<tr class="rm_dabbawala_code">\r\n <td>\r\n                            <div style="float:left">\r\n                                <span style="font-size:36px;float: left; padding-left:2px">#dabbawala_code#</span>\r\n                 </div>\r\n </td>\r\n                    </tr>}}\r\n         <tr class="date">\r\n                        <td class=""><span>#order_date#</span></td>\r\n             </tr>\r\n                </thead>\r\n         </table>\r\n            </td>\r\n <td class="left out">\r\n                <table class="innertable">\r\n                    <thead>\r\n                     <tr class="name">\r\n         <td class=""><strong>Name : </strong>#customer_name# \r\n {{<span class="rm_customer_phone" style="float:none;">(#phone#)</span>}}\r\n             </td>\r\n                            <td class=""><strong></strong>\r\n             <span style="font-weight:Bold; font-size:18px;">#customer_code#</span>\r\n                 </td>\r\n </tr>\r\n                        <tr class="rm_item_details products">\r\n     <td class=""><strong>Order Details : </strong>#products#</td>\r\n </tr>\r\n                        <tr class="date">\r\n                            <td class=""><span>#order_date#</span></td>\r\n                     </tr>\r\n </thead>\r\n                </table>\r\n </td>\r\n        </tr>\r\n </tbody>\r\n</table>', 'arrange', '2015-12-09 15:01:34', '2016-07-11 16:02:26'),
    (2, '10_label_half_static', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_barcode":"yes","show_delivery_type":"yes","show_merchant_phone":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '8', 'default', '<table class="outertable">\r\n <tbody>\r\n        <tr>\r\n            <td class="left out"> \r\n                <table class="innertable">\r\n                    <thead>\r\n                     <tr>\r\n <td style="font-weight:Bold; font-size:14px;">#company_name#\r\n     {{<span  class="rm_merchant_website" style="font-weight:normal;font-style:italic;"> #website#</span>}}\r\n </td>\r\n                        </tr>\r\n             {{<tr class="rm_merchant_phone" id="headid">\r\n                            <td>\r\n                             <span style="font-weight:normal;font-size:12px;">Mob :#company_phone#</span>\r\n </td>\r\n                        </tr>}}\r\n             </thead>\r\n <tbody>\r\n                    {{<tr class="rm_customer_code">\r\n                        <td class="left">\r\n <strong>Customer Code:</strong>#customer_code#\r\n                     </td>\r\n </tr>}}\r\n                    {{<tr class="rm_customer_name">\r\n                        <td class="left">\r\n                            <strong>Name :</strong>#customer_name#\r\n </td>\r\n                    </tr>}}\r\n         {{<tr class="rm_customer_phone">\r\n         <td class="left">\r\n <strong>Phone :</strong>#phone#\r\n     </td>\r\n                    </tr>}}\r\n             {{<tr class="rm_delivery_type">\r\n             <td class="left">\r\n <strong>Delivery type :</strong>#delivery_type#\r\n                     </td>\r\n </tr>}}\r\n                    <tr class="rm_item_details">\r\n                        <td class="left">\r\n                            <strong>Meal :</strong>#products#\r\n </td>\r\n                    </tr>\r\n     {{<tr class="rm_location">\r\n <td class="left">\r\n <strong>Address :</strong>#ship_address#\r\n                 </td>\r\n </tr>}}\r\n                    {{<tr class="rm_location_code">\r\n <td>\r\n                            <table>\r\n                         <tr class="rm_dabbawala_code_text">\r\n         <td class="left">\r\n         <div style="float:left">\r\n                     <span style="font-size:36px;float: left; padding-left:2px"> #dabbawala_code# </span>\r\n                                 <div>\r\n                 </td>\r\n </tr>\r\n                                <tr class="rm_dabbawala_code_img">\r\n         <td class="left">\r\n         <img src="#dabbawala_image#" style="float: right;width:80px;height:54px;"/>\r\n         </td>\r\n </tr>\r\n                            </table>\r\n                     </td>\r\n </tr>}}\r\n                    <tr class="date">\r\n                        <td class="left"><span>#order_date#</span>\r\n                 </td>\r\n </tr>\r\n                </tbody>\r\n </table>\r\n        </td>\r\n </tr>\r\n</tbody>\r\n</table>', 'arrange', '2015-12-22 17:20:36', '2016-07-11 16:21:35'),
    (3, '10_label_color_half_static', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_delivery_type":"yes","show_barcode":"yes","show_merchant_phone":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '10', 'colorlayout', '<table class="outertable" cellspacing="0" cellpadding="0">\r\n\r\n    <tbody>\r\n <tr>\r\n            <td class="left out"> \r\n         <table class="innertable">\r\n\r\n     <thead>\r\n                        <tr>\r\n                     <th style="font-weight:bolder; font-size:11px;">\r\n                                <span class="left" style="width: 100px; text-align: left;">#location_name#</span>\r\n         {{<span class="rm_delivery_person" style="width: 175px; text-align: right;">#delivery_person#</span>}}\r\n                         {{<span class="rm_delivery_type" style="width: 175px; text-align: right;">#delivery_type#</span>}}\r\n             {{<span class="rm_dibbawala_code" style="width: 50px; float: right; text-align: center;">#dabbawala_code#</span>}}\r\n         </th>    \r\n </tr>\r\n                    </thead>\r\n\r\n\r\n                 <tbody>\r\n                        <tr class="name">\r\n                            <td class="left"><strong>Name : #customer_name#</strong></td>\r\n     </tr>\r\n\r\n                        <tr class="rm_item_details products onlyproducts">\r\n                 <td class="left">\r\n         <div style="position: relative;float:left;width:12%;display: inline-block">\r\n                                 <div style="top: 4px; left: 0px; width:360px;">\r\n <strong>Meal :  #products#</strong>\r\n                     </div>\r\n     </div>\r\n                            </td>\r\n                     </tr>\r\n\r\n <tr class="products">\r\n                            <td class="left">\r\n                                <span style="display:block;">#food_preference#</span>\r\n                         </td>\r\n </tr>\r\n\r\n                        <tr class="location">\r\n                            <td class="left"><strong>Address : </strong>#ship_address#</td>\r\n     </tr>\r\n\r\n                        <tr>\r\n                         <td style="font-weight:bolder; font-size:11px;" >#company_name#\r\n     {{<span class="rm_merchant_website" style="font-weight:normal;font-style:italic;margin-left:5px;">#website#</span>}}\r\n                             <span style="float:right; text-align: center; width: 92px;">#order_date#</span>\r\n </td>\r\n                        </tr>\r\n         </tbody>\r\n                 </table>\r\n         </td>\r\n         </tr>\r\n </tbody>\r\n</table>', 'fixed', '2015-12-22 17:20:36', '2016-07-11 16:03:59'),
    (4, '10_label_half_dynamic', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_barcode":"yes","show_merchant_phone":"yes","show_delivery_type":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '10', 'colordaylayout', '<table class="outertable" cellspacing="0" cellpadding="0">\r\n    <tbody>\r\n     <tr>\r\n            <td class="left out">\r\n             <table class="innertable">\r\n     <thead>\r\n                        <tr>\r\n                     <th style="font-weight:bolder; font-size:11px;">\r\n                                <span class="left" style="width: 100px; text-align: left;">#location_name#</span>\r\n         {{<span class="left" style="width: 125px; text-align: right;">#delivery_person#</span>}}\r\n             {{<span class="left" style="width: 125px; text-align: right;">#delivery_type#</span>}}\r\n                         {{<span class="left" style="width: 65px; float: right; text-align: center;">#dabbawala_code#</span>}}\r\n         </th>\r\n                        \r\n         </tr>\r\n                    </thead>\r\n                 <tbody>\r\n                        <tr class="name">\r\n                            <td class="left" ><strong>Name : #customer_name#</strong></td>\r\n     </tr>\r\n                        {{<tr class="rm_customer_phone mobile">\r\n     <td class="left" ><strong>Mobile No. :</strong>#phone#</td>\r\n </tr>}}\r\n        \r\n                        <tr class="products onlyproducts">\r\n <td class="left">\r\n <span style="font-size: 10px;"> <strong>Meal</strong> </span>\r\n                 <span class="diet">\r\n                 <font style="width:200px;color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 16px;">#products#</font>\r\n     </span>    \r\n                                <span class="nonveg">\r\n                                    \r\n                                 <font style="color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 16px;">#nonvegtext#</font>\r\n     </span>\r\n </td>\r\n                        </tr>\r\n             {{<tr class="rm_item_details products ">\r\n                         <td class="left" >\r\n                     <span style="font-size: 14px;">#items#</span>\r\n </td>\r\n                        </tr>}}\r\n                 \r\n                        <tr class="location">\r\n                            <td class="left" ><strong>Address :</strong>#ship_address#</td>\r\n     </tr>\r\n                    \r\n <tr>\r\n                            <td style="font-weight:bolder; font-size:11px;" >#company_name#\r\n                            {{<span style="font-weight:normal;font-style:italic;"> #website#</span>}}\r\n                            <span style="float:right; text-align: center; width: 92px;">#order_date#</span></td>\r\n         </tr>\r\n                    </tbody>\r\n             </table>\r\n            </td>\r\n </tr>\r\n    </tbody>\r\n</table>', 'arrange', '2015-12-22 17:20:36', '2016-07-11 16:04:30'),
    (5, '10_label_half_dynamic', '{"show_customer_phone":"yes","show_dibbawala_code":"yes","show_product_details":"yes","show_delivery_type":"yes","show_barcode":"yes","show_merchant_phone":"yes","show_merchant_website":"yes"\r\n,"show_delivery_person":"yes","text_color":"MM:#f0f"}', '10', 'colordaylayout', '<table class="outertable" cellspacing="0" cellpadding="0">\n    <tbody>\n <tr>\n            <td class="left out">\n     <table class="innertable">\n <tbody>\n                        <tr class="name">\n                            <td class="left" ><strong>SOULCARE</strong></td>\n             </tr>\n                        {{<tr class="rm_customer_phone mobile">\n <td class="left" >#phone#</td>\n     </tr>}}        \n                        {{<tr class="rm_customer_code">\n                            <td class="left">\n                                CUSTOMER CODE : #customer_code#\n                            </td>\n                 </tr>}}\n                        <tr class="name">\n                            <td class="left" ><strong>DELIVERY BOY : #delivery_person#</strong></td>\n         <td class="left" ><strong>DELIVERY TYPE : #delivery_type#</strong></td>\n </tr>\n                        <tr class="name">\n                         <td class="left" ><strong>NAME : #customer_name#</strong></td>\n     </tr>\n\n                        <tr class="products onlyproducts">\n                            <td class="left">\n                                <span style="font-size: 10px;"> MEAL : </span>\n                 <span class="diet">\n             <font style="width:200px;color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 14px;">#products#</font>\n                             </span>    \n             <span class="nonveg">\n             \n                                    <font style="color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 14px;">#nonvegtext#</font>\n     </span>\n                            </td>\n                     </tr>\n                        {{<tr class="rm_item_details products ">\n <td class="left" >\n                                <span style="font-size: 14px;">#items#</span>\n             </td>\n </tr>}}\n                        \n <tr class="location">\n                            <td class="left" >#ship_address#</td>\n     </tr>\n                    \n <tr>\n                            <td style="font-weight:bolder; font-size:11px;" >\n             <span style="float:right; text-align: center; width: 92px;">#order_date#</span></td>\n             </tr>\n                    </tbody>\n             </table>\n            </td>\n </tr>\n    </tbody>\n</table>', 'arrange', '2015-12-22 17:20:36', '2016-07-11 16:43:23');

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('PRINT_LABEL_SHOW_DELIVERY_TYPE', 'yes', '2016-07-11 16:52:15', '2016-07-11 16:52:16');

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

ALTER TABLE `payment_transaction`
    CHANGE COLUMN `pre_order_id` `pre_order_id` VARCHAR(100) NULL DEFAULT NULL AFTER `transaction_charges`

/// add to mahatiffin also  ///
ALTER TABLE `temp_pre_orders`
ADD COLUMN `tp_delivery` INT(11) NULL DEFAULT NULL AFTER days_preference,
ADD COLUMN `tp_delivery_charges` DECIMAL(10,2) NULL DEFAULT 0.00 AFTER `tp_delivery`,
ADD COLUMN `tp_delivery_charges_type` VARCHAR(45) NULL DEFAULT NULL AFTER `tp_delivery_charges`;

INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('ENABLE_AUTO_DISPATCH', 'no', '2016-08-09 16:52:15', '2016-07-11 16:52:16');


///////////////////////////////////// Hemant 03/09/2016//////////////////////////////////////////////////////////////////////////////////

UPDATE `tax` SET `tax_name`='STService' WHERE  `tax_id`=3;
ALTER TABLE `customer_wallet`
    CHANGE COLUMN `description` `description` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci' AFTER `payment_date`;

ALTER TABLE `sms_queue`
    COLLATE='utf8_general_ci',
    CONVERT TO CHARSET utf8;

================== 24 Aug 2016, by shilbhushan - meal swapping columns ============

ALTER TABLE `products`
    ADD COLUMN `is_swappable` ENUM('1','0') NOT NULL DEFAULT '0' COMMENT '1 - meal swappable , 0- meal not swapable' AFTER `is_custom`,
    ADD COLUMN `swap_with` ENUM('nocharge','askdifference','swapcharge') NULL COMMENT 'nocharge = No charge applies , askdifference = Pay or refund extra amount, swapcharge = pay flat swapping charges ' AFTER `is_swappable`,
    ADD COLUMN `swap_charges` FLOAT(10,2) UNSIGNED NULL COMMENT 'swapping charges' AFTER `swap_with`;

======================= in sprint 5 for meal swapping queries ==============
ALTER TABLE `orders`
    ADD COLUMN `product_type` VARCHAR(60) NOT NULL DEFAULT 'Meal' COMMENT 'Meal , Extra' AFTER `product_description`;

ALTER TABLE `temp_pre_orders`
    CHANGE COLUMN `product_type` `product_type` VARCHAR(45) NOT NULL DEFAULT 'Meal' COMMENT '\'Meal\'  or  \'Extra\'' AFTER `product_description`;

CREATE TABLE `order_swapped_items` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `kitchen_code` INT(11) UNSIGNED NOT NULL DEFAULT '0',
    `order_no` VARCHAR(100) NOT NULL,
    `order_date` DATE NOT NULL,
    `order_menu` VARCHAR(50) NOT NULL,
    `order_bill_no` VARCHAR(50) NOT NULL,
    `product_code` INT(11) UNSIGNED NOT NULL,
    `swapped_product_code` INT(11) UNSIGNED NOT NULL,
    `swapped_product_name` VARCHAR(100) NOT NULL,
    `swapped_product_description` VARCHAR(255) NOT NULL,
    `swapped_product_type` VARCHAR(20) NOT NULL,
    `swapped_product_food_type` VARCHAR(20) NOT NULL,
    `swapped_product_items` VARCHAR(500) NOT NULL,
    `amount` FLOAT UNSIGNED NOT NULL,
    `refund` FLOAT UNSIGNED NOT NULL,
    `swap_charges` FLOAT UNSIGNED NOT NULL,
    `tax` FLOAT UNSIGNED NOT NULL,
    `tax_details` VARCHAR(500) NULL DEFAULT NULL,
    `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
)
COMMENT='History of swapped items with amount '
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;

ALTER TABLE `payment_transaction`
    ADD COLUMN `context` VARCHAR(45) NULL DEFAULT NULL AFTER `failure_url`;



==============================================================================================

Hemant Changes for Mapping meals to plan
========================================

alter table `products` add column `meal_plans` text default null after `swap_charges`;

CREATE TABLE `customer_fcm_tokens` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `fk_customer_code` INT(11) UNSIGNED NOT NULL DEFAULT '0',
    `token` VARCHAR(500) NOT NULL DEFAULT '0',
    `device` VARCHAR(20) NOT NULL DEFAULT '0',
    `status` ENUM('1','0') NOT NULL DEFAULT '1',
    `created_date` DATETIME NOT NULL,
    `modified_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;


ALTER TABLE `products`
    ADD COLUMN `product_subtype` ENUM('generic','specific') NULL COMMENT 'Define this for main and extra in product_type' AFTER `meal_plans`;

CREATE TABLE IF NOT EXISTS `product_planner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date DEFAULT NULL,
  `menu` varchar(50) DEFAULT NULL,
  `fk_kitchen_code` int(11) DEFAULT NULL,
  `generic_product_code` int(11) NOT NULL,
  `specific_product_code` int(11) NOT NULL,
  `specific_product_name` varchar(50) DEFAULT NULL,
  `swap_with` enum('nocharge','askdifference','swapcharge') DEFAULT NULL,
  `swap_charges` float DEFAULT NULL,
  `isdefault` enum('yes','no') DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT NULL,
  `modified_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COMMENT='Mapping specific products to generic';

Setting table changes
========================================

INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('GLOBAL_ALLOW_MENU_PLANNER', 'yes', '2016-09-30 16:21:07', '2016-09-30 16:21:08');
INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('GLOBAL_PUBLISH_MENU_PLANNER', 'yes', '2016-09-30 16:20:16', '2016-09-30 16:20:17');
INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('GLOBAL_ALLOW_MEAL_ITEM_SWAP', 'yes', '2016-09-30 16:20:43', '2016-09-30 16:20:44');


Swaping table alter
========================================
ALTER TABLE `order_swapped_items`
    CHANGE COLUMN `amount` `amount` FLOAT UNSIGNED NOT NULL DEFAULT '0' AFTER `swapped_product_items`,
    CHANGE COLUMN `refund` `refund` FLOAT UNSIGNED NULL DEFAULT '0' AFTER `amount`,
    CHANGE COLUMN `swap_charges` `swap_charges` FLOAT UNSIGNED NULL DEFAULT '0' AFTER `refund`,
    ADD COLUMN `extra_amount` FLOAT UNSIGNED NULL DEFAULT '0' AFTER `swap_charges`,
    CHANGE COLUMN `tax` `tax` FLOAT UNSIGNED NULL DEFAULT '0' AFTER `extra_amount`;

Database changes for set-preferences (product-swapping) (24/11/2016)
====================================================================
ALTER TABLE `products`
    CHANGE COLUMN `swap_with` `swap_with` ENUM('nocharge','askdifference','swapcharge','aspercategory') NULL DEFAULT NULL COMMENT 'nocharge = No charge applies , askdifference = Pay or refund extra amount, swapcharge = pay flat swapping charges ' AFTER `is_swappable`;

ALTER TABLE `order_swapped_items`
    ADD COLUMN `order_meal_qty` TINYINT(4) UNSIGNED NOT NULL AFTER `order_bill_no`;


/* spicebox. oct19. setting key ...sankalp */
INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_DELIVERY_CHARGES', '30');

ALTER TABLE `plan_master`
ADD COLUMN `plan_name_alias` VARCHAR(45) NULL DEFAULT NULL AFTER `plan_name`;


/* table product planner - hemant  */
CREATE TABLE `product_planner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date DEFAULT NULL,
  `menu` varchar(50) DEFAULT NULL,
  `fk_kitchen_code` int(11) DEFAULT NULL,
  `generic_product_code` int(11) NOT NULL,
  `specific_product_code` int(11) NOT NULL,
  `specific_product_name` varchar(50) DEFAULT NULL,
  `swap_with` enum('nocharge','askdifference','swapcharge') DEFAULT NULL,
  `swap_charges` float DEFAULT NULL,
  `isdefault` enum('yes','no') DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT NULL,
  `modified_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=latin1 COMMENT='Mapping specific products to generic';


/*  Promocode for wallet - 28Nov sankalp */
ALTER TABLE `promo_codes`
ADD COLUMN `applied_on` ENUM('order', 'wallet', 'registration') NOT NULL DEFAULT 'order' AFTER `Product_order_quantity`,
ADD COLUMN `wallet_amount` DECIMAL(10,2) NULL DEFAULT NULL COMMENT 'min amount on which discount is applied' AFTER `applied_on`;

ALTER TABLE `promo_codes`
    ADD COLUMN `promo_type` VARCHAR(45) NOT NULL DEFAULT 'discount' AFTER `promo_code`;

ALTER TABLE `promo_codes`
CHANGE COLUMN `product_code` `product_code` TEXT NULL ;

ALTER TABLE `payment_transaction`
ADD COLUMN `promo_code` VARCHAR(45) NULL DEFAULT NULL AFTER `context`,
ADD COLUMN `discount` DECIMAL(10,2) NULL DEFAULT NULL COMMENT 'Flat value in Rs.' AFTER `promo_code`;

///////////////////// Instant Order //////////////////////
ALTER TABLE `orders`
    ADD COLUMN `delivery_time` TIME NULL DEFAULT NULL AFTER `delivery_type`;

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `delivery_time` TIME NULL DEFAULT NULL AFTER `delivery_type`;

ALTER TABLE `delivery_locations`
    ADD COLUMN `delivery_time` VARCHAR(10) NULL DEFAULT '30' AFTER `delivery_charges`;

===========================================================================

ALTER TABLE `temp_pre_orders`
    CHANGE COLUMN `order_for` `order_for` ENUM('customized','fixed','instant') NOT NULL DEFAULT 'fixed' AFTER `total_third_party_charges`;


INSERT INTO `settings` (`key`, `value`, `created_date`, `modified_date`) VALUES ('GLOBAL_ALLOW_INSTANT_ORDER', 'yes', '2017-01-10 15:03:28', '2017-01-10 15:03:30');

ALTER TABLE `order_details`
    ADD COLUMN `product_amount` DECIMAL(10,2) UNSIGNED NULL DEFAULT NULL AFTER `order_date`;

ALTER TABLE `order_details`
    ADD COLUMN `product_tax` DECIMAL(10,2) UNSIGNED NULL DEFAULT NULL AFTER `product_amount`;

///////////////////////////////////promo code datatype///////////////////////////////////

ALTER TABLE `orders`
    CHANGE COLUMN `promo_code` `promo_code` VARCHAR(50) NULL DEFAULT NULL AFTER `quantity`;

/* partial payment - 25jan17*/
INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_ALLOW_PARTIAL_PAYMENT', 'yes');

////////////////////////////Promo code datatype change //////////////////////////////////////

ALTER TABLE `orders`
    CHANGE COLUMN `promo_code` `promo_code` VARCHAR(50) NULL DEFAULT NULL AFTER `quantity`;

ALTER TABLE `payment_transaction`
ADD COLUMN `wallet_amount` DECIMAL(10,2) DEFAULT NULL
COMMENT 'partial payment field' AFTER `transaction_charges`;

ALTER TABLE `customer_wallet`
CHANGE COLUMN `amount_type` `amount_type` ENUM('cr', 'dr', 'lock') NULL DEFAULT NULL ;

ALTER TABLE `customer_wallet`
CHANGE COLUMN `payment_type` `payment_type` ENUM('neft', 'cash', 'cheque', 'online', 'wallet', 'partial') NOT NULL DEFAULT 'cash' ;
/* partial payment ends */

/*Email_Address field Add*/
ALTER TABLE `orders`
ADD COLUMN `email_address` VARCHAR(45) NULL DEFAULT NULL AFTER `phone`;

ALTER TABLE `temp_pre_orders`
ADD COLUMN `email_address` VARCHAR(45) NULL DEFAULT NULL AFTER `phone`;


ALTER TABLE `temp_pre_orders`
ADD COLUMN `item_preference` TEXT NULL DEFAULT NULL AFTER `delivery_time`;

ALTER TABLE `order_details`
    ADD COLUMN `product_subtype` VARCHAR(50) NULL DEFAULT NULL AFTER `product_tax`,
    ADD COLUMN `product_generic_code` INT NULL DEFAULT NULL AFTER `product_subtype`,
    ADD COLUMN `product_generic_name` VARCHAR(100) NULL DEFAULT NULL AFTER `product_generic_code`;


ALTER TABLE `customer_address`
    ADD COLUMN `location_zipcode` VARCHAR(250) NULL AFTER `location_address`;




=======


=============== Country table =======================
/* Note : Insert countries data also otherwise order will not place.
*
CREATE TABLE `countries` (
    `pk_country_id` INT(5) NOT NULL AUTO_INCREMENT,
    `country_code` CHAR(2) NOT NULL DEFAULT '',
    `country_code_3d` CHAR(3) NULL DEFAULT NULL,
    `country_name` VARCHAR(256) NOT NULL DEFAULT '',
    `language_code` CHAR(5) NULL DEFAULT '',
    `currency_code` CHAR(3) NULL DEFAULT NULL,
    `active` INT(11) NULL DEFAULT NULL,
    `iso_code` VARCHAR(50) NULL DEFAULT NULL,
    `entity_code` VARCHAR(50) NULL DEFAULT NULL,
    PRIMARY KEY (`pk_country_id`)
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=251;

*/
/*Prom_code menu and plan based 16 Mar17 Ashwini*/
ALTER TABLE `promo_codes`
ADD COLUMN `menu_type` VARCHAR(50) NULL DEFAULT NULL AFTER `wallet_amount`;

ALTER TABLE `promo_codes`
ADD COLUMN `menu_operator` ENUM('&&','||') NULL DEFAULT NULL AFTER `menu_type`;


ALTER TABLE `plan_master`
ADD COLUMN `promo_code` VARCHAR(45) NULL DEFAULT NULL AFTER `fk_kitchen_code`;


ALTER TABLE `promo_codes`
CHANGE COLUMN `promo_limit` `promo_limit` INT(11) NULL DEFAULT NULL COMMENT 'promo limit' ;

ALTER TABLE `temp_pre_orders`
ADD COLUMN `system_promo_code` VARCHAR(45) NULL DEFAULT NULL AFTER `promo_code`;

ALTER TABLE `temp_pre_orders`
ADD COLUMN `product_price` DECIMAL(10,2) NULL DEFAULT NULL AFTER `system_promo_code`;

ALTER TABLE `orders`
ADD COLUMN `system_promo_code` VARCHAR(45) NULL DEFAULT NULL AFTER `promo_code`;

ALTER TABLE `orders`
ADD COLUMN `product_price` DECIMAL(10,2) NULL DEFAULT NULL AFTER `system_promo_code`;

ALTER TABLE `temp_pre_orders`
CHANGE COLUMN `applied_discount` `applied_discount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' ;

ALTER TABLE `orders`
CHANGE COLUMN `applied_discount` `applied_discount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' ;


ALTER TABLE `promo_codes`
ADD COLUMN `min_amount` DECIMAL(10,2) NULL DEFAULT NULL AFTER `amount`;

ALTER TABLE `promo_codes`
CHANGE COLUMN `promo_type` `promo_type` ENUM('discount','cashback') NOT NULL ;

ALTER TABLE `promo_codes`
CHANGE COLUMN `discount_type` `discount_type` ENUM('fixed','percentage') NOT NULL ;



ALTER TABLE `promo_codes`
CHANGE COLUMN `promo_limit` `promo_limit` INT(11) NULL COMMENT 'promo limit' ;

ALTER TABLE `promo_codes`
CHANGE COLUMN `applied_on` `applied_on` ENUM('order','wallet','registration','menu') NOT NULL DEFAULT 'order' ;

ALTER TABLE `promo_codes`
CHANGE COLUMN `Product_order_quantity` `Product_order_quantity` INT(11) NULL ;

ALTER TABLE `promo_codes`
CHANGE COLUMN `applied_on` `applied_on` ENUM('order','wallet','registration','menu','plan') NOT NULL DEFAULT 'order' ;


========================= added delivery time end time ========================

ALTER TABLE `orders`
    ADD COLUMN `delivery_end_time` TIME NULL DEFAULT NULL AFTER `delivery_time`;

ALTER TABLE `temp_pre_orders`
    ADD COLUMN `delivery_end_time` TIME NULL DEFAULT NULL AFTER `delivery_time`;


ALTER TABLE `promo_codes`
CHANGE COLUMN `menu_type` `menu_type` VARCHAR(150) NULL DEFAULT NULL ;

ALTER TABLE `plan_master`
CHANGE COLUMN `promo_code` `fk_promo_code` INT(11) NULL DEFAULT NULL ;



=====================mealplan for weekwise pradeep maurya 30 march 17=====================

INSERT INTO `acl_tpl` (`module`,`controller`,`action`,`type`,`added_on`,`modified_on`,`status`,`resource_type`) VALUES ('meal','Admin\\Controller\\Product','mealplan','write','0000-00-00 00:00:00','0000-00-00 00:00:00',1,'public');

======== Increases pre_order_id size to text in payment transaction =============
ALTER TABLE `payment_transaction` 
CHANGE COLUMN `pre_order_id` `pre_order_id` TEXT NULL DEFAULT NULL ;




=============================================================================================
CREATE TABLE `theme_master` (
  `id` INT(11) NOT NULL,
  `theme_name` VARCHAR(45) NOT NULL,
  `status` INT(11) NOT NULL,
  `created_date` DATE NOT NULL,
  `modified_date` DATE NOT NULL,
  PRIMARY KEY (`id`));


CREATE TABLE `theme_skin_mapping` (
  `id` INT(11) NOT NULL,
  `theme_id` INT(11) NOT NULL,
  `skin_name` VARCHAR(45) NOT NULL,
  `status` INT(11) NOT NULL,
  `created_date` DATE NOT NULL,
  `modified_date` DATE NOT NULL,
  PRIMARY KEY (`id`));


CREATE TABLE `theme_style_mapping` (
  `id` INT(11) NOT NULL,
  `theme_id` INT(11) NOT NULL,
  `style_name` VARCHAR(45) NOT NULL,
  `status` INT(11) NOT NULL,
  `created_date` DATE NOT NULL,
  `modified_date` DATE NOT NULL,
  PRIMARY KEY (`id`));


INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_THEME', 'Theme1');
INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_STYLE', 'Style1');
INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_SKIN', 'Skin3');


============================================

INSERT INTO `settings` (`key`, `value`) VALUES ('GLOBAL_SKIP_KITCHEN', 'yes');

===========================================


ALTER TABLE `orders`
	ALTER `product_description` DROP DEFAULT;

ALTER TABLE `orders`
	CHANGE COLUMN `product_description` `product_description` VARCHAR(60) NULL COLLATE 'utf8_unicode_ci' AFTER `product_name`;	

ALTER TABLE `temp_pre_orders`
	CHANGE COLUMN `total_amt` `total_amt` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Total Price for days and quantity ' AFTER `amount`;

ALTER TABLE `temp_pre_orders`
	CHANGE COLUMN `product_description` `product_description` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `product_name`;


ALTER TABLE `orders` 
CHANGE COLUMN `` `remark` VARCHAR(225) NULL ;

=========================Recurring Orders=============================

CREATE TABLE IF NOT EXISTS `recurring_orders` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `unit_id` int(10) unsigned NOT NULL,
  `order_no` varchar(50) NOT NULL,
  `order_date` date NOT NULL,
  `day_preferences` varchar(50) DEFAULT NULL,
  `recurring_amount` decimal(10,2) NOT NULL,
  `customer_name` varchar(50) NOT NULL,
  `customer_id` int(10) NOT NULL,
  `card_type` varchar(10) DEFAULT NULL,
  `ta_token` varchar(50) DEFAULT NULL,
  `cc_expiry` varchar(4) DEFAULT NULL,
  `status` char(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

ALTER TABLE `orders`
  ADD COLUMN `recurring_status` ENUM('1','0') NULL DEFAULT '0' COMMENT 'Same Order is placed when set 1' AFTER `delivery_end_time`;

ALTER TABLE `temp_pre_orders`
  ADD COLUMN `recurring_status` ENUM('1','0') NULL DEFAULT '0' COMMENT 'Same Order is placed when set 1' AFTER `item_preference`;

ALTER TABLE `payment_transaction`
  ADD COLUMN `recurring` INT(11) NULL DEFAULT NULL AFTER `discount`;

ALTER TABLE `temp_order_payment`
  ADD COLUMN `recurring_status` ENUM('1','0') NULL AFTER `order_menu`;
    
ALTER TABLE `temp_order_payment`
  CHANGE COLUMN `recurring_status` `recurring_status` ENUM('1','0') NULL DEFAULT '0' COLLATE 'utf8_unicode_ci' AFTER `order_menu`;

INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('city', 'Admin\\Controller\\City', 'index', 'read', '2017-07-04 11:34:58', '2017-07-04 01:14:00', 1, 'public');
INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('city', 'Admin\\Controller\\City', 'add', 'write', '2017-07-04 11:34:58', '2017-07-04 11:34:58', 1, 'public');
INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('city', 'Admin\\Controller\\City', 'edit', 'write', '2017-07-04 11:34:58', '2017-07-04 11:34:58', 1, 'public');
INSERT INTO `acl_tpl` (`module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('city', 'Admin\\Controller\\City', 'delete', 'delete', '2017-07-04 11:34:58', '2017-07-04 11:34:58', 1, 'public');

/////////////////////// CustomerDetailReport /////////////////////////////////

delimiter //

CREATE PROCEDURE `CustomerDetailReport`(IN `sdate` varchar(20), IN `edate` varchar(20), IN `comid` int(10), IN `unitid` int(10))
	LANGUAGE SQL
	NOT DETERMINISTIC
	CONTAINS SQL
	SQL SECURITY DEFINER
	COMMENT ''
BEGIN
            DECLARE firstDone INT DEFAULT FALSE;

            DECLARE cId INT(10);
            DECLARE cName varchar(45);
            DECLARE oAmt DECIMAL(10,2);
            DECLARE dAmt DECIMAL(10,2);
            DECLARE taxAmt DECIMAL(10,2);
            DECLARE del_charge_Amt DECIMAL(10,2);



            DECLARE orders_summary CURSOR FOR select customer_code,customer_name,sum(tax) as taxAmt, sum(delivery_charges) as delivery_charges_amt , sum(if(tax_method='exclusive',(amount+delivery_charges+service_charges+tax-applied_discount),(amount+delivery_charges+service_charges-applied_discount))) as orderAmt , 
            sum(if(order_status in ('Complete'),(if(tax_method='exclusive',(amount+delivery_charges+service_charges+tax-applied_discount),(amount+delivery_charges+service_charges-applied_discount))),0)) as DeliveredAmt 
            from orders
            where company_id=comid and unit_id=unitid and order_status in('New','Complete') and IF(sdate != '', created_date , sdate) >= sdate and IF(edate != '', created_date , edate) <= edate group by customer_code  ;

            DECLARE CONTINUE HANDLER FOR NOT FOUND SET firstDone = 1;

            truncate temp_customer_account_report;

            OPEN orders_summary;

            firstloop: LOOP

            FETCH orders_summary INTO cId,cName,taxAmt,del_charge_Amt,oAmt,dAmt;
            IF firstDone THEN
            close orders_summary;
            LEAVE firstloop;
            END IF;
            INSERT INTO temp_customer_account_report
            (company_id,unit_id,pk_customer_code, customer_name, order_amt, delivered_amt,delivery_charges_amt,tax_amount)
            VALUES
            (comid,unitid,cId, cName,oAmt, dAmt, del_charge_Amt,taxAmt);
            END LOOP;

                    block2: BEGIN
                            DECLARE secondDone INT DEFAULT FALSE;
                            DECLARE pAmt double(10,2);

                            DECLARE payment_summary CURSOR FOR select fk_customer_code as customer_code,
                            sum(wallet_amount) as payment_received
                            from customer_wallet 
                            where company_id=comid and unit_id=unitid and amount_type='cr' and IF(sdate != '', payment_date , sdate) >= sdate and IF(edate != '', payment_date , edate) <= edate group by fk_customer_code ;

                            DECLARE CONTINUE HANDLER FOR NOT FOUND SET secondDone = 1;

                            OPEN payment_summary;

                            secondloop: LOOP

                            FETCH payment_summary INTO cId,pAmt;
                            IF secondDone THEN
                            close payment_summary;  
                            LEAVE secondloop;
                            END IF;
                            UPDATE  temp_customer_account_report SET received_amt = pAmt , ownbycompany_amt = (pAmt - delivered_amt) WHERE pk_customer_code = cId;  
                            END LOOP;

                        END block2;

                        block3: BEGIN
                            DECLARE ThirdDone INT DEFAULT FALSE;
                            DECLARE due DECIMAL(10,2);

                            DECLARE duesummary CURSOR FOR select sum(ip.amount_due) as dueAmt , cust_ref_id 
                            from invoice as i
                            inner join invoice_payments as ip
                            on ip.invoice_ref_id = i.invoice_id
                            where i.company_id = comid and i.unit_id = unitid
                            group by cust_ref_id;

                            DECLARE CONTINUE HANDLER FOR NOT FOUND SET ThirdDone = 1;

                            OPEN duesummary;

                            Thirdloop: LOOP

                            IF ThirdDone THEN
                            close duesummary;  
                            LEAVE Thirdloop;
                            END IF;
                            
                            FETCH duesummary INTO due,cId;
                            
                            UPDATE  temp_customer_account_report SET due_amt = due  WHERE pk_customer_code = cId;  
                            END LOOP;

                        END block3;

            END//
            
delimiter ;            

/////////////////////// CustomerDetailReport End /////////////////////////////////			

//////////////////pradeep acl_transactions 07/08/2017/////////////////////

INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('8', '8', '1', 'Admin', 'city', 'add', 'Admin\\Controller\\City', 'write', '1', '2017-07-20 05:30:00', '2017-08-07 05:33:39', '1', 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('8', '8', '1', 'Admin', 'city', 'edit', 'Admin\\Controller\\City', 'write', '1', '2017-07-20 05:30:00', '2017-08-07 05:33:39', '1', 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES ('8', '8', '1', 'Admin', 'city', 'delete', 'Admin\\Controller\\City', 'delete', '1', '2017-07-20 05:30:00', '2017-08-07 05:33:39', '1', 'public');

ALTER TABLE `test_qs_165`.`payment_transaction` 
CHANGE COLUMN `gateway_transaction_id` `gateway_transaction_id` VARCHAR(200) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

/////////////////////////////////////// Timeslot ////////////////////////////////////////////////////

INSERT INTO `acl_tpl` (`company_id`, `unit_id`, `module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 'timeslot', 'Admin\\Controller\\Timeslot', 'add', 'write', '2018-07-11 15:33:05', '2018-07-11 15:33:34', 1, 'public');
INSERT INTO `acl_tpl` (`company_id`, `unit_id`, `module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 'timeslot', 'Admin\\Controller\\Timeslot', 'edit', 'write', '2018-07-11 15:33:46', '2018-07-11 15:34:14', 1, 'public');
INSERT INTO `acl_tpl` (`company_id`, `unit_id`, `module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 'timeslot', 'Admin\\Controller\\Timeslot', 'delete', 'delete', '2018-07-11 15:34:23', '2018-07-11 15:34:47', 1, 'public');
INSERT INTO `acl_tpl` (`company_id`, `unit_id`, `module`, `controller`, `action`, `type`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 'timeslot', 'Admin\\Controller\\Timeslot', 'create', 'write', '2018-07-14 12:59:23', '2018-07-14 12:59:51', 1, 'public');

INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 1, 'Admin', 'timeslot', 'index', 'Admin\\Controller\\Timeslot', 'read', '1', '2018-07-11 00:00:00', '2018-07-11 15:36:46', 1, 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 1, 'Admin', 'timeslot', 'add', 'Admin\\Controller\\Timeslot', 'write', '1', '2018-07-11 00:00:00', '2018-07-11 15:36:46', 1, 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 1, 'Admin', 'timeslot', 'edit', 'Admin\\Controller\\Timeslot', 'write', '1', '2018-07-11 00:00:00', '2018-07-11 15:36:46', 1, 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 1, 'Admin', 'timeslot', 'delete', 'Admin\\Controller\\Timeslot', 'delete', '1', '2018-07-11 00:00:00', '2018-07-11 15:36:46', 1, 'public');
INSERT INTO `acl_transactions` (`company_id`, `unit_id`, `role_id`, `role`, `module_name`, `action`, `controller`, `type`, `allowed`, `added_on`, `modified_on`, `status`, `resource_type`) VALUES (165, 165, 1, 'Admin', 'timeslot', 'create', 'Admin\\Controller\\Timeslot', 'write', '1', '2018-07-14 13:01:11', '2018-07-16 15:06:42', 1, 'public');

INSERT INTO `settings` (`company_id`, `unit_id`, `key`, `value`, `created_date`, `modified_date`) VALUES (165, 165, 'GLOBAL_ALLOW_TIMESLOT', 'yes', '2018-07-11 17:45:23', '2018-07-11 18:21:35');

CREATE TABLE `timeslot` (
  `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `unit_id` INT(10) UNSIGNED NOT NULL DEFAULT '0',
  `starttime` VARCHAR(50) NOT NULL DEFAULT '00:00:00',
  `endtime` VARCHAR(50) NOT NULL DEFAULT '00:00:00',
  `day` VARCHAR(15) NOT NULL DEFAULT '00:00:00',
  `menu_type` VARCHAR(15) NOT NULL DEFAULT '0',
  `kitchen` VARCHAR(15) NOT NULL DEFAULT '0',
  `status` INT(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
AUTO_INCREMENT=7;

ALTER TABLE stg_quickserve_38.products MODIFY name VARCHAR(75);

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//////////Sheefa Image Alt Tag for SEO//////////////

alter table `test_qs_165`.`image` 
ADD COLUMN `image_alt_tag` VARCHAR(100) AFTER `image_title`;


///// SHEEFA (15-10-18)- Remark on the Print Label ////////////////


UPDATE `label_templates` SET `content`='<table class="outertable">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<thead>\n<tr class="name">\n<td class=""><strong>Name : </strong>#customer_name# \n{{<span class="rm_customer_phone" style="float:none;">(#phone#)</span>}} </td>\n<td class=""><strong></strong><span style="font-weight:Bold; font-size:18px;">#customer_code#</span></td>\n</tr>\n<tr class="name">\n<td class="" style=" vertical-align: top;"><strong>Location : </strong>#location_name#</td>\n</tr>\n<tr class="location">\n<td class="" style=" vertical-align: top;"><strong>Address : </strong>#ship_address#</td>\n</tr>\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n{{<tr class="rm_dabbawala_code">\n<td>\n<div style="float:left">\n<span style="font-size:36px;float: left; padding-left:2px">#dabbawala_code#</span>\n</div>\n</td>\n</tr>}}\n<tr class="date">\n<td class=""><span>#order_date#</span></td>\n</tr>\n</thead>\n</table>\n</td>\n<td class="left out">\n<table class="innertable">\n<thead>\n<tr class="name">\n<td class=""><strong>Name : </strong>#customer_name# \n{{<span class="rm_customer_phone" style="float:none;">(#phone#)</span>}}\n</td>\n<td class=""><strong></strong>\n<span style="font-weight:Bold; font-size:18px;">#customer_code#</span>\n</td>\n</tr>\n<tr class="rm_item_details products">\n<td class=""><strong>Order Details : </strong>#products#</td>\n</tr>\n<tr class="date">\n<td class=""><span>#order_date#</span></td>\n</tr>\n</thead>\n</table>\n</td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=1;

UPDATE `label_templates` SET `content`='<table class="outertable">\n<tbody>\n<tr>\n<td class="left out"> \n<table class="innertable">\n<thead>\n<tr>\n<td style="font-weight:Bold; font-size:14px;">#company_name#\n{{<span  class="rm_merchant_website" style="font-weight:normal;font-style:italic;"> #website#</span>}}\n</td>\n</tr>\n{{<tr class="rm_merchant_phone" id="headid">\n<td>\n<span style="font-weight:normal;font-size:12px;">Mob :#company_phone#</span>\n</td>\n</tr>}}\n</thead>\n<tbody>\n{{<tr class="rm_customer_code">\n<td class="left">\n<strong>Customer Code:</strong>#customer_code#\n</td>\n</tr>}}\n{{<tr class="rm_customer_name">\n<td class="left">\n<strong>Name :</strong>#customer_name#\n</td>\n</tr>}}\n{{<tr class="rm_customer_phone">\n<td class="left">\n<strong>Phone :</strong>#phone#\n</td>\n</tr>}}\n{{<tr class="rm_delivery_type">\n<td class="left">\n<strong>Delivery type :</strong>#delivery_type#\n</td>\n</tr>}}\n<tr  class="rm_item_details">\n<td class="left">\n<strong>Meal :</strong>#products#\n</td>\n</tr>\n{{<tr class="rm_location">\n<td class="left">\n<strong>Address :</strong>#ship_address#\n</td>\n</tr>}}\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n{{<tr class="rm_dibbawala_code">\n<td>\n<table>\n<tr class="rm_dabbawala_code_text">\n<td class="left">\n<div style="float:left">\n<span style="font-size:36px;float: left; padding-left:2px"> #dabbawala_code# </span>\n<div>\n</td>\n</tr>\n<tr class="rm_dabbawala_code_img">\n<td class="left">\n<img src="#dabbawala_image#" style="float: right;width:80px;height:54px;"/>\n</td>\n</tr>\n</table>\n</td>\n</tr>}}\n{{<tr class="rm_barcode">\n<td class="left">\n<IMG SRC="data:image/gif;base64,#barcode#" />\n</td>\n</tr>}}\n<tr class="date">\n<td class="left"><span>#order_date#</span>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=2;

UPDATE `label_templates` SET `content`='<table class="outertable" cellspacing="0" cellpadding="0">\n\n<tbody>\n<tr>\n<td class="left out"> \n<table class="innertable">\n\n<thead>\n<tr>\n<th style="font-weight:bolder; font-size:11px;">\n<span class="left" style="width: 100px; text-align: left;">#location_name#</span>\n{{<span class="rm_delivery_person" style="width: 175px; text-align: right;">#delivery_person#</span>}}\n{{<span class="rm_delivery_type" style="width: 175px; text-align: right;">#delivery_type#</span>}}\n{{<span class="rm_dibbawala_code" style="width: 50px; float: right; text-align: center;">#dabbawala_code#</span>}}\n</th>  \n</tr>\n</thead>\n\n\n<tbody>\n<tr class="name">\n<td class="left"><strong>Name : #customer_name#</strong></td>\n</tr>\n\n<tr class="rm_item_details products onlyproducts">\n<td class="left">\n<div style="position: relative;float:left;width:12%;display: inline-block">\n<div style="top: 4px; left: 0px; width:360px;">\n<strong>Meal :  #products#</strong>\n</div>\n</div>\n</td>\n</tr>\n\n<tr class="products">\n<td class="left">\n<span style="display:block;">#food_preference#</span>\n</td>\n</tr>\n\n<tr class="location">\n<td class="left"><strong>Address :  </strong>#ship_address#</td>\n</tr>\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n<tr>\n<td style="font-weight:bolder; font-size:11px;" >#company_name#\n{{<span class="rm_merchant_website" style="font-weight:normal;font-style:italic;margin-left:5px;">#website#</span>}}\n<span style="float:right; text-align: center; width: 92px;">#order_date#</span>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=3;

UPDATE `label_templates` SET `content`='<table class="outertable">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<thead>\n<tr>\n<td style="font-weight:Bold; font-size:14px;">\n#company_name#\n{{<span style="font-weight:normal;font-style:italic;"> #website#</span>}}\n</td>\n</tr>\n{{<tr class="rm_merchant_phone" id="headid">\n<td>\n<span style="font-weight:normal;font-size:10px;">Mob :#company_phone#</span>\n</td>\n</tr>}}\n</thead>\n<tbody>\n{{<tr class="rm_customer_name">\n<td class="left">\n<strong>Name :</strong>#customer_name#\n</td>\n</tr>}}\n{{<tr class="rm_customer_phone">\n<td class="left">\n<strong>Phone :</strong>#phone#\n</td>\n</tr>}}\n<tr class="products ">\n<td class="left"><strong>Meal :</strong> #products#</td>\n</tr>\n{{<tr class="rm_price">\n<td class="left"><strong>Meal Price :</strong> #net_amount#</td>\n</tr>}}\n{{<tr class="rm_location">\n<td class="left"><strong>Address :</strong>#ship_address#</td>\n</tr>}}\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n{{<tr class="rm_dibbawala_code">\n<td>\n\n<span class="rm_dabbawala_code_text" style="font-size:20px;float: left; padding-left:2px">#dabbawala_code#</span>\n<span>#order_date#</span>\n</td>\n</tr>}}\n{{<tr class="rm_barcode">\n<td class="left">\n<IMG SRC="data:image/gif;base64,#barcode#" />\n</td>\n</tr>}}\n</tbody>\n</table></td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=4;

UPDATE `label_templates` SET `content`='<table class="outertable" cellspacing="0" cellpadding="0">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<tbody>\n<tr class="name">\n<td class="left" ><strong>#company_name#</strong></td>\n</tr>\n{{<tr class="rm_customer_phone mobile">\n<td class="left" >#phone#</td>\n</tr>}}   \n{{<tr class="rm_customer_code">\n<td class="left">\nCUSTOMER CODE : &nbsp;&nbsp;&nbsp;&nbsp;<strong><font style="font-size: 22px;">#customer_code#</font></strong>\n</td>\n</tr>}}\n<tr class="name">\n<td class="left" ><strong>DELIVERY BOY : #delivery_person#</strong></td>\n\n</tr>\n<tr class="name">\n<td class="left" ><strong>NAME : #customer_name#</strong></td>\n</tr>\n\n<tr  class="rm_item_details">\n<td class="left">\n<strong>Meal :</strong>#products#\n</td>\n</tr>\n\n<tr class="location">\n<td class="left" >#ship_address#</td>\n</tr>\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n{{<tr class="rm_barcode">\n<td class="left">\n<IMG SRC="data:image/gif;base64,#barcode#" />\n</td>\n</tr>}}\n\n<tr>\n<td style="font-weight:bolder; font-size:11px;" >\n<span style="float:left; text-align: left; width: 92px;">#weekday#</span>\n<span style="float:right; text-align: center; width: 92px;">#order_date#</span></td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=5;

UPDATE `label_templates` SET `content`='<table class="outertable">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<tbody>\n<tr>\n<td class="left"><b>#label_count#</b></td>\n</tr>\n<tr class="name">\n<td class="left">#customer_name# : #phone# [#order_date#]</td>\n</tr>\n<tr class="name">\n<td class="left">#products#</td>\n</tr>          \n<tr class="location">\n<td class="left">#ship_address#</td>\n</tr>\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n</tbody>\n</table></td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=6;

UPDATE `label_templates` SET `content`='<table class="outertable">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<thead>\n<tr>\n<td style="font-weight:Bold; font-size:14px;">\n#company_name#\n{{<span style="font-weight:normal;font-style:italic;">visit :  #website#</span>}}\n</td>\n</tr>\n{{<tr class="rm_merchant_phone" id="headid">\n<td>\n<span style="font-weight:normal;font-size:10px;">Call Us : #company_phone#</span>\n</td>\n</tr>}}\n</thead>\n<tbody>\n<tr class="rm_customer_name">\n<td class="left">\n<strong>Bill No. :</strong>#pk_order_no#\n</td>\n</tr>\n<tr class="rm_paid_status">\n<td class="left">\n<strong>Payment Status :</strong>#paid_status#\n</td>\n</tr>\n{{<tr class="rm_customer_name">\n<td class="left">\n<strong>Customer :</strong>#customer_name#\n</td>\n</tr>}}\n{{<tr class="rm_customer_phone">\n<td class="left">\n<strong>Phone :</strong>#phone#\n</td>\n</tr>}}\n{{<tr class="rm_location">\n<td class="left"><strong>Address :</strong>#ship_address#</td>\n</tr>}}\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n{{<tr class="rm_barcode">\n<td class="left">\n<IMG SRC="data:image/gif;base64,#barcode#" />\n</td>\n</tr>}}\n<tr class="products ">\n<td class="left">#products_str#</td>\n</tr>\n\n</tbody>\n</table></td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=7;

UPDATE `label_templates` SET `content`='<table class="outertable" cellspacing="0" cellpadding="0">\n<tbody>\n<tr>\n<td class="left out">\n<table class="innertable">\n<tbody>\n<tr class="name">\n<td style="font-weight:Bold; font-size:14px;">\n#company_name#\n{{<span style="font-weight:normal;font-style:italic;">#website#</span>}}\n</td>\n</tr>\n{{<tr class="rm_customer_code">\n<td class="left">\nCUSTOMER CODE : &nbsp;&nbsp;&nbsp;&nbsp;<strong><font style="font-size: 22px;">#customer_code#</font></strong>\n</td>\n</tr>}}\n<tr class="name">\n<td class="left" ><strong>#customer_name#</strong></td>\n</tr>\n{{<tr class="rm_customer_phone mobile">\n<td class="left" >#phone#</td>\n</tr>}} \n<tr class="products onlyproducts">\n<td class="left">\n<span class="diet">\n<font style="width:200px;color: rgb(0, 0, 0); left: 9px; top: 0px; font-size: 14px;">#products#</font>\n</span> \n</td>\n</tr>\n{{<tr class="rm_item_details products">\n<td class="left" >\n<span style="font-size: 14px;">#items#</span>\n</td>\n</tr>}}\n\n<tr class="location">\n<td class="left" >#ship_address#</td>\n</tr>\n{{<tr class="rm_customer_preference">\n<td class="left">\n<strong>Remark :</strong>#customer_preference#\n</td>\n</tr>}}\n<tr>\n<td style="font-weight:bolder; font-size:11px;" >\n<span style="float:left; text-align: left; width: 150px;">#weekday# {{#dabbawala_code#}}</span>\n<span style="float:right; text-align: center; width: 92px;">#order_date#</span></td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>' WHERE  `pk_template_id`=8;

/////////////////////////////////////////////////////////////////////////////

/////// Sheefa - MIN COD Amount key ////////////

INSERT INTO `settings` (`company_id`, `unit_id`, `key`, `value`) VALUES (165, 165, 'GLOBAL_MIN_COD_PRICE', '');

////////////////////////////////////////////////////////////////////////////

/////// Sheefa - refund key ////////////

INSERT INTO `settings` (`company_id`, `unit_id`, `key`, `value`) VALUES (165, 165, 'GLOBAL_ALLOW_REFUND', 'no');

INSERT INTO `settings` (`company_id`, `unit_id`, `key`, `value`) VALUES (165, 165, 'GLOBAL_REFUND_GATEWAYS', '');

/////////  12th Nov 19  - sankalp   /////////////
ALTER TABLE `test_qs_165`.`customers` 
ADD COLUMN `auth_id` VARCHAR(55) NULL DEFAULT NULL AFTER `unit_id`;

//////////////////////////////////////////////////////////////////////////////
ALTER TABLE `customer_address`
  CHANGE COLUMN `delivery_person_id` `delivery_person_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `location_zipcode`;

//////////////////////////////////////////////////////////////////////////////////
ALTER TABLE `customers` 
ADD COLUMN `auth_id` VARCHAR(55) NULL DEFAULT NULL AFTER `customer_name`;
