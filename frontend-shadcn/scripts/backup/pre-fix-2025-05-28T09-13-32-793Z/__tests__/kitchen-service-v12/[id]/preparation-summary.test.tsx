import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]PreparationSummary } from '@/components/kitchen-service-v12/[id]/preparation-summary';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]PreparationSummary', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]PreparationSummary />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]PreparationSummary')).toBeInTheDocument();
  });
});