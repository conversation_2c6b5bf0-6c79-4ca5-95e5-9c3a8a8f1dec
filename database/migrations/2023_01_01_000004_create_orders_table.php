<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id('pk_order_no');
            $table->string('order_no')->unique();
            $table->unsignedBigInteger('customer_code');
            $table->string('customer_name');
            $table->string('customer_phone', 20);
            $table->text('ship_address');
            $table->date('order_date');
            $table->enum('delivery_status', ['Pending', 'Dispatched', 'Delivered', 'Failed'])->default('Pending');
            $table->enum('order_status', ['New', 'Processing', 'Complete', 'Cancelled'])->default('New');
            $table->unsignedBigInteger('delivery_person')->nullable();
            $table->unsignedBigInteger('location_code');
            $table->decimal('amount', 10, 2)->default(0);
            $table->decimal('tax', 10, 2)->default(0);
            $table->decimal('delivery_charges', 10, 2)->default(0);
            $table->decimal('applied_discount', 10, 2)->default(0);
            $table->string('payment_mode')->default('Online');
            $table->boolean('amount_paid')->default(false);
            $table->string('fk_kitchen_code')->nullable();
            $table->string('order_menu')->nullable();
            $table->enum('delivery_type', ['delivery', 'pickup'])->default('delivery');
            $table->time('delivery_time')->nullable();
            $table->time('delivery_end_time')->nullable();
            $table->string('tp_delivery_order_id')->nullable();
            $table->unsignedBigInteger('company_id')->default(1);
            $table->unsignedBigInteger('unit_id')->default(1);
            $table->timestamps();
            
            $table->foreign('location_code')->references('pk_location_code')->on('delivery_locations');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
