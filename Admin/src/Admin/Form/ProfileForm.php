<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: UserForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class ProfileForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('profile');
        $this->service_locator = $sm;

        $this->setAttribute('method', 'post');
        $this->add(array(
            'name' => 'pk_user_code',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));
		 $this->add(array(
            'name' => 'first_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter First Name...',
            ),
            'options' => array(
                'label' => 'First Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
		 $this->add(array(
		 		'name' => 'last_name',
		 		'type' => 'Zend\Form\Element\Text',
		 		'attributes' => array(
		 				'class' => 'smallinput',
		 				'placeholder' => 'Enter Last Name...',
		 				//'required' => 'required',
		 		),
		 		'options' => array(
		 				'label' => 'Last Name<span class="red">*</span>',
		 				'label_options' => array('disable_html_escape' => true),
		 		),
		 ));
		$this->add(array(
            'name' => 'phone',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Contact number...',
               // 'required' => 'required',

            ),
            'options' => array(
                'label' => 'Contact Number<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

       $this->add(array(
            'name' => 'email_id',
            'type' => 'Zend\Form\Element\Email',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Email Address...',
                //'required' => 'required',
            ),
            'options' => array(
                'label' => 'Email Id<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

       $this->add(array(
       		'name' => 'old_password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'old_password',
       				'placeholder' => 'old password Here...',
       		),
       		'options' => array(
       				'label' => 'Old Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		),
       ));

       $this->add(array(
       		'name' => 'new_password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'new_verify',
       				'placeholder' => 'New password',
       				//'required' => 'required',
       		),
       		'options' => array(
       				'label' => 'New Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		),
       ));

       $this->add(array(
       		'name' => 'confirm_password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'confirm_password',
       				'placeholder' => 'Confirm password',
       				//'required' => 'required',
       		),
       		'options' => array(
       				'label' => 'Confirm Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		),
       ));
       
	
        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        		),
        ));

        $this->add(array(
        	'name'=>'backurl',
        	'type' => 'Zend\Form\Element\Hidden',
        	'attributes'=>array(
        			'id'=>'backurl',
        			'value'=>'/users',
        ),
        ));
    }
	/**
	 * To get the list of active user roles
	 *
	 * @return array
	 */
    public function getRoles()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('roles');
    	//$select->where('isDefault',1);
    	$select->where('status',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$selectData[$res['pk_role_id']] = $res['role_name'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    }
	/**
	 * To get the list of active countries
	 *
	 * @return array
	 */
	public function getCountry()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('countries');
    	//$select->where('currency_code',1);
    	$select->where('active',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$selectData[$res['country_name']] = $res['country_name'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    }
    /**
     * To get the list of active delivery locations
     *
     * @return array
     */
    public function getLocation()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('delivery_locations');
    	$select->where('status',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$selectData[$res['pk_location_code']] = $res['location'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    }
}
