import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]PreparedAll } from '@/components/kitchen-service-v12/[id]/prepared/all';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]PreparedAll', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]PreparedAll />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]PreparedAll')).toBeInTheDocument();
  });
});