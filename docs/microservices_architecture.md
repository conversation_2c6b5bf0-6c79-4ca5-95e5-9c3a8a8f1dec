# Microservices Architecture

## Overview

This document outlines the high-level architecture for the migration from a monolithic Zend Framework application to a microservices architecture using Laravel 12.

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│                           Client Applications                           │
│                                                                         │
│    ┌──────────────┐      ┌──────────────┐      ┌──────────────┐        │
│    │   Web App    │      │  Mobile App  │      │  Admin Panel │        │
│    └──────────────┘      └──────────────┘      └──────────────┘        │
│              │                  │                     │                 │
└──────────────┼──────────────────┼─────────────────────┼─────────────────┘
               │                  │                     │
               ▼                  ▼                     ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│                            Kong API Gateway                             │
│                                                                         │
│    ┌──────────────────────────────────────────────────────────────┐    │
│    │  Authentication │ Rate Limiting │ Logging │ Request Routing  │    │
│    └──────────────────────────────────────────────────────────────┘    │
│                                                                         │
└───────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────┘
        │             │             │             │             │
        ▼             ▼             ▼             ▼             ▼
┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐
│              ││              ││              ││              ││              │
│ Auth Service ││ Customer Svc ││ Product Svc  ││  Order Svc   ││ Payment Svc  │
│              ││              ││              ││              ││              │
└──────────────┘└──────────────┘└──────────────┘└──────────────┘└──────────────┘
        │             │             │             │             │
        │             │             │             │             │
┌───────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────┐
│                                                                         │
│                         Message Broker (RabbitMQ)                       │
│                                                                         │
└───────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────┘
        │             │             │             │             │
        ▼             ▼             ▼             ▼             ▼
┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐
│              ││              ││              ││              ││              │
│ Kitchen Svc  ││ Delivery Svc ││ Analytics Svc││ Catalog Svc  ││ Notification │
│              ││              ││              ││              ││    Service   │
└──────────────┘└──────────────┘└──────────────┘└──────────────┘└──────────────┘
```

## Components

### Client Applications

- **Web App**: Browser-based application for customers
- **Mobile App**: Native mobile application for customers
- **Admin Panel**: Web-based application for administrators

### Kong API Gateway

The API Gateway serves as the entry point for all client requests and handles:

- **Authentication and Authorization**: Validates user credentials and permissions
- **Rate Limiting**: Prevents abuse by limiting request frequency
- **Request Routing**: Directs requests to the appropriate microservice using path-based routing with the pattern '/v2/{service-name}/*'
- **Logging**: Records all API requests for monitoring and debugging
- **Response Transformation**: Standardizes API responses
- **Health Checks**: Monitors the health of microservices
- **CORS Configuration**: Manages Cross-Origin Resource Sharing
- **Error Handling**: Provides consistent error responses

For detailed information about the Kong API Gateway configuration, see the [Kong API Gateway Configuration](kong_api_gateway.md) document.

### Microservices

Each microservice is a self-contained Laravel 12 application with its own database and business logic:

1. **Auth Service**
   - User authentication and authorization
   - User registration and profile management
   - Role and permission management
   - Token management

2. **Customer Service**
   - Customer profile management
   - Address management
   - Customer preferences
   - Customer history

3. **Product Service**
   - Product catalog management
   - Product categories
   - Product attributes
   - Inventory management

4. **Order Service**
   - Order creation and processing
   - Order status management
   - Order history
   - Order reporting

5. **Payment Service**
   - Payment processing
   - Payment methods
   - Payment status management
   - Payment history

6. **Kitchen Service**
   - Order preparation management
   - Recipe management
   - Inventory management
   - Kitchen reporting

7. **Delivery Service**
   - Delivery tracking
   - Delivery assignment
   - Delivery status management
   - Delivery optimization

8. **Analytics Service**
   - Business intelligence
   - Reporting
   - Data analysis
   - Dashboards

9. **Catalog Service**
   - Menu management
   - Meal planning
   - Nutritional information
   - Dietary restrictions

10. **Notification Service**
    - Email notifications
    - SMS notifications
    - Push notifications
    - In-app notifications

### Message Broker (RabbitMQ)

The message broker facilitates asynchronous communication between microservices:

- **Event-driven architecture**: Services publish events that other services can subscribe to
- **Decoupling**: Services don't need to know about each other directly
- **Reliability**: Messages are persisted until processed
- **Scalability**: Services can scale independently based on message load

## Database Architecture

Each microservice has its own database, following the database-per-service pattern:

```
┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐
│              ││              ││              ││              ││              │
│  Auth DB     ││ Customer DB  ││  Product DB  ││   Order DB   ││  Payment DB  │
│              ││              ││              ││              ││              │
└──────────────┘└──────────────┘└──────────────┘└──────────────┘└──────────────┘

┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐┌──────────────┐
│              ││              ││              ││              ││              │
│  Kitchen DB  ││ Delivery DB  ││ Analytics DB ││  Catalog DB  ││ Notification │
│              ││              ││              ││              ││      DB      │
└──────────────┘└──────────────┘└──────────────┘└──────────────┘└──────────────┘
```

## Communication Patterns

### Synchronous Communication

- **REST APIs**: For direct service-to-service communication
- **GraphQL**: For complex data queries (optional)

### Asynchronous Communication

- **Event-driven**: Services publish events to the message broker
- **Command-based**: Services send commands to other services via the message broker

## Deployment Architecture

The microservices are deployed using Docker containers orchestrated by Kubernetes:

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│                           Kubernetes Cluster                            │
│                                                                         │
│    ┌──────────────┐      ┌──────────────┐      ┌──────────────┐        │
│    │  Ingress     │      │  Kong API    │      │  Service     │        │
│    │  Controller  │──────▶   Gateway    │──────▶  Mesh        │        │
│    └──────────────┘      └──────────────┘      └──────────────┘        │
│              │                                        │                 │
│              ▼                                        ▼                 │
│    ┌──────────────────────────────────────────────────────────────┐    │
│    │                                                              │    │
│    │                     Microservices Pods                       │    │
│    │                                                              │    │
│    └──────────────────────────────────────────────────────────────┘    │
│              │                                        │                 │
│              ▼                                        ▼                 │
│    ┌──────────────┐      ┌──────────────┐      ┌──────────────┐        │
│    │  Persistent  │      │  RabbitMQ    │      │  Redis       │        │
│    │  Volumes     │      │  Cluster     │      │  Cluster     │        │
│    └──────────────┘      └──────────────┘      └──────────────┘        │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

## Monitoring and Observability

- **Prometheus**: Metrics collection
- **Grafana**: Metrics visualization
- **ELK Stack**: Log aggregation and analysis
- **Jaeger**: Distributed tracing

## Security Architecture

- **Authentication**: JWT-based authentication via Laravel Sanctum
- **Authorization**: Role-based access control
- **API Security**: Rate limiting, input validation, CORS
- **Data Security**: Encryption at rest and in transit
- **Network Security**: Service mesh for secure service-to-service communication
