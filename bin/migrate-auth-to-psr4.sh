#!/bin/bash

# Script to convert Auth module to PSR-4 structure
echo "Starting Auth module PSR-4 conversion..."

# Create a temporary Rector configuration for Auth module
cat > auth-psr4-rector.php << 'EOF'
<?php

declare(strict_types=1);

use App\Set\ValueObject\CustomSetList;
use Rector\Config\RectorConfig;
use <PERSON>\Renaming\Rector\Namespace_\RenameNamespaceRector;

return static function (RectorConfig $rectorConfig): void {
    // Define paths to refactor - only the Auth module
    $rectorConfig->paths([
        __DIR__ . '/module/SanAuth/src',
    ]);

    // Define PHP version for features
    $rectorConfig->phpVersion(80100); // PHP 8.1

    // Skip certain files or patterns
    $rectorConfig->skip([
        // Skip test files
        __DIR__ . '/module/SanAuth/test',
    ]);

    // Rename namespaces from Zend module style to PSR-4
    $rectorConfig->ruleWithConfiguration(RenameNamespaceRector::class, [
        // Map SanAuth namespace to App\Auth
        'SanAuth' => 'App\Auth',
        'SanAuth\Controller' => 'App\Auth\Controller',
        'SanAuth\Model' => 'App\Auth\Model',
        'SanAuth\Service' => 'App\Auth\Service',
        'SanAuth\Form' => 'App\Auth\Form',
        'SanAuth\Validator' => 'App\Auth\Validator',
        'SanAuth\Factory' => 'App\Auth\Factory',
        'SanAuth\Middleware' => 'App\Auth\Middleware',
        'SanAuth\Session' => 'App\Auth\Session',
    ]);
};
EOF

# Run Rector with Auth PSR-4 configuration
echo "Running Rector with Auth PSR-4 configuration (dry run)..."
vendor/bin/rector process --config=auth-psr4-rector.php --dry-run

# If dry run is successful, ask for confirmation to proceed
read -p "Do you want to proceed with the actual conversion? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    # Run Rector with Auth PSR-4 configuration (actual conversion)
    echo "Running Rector with Auth PSR-4 configuration (actual conversion)..."
    vendor/bin/rector process --config=auth-psr4-rector.php

    # Create directory structure if it doesn't exist
    mkdir -p src/Auth/{Controller,Model,Service,Form,Validator,Factory,Middleware,Session}

    # Move files to the new PSR-4 structure
    echo "Moving files to the new PSR-4 structure..."

    # Process AuthController.php specifically
    if [ -f "module/SanAuth/src/SanAuth/Controller/AuthController.php" ]; then
        echo "Processing AuthController.php..."
        cp module/SanAuth/src/SanAuth/Controller/AuthController.php src/Auth/Controller/
    else
        echo "AuthController.php not found!"
    fi

    # Process Auth* model files
    echo "Processing Auth* model files..."
    find module/SanAuth/src/SanAuth/Model -name "Auth*.php" -exec cp {} src/Auth/Model/ \; 2>/dev/null || echo "No Auth* model files found!"

    # Also check for Auth files in vendor/Lib/Auth
    echo "Processing Auth files from vendor/Lib/Auth..."
    if [ -d "vendor/Lib/Auth" ]; then
        find vendor/Lib/Auth -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No Auth files found in vendor/Lib/Auth!"
    else
        echo "vendor/Lib/Auth directory not found!"
    fi

    # Process other SanAuth files
    echo "Processing other SanAuth files..."
    find module/SanAuth/src/SanAuth/Controller -name "*.php" ! -name "AuthController.php" -exec cp {} src/Auth/Controller/ \;
    find module/SanAuth/src/SanAuth/Model -name "*.php" ! -name "Auth*.php" -exec cp {} src/Auth/Model/ \;
    find module/SanAuth/src/SanAuth/Service -name "*.php" -exec cp {} src/Auth/Service/ \;
    find module/SanAuth/src/SanAuth/Form -name "*.php" -exec cp {} src/Auth/Form/ \;
    find module/SanAuth/src/SanAuth/Validator -name "*.php" -exec cp {} src/Auth/Validator/ \;
    find module/SanAuth/src/SanAuth/Factory -name "*.php" -exec cp {} src/Auth/Factory/ \;
    find module/SanAuth/src/SanAuth/Middleware -name "*.php" -exec cp {} src/Auth/Middleware/ \;
    find module/SanAuth/src/SanAuth/Session -name "*.php" -exec cp {} src/Auth/Session/ \;

    echo "Auth module PSR-4 conversion completed successfully!"
    echo "Please run 'composer dump-autoload' to regenerate the autoloader."

    # Clean up temporary file
    rm auth-psr4-rector.php
else
    echo "Auth module PSR-4 conversion cancelled."
    # Clean up temporary file
    rm auth-psr4-rector.php
fi
