<?php

namespace App\Repositories\Contracts;

use App\Models\Order;
use Illuminate\Pagination\LengthAwarePaginator;

interface DeliveryRepositoryInterface
{
    /**
     * Get orders for delivery.
     *
     * @param int $userId
     * @param int|null $locationId
     * @param string|null $date
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getOrdersForDelivery(int $userId, ?int $locationId = null, ?string $date = null): LengthAwarePaginator;
    
    /**
     * Search orders.
     *
     * @param int $userId
     * @param string $searchTerm
     * @param int|null $locationId
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchOrders(int $userId, string $searchTerm, ?int $locationId = null): LengthAwarePaginator;
    
    /**
     * Update delivery status.
     *
     * @param int $orderId
     * @param int $userId
     * @param bool $orderCompleted
     * @return bool
     */
    public function updateDeliveryStatus(int $orderId, int $userId, bool $orderCompleted = false): bool;
    
    /**
     * Get order by ID.
     *
     * @param int $orderId
     * @return \App\Models\Order|null
     */
    public function getOrderById(int $orderId): ?Order;
    
    /**
     * Assign delivery person to order.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     * @return bool
     */
    public function assignDeliveryPerson(int $orderId, int $deliveryPersonId): bool;
}
