<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\CommonConfig as QSCommon;

class ThemeSettingForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	
	private $service_locator;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('setting');
        $adapter = $sm->get('Write_Adapter');
        $this->adapter = $adapter;
        $this->service_locator = $sm;

        $this->setAttribute('method', 'post');
        
        $this->add(array(
			'name' => 'id',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes' => array(
					'id' => 'id',
			),
			'options' => array(
					'label' => 'undefined',
			),
        ));
        
       
        $this->add(array(
			'name' => 'csrf',
			'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
			'name'=>'submit',
			'attributes'=>array(
				'type'=>'submit',
				'value'=>'Save',
				'class'=>'dark-greenBg',
				'id' => 'submitbutton',
			)
        ));
        
        
         $this->add(array(
			'name' => 'cancel',
			'attributes' => array(
				'type'  => 'submit',
				'value' => 'Cancel',
				'id' => 'button',
				'class'=>'button left tiny left5 redBg'
	
			),
        ));
        
        $this->add(array(
			'name'=>'backurl',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes'=>array(
				'id'=>'backurl',
				'value'=>'/setting/view-system-setting',
			),
        )); 

               
    $theme = $this->getThemes();
      
        $this->add(array(
			'name' => 'GLOBAL_THEME',
			'type' => 'Zend\Form\Element\Select',
			'attributes' => array(
				'class'=> 'smallinput chosen-select',
				'id' => 'GLOBAL_THEME',
				'required' => 'required',
			),
			'options' => array(
				'label' => 'Select Theme <span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),        				
				'value_options' => $theme,
				'label_options' => array('disable_html_escape' => true),
			),
        )); 
        
        
        
        $style = $this->getStyles();
        $this->add(array(
			'name'=>'GLOBAL_STYLE',
			'type' => 'Zend\Form\Element\Select',
			'attributes'=>array(
				'class'=> 'smallinput chosen-select',
				'id' => 'GLOBAL_STYLE',
				'required' => 'required',
			),
			'options'=>array(
				'label'=>'Select Style<span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),
				'value_options' => $style,
				'label_options' => array('disable_html_escape' => true),
	
			)
        ));
       $skin = $this->getSkins();

        $this->add(array(
			'name'=>'GLOBAL_SKIN',
			'type' => 'Zend\Form\Element\Select',
			'attributes'=>array(
				'class'=> 'smallinput chosen-select',
				'id' => 'GLOBAL_SKIN',
				'required' => 'required',
			),
			'options'=>array(
				'label'=>'Select Skin<span class="red">*</span>',
				'label_attributes' => array(
					'class'  => 'inline right'
				),
				'value_options' => $skin,
				'label_options' => array('disable_html_escape' => true),
	
			)
        ));
        $this->add(array(
			'name'=>'theme-button',
			'attributes'=>array(
				'type'=>'theme-button',
				'value'=>'Apply Changes',
				'class'=>'theme-button',
				'id' => 'submitbutton',
			)
        ));
        
        
         $this->add(array(
			'name' => 'preview',
			'attributes' => array(
				'type'  => 'preview',
				'value' => 'Preview',
				'id' => 'preview',
				'class'=>'theme-pre-button'
	
			),
        ));
    }

    /**
     * to get list of timezone
     * @method getTimeZones()
     * @return array $returndata
     */
    
    /************Ashwini 13/04/2017*******************/
    
    public function getThemes() {

    	$libCommon = QSCommon::getInstance($this->service_locator);
    
    	$global_theme = $libCommon->getThemes();
    	 
    	$arrTheme = array(""=>"Select Theme");
    	 
    	foreach($global_theme as $theme) {
    		$arrTheme[$theme['theme_name']] = $theme['theme_name'];
            
    	}
    	
    	return $arrTheme;   
        
        
	}
        
	public function getStyles() {
        
    	$libCommon = QSCommon::getInstance($this->service_locator);
    
    	$global_style = $libCommon->getStyles();
    	 
    	$arrStyle = array(""=>"Select Style");
    	 
    	foreach($global_style as $style) {
    		$arrStyle[$style['style_name']] = $style['style_name'];
    	}
    	
    	return $arrStyle;   
	}

    public function getSkins() {
        
    	$libCommon = QSCommon::getInstance($this->service_locator);
    
    	$global_skin = $libCommon->getSkins();
    	 
    	$arrSkin = array(""=>"Select Skin");
    	 
    	foreach($global_skin as $skin) {
    		$arrSkin[$skin['skin_name']] = $skin['skin_name'];
            
    	}
    	
    	return $arrSkin;   
		
	}

}

