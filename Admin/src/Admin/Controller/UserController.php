<?php

/**
 * This File manages the users on fooddialer system
 * It is used to add ,update delete promocode
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: UserController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Admin\Controller;


use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use QuickServe\Model\Profile;
use Admin\Form\ProfileForm;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as QScommon;

use QuickServe\Model\User;
use Admin\Form\UserForm;

class UserController extends AbstractActionController {

    /**
     * It has an instance of QuickServe\Model\UserTable model
     *
     * @var QuickServe\Model\UserTable $taxtable
     */
    protected $userTable;

    /**
     * It has an instance of AuthService model
     *
     * @var AuthService $authservice
     */
    protected $authservice;
    protected $service_locator;
    /**
     * To view the list of users
     *
     * @return \Zend\View\Model\ViewModel
     */
    public function indexAction() {
        if (!$this->authservice) {
            $this->authservice = $this->getServiceLocator()
                ->get('AuthService');
        }
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);       
        
        $adapter = $sm->get('Write_Adapter');
        //*****Ashwini******//
       
        $select = new QSelect ();
		$select->from("city");
        $iden = $this->authservice->getIdentity();
        
        $layoutviewModel = $this->layout();
        $acl = $layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        $select->where(array('status = 1'));
        
        //$selectString = $sql->getSqlStringForSqlObject($select);
        //$cities = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
        
        $cities = $sql->execQuery($select);
//        echo "<pre>";print_r($cities->toArray());exit;
        $this->layout()->setVariables(array('page_title' => "User Account", 'description' => "Account Info", 'breadcrumb' => "User Account "));
        return new ViewModel(array(
            /*  'order_by' => $order_by,
              'order' => $order,
              'page' => $page,
              'paginator' => $returnvar, */
            'acl' => $acl,
            'loggedUser' => $loguser,
            'cities' => $cities,
            'flashMessages' => $this->flashMessenger()->getMessages()
        ));
    }

    public function getDeliveryLocationAction() {
       
        $city_id = $_POST['city_id'];
        $sm = $this->getServiceLocator();
        //$adapter = $sm->get('Write_Adapter');
        $sql = new QSql($sm);
        $select = new QSelect ();
        $this->table="delivery_locations";
		$select->from( $this->table );
        $select->where(array('city'=>$city_id ,'status'=>'1'));
        $selectString = $sql->getSqlStringForSqlObject($select);
        $result2 = $adapter->query(
            $selectString, $adapter::QUERY_MODE_EXECUTE
        );
        $rs = $result2->toArray();
        return new JsonModel($rs);
    }

    public function getSelectedDeliveryLocationAction() {
        
        $user_id = $_POST['user_id'];
        $city_selected_id = $_POST['city_selected_id'];
        $sm = $this->getServiceLocator();
        //$adapter = $sm->get('Write_Adapter');
        $this->table ="user_locations";
        $sql = new QSql($sm);
        $select = new QSelect();
		$select->from( $this->table );
        $select->where(array('fk_user_code' => $user_id, 'fk_city_code' => $city_selected_id));
        $selectString = $sql->getSqlStringForSqlObject($select);
        $result2 = $adapter->query(
            $selectString, $adapter::QUERY_MODE_EXECUTE
        );
        $rs = $result2->toArray();
        $selected_array = array();
        foreach ($rs as $k => $v) {
            $selected_array[] = $v['fk_location_code'];
        }
        //echo "<pre>";print_r($selected_array);
        return new JsonModel($selected_array);
    }

    public function saveDeliveryLocationAction() {
        
        $user_id = $_POST['user_id'];
        $delivery_locations = $_POST['delivery_locations'];
        $fk_city_code = $_POST['fk_city_code'];
        $sm = $this->getServiceLocator();
        //$adapter = $sm->get('Write_Adapter');
        $sql = new QSql($sm);

        $sql1 = "DELETE FROM user_locations WHERE fk_user_code = '" . $user_id . "' and fk_city_code = '" . $fk_city_code . "'";
        //echo $sql1;exit;
        $result3 = $adapter->query(
            $sql1, $adapter::QUERY_MODE_EXECUTE
        );
        if (!empty($delivery_locations)) {
            //echo "asf";exit;
            $sql = "INSERT INTO user_locations (fk_user_code, fk_location_code, fk_city_code, created_date) values ";

            $valuesArr = array();
            foreach ($delivery_locations as $row) {

                $fk_user_code = $user_id;
                $fk_location_code = $row;
                $created_date = date('Y-m-d');

                $valuesArr[] = "('$fk_user_code', '$fk_location_code', '$fk_city_code', '$created_date')";
            }

            $sql .= implode(',', $valuesArr);
            $result2 = $adapter->query(
                $sql, $adapter::QUERY_MODE_EXECUTE
            );
        }
        //$rs = $result2->toArray();
        return new JsonModel($rs);
    }

    public function ajxUserAction() {
        if (!$this->authservice) {
            $this->authservice = $this->getServiceLocator()
                ->get('AuthService');
        }

        $iden = $this->authservice->getIdentity();
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;

        $utility = Utility::getInstance();

        $layout = $this->layout();
        $acl = $layout->acl;

        $viewModel = new ViewModel();

        $loggedUser = $layout->loggedUser;

        $select = new QSelect();

        $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0' => array('column' => 0, 'dir' => 'desc'));
        $arrColumns = array('0' => 'pk_user_code', '1' => 'first_name', '2' => 'role_name', '3' => 'email_id', '4' => 'phone');

        $order_by = $arrColumns[$arrOrder[0]['column']];
        $order = $arrOrder[0]['dir'];

        $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

        $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
        $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0;
        $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1;
        $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
        $search = ($arrSearch['value'] != "") ? $arrSearch['value'] : "";

        $status = $this->params()->fromQuery('status');

        $columns = $this->params()->fromQuery('columns');

        $select->where(
            new \Zend\Db\Sql\Predicate\PredicateSet(
            array(
            new \Zend\Db\Sql\Predicate\Operator('pk_user_code', 'LIKE', '%' . $search . '%'),
            new \Zend\Db\Sql\Predicate\Operator('first_name', 'LIKE', '%' . $search . '%'),
            new \Zend\Db\Sql\Predicate\Operator('role_name', 'LIKE', '%' . $search . '%'),
            new \Zend\Db\Sql\Predicate\Operator('email_id', 'LIKE', '%' . $search . '%'),
            new \Zend\Db\Sql\Predicate\Operator('phone', 'LIKE', '%' . $search . '%'),
            ),
            // optional; OP_AND is default
            \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )
        );

        $select->order($order_by . ' ' . $order);

        $users = $this->getUserTable()->fetchAll($select, $page);
      
        $users->setCurrentPageNumber($page)
            ->setItemCountPerPage($itemsPerPage)
            ->setPageRange(7);

        $returnVar = array();
        $returnVar['draw'] = $draw;
        $returnVar['recordsTotal'] = $users->getTotalItemCount();
        $returnVar['recordsFiltered'] = $users->getTotalItemCount();
        $returnVar['data'] = array();

        /* prosim changes. 12may17 */
        $roles = $this->getServiceLocator()->get('\Quickserve\Model\RoleTable')->fetchAll();
        
        $session_token = $iden->session_token;
        $config = $this->getServiceLocator()->get('config');
        foreach ($users as $user) {
            
            $arrTmp = array();

            array_push($arrTmp, $user['pk_user_code']);
            array_push($arrTmp, $user['first_name'] . " " . $user['last_name']);
            array_push($arrTmp, $user['email_id']);

            array_push($arrTmp, $user['role_name']);
            /* dropdown for role assignment */
//            $dropdown = '<select name="role" style="margin: 0px;">';
//
//            $dropdown .='<option>--Assign a role--</option>';
//           
//            foreach($roles->toArray() as $role){
//                $selected = ($role['pk_role_id'] == $user['role_id']) ? 'selected' : '';
//                $dropdown .= "<option ".$selected." value=".$role['pk_role_id'].">".$role['role_name']."</option> ";
//            }
//            $dropdown .= "</select>";
//            
//            array_push($arrTmp, $dropdown);
            /* dropdown ends*/ 
            
            $status = ($user['status'] == "1" ) ? '<span class="active">Active</span>' : '<span class="inactive">Inactive</span>';
            array_push($arrTmp, $status);

            $str = "";

            if ($acl->isAllowed($loggedUser->rolename, 'user_crud', 'edit')) {

                $textadd = ($user['status']) == "0" ? 'Activate' : 'Deactivate';

                $str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\'' . $this->url()->fromRoute('user_crud', array('action' => 'edit', 'id' => $user['pk_user_code'])) . '\'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
                
                $redirectUrl = ($iden->pk_user_code == $user['pk_user_code']) ? "admin/profile/edit" : "admin/user/edit/".$user['prosim_user_id'];
                
                
                $autologinUrl = $config['account_domain']."autologin"."?token=".$session_token."&username=".$iden['email_id']."?redirect_url=".$redirectUrl;
                
                $str .= '<button formtarget="_blank" class="smBtn greenBg has-tip tip-top" onClick="window.open(\'' . $autologinUrl . '\')" data-tooltip title="Prosim Profile Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
            }

            if ($acl->isAllowed($loggedUser->rolename, 'user_crud', 'delete')) {
                $str .= ' <a href="' . $this->url()->fromRoute('user_crud', array('action' => 'delete', 'id' => $user['pk_user_code'])) . '" onclick="return confirm(\'Are you sure you want to ' . $textadd . ' this user ?\')">';
//                if ($textadd == 'Deactivate') {
//                    $str .= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"  data-text-swap="Wait.."><i class="fa fa-ban"></i></button>';
//                } else {
//                    $str.='<button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
//                }
                $str.= '</a>';
            }

            $str .= '<button data-id="' . $user['pk_user_code'] . '" class="smBtn yellowBg has-tip tip-top" data-tooltip  title="Location" id="myModalLocation" ><i class="fa fa-map-marker"></i></button>';
            array_push($arrTmp, $str);

            array_push($returnVar['data'], $arrTmp);
        }
       // dd($returnVar);
        return new JsonModel($returnVar);
    }

    /**
     * This function sends an email to the users
     * A newly addded user get an email with the account credentials
     *
     * @param array $from
     * @param array $to
     * @param array $cc
     * @param array $bcc
     * @param string $subject
     * @param string $content_message
     * @param string $encoding
     * @param string $files
     * @return boolean
     */
    private function sendmail($from = array(), $to = array(), $cc = array(), $bcc = array(), $subject = null, $content_message = null, $encoding = 'utf-8', $files = array()) {
        $sm = $this->getServiceLocator();
        $config = $sm->get('config');
        $mailer_config = $config['mail']['transport']['options'];

        $hasAttachment = (is_array($files) && !empty($files)) ? true : false;

        $mail = new \Frontend\Model\PHPMailer\PHPMailer();

        $mail->IsSMTP();                                      // Set mailer to use SMTP
        $mail->Host = $mailer_config['host'];  // Specify main and backup server
        $mail->SMTPAuth = true;                               // Enable SMTP authentication
        $mail->Username = $mailer_config['connection_config']['username'];                            // SMTP username
        $mail->Password = $mailer_config['connection_config']['password'];                           // SMTP password
        $mail->SMTPSecure = 'tls';                            // Enable encryption, 'ssl' also accepted
        $mail->Port = $mailer_config['port']; #587                   // set the SMTP port for the GMAIL server

        if (is_array($from) && !empty($from)) {
            $from_address_array = $mailer_config['from_email_address'];
            foreach ($from as $name => $email) {

                if (isset($from_address_array[$name])) {
                    $mail->SetFrom($from_address_array[$name], $name);
                    break;
                } else {
                    $mail->SetFrom('<EMAIL>', 'Information');
                    break;
                }
            }
        } else {
            $mail->SetFrom('<EMAIL>', 'Information');
        }

        if (is_array($to) && !empty($to)) {
            foreach ($to as $name => $email) {
                $mail->AddAddress($email, $name);
            }
        }

        if (is_array($cc) && !empty($cc)) {
            foreach ($cc as $name => $email) {
                $mail->AddCC($email, $name);
            }
        }

        if (is_array($bcc) && !empty($bcc)) {
            foreach ($bcc as $name => $email) {
                $mail->AddBCC($email, $name);
            }
        }

        $mail->WordWrap = 50;                                 // Set word wrap to 50 characters
        if (is_array($files) && !empty($files)) {
            foreach ($files as $itr => $full_file_path) {
                $mail->AddAttachment($full_file_path, basename($full_file_path));
            }#end of foreach
        }
        #$mail->AddAttachment('/var/tmp/file.tar.gz');         // Add attachments
        #$mail->AddAttachment('/tmp/image.jpg', 'new.jpg');    // Optional name
        $mail->IsHTML(true);                                  // Set email format to HTML

        $mail->Subject = $subject;
        $mail->Body = trim($content_message);
        $mail->AltBody = trim(stripslashes($content_message));
        #echo "<pre>";print_r($mail);exit();
        try {
            if (!$mail->Send()) {
                echo 'Message could not be sent.' . '<br/>';
                echo 'Mailer Error: ' . $mail->ErrorInfo;
                return false;
            }
        } catch (\Exception $e) {
            #echo $e->getCode().' :: '.print_r($e->getMessage(), true);
        }

        unset($mail);
        return true;
    }

    /**
     * To add new user
     *
     * @return \Admin\Form\UserForm
     */
    public function addAction() {

        $layoutviewModel = $this->layout();
        $loguser = $layoutviewModel->loggedUser;

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCommon = QScommon::getInstance($sm);
        
        $form = new UserForm($sm);

        $form->get('submit')->setAttribute('value', 'Add');

        $utility = \Lib\Utility::getInstance();

        $request = $this->getRequest();

        $errStr = "";

        if ($request->isPost()) {
            $user = new User();
            $user->getInputFilter()->get('email_id')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'email_id',
                    'adapter' => $adapt,
                    'message' => 'Email address already exists',
                    )
            ));
            $user->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'phone',
                    'adapter' => $adapt,
                    'message' => 'Contact number already exist.',
                    )
            ));
           
            //$user->setAdapter($adapt);
            $form->setInputFilter($user->getInputFilter());

            $data = $request->getPost();

            $form->setData($data);

            /// Check if kitchen account user has cross its limit. ///

            $userCount = $this->getUserTable()->getUserCount($data['role_id']);
          
            $subsKey = "";
            $subscriptionCheck = 1;

            if ($userCount['role_name'] == 'Chef') {

                $subsKey = "kitchen_user";
            } elseif ($userCount['role_name'] == 'Admin') {

                $subsKey = "admin_user";
            } elseif ($userCount['role_name'] == 'Delivery Person') {

                $subsKey = "delivery_user";
            }

            if ($subsKey != "") {
                $subscriptionCheck = $utility->checkSubscription($subsKey, 'count', $userCount['count']);
                        if(!$subscriptionCheck){
                            $errStr = "Maximum no. of $subsKey  limit has reached ";
                            goto SHOWDETAILS;
                        }
            }
            
            $roleid = $request->getPost('role_id');
            $fk_kitchen_code = $request->getPost('fk_kitchen_code');

            if ($roleid != "") {
                $role = $this->getUserTable()->getRole($roleid);
            }

            if ($form->isValid()) {
                
                $user = new User();

                $user->exchangeArray($form->getData());
                
                /* 1. add user to prosim */
                $prosimUser = $this->addUserToProsim($user);
                
                if($prosimUser['status'] != 201){
                    $errStr = '';
                    
                    foreach($prosimUser['data'] as $key => $error){
                        if(is_array($error)){
                            $errStr .= $key.' : '.implode('. ',$error).'<br>';
                        }else{
                            $errStr .= $key.' : '.$error.'<br>';
                        }
                    }
                    
                    goto SHOWDETAILS;
                    
                }
                
                /* 2. add user to master  */
                $this->addUserToMasterDB($prosimUser['data']);
                 
                /* 3. add user to tenant */
                $user->prosim_user_id = $prosimUser['data']->user_id;
                
                $data_user = $this->getUserTable()->saveUser($user);

                if ($data_user) {
                    $user_name = $data_user['first_name'] . " " . $data_user['last_name'];
                    $full_name = $loguser->first_name . " " . $loguser->last_name;
                    $activity_log_data = array();
                    $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
                    $activity_log_data['context_name'] = $full_name;
                    $activity_log_data['context_type'] = 'user';
                    $activity_log_data['controller'] = 'user';
                    $activity_log_data['action'] = 'add';
                    $activity_log_data['description'] = "User : New user account for '$user_name' created for role " . $role['role_name'] . ".";
                    /* 	$activity_log_data['description']= "'$user_name' customer added by $full_name"; */
                    $libCommon->saveActivityLog($activity_log_data);

                    $lastId = $data_user['last_id'];

                    $valuesArr = array();
                    $sql5 = "INSERT INTO user_kitchens (fk_user_code, fk_kitchen_code, created_date, company_id, unit_id) values ";
                    foreach ($fk_kitchen_code as $row) {

                        $lastId = $lastId;
                        $kitchen_code = $row;
                        $created_date = date('Y-m-d');

                        $valuesArr[] = "('$lastId', '$kitchen_code', '$created_date', ".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].")";
                    }

                    $sql5 .= implode(',', $valuesArr);

                    $result5 = $adapt->query(
                        $sql5, $adapt::QUERY_MODE_EXECUTE
                    );
                }

                ($data_user) ? $this->flashMessenger()->addSuccessMessage("User added successfully") : $this->flashMessenger()->addErrorMessage("Error adding user.");

                // Redirect to list of albums
                return $this->redirect()->toRoute('user_crud');
            } else {
                $error_array = $form->getMessages();
                if (is_array($error_array) && !empty($error_array)) {
                    foreach ($error_array as $ear => $error) {
                        // show only general error messages for the form together.
                        if (in_array(strtolower($ear), array('csrf')) !== false) {
                            $errStr = empty($errStr) ? $errStr : $errStr . '<br/>';
                            if (is_array($error)) {
                                $errStr.= implode('<br/>', $error);
                            } else {
                                $errStr.= $error;
                            }
                        }
                    }// end of foreach
                }
                //echo '<pre>';print_r($form->getMessages());exit;
            }
        }

        SHOWDETAILS:
        
        $this->layout()->setVariables(array('page_title' => "Add User Account", 'description' => "Account Info", 'breadcrumb' => "Add User Account"));
        return array(
            'form' => $form,
            'errStr' => $errStr
        );
    }

    /**
     * To update user of given user id
     *
     * @param int id
     * @return  \Admin\Form\UserForm
     */
    public function editAction() {
        $id = (int) $this->params('id');
        $selected_locations = '';
        $selected_default_location_code = '';
        if (!$id) {
            return $this->redirect()->toRoute('user_crud', array('action' => 'add'));
        }

        $select = new QSelect();
        $select->join('roles', 'roles.pk_role_id = users.role_id', array('role_name', 'role_type'));
        $user = $this->getUserTable()->getUser($id, 'id', $select);

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');

        $sql6 = "SELECT fk_kitchen_code FROM user_kitchens WHERE fk_user_code = '" . $id . "'";
        $result6 = $adapt->query(
            $sql6, $adapt::QUERY_MODE_EXECUTE
        );
        $selected_kitchen = $result6->toArray();

        $selected_kitchen_array = array();
        foreach ($selected_kitchen as $k => $v) {
            $selected_kitchen_array[] = $v['fk_kitchen_code'];
        }

        $layout = $this->layout();
        $acl = $layout->acl;
        $viewModel = new ViewModel();
        $loguser = $layout->loggedUser;

        $libCommon = QScommon::getInstance($sm);

        $config_variables = $sm->get('config');
        $form = new UserForm($sm);
        $user->delivery_location_code = explode(",", $user->delivery_location_code);
        $selected_locations = $user->delivery_location_code;
        $selected_default_location_code = $user->default_location_code;

        // $form = new ContentForm();
        //echo '<pre>';print_r($user);exit();

        $location = $libCommon->getLocationById($user->default_location_code);
        $user['city'] = $location->city;

        $form->bind($user);

        if ($user['role_type'] == 'system') {
            $form->get('role_id')->setAttributes(array(
                'disabled' => 'disabled',
            ));
        }

        $form->get('submit')->setAttribute('value', 'Edit');

        $valemail = $user->email_id;
        $valphone = $user->phone;

        $request = $this->getRequest();
        
        if ($request->isPost()) {
            
            $userValidator = new User();

            /*
            $user->getInputFilter()->get('email_id')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'email_id',
                    'adapter' => $adapt,
                    'message' => 'Email Address Already exists',
                    'exclude' => array(
                        'field' => 'email_id',
                        'value' => $valemail,
                    )
                    )
            ));

            $user->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'phone',
                    'adapter' => $adapt,
                    'message' => 'Phone Number Already exists',
                    'exclude' => array(
                        'field' => 'phone',
                        'value' => $valphone,
                    )
                    )
            ));

            $user->getInputFilter()->remove("password");

            //$user->setAdapter($adapt);
            $form->setInputFilter($user->getInputFilter());
            */
            // echo remove validation
            $form->getInputFilter()->remove('password');
            $form->getInputFilter()->remove('password_verify');
            $form->getInputFilter()->remove('gender');
            
            $form->setData($request->getPost());

            $roleid = $request->getPost('role_id');
            //echo"<pre>";print_r($request->getPost());die;
            if ($roleid != "") {
                $role = $this->getUserTable()->getRole($roleid);
            }
            
            $tmp = clone $user;
            
            if ($form->isValid()) {

                $formData = $form->getData();
                
                $tmp['role_id'] = $formData['role_id'];
                $tmp['status'] = $formData['status'];
                
                $userValidator->exchangeArray($tmp);
                
                $data_user = $this->getUserTable()->saveUser($userValidator);
                $fk_kitchen_code = $request->getPost('fk_kitchen_code');
                
                if ($data_user) {
                    $user_name = $userValidator->first_name . " " . $userValidator->last_name;
                    $full_name = $loguser->first_name . " " . $loguser->last_name;
                    $activity_log_data = array();
                    $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
                    $activity_log_data['context_name'] = $full_name;
                    $activity_log_data['context_type'] = 'user';
                    $activity_log_data['controller'] = 'user';
                    $activity_log_data['action'] = 'edit';
                    $activity_log_data['description'] = "User : User account for '$user_name' updated.";

                    //                 	$activity_log_data['description']= "'$user_name' customer edited by $full_name";
                    $libCommon->saveActivityLog($activity_log_data);

                    $sql1 = "DELETE FROM user_kitchens WHERE fk_user_code = '" . $id . "'";
                    $result3 = $adapt->query(
                        $sql1, $adapt::QUERY_MODE_EXECUTE
                    );

                    $valuesArr = array();
                    $sql5 = "INSERT INTO user_kitchens (fk_user_code, fk_kitchen_code, created_date, company_id, unit_id) values ";
                    foreach ($fk_kitchen_code as $row) {

                        $lastId = $lastId;
                        $kitchen_code = $row;
                        $created_date = date('Y-m-d');

                        $valuesArr[] = "('$id', '$kitchen_code', '$created_date', ".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].")";
                    }

                    $sql5 .= implode(',', $valuesArr);
                    //echo $sql5;exit;
                    $result5 = $adapt->query(
                        $sql5, $adapt::QUERY_MODE_EXECUTE
                    );
                }

                $this->flashMessenger()->addSuccessMessage("User updated successfully");
                // Redirect to list of albums
                return $this->redirect()->toRoute('user_crud');
            } else{
                
                dd($form->getMessages());
            }
        }
//        echo "<pre>";print_r($user);exit;
        $this->layout()->setVariables(array('page_title' => "Edit User Account", 'description' => "Account Info", 'breadcrumb' => "Edit User Account"));
        return array(
            'id' => $id,
            'form' => $form,
            'selected_locations' => json_encode($selected_locations),
            'selected_default_location_code' => $selected_default_location_code,
            'selected_kitchen' => json_encode($selected_kitchen_array),
            'user' => $user
        );
    }

    /**
     * 
     * Edit user profile
     */
    public function editProfileAction() {

        $id = (int) $this->params('id');

        if (!$id) {
            return $this->redirect()->toRoute('user_crud', array('action' => 'add'));
        }

        $user = $this->getUserTable()->getUser($id);


        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $config_variables = $sm->get('config');
        $form = new ProfileForm($sm);
        $valid_flag = true;


        $layoutviewModel = $this->layout();
        $acl = $layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;


        $libCommon = QScommon::getInstance($sm);


        $valpwd = $user->password;
        $form->bind($user);

        $valemail = $user->email_id;
        $valphone = $user->phone;

        $request = $this->getRequest();
        if ($request->isPost()) {
            $profile = new Profile();

            $profile->getInputFilter()->get('email_id')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'email_id',
                    'adapter' => $adapt,
                    'message' => 'Email Address Already exists',
                    'exclude' => array(
                        'field' => 'email_id',
                        'value' => $valemail,
                    )
                    )
            ));

            $profile->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table' => 'users',
                    'field' => 'phone',
                    'adapter' => $adapt,
                    'message' => 'Phone Number Already exists',
                    'exclude' => array(
                        'field' => 'phone',
                        'value' => $valphone,
                    )
                    )
            ));

            if ($request->getPost('new_password')) {
                $profile->getInputFilter()->get('old_password')->setRequired(true);
                $profile->getInputFilter()->get('confirm_password')->setRequired(true);
            }
           // $profile->setAdapter($adapt);
            $form->setInputFilter($profile->getInputFilter());

            $form->setData($request->getPost());

            if ($form->isValid()) {

                $profile->exchangeArray($form->getData());

                //	$user->exchangeArray($form->getData());
                //$hdnrole = $profile->getInputFilter()->get('hdnuserrole')->getValue();

                if ($profile->old_password) {

                    if ($valpwd == MD5($profile->old_password)) {

                        $data_user = $this->getUserTable()->UpdateProfile($profile);

                        if ($data_user) {


                            $user_name = $profile->first_name . " " . $profile->last_name;
                            $full_name = $loguser->first_name . " " . $loguser->last_name;
                            $activity_log_data = array();
                            $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
                            $activity_log_data['context_name'] = $full_name;
                            $activity_log_data['context_type'] = 'user';
                            $activity_log_data['controller'] = 'user';
                            $activity_log_data['action'] = 'edit';
                            $activity_log_data['description'] = "User : User account for '$user_name' updated.";

                            //                 	$activity_log_data['description']= "'$user_name' customer edited by $full_name";
                            $libCommon->saveActivityLog($activity_log_data);
                        }

                        $this->flashMessenger()->addSuccessMessage("User updated successfully");
                        return $this->redirect()->toRoute('user_crud');
                    } else {
                        $valid_flag = false;
                        $errMsg = "<small class='error'>old password does not match</small>";
                    }
                } else {
                    $data_user = $this->getUserTable()->UpdateProfile($profile);


                    if ($data_user) {


                        $user_name = $profile->first_name . " " . $profile->last_name;
                        $full_name = $loguser->first_name . " " . $loguser->last_name;
                        $activity_log_data = array();
                        $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
                        $activity_log_data['context_name'] = $full_name;
                        $activity_log_data['context_type'] = 'user';
                        $activity_log_data['controller'] = 'user';
                        $activity_log_data['action'] = 'edit';
                        $activity_log_data['description'] = "User : User account for '$user_name' updated.";

                        //                 	$activity_log_data['description']= "'$user_name' customer edited by $full_name";
                        $libCommon->saveActivityLog($activity_log_data);
                    }


                    $message = $this->flashMessenger()->addSuccessMessage("User updated successfully");
                    return $this->redirect()->toRoute('user_crud');
                }
            }
            // echo '<pre>';print_r($form);exit;
            /* if ($form->isValid()) {

              echo "<pre>";print_r($valpwd);die;
              $profile->exchangeArray($form->getData());
              if($request->getPost('old_password'))
              {
              if($user->password==MD5($request->getPost('old_password'))) {
              $data_user=$this->getUserTable()->UpdateProfile($profile,$request->getPost('new_password'));
              $this->flashMessenger()->addSuccessMessage("User updated successfully");
              return $this->redirect()->toRoute('user_crud');
              }

              else {
              $valid_flag=false;
              $errMsg = "<small class='error'>old password does not match</small>";
              }

              }
              else
              {
              $data_user=$this->getUserTable()->UpdateProfile($profile,false);
              $message= $this->flashMessenger()->addSuccessMessage("User updated successfully");
              return $this->redirect()->toRoute('user_crud');

              }

              } */
        }
        $this->layout()->setVariables(array('page_title' => "Edit User Profile", 'description' => "Profile Info", 'breadcrumb' => "Edit User Profile"));
        return array(
            'id' => $id,
            'form' => $form,
            'errMsg' => $errMsg,
            'valid_flag' => $valid_flag
        );
    }

    /**
     * To delete the user of given id
     *
     * @param int id
     * @return route user_crud
     */
    public function deleteAction() {
        $id = (int) $this->params('id');
        if (!$id) {
            return $this->redirect()->toRoute('user_crud');
        }

        $layout = $this->layout();
        $acl = $layout->acl;
        $viewModel = new ViewModel();
        $loguser = $layout->loggedUser;

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCommon = QScommon::getInstance($sm);


        $select = new QSelect();
        $select->where(array('pk_user_code' => $id));
        $users = $this->getUserTable()->fetchAll($select);
        $arrusers = $users->toArray();

        $user_name = $arrusers[0]['first_name'] . " " . $arrusers[0]['last_name'];
        $user_status = ($arrusers[0]['status']) == '1' ? 'deactivated' : 'activated';
        $data_user = $this->getUserTable()->deleteUser($id);

        if ($data_user) {
            $user_name = $user_name;
            $full_name = $loguser->first_name . " " . $loguser->last_name;
            $activity_log_data = array();
            $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
            $activity_log_data['context_name'] = $full_name;
            $activity_log_data['context_type'] = 'user';
            $activity_log_data['controller'] = 'user';
            $activity_log_data['action'] = 'delete';
            $activity_log_data['description'] = "User : User account for '$user_name' $user_status.";

            //$activity_log_data['description']= "'$user_name' customer $user_status by $full_name";
            $libCommon->saveActivityLog($activity_log_data);
        }

        ($data_user) ? $this->flashMessenger()->addSuccessMessage("User updated successfully") : $this->flashMessenger()->addErrorMessage("Error updating user.");
        return $this->redirect()->toRoute('user_crud');
    }

    /**
     * Get instance of QuickServe\Model\UserTable
     *
     * @return QuickServe\Model\UserTable
     *
     */
    public function getUserTable() {
        if (!$this->userTable) {
            $sm = $this->getServiceLocator();
            $this->userTable = $sm->get('QuickServe\Model\UserTable');
        }
        return $this->userTable;
    }

    /**
     * add user to prosim.
     * 
     * @param User $user            Quickserve\Model\User object
     * @return Prosim/user $user
     */
    private function addUserToProsim(User $user){

        $sm = $this->getServiceLocator();
        $config = $sm->get('config');
        $libMultitenant = \Lib\Multitenant::getInstance($sm);
        /* domain name set in global settings. */
        $api_url = $config['auth_domain'].'api/v1/apps/companies/'.$GLOBALS['company_id'].'/users';
        
        $request = new \Zend\Http\Request();

        $request->getHeaders()->addHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8'
        ]);
        
        $request->setUri($api_url);
        $request->setMethod(\Zend\Http\Request::METHOD_POST); //uncomment this if the POST is used
        $request->getPost()->set('curl', 'true');
        
        $request->getPost()->set('server_api_key',$libMultitenant->getServerApiKey($config['master_db']));
        $request->getPost()->set('app_api_key', $libMultitenant->getApiKey($config['master_db']));
        $request->getPost()->set('first_name', $user->first_name);
        $request->getPost()->set('last_name', $user->last_name);
        $request->getPost()->set('email', $user->email_id);
        $request->getPost()->set('mobile', $user->phone); // dynamic value
        $request->getPost()->set('gender', $user->gender);
        $request->getPost()->set('platform', 'web');
        $request->getPost()->set('source', 'QuickServe');
        $request->getPost()->set('role_id', 2);
        $request->getPost()->set('company_id', $GLOBALS['company_id']);
        $request->getPost()->set('unit_id', $GLOBALS['unit_id']);

        $client = new \Zend\Http\Client();
        //$client->setAdapter(new \Zend\Http\Client\Adapter\Curl());

        $result = $client->dispatch($request);
        
        $response = json_decode($result->getContent());
        
        $errors = array();
        
        switch($result->getStatusCode()):
            case (201) :  
                return array('status' => $result->getStatusCode(), 'data' => $response->data);
            case (200) :// ? 200 is update
                $errors['auth_error'] = 'User already exists';
                break;
            case 400:
                $errors['auth_error'] = $response->error;
                break;
            case 422:
                foreach($response->errors as $key => $error){
                    $errors[$key] = $error;
                }
                break;
            case 404:
                $errors['auth_error'] = $response->error;
                break;
        endswitch;
        
        return array('status' => $result->getStatusCode(), 'data' => $errors);
        
    }
    /**
     * 
     * @param object $prosimUser            StdClass object
     * @return void
     */
    function addUserToMasterDB($prosimUser){
        
        $master_db_connection = $this->getServiceLocator()->get('config')['master_db'];
        $data = array(
            'auth_id' => $prosimUser->user_id,
            'company_id' => $GLOBALS['company_id'],
            'unit_id' => $GLOBALS['unit_id'],
            'username' => $prosimUser->user_name,
            'email' => $prosimUser->email,
            'first_name' => $prosimUser->first_name,
            'last_name' => $prosimUser->last_name,
            'mobile' => $prosimUser->mobile,
            'app_token' => $prosimUser->auth_code,
            'app_token_expiry' => $prosimUser->auth_code_expiry,
            'gender'  => $prosimUser->gender,
            'city'  => $prosimUser->city,
            'state' => $prosimUser->state,
            'country' => $prosimUser->country,
            'status' => 'active'
            // avatar
        );
      
        $adapter = new Adapter($master_db_connection);
        
        $sql = new \Zend\Db\Sql\Sql($adapter);
        
        $insert = $sql->insert('users');
        
        $insert->values($data);
        
        $insertString = $sql->getSqlStringForSqlObject($insert);
        
        $result = $adapter->query(
                $insertString, $adapter::QUERY_MODE_EXECUTE
        );
        
    }
    
}
