<?php

namespace Database\Factories;

use App\Models\DeliveryLocation;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_no' => 'ORD-' . fake()->unique()->numberBetween(10000, 99999),
            'customer_code' => fake()->numberBetween(1000, 9999),
            'customer_name' => fake()->name(),
            'customer_phone' => fake()->phoneNumber(),
            'ship_address' => fake()->address(),
            'order_date' => fake()->date(),
            'delivery_status' => fake()->randomElement(['Pending', 'Dispatched', 'Delivered', 'Failed']),
            'order_status' => fake()->randomElement(['New', 'Processing', 'Complete', 'Cancelled']),
            'delivery_person' => User::factory()->deliveryPerson(),
            'location_code' => DeliveryLocation::factory(),
            'amount' => fake()->randomFloat(2, 50, 500),
            'tax' => fake()->randomFloat(2, 5, 50),
            'delivery_charges' => fake()->randomFloat(2, 0, 20),
            'applied_discount' => fake()->randomFloat(2, 0, 50),
            'payment_mode' => fake()->randomElement(['Online', 'Cash', 'Card']),
            'amount_paid' => fake()->boolean(70),
            'fk_kitchen_code' => 'K' . fake()->numberBetween(1, 5),
            'order_menu' => fake()->randomElement(['lunch', 'dinner']),
            'delivery_type' => fake()->randomElement(['delivery', 'pickup']),
            'delivery_time' => fake()->time(),
            'delivery_end_time' => fake()->time(),
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }
}
