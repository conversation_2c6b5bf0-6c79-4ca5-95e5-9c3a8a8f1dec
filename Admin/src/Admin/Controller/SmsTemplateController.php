<?php

/**
 * This File mainly used for Order process at through BackEnd
* <PERSON><PERSON> can place customer's order by logging into his account
* He can also recharge into customer's account through Admin Panel
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: BackorderController.php 2015-05-04 $
* @package Admin/Controller
* @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
* @license Copyright (C) 2014 � Futurescape Technology
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Controller Admin>
* <AUTHOR> <<EMAIL>>
* @since File available since Release 1.1.0
*
*/

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use QuickServe\Model\SMSTable;
use Admin\Form\SMSForm;

use Zend\Session\Container;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\Utility;


use QuickServe\Model\SMSValidator;

class SmsTemplateController extends AbstractActionController {

	/**
	 * It has an instance of QuickServe\Model\EmailTable model
	 *
	 * @var QuickServe\Model\DiscountTable $emailTable
	 */
	protected $smsTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of emailsets
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	
	public function indexAction()
	{ 
       
        if (! $this->authservice) {
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
		$this->params()->fromRoute('order_by'):'pk_set_id';
		$order = $this->params()->fromRoute('order')?
		$this->params()->fromRoute('order'): QSelect::ORDER_DESCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;
		$select1 = $select->order($order_by . ' ' . $order);
		
		$emailset = $this->getSMSTable()->fetchAll($select1);
		
		$returnvar = $emailset->toArray();
		$itemsPerPage = 2;
		
		$emailset->current();
		$paginator = new Paginator(new paginatorIterator($emailset));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);
		
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser; 
		
		$this->layout()->setVariables(array('page_title'=>"SMS Sets",'breadcrumb'=>"SMS Sets"));
		
	//	echo'<pre>';print_r($returnvar);die;
		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar, 
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	
	public function smslogAction()
	{
	
		if (! $this->authservice) {
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
	
		$iden = $this->authservice->getIdentity();
	
	
		/* $emailqueue= $this->getEmailTable()->getemailqueue();
		$returnvar =  $emailqueue->toArray(); */
	
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
	
		$this->layout()->setVariables(array('page_title'=>"SMS Log",'breadcrumb'=>"SMS Log"));
		return new ViewModel(array(
				
				//'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	
	public function ajxSmslogAction()
	{
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
	
		$iden = $this->authservice->getIdentity();
	
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
	
		$utility = Utility::getInstance();
	
		$layout = $this->layout();
		$acl = $layout->acl;
	
		$viewModel = new ViewModel();
	
		$loggedUser = $layout->loggedUser;
	
		$select = new QSelect();
	
		
		//echo'<pre>';print_r($this->params()->fromQuery());die;
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>1,'dir'=>'desc'));
		$arrColumns = array('0'=>'sms_from','1'=>'sent_date','2'=>'mobile','3'=>'sms_content','4'=>'status');
	
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
			
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
			
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
			
		//$status = $this->params()->fromQuery('status');
			
		$columns = $this->params()->fromQuery('columns');
	
		$select->where(
	
				new \Zend\Db\Sql\Predicate\PredicateSet(
						array(
								new \Zend\Db\Sql\Predicate\Operator('sms_from', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('sent_date', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('mobile', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('sms_content', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('status', 'LIKE', '%'.$search.'%'),
									
						),
						\Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
		);
	
		$select->order($order_by . ' ' . $order);
	
		//echo'<pre>';print_r($select->getSqlString());die;
		$smsqueues = $this->getSMSTable()->getsmsqueue($select,$page);
	
		$smsqueues->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
	
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $smsqueues->getTotalItemCount();
		$returnVar['recordsFiltered'] = $smsqueues->getTotalItemCount();
		$returnVar['data'] = array();

		foreach($smsqueues as $smsque){
			$arrTmp = array();
			array_push($arrTmp,$smsque['sms_from']);
			array_push($arrTmp,$utility->displayDate($smsque['sent_date'],$setting['DATE_FORMAT']));
			array_push($arrTmp,$smsque['mobile']);
			array_push($arrTmp,str_replace("+"," ",$smsque['sms_content']));
			
			if($smsque['status'] =="sent"){ $licls="yes"; $state = "Sent";}
			elseif($smsque['status'] =="waiting"){ $licls="inQue"; $state = "In Queue";}
			elseif($smsque['status'] =="error"){ $licls="no"; $state = "Not Sent";}
			
			$status = '<span class="'.$licls.'"><strong>'.$state.'</strong></span>';
			
			array_push($arrTmp,$status);
		
			if (strpos($smsque['msg_response'],'MsgID') !== false) {
				$str ="Accepted";
			}
			else 
				$str = $smsque['msg_response'];
			
			array_push($arrTmp,$str);
			array_push($returnVar['data'],$arrTmp);
		 } 
		
		return new JsonModel($returnVar);
	}
	/**
	 * Add New SMS Template Set
	 * 
	 */
	public function addsmssetAction()
	{ 
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new SMSForm($sm);
		
		$request = $this->getRequest();
		
		if ($request->isPost())
		{
			$smsvalidator = new SMSValidator();
			 
			$form->setValidationGroup('name','description','default','character');
			
			//$smsvalidator->setAdapter($adapt);
			$form->setInputFilter($smsvalidator->getInputFilter());
			$form->setData($request->getPost());
			
			if ($form->isValid())
			{
				$smsvalidator->exchangeArray($form->getData());
                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                $smsvalidator->created_by = $authservice->pk_user_code;
                $smsvalidator->modified_by = $authservice->pk_user_code;
                $smsset = $this->getSMSTable()->saveSmsset($smsvalidator);
				return $this->redirect()->toRoute('smstemplate'); 
			}
		}
		
		$this->layout()->setVariables(array('page_title'=>"Add SMS Set",'breadcrumb'=>"SMS Set"));
	
		return array('form' => $form);
	}
	
	public function updatedefaultsetAction()
	{
		$id = $_POST['id'];
	
		$result = $this->getSMSTable()->updateisdefault($id);
		
		echo $result;die;
	
	}
	public function setdetailsAction()
	{
		$id = (int) $this->params('id');
		
		if (!$id) {
			return $this->redirect()->toRoute('smstemplate');
		}
		$smsdetailsarray = $this->getSMSTable()->getSMSTemplates($id);
		
		$setname = $this->getSMSTable()->getsmsset($id);
		
		$smsdetails = $smsdetailsarray->toArray();
		
		$this->layout()->setVariables(array('page_title'=>"SMS Templates",'breadcrumb'=>"SMS Templates"));
		return array(
				'smstemplate'=>$smsdetails,
				'id' => $id,
				'setname' => $setname->name
		);
	}
	
	public function editsetdetailsAction(){
       
        $sm = $this->getServiceLocator();        
        $libCommon = QSCommon::getInstance($sm);
        
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		$mailer = new \Lib\Email\Email();
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData($sms_common);
		
		$id = (int) $this->params('id');
		$setid = (int) $this->params('setid');
		
		$editsmstemplate = $this->getSMSTable()->editsmstemplate($setid,$id);
		$mergefields = $this->getSMSTable()->getMergeFields();
		
		$basicaraay = array();
		$i=0;
		foreach ($mergefields->toArray() as $k => $v)
		{
			if($v['type'] == 'basic')
			{
				$basicaraay[$i]['variable']=$v['variable'];
				$basicaraay[$i]['content']=$v['content'];
				$i++;
			}
				
		}
		
		$otheraray = $this->getSMSTable()->getothervariable($id,$setid);
		
		if (!$id) { 
			return $this->redirect()->toRoute('smstemplate');
		}
				
		$adapt = $sm->get('Write_Adapter');
		$form = new SMSForm($sm);
		$form->bind($editsmstemplate);
		
		$form->get('purpose')->setValue($editsmstemplate['template_key']);
		$form->get('description')->setValue($editsmstemplate['sms_content']);
		$form->get('is_approved')->setValue($editsmstemplate['is_approved']);
		$form->get('sms_template_id')->setValue($id);
		$form->get('pk_set_id')->setValue($setid);
		
		
		$request = $this->getRequest();
		
		if ($request->isPost()){
           
			$smsvalidator = new SMSValidator();
            
			$form->setValidationGroup('description','sms_template_id');
			
			$smsvalidator->exchangeArray(array('character'=>$editsmstemplate->character_limit));
			
			$form->setInputFilter($smsvalidator->getInputFilter());
			$form->setData($request->getPost());
			$postdata = $request->getPost();
			
			$editsmstemplate = $this->getSMSTable()->editsmstemplate($postdata->pk_set_id,$postdata->sms_template_id);
			
			if ($form->isValid()){
                
				$smsvalidator->exchangeArray($form->getData());
				 $emailset = $this->getSMSTable()->saveSMSTemplate($smsvalidator);
				 $support_contact = $sm->get('Config')['fooddialer_contact_info']['customer_support']['phone'];
				 $email_vars_array = array(
                    'set_name' => $editsmstemplate->name,
                    'template_key' => $editsmstemplate->template_key,
                    'company_name' => $setting['MERCHANT_COMPANY_NAME'], 
                     'support_contact' => $support_contact,
				 );
				 
				 $signature_vars_array = array(
				 		'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
				 );
				 	
				 $email_data = $libCommon->getEmailTemplateMsg('approve_sms',$email_vars_array, $signature_vars_array);
				 $contenttype = $email_data['type'];
				 $signature = $email_data['signature'];
				 
				 $mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
				 $mailer->setConfiguration($mailer_config);
				 $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
				 	
				 $sm = $this->getServiceLocator();
				 $storage_adapter = $sm->get("Write_Adapter");
				 $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
				 $queue = new \Lib\Email\Queue();
				 $queue->setStorage($mail_storage);
				 $mailer->setQueue($queue);
				 	
				 $admin_emails = $libCommon->getAdminEmail();
				 $to_array = array();
				 
				 foreach ($admin_emails as $email){
				 	$to_array[$email['first_name']] = $email['email_id'];
				 }	
				 
				 if($email_data['subject']!="" && $email_data['body']!="")
				 {
				 	$mailer->sendmail(array(),$to_array, array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
				 }
				 
				 
				return $this->redirect()->toRoute('smstemplate',array('action'=>'setdetails','id'=>$setid)); 
			}
			/* else
			{
			 	echo '<pre>ddd';print_r($form->getMessages());exit;
			} */
			
		}
		
		$this->layout()->setVariables(array('page_title'=>"Set Details",'breadcrumb'=>"SMS Details"));
		return array('form' => $form,'id' => $id,'mergefields' =>$mergefields,'basicvariable' => $basicaraay,'othervariable' => $otheraray);
		
	}
	
	public function updatesetstatusAction()
	{
		$templateid = $_POST['id'];
		$fkid = $_POST['fkid'];
		$val = $_POST['val'];
		
		$result = $this->getSMSTable()->updatestatus($templateid,$fkid,$val);
		
		echo $result;die;
		
	}
	
	public function editAction()
	{ 
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('smstemplate');
		}
		
		$getsmsset = $this->getSMSTable()->getsmsset($id);
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new SMSForm($sm);
		//echo'<pre>';print_r($form);die;
		
		$form->bind($getsmsset);
		$form->get('default')->setValue($getsmsset['is_default']);
		$form->get('character')->setValue($getsmsset['character_limit']);
		
		$request = $this->getRequest();
		
		if ($request->isPost())
		{
			
			//echo '<pre> request = ';print_r($request);die;
			$smsvalidator = new SMSValidator();
		
			$form->setValidationGroup('pk_set_id','name','description','character');
		
			//$smsvalidator->setAdapter($adapt);
			$form->setInputFilter($smsvalidator->getInputFilter());
			$form->setData($request->getPost());
		
			if ($form->isValid())
			{
				$smsvalidator->exchangeArray($form->getData());
				$smsset = $this->getSMSTable()->saveSmsset($smsvalidator);
				return $this->redirect()->toRoute('smstemplate');
			}
			/* else
			{
				echo '<pre>ddd';print_r($form->getMessages());exit;
			} */
		} 

		$this->layout()->setVariables(array('page_title'=>"Edit SMS Set",'breadcrumb'=>"SMS Set"));
		return array('form' => $form);
	
	}
	
	/**
	 * Get instance of QuickServe\Model\SMSTable
	 *
	 * @return QuickServe\Model\SMSTable
	 */
	public function getSMSTable(){
		if (!$this->smsTable) {
			$sm = $this->getServiceLocator();
			$this->smsTable = $sm->get('QuickServe\Model\SMSTable');
		}
		return $this->smsTable;
	}
	
	public function deletelogAction(){
		$smsset = $this->getSMSTable()->deleteSmsLog();
		return new JsonModel(array('msg'=>'Log deleted successfully'));
	}
}
