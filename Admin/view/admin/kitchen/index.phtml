<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addkit" class="common-orange-btn-on-hover"> Add Kitchen</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updkit" class="common-orange-btn-on-hover">Update Kitchen details</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}
			elseif ($this->FlashMessenger()->hasInfoMessages()){
				foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box info round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}elseif($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Kitchen Screens</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'kitchen_master','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('kitchen_master', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Kitchen Screen</button>
                    </div>
                  <?php } ?>
                </li>
               
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="product_category" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Kitchen Name</th>
                            <th width="15%">Kitchen Alias</th>
                            <th width="40%">Location</th>
                            <th>Base Kitchen</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                    
                     <?php foreach ($paginator as $kitchens) { 
                     
                     	?>
                        <tr>
                            <td><?php echo $this->escapeHtml($kitchens->kitchen_name);?></td>
                          
                            <td><?php echo ucfirst($this->escapeHtml($kitchens->kitchen_alias));?></td>
                            <td><?php echo ucfirst($this->escapeHtml($kitchens->location));?></td>
                            <td><?php 
                            		if($kitchens->base_kitchen=='0'){
                            			echo "No";
                            		}else{
                            			echo "Yes";
                            		}
                            	?>
                            </td>
                            <td>
	                            <?php if($acl->isAllowed($loggedUser->rolename,'kitchen_master','edit')) { ?>
	                            <a href="<?php echo $this->url('kitchen_master', array('action' => 'edit', 'id' => $kitchens->pk_kitchen_code)); ?>" class="btn btn5 btn_pencil5">
	                            	<button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
	                            </a>
	                            <?php } ?>
                            </td>
                        </tr>
                       <?php } ?>
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();
	$("#product_category").dataTable().fnDestroy();

	$('#product_category').dataTable( {
    "aoColumnDefs": [
      	                {
      	                   bSortable: false,
      	                   aTargets: [ -1 ]
      	                }
      	              ],
    });	
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addkit",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add kitchen settings");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updkit",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-intro", "Click here to edit kitchen settings");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-intro");
  });

</script>  