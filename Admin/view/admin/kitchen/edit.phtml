<?php 
$utility = \Lib\Utility::getInstance();;
$form = $this->form;
$form->setAttribute('action', $this->url('kitchen_master', array('action' => 'edit','id'=>$this->id)));
//$form->setAttribute('class','stdform');
$form->prepare();

$base_kitchen  = $form->get('base_kitchen');
$base_kitchen_opt = $base_kitchen->getOptions();

$seleted_print = $form->get('base_kitchen')->getValue();
$kitchen_settings_arr = json_decode($kitchen_settings);
$arr = array();
foreach($kitchen_settings_arr as $h){
	$arr[$h->key]=$h->value;
}

//echo "<pre>";print_r($arr);die();
?>
<style>
ul, ol, dl {
	font-size: inherit;
}

/* .calendar .selector .date-selector{ */
/*     height: 1.6rem; */
/* } */
div.checker, div.checker span, div.checker input {
	float: left;
    height: 19px;
    margin-top: 0px;
    width: 19px;
}
.calendar {
    left: 6px;
}
.calendar .selector .date-selector, .calendar .selector .time-selector {
	height: 25px;
    padding: 0 0 0 5px;
}

.add-customer-checkbox label {
	display: inline-block;
	line-height: 0px;
}
</style>      
      <!-- END PAGE HEADER-->
      
      <div id="content">
      <?php echo $this->form()->openTag($form);?>
        <div class="large-8 columns">
        <fieldset>
			<legend>
				Kitchen Info
			</legend>
          
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('kitchen_name')); ?></label>
              </div>
              <div class="large-4 small-8 medium-8 columns left">
                <?php  
                 	echo $this->formHidden($form->get('pk_kitchen_code'));
					echo $this->formElement($form->get('kitchen_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('kitchen_name'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('kitchen_alias')); ?></label>
              </div>
              <div class="large-4 small-8 medium-8 columns left">
                  <?php 
                  	echo $this->formElement($form->get('kitchen_alias'));
					echo $this->formElementErrors($form->get('kitchen_alias')); 
				 ?>
              </div>
            </div>
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('city_id')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('city_id'));
					echo $this->formElementErrors($form->get('city_id')); 
				?>
            	</div>
            </div>
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('location_id')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('location_id'));
					echo $this->formElementErrors($form->get('location_id')); 
				?>
            	</div>
            </div>
            
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('kitchen_address')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('kitchen_address'));
					echo $this->formElementErrors($form->get('kitchen_address')); 
				?>
            	</div>
            </div>
            
            <div class="row">
				<div class="large-4 small-4 medium-4 columns">
					<?php echo $this->formLabel($form->get('base_kitchen')); ?>
				</div>
				<div class="large-4 small-8 medium-8 columns prepaid left">
				
	            <?php
	                foreach($base_kitchen_opt['value_options'] as $key=>$val){
	            ?>
				<input  type="radio" id="<?php echo $key;?>" name="base_kitchen"  value="<?php echo $key;?>" <?php echo $checked=($seleted_print==$key)?'checked':''?>>
				<label class="pull-left" for="<?php echo $key;?>" id="<?php echo $key;?>"><?php echo $val;?></label>
				<?php }?>
			
				</div>
				<?php echo $this->formElementErrors($form->get('base_kitchen'));?>
			</div>
			
					<?php 
						$generalsettingarr= array("CUSTOMER_PAYMENT_MODE","MIN_ORDER_PRICE","MAX_ORDER_PRICE","MENU_TYPE","ORDER_NOTIFICATION_EMAIL");
						$allpaymentmodes = array('neft','cheque');
                        
                        if($utility->checksubscription('customer_wallet','allowed')){
                            $wallet = 'wallet';
            
                            array_push($allpaymentmodes,$wallet);
                         }
        
                          if($utility->checksubscription('payment_online','allowed')){
                            $online = 'online';
            
                            array_push($allpaymentmodes,$online);
                         }
                         
                         if($utility->checksubscription('payment_cod','allowed')){
                            $cash = 'cash';
            
                            array_push($allpaymentmodes,$cash);
                         }
        
                       $form->get('CUSTOMER_PAYMENT_MODE')->setValueOptions($allpaymentmodes);
                       
						//$allmenutype = array('breakfast','lunch','dinner')
					?>
            		
			<fieldset>
				<legend><?php echo 'General Settings';?></legend>
					<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('CUSTOMER_PAYMENT_MODE')); ?></label>
		             	</div>
		            	<div class="large-8  small-8 medium-8 columns left">
		            	
		            	<?php 
		            	$myArray = explode(',', $arr['K'.$kitchen->pk_kitchen_code.'_'.'CUSTOMER_PAYMENT_MODE']);
		            	foreach($allpaymentmodes as $key=>$val)
						{
							$class="";
							foreach($myArray as $selkey=>$selval)
							{
								if($val==$selval)
								{
									$class="checked";
									break;
								}
							
							}
						?>
						<label class="inline left mr5" style="line-height: 0.9em;">
							<input type="checkbox"  value="<?php echo $val;?>" class="<?php echo $val;?>check"  name="CUSTOMER_PAYMENT_MODE[]" <?php echo $class; ?> data-show=".<?php echo $val;?>">
							<label class="mr25 mtm4"><?php echo ucfirst($val);?></label>	
						</label>
						<?php }
						echo $this->formElementErrors($form->get('CUSTOMER_PAYMENT_MODE')); 
						?>
					
		   	         	</div>
            		</div>
            	
            		<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo $this->formLabel($form->get('MIN_ORDER_PRICE')); ?></label>
            				</div>
            				<div class="large-4  small-8 medium-8 columns left">

            						<input type="text" name = "<?php echo $generalsettingarr[1];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.'MIN_ORDER_PRICE'];?>"/>
									<?php echo $this->formElementErrors($form->get('MIN_ORDER_PRICE'));?> 
	            			</div>
            			</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('MAX_ORDER_PRICE')); ?></label>
		             	</div>
		            	<div class="large-4  small-8 medium-8 columns left">
		            	<input type="text" name = "<?php echo $generalsettingarr[2];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.'MAX_ORDER_PRICE'];?>"/>
		            	<?php 
	
							echo $this->formElementErrors($form->get('MAX_ORDER_PRICE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('MENU_TYPE')); ?></label>
		             	</div>
		            	<div class="large-6  small-8 medium-8 columns left">
		            	<?php 
		            	
		            	$mymenuArray = explode(',', $arr['K'.$kitchen->pk_kitchen_code.'_'.'MENU_TYPE']);
		            	 
		            	foreach($menu_type as $key=>$val)
		            	{
		            		$class="";
		            		foreach($mymenuArray as $selkey=>$selval)
		            		{
		            			if($val==$selval)
		            			{
		            				$class="checked";
		            				break;
		            			}
		            		}
		            		?>
		            		<div class="add-customer-checkbox">
		            			<input type="checkbox"  value="<?php echo $val;?>" class="<?php echo $val;?>check"  name="MENU_TYPE[]" <?php echo $class; ?> data-show=".<?php echo $val;?>">
		            		<label class="loc"><?php echo ucfirst($val);?></label>	
		            		</div>
		            	<?php }
		            	
		           			echo $this->formElementErrors($form->get('MENU_TYPE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('ORDER_NOTIFICATION_EMAIL')); ?></label>
		             	</div>
		            	<div class="large-6  small-8 medium-8 columns left">
		            	<input type="text" name = "<?php echo $generalsettingarr[4];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.'ORDER_NOTIFICATION_EMAIL'];?>"/>
		            	<?php 
		               		echo $this->formElementErrors($form->get('ORDER_NOTIFICATION_EMAIL')); 
						?>
		   	         	</div>
            		</div>
	           </fieldset>
			
			<?php 
				$timearray = array("ORDER_CUT_OFF_TIME","ORDER_CANCEL_CUT_OFF_TIME","AUTO_DELIVERY","AUTO_DISPATCH");
				foreach($menu_type as $k=>$v){ 
					$v = strtoupper($v);
				?>
				<fieldset>
					<legend><?php echo $v.' Settings';?></legend>
						<div class="row">
				             	<div class="large-4 small-4 medium-4 columns">
				            		<label class="inline right"><?php echo 'Order cut off day';?></label>
				             	</div>
				            	<div class="large-8  small-8 medium-8 columns">
				            		<div class="large-3 small-3 medium-3 columns">
										<select class="settingsvariable" name = "<?php echo 'settings_'.$v;?>_ORDER_CUT_OFF_DAY">
											<option value = "0">Same Day</option>
											<option value = "1">One Day Before</option>
											<option value = "2">Two Day Before</option>
											<option value = "3">Three Day Before</option>
											<option value = "4">Four Day Before</option>
											<option value = "5">Five Day Before</option>
											<option value = "6">Six Day Before</option>
											<option value = "7">Seven Day Before</option>
										</select>
									</div>
									<div class="large-3 small-3 medium-3 columns pl0 left">
										<input type="text"  name = "<?php echo 'settings_'.$v.'_'.$timearray[0];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[0]];?>"/>
									</div>
				            	</div>
	            		</div>
            			<?php /*<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[0])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto">
            						<input type="text" data-time name = "<?php echo 'settings_'.$v.'_'.$timearray[0];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[0]];?>"/>
	            				</div>
	            			</div>
            			</div> */ ?>
            			
	            		<div class="row">
				             	<div class="large-4 small-4 medium-4 columns">
				            		<label class="inline right"><?php echo 'Order cancel cut off day';?></label>
				             	</div>
				            	<div class="large-8  small-8 medium-8 columns">
				            		<div class="large-3 small-3 medium-3 columns">
										<select class="settingsvariable" name = "<?php echo 'settings_'.$v;?>_ORDER_CANCEL_CUT_OFF_DAY" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_ORDER_CANCEL_CUT_OFF_DAY'];?>">
											<option value = "0">Same Day</option>
											<option value = "1">One Day Before</option>
											<option value = "2">Two Day Before</option>
											<option value = "3">Three Day Before</option>
										</select>
									</div>
									<div class="large-3 small-3 medium-3 columns pl0 left">
										<input type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[1];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[1]];?>"/>
									</div>
				            	</div>
	            		</div>
	            		<?php /*<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[1])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto">
            						<input type="text" data-time name = "<?php echo 'settings_'.$v.'_'.$timearray[1];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[1]];?>"/>
	            				</div>
	            			</div>
            			</div>*/ ?>
            			<?php if(array_key_exists('ENABLE_AUTO_DELIVERY', $setting) && $setting['ENABLE_AUTO_DELIVERY']=='yes'){ ?>
            			<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[2])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
                                <?php $auto_delivery_array = explode(',' ,$arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[2]]);  ?>
                                <input type="hidden" id="auto_delivery" name= "<?php echo 'settings_'.$v.'_'.$timearray[2];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[2]] ;?>"/>
                                <div class="large-3 small-3 medium-3 columns">
										<select id="auto_delivery_day" class="" name = "<?php echo $v.'_'.$timearray[2].'_DAY';?>" >
											<option <?php if ($auto_delivery_array[0] == 0 ) echo 'selected' ; ?>  value = "0">Same Day</option>
											<option <?php if ($auto_delivery_array[0] == 1 ) echo 'selected' ; ?>  value = "1">One Day After</option>
											<option <?php if ($auto_delivery_array[0] == 2 ) echo 'selected' ; ?>  value = "2">Two Day After</option>
											<option <?php if ($auto_delivery_array[0] == 3 ) echo 'selected' ; ?>  value = "3">Three Day After</option>
										</select>
                                </div> 
                                <div class="large-3 small-3 medium-3 columns pl0 left">
            						<input id="auto_delivery_time" type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[2];?>" value = "<?php if($auto_delivery_array) echo $auto_delivery_array[1];?>"/>
	            				</div>
	            			</div>
            			</div>
            			<?php } ?>
            			<?php if(array_key_exists('ENABLE_AUTO_DISPATCH', $setting) && $setting['ENABLE_AUTO_DISPATCH']=='yes'){ ?>
            			<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[3])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
                                <?php $auto_dispatch_array = explode(',' ,$arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[3]]);  ?>
                                <input type="hidden" id="auto_delivery" name= "<?php echo 'settings_'.$v.'_'.$timearray[3];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[3]] ;?>"/>
                                <div class="large-3 small-3 medium-3 columns">
										<select id="auto_delivery_day" class="" name = "<?php echo $v.'_'.$timearray[3].'_DAY';?>" >
											<option <?php if ($auto_dispatch_array[0] == 0 ) echo 'selected' ; ?>  value = "0">Same Day</option>
											<option <?php if ($auto_dispatch_array[0] == 1 ) echo 'selected' ; ?>  value = "1">One Day After</option>
											<option <?php if ($auto_dispatch_array[0] == 2 ) echo 'selected' ; ?>  value = "2">Two Day After</option>
											<option <?php if ($auto_dispatch_array[0] == 3 ) echo 'selected' ; ?>  value = "3">Three Day After</option>
										</select>
                                </div> 
                                <div class="large-3 small-3 medium-3 columns pl0 left">
            						<input id="auto_dispatch_time"  type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[3];?>" value = "<?php if($auto_dispatch_array) echo $auto_dispatch_array[1];?>"/>
	            				</div>
	            			</div>
            			</div>
            			<?php } ?>
                        <?php if(array_key_exists('GLOBAL_THIRDPARTY_DELIVERY', $setting) && !empty($setting['GLOBAL_THIRDPARTY_DELIVERY']) ){
                                $tpArray = explode(',',$setting['GLOBAL_THIRDPARTY_DELIVERY']);
                                foreach($tpArray as $tp){
                        ?>
	            		<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
                                <label class="inline right"><?php echo strtoupper($tp)?> Pickup Time</label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto">
            						<input type="text" name = "<?php echo 'settings_'.$v.'_'.strtoupper($tp).'_PICKUPTIME';?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.strtoupper($tp).'_PICKUPTIME'];?>"/>
	            				</div>
	            			</div>
            			</div>              
                        <?php      } 
                                }
                        ?>   
                    <!-- 12th april - sankalp --> 
                    <div class="row prepaidrow " style="display:<?php //echo $display_day;?>">
                             <div class="large-4 small-4 medium-4 columns">
                                    <label class="inline right">Choose Week-Off Days</label>
                            </div>
                            <div class="large-6 small-8 medium-8 columns left">

                                <select name = "<?php echo 'settings_'.$v;?>_WEEKOFFS[]" multiple="multiple" placeholder="select day" class="SlectBox day unidays">
                                    <?php 
                                    if(array_key_exists( 'K'.$kitchen->pk_kitchen_code.'_'.$v.'_WEEKOFFS', $arr)){

                                            $unique = explode(',',$arr[ 'K'.$kitchen->pk_kitchen_code.'_'.$v.'_WEEKOFFS' ]);

                                            foreach ($allDays as $key_u => $val_u)
                                            {
                                                         $selected = (in_array($key_u,$unique )) ? 'selected': '';
                                                    ?>
                                                        <option <?php echo $selected?> value="<?php echo $key_u;?>"><?php echo $val_u; ?></option>
                                                    <?php 
                                            }
                                    } 
                                    else{
                                        foreach ($allDays as $key_u => $val_u)
                                        {?>
                                                    <option  value="<?php echo $key_u;?>"><?php echo $val_u; ?></option>
                                    <?php 
                                        }
                                    }
                                    ?> 
                                </select>

                            </div>

                    </div> 
            	</fieldset>
			<?php }
			?>
			
		</fieldset>
		 
			<?php echo $this->formElement($form->get('csrf')); ?>
			<?php echo $this->formElement($form->get('backurl'));?>
			<div class="large-12 columns pl0 pr0">
	            <div class="row">
	              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
	              <div class="large-8 small-8 medium-8 columns">
	              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
	                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
	              </div>
	            </div>
           </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>

<?php 
$arrLcode = explode("#",$kitchen->location_id);
$lcode = $arrLcode[0];

?>
    <!-- END PAGE CONTAINER--> 
    
<script type="text/javascript">
    
 $(document).ready(function(){
     
    // auto delivery
    $('#auto_delivery_day').on('change', function(){
        var auto_delivery = $('#auto_delivery').val();
        var auto_delivery_array = auto_delivery.split(',');
        $('#auto_delivery').val(this.value+','+auto_delivery_array[1]);
    });
    
	window.asd = $('.SlectBox').SumoSelect({ csvDispCount: 3 });
	
	var city_id = $("#city_id").val();

	if(city_id !=""){
		getAllLocations(city_id,"location_id");
		renderLocationOptions("location_id","<?php echo $lcode;?>");
	}

	$("#city_id").on('change',function(event){

		getAllLocations($(this).val(),"location_id");
		renderLocationOptions("location_id");
		
	});	

	$(".chosen-select").chosen();
	<?php 
		//print_r(json_decode($kitchen_settings));
		$kitchen_settings = json_decode($kitchen_settings);
		
		foreach($kitchen_settings as $y){ ?>
			var dbkitchensettingname = '<?php echo $y->key;?>';
			$('.settingsvariable').each(function(n,k){
				var nametopost = $(this).attr('name').replace('settings','K'+<?php echo $kitchen->pk_kitchen_code;?>);
				if(dbkitchensettingname == nametopost){
					$(this).val('<?php echo $y->value;?>');
				}
				//console.log(nametopost);
			});
	<?php }
	?>
 });
</script>     
