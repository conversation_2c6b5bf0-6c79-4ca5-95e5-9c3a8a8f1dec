<?php

echo "🔄 Testing Order Swap API - Product Swapping Within Same Category\n";
echo "=" . str_repeat("=", 70) . "\n\n";

echo "📋 NEW API ENDPOINT: Order Swap Functionality\n";
echo "-" . str_repeat("-", 70) . "\n";
echo "Endpoint: POST /api/v2/order-management/swap/{orderNo}\n";
echo "Purpose: Swap order product with another product from the same category\n";
echo "Updates: Both orders and order_details tables\n\n";

echo "🎯 API Features:\n";
echo "-" . str_repeat("-", 70) . "\n";
$features = [
    '✅ Product Category Validation - Only same category swaps allowed',
    '✅ Order Status Validation - Only swappable orders (not delivered/cancelled)',
    '✅ Price Difference Calculation - Automatic price adjustment',
    '✅ Swap Charges Support - Additional charges for premium swaps',
    '✅ Tax Recalculation - Updated tax based on new amount',
    '✅ Order Details Update - All related records updated',
    '✅ Audit Logging - Complete swap history tracking',
    '✅ Transaction Safety - Database rollback on errors'
];

foreach ($features as $feature) {
    echo "  {$feature}\n";
}

echo "\n📊 Database Tables Updated:\n";
echo "-" . str_repeat("-", 70) . "\n";

$tableUpdates = [
    'orders' => [
        'product_code' => 'Updated to new product code',
        'product_name' => 'Updated to new product name',
        'product_description' => 'Updated to new product description',
        'amount' => 'Adjusted for price difference + swap charges',
        'tax' => 'Recalculated based on new amount',
        'last_modified' => 'Updated to current timestamp',
        'remark' => 'Appended with swap details and reason'
    ],
    'order_details' => [
        'product_code' => 'Updated to new product code',
        'product_name' => 'Updated to new product name',
        'product_amount' => 'Proportionally adjusted',
        'product_tax' => 'Recalculated for new amount',
        'product_generic_code' => 'Updated to new product code',
        'product_generic_name' => 'Updated to new product name'
    ]
];

foreach ($tableUpdates as $table => $fields) {
    echo "📋 {$table} table:\n";
    foreach ($fields as $field => $description) {
        echo "  • {$field}: {$description}\n";
    }
    echo "\n";
}

echo "🔍 Validation Rules:\n";
echo "-" . str_repeat("-", 70) . "\n";

$validationRules = [
    'Same Category' => 'Products must belong to the same category/type',
    'Order Status' => 'Order must not be Cancelled, Delivered, or Complete',
    'Delivery Status' => 'Order must not be Delivered, Failed, or Dispatched',
    'Order Date' => 'Cannot swap orders for past dates',
    'Product Status' => 'Both current and new products must be active',
    'Swappable Flag' => 'New product must have is_swappable = true',
    'Food Type Match' => 'Veg/Non-veg classification must match'
];

foreach ($validationRules as $rule => $description) {
    echo "  ✅ {$rule}: {$description}\n";
}

echo "\n💰 Price Calculation:\n";
echo "-" . str_repeat("-", 70) . "\n";
echo "Formula: New Amount = Old Amount + Price Difference + Swap Charges\n";
echo "Example:\n";
echo "  Old Product Price: ₹150.00\n";
echo "  New Product Price: ₹200.00\n";
echo "  Swap Charges: ₹25.00\n";
echo "  Price Difference: ₹50.00\n";
echo "  Total Amount Change: ₹75.00\n";
echo "  New Order Amount: ₹225.00 (if old order was ₹150)\n\n";

echo "📋 Request Format:\n";
echo "-" . str_repeat("-", 70) . "\n";
echo "POST /api/v2/order-management/swap/{orderNo}\n";
echo "Content-Type: application/json\n\n";

echo "Request Body:\n";
echo "{\n";
echo "  \"order_date\": \"2025-09-03\",\n";
echo "  \"new_product_code\": 342,\n";
echo "  \"reason\": \"Customer wants to change from Poha to Upma\",\n";
echo "  \"meal_type\": \"breakfast\"\n";
echo "}\n\n";

echo "📋 Response Format:\n";
echo "-" . str_repeat("-", 70) . "\n";

echo "Success Response (200):\n";
echo "{\n";
echo "  \"success\": true,\n";
echo "  \"message\": \"Order swapped successfully\",\n";
echo "  \"data\": {\n";
echo "    \"order_id\": 127810,\n";
echo "    \"order_no\": \"QA93250725\",\n";
echo "    \"order_date\": \"2025-09-03\",\n";
echo "    \"swap_details\": {\n";
echo "      \"old_product\": {\n";
echo "        \"code\": 341,\n";
echo "        \"name\": \"Poha\",\n";
echo "        \"price\": 150.00\n";
echo "      },\n";
echo "      \"new_product\": {\n";
echo "        \"code\": 342,\n";
echo "        \"name\": \"Upma\",\n";
echo "        \"price\": 200.00\n";
echo "      },\n";
echo "      \"price_difference\": 50.00,\n";
echo "      \"swap_charges\": 25.00,\n";
echo "      \"total_amount_change\": 75.00,\n";
echo "      \"new_order_amount\": 225.00\n";
echo "    },\n";
echo "    \"reason\": \"Customer wants to change from Poha to Upma\"\n";
echo "  }\n";
echo "}\n\n";

echo "Error Responses:\n";
echo "404 - Order not found:\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Order not found for the specified date\",\n";
echo "  \"data\": {\n";
echo "    \"order_no\": \"QA93250725\",\n";
echo "    \"order_date\": \"2025-09-03\"\n";
echo "  }\n";
echo "}\n\n";

echo "400 - Products not swappable:\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Products are not in the same category or not swappable\",\n";
echo "  \"data\": {\n";
echo "    \"current_product\": {\n";
echo "      \"code\": 341,\n";
echo "      \"name\": \"Poha\",\n";
echo "      \"category\": \"Breakfast\",\n";
echo "      \"type\": \"Meal\"\n";
echo "    },\n";
echo "    \"new_product\": {\n";
echo "      \"code\": 355,\n";
echo "      \"name\": \"Biryani\",\n";
echo "      \"category\": \"Lunch\",\n";
echo "      \"type\": \"Meal\"\n";
echo "    }\n";
echo "  }\n";
echo "}\n\n";

echo "🧪 Test Scenarios:\n";
echo "-" . str_repeat("-", 70) . "\n";

$testScenarios = [
    [
        'name' => 'Valid Breakfast Swap',
        'order_no' => 'QA93250725',
        'request' => [
            'order_date' => '2025-09-03',
            'new_product_code' => 342,
            'reason' => 'Change from Poha to Upma',
            'meal_type' => 'breakfast'
        ],
        'expected' => 'Success - Same breakfast category',
        'status' => '✅ Should Work'
    ],
    [
        'name' => 'Cross-Category Swap (Should Fail)',
        'order_no' => 'QA93250725',
        'request' => [
            'order_date' => '2025-09-03',
            'new_product_code' => 355,
            'reason' => 'Change breakfast to lunch item'
        ],
        'expected' => 'Error - Different categories',
        'status' => '❌ Should Fail'
    ],
    [
        'name' => 'Past Date Swap (Should Fail)',
        'order_no' => 'QA93250725',
        'request' => [
            'order_date' => '2025-07-20',
            'new_product_code' => 342,
            'reason' => 'Change past order'
        ],
        'expected' => 'Error - Past date not swappable',
        'status' => '❌ Should Fail'
    ],
    [
        'name' => 'Cancelled Order Swap (Should Fail)',
        'order_no' => 'CANCELLED123',
        'request' => [
            'order_date' => '2025-09-03',
            'new_product_code' => 342,
            'reason' => 'Change cancelled order'
        ],
        'expected' => 'Error - Order not swappable',
        'status' => '❌ Should Fail'
    ]
];

foreach ($testScenarios as $index => $scenario) {
    echo ($index + 1) . ". {$scenario['name']} ({$scenario['status']})\n";
    echo "   Order: {$scenario['order_no']}\n";
    echo "   Request: " . json_encode($scenario['request']) . "\n";
    echo "   Expected: {$scenario['expected']}\n\n";
}

echo "🚀 Testing Commands:\n";
echo "-" . str_repeat("-", 70) . "\n";

echo "1. Valid breakfast item swap:\n";
echo "curl -X POST 'http://192.168.1.16:8000/api/v2/order-management/swap/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '{\n";
echo "    \"order_date\": \"2025-09-03\",\n";
echo "    \"new_product_code\": 342,\n";
echo "    \"reason\": \"Customer wants to change from Poha to Upma\",\n";
echo "    \"meal_type\": \"breakfast\"\n";
echo "  }'\n\n";

echo "2. Test cross-category swap (should fail):\n";
echo "curl -X POST 'http://192.168.1.16:8000/api/v2/order-management/swap/QA93250725' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '{\n";
echo "    \"order_date\": \"2025-09-03\",\n";
echo "    \"new_product_code\": 355,\n";
echo "    \"reason\": \"Try to change breakfast to lunch item\"\n";
echo "  }'\n\n";

echo "3. Check order details after swap:\n";
echo "curl -X GET 'http://192.168.1.16:8000/api/v2/order-management/details/QA93250725'\n\n";

echo "📋 Database Verification Queries:\n";
echo "-" . str_repeat("-", 70) . "\n";

echo "1. Check updated order:\n";
echo "SELECT pk_order_no, order_no, product_code, product_name, amount, tax, remark, last_modified\n";
echo "FROM orders WHERE order_no = 'QA93250725' AND order_date = '2025-09-03';\n\n";

echo "2. Check updated order details:\n";
echo "SELECT pk_order_detail_id, ref_order_no, product_code, product_name, product_amount, product_tax\n";
echo "FROM order_details WHERE ref_order_no = 'QA93250725' AND order_date = '2025-09-03';\n\n";

echo "3. Check product categories for validation:\n";
echo "SELECT pk_product_code, name, product_category, category, product_type, food_type, is_swappable, swap_charges\n";
echo "FROM products WHERE pk_product_code IN (341, 342, 355);\n\n";

echo "🎯 Key Benefits:\n";
echo "-" . str_repeat("-", 70) . "\n";
$benefits = [
    '✅ Customer Flexibility - Easy meal swapping within categories',
    '✅ Revenue Protection - Swap charges for premium upgrades',
    '✅ Data Integrity - Comprehensive validation and error handling',
    '✅ Audit Trail - Complete logging of all swap activities',
    '✅ Real-time Updates - Immediate reflection in orders and details',
    '✅ Category Safety - Prevents inappropriate cross-category swaps',
    '✅ Status Validation - Ensures only valid orders can be swapped',
    '✅ Price Accuracy - Automatic recalculation of amounts and taxes'
];

foreach ($benefits as $benefit) {
    echo "  {$benefit}\n";
}

echo "\n🎉 ORDER SWAP API READY!\n";
echo "-" . str_repeat("-", 70) . "\n";
echo "The new order swap functionality allows customers to change their\n";
echo "meal selection within the same category while maintaining data\n";
echo "integrity and proper financial calculations.\n\n";

echo "Status: ✅ IMPLEMENTED - Ready for testing!\n";

function testOrderSwapAPI($orderNo, $requestData) {
    $url = "http://192.168.1.16:8000/api/v2/order-management/swap/{$orderNo}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return "Failed to make request";
    }
    
    return "HTTP {$httpCode}: " . $response;
}
