import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AdminCompanyProfile } from '@/components/admin-service-v12/company-profile';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AdminCompanyProfile', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AdminCompanyProfile />
      </QueryClientProvider>
    );

    expect(screen.getByText('AdminCompanyProfile')).toBeInTheDocument();
  });
});