
<script type="text/javascript" src="./js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="./js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="./js/custom/general.js"></script>
<script type="text/javascript" src="./js/custom/form.js"></script>

        <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Order Summary Details</span></h2>
          </div>
          <!--contenttitle-->

          <form class="stdform txtFieldContent" action="" method="post">
            <div class="subHeader">
              <p>Customer Info</p>
            </div>
            <p>
              <label>Order No :</label>
              <span class="txtField">
              <label><?php echo $order['pk_order_no']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Customer Name :</label>
              <span class="txtField">
              <label><?php echo $order['customer_name']. PHP_EOL; ?></label>
              </span> </p>

            <p>
              <label>Group :</label>
              <span class="txtField">
              <label><?php echo $order['group_name']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Phone :</label>
              <span class="txtField">
              <label><?php echo $order['phone']. PHP_EOL; ?></label>
              </span> </p>

             <p>
              <label>City :</label>
              <span class="txtField">
              <label><?php echo $order['city']. PHP_EOL; ?></label>
              </span> </p>
             <p>
              <label>Address :</label>
              <span class="txtField">
              <label>1909, Cyber one</label>
              </span> </p>

            <div class="subHeader">
              <p>Product Info</p>
            </div>
            <p>
              <label>Product:</label>
              <span class="txtField">
              <label><?php echo $order['name']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Quantity :</label>
              <span class="txtField">
              <label><?php echo $order['quantity']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Promo Code :</label>
              <span class="txtField">
              <label><?php echo $order['promo_code']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Amount :</label>
              <span class="txtField">
              <label><?php echo $order['amount']. PHP_EOL; ?> </label>
              </span> </p>
            <p>
              <label>Applied Discount :</label>
              <span class="txtField">
              <label><?php echo $order['applied_discount']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Delivery Location :</label>
              <span class="txtField">
              <label><?php echo $order['delivery_status']. PHP_EOL; ?></label>
              </span> </p>

            <div class="subHeader">
              <p>Time Periods</p>
            </div>
            <p>
              <label>Order Status :</label>
              <span class="txtField">
              <label><?php echo $order['order_status']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Order Date :</label>
              <span class="txtField">
              <label><?php echo $order['order_date']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Due Date :</label>
              <span class="txtField">
              <label><?php echo $order['due_date']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Last Modified :</label>
              <span class="txtField">
              <label><?php echo $order['last_modified']. PHP_EOL; ?></label>
              </span> </p>
            <p>
              <label>Delivery Status :</label>
              <span class="txtField">
              <label><?php echo $order['delivery_status']. PHP_EOL; ?></label>
              </span> </p>
              <p>
              <label>Kitchen Status :</label>
              <span class="txtField">
              <label><?php echo $order['kitchen_status']. PHP_EOL; ?></label>
              </span> </p>
          </form>
          <br />
          <br />
        </div>
        <!--content-->


