describe('Parent Dashboard E2E Tests', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('GET', '/v2/parents/profile', {
      fixture: 'school-tiffin/parent-profile.json'
    }).as('getParentProfile');

    cy.intercept('GET', '/v2/parents/children', {
      fixture: 'school-tiffin/children.json'
    }).as('getChildren');

    cy.intercept('GET', '/v2/school-meal-subscriptions?status=active', {
      fixture: 'school-tiffin/active-subscriptions.json'
    }).as('getActiveSubscriptions');

    cy.intercept('GET', '/v2/school/batches?delivery_date=*', {
      fixture: 'school-tiffin/today-deliveries.json'
    }).as('getTodayDeliveries');

    cy.intercept('GET', '/v2/meal-plans*', {
      fixture: 'school-tiffin/meal-plans.json'
    }).as('getMealPlans');

    cy.intercept('GET', '/v2/schools*', {
      fixture: 'school-tiffin/schools.json'
    }).as('getSchools');

    // Set up authentication
    cy.window().then((win) => {
      win.localStorage.setItem('auth_token', 'mock-jwt-token');
    });

    // Visit the parent dashboard
    cy.visit('/school-tiffin/parent-dashboard');
  });

  describe('Dashboard Loading and Navigation', () => {
    it('loads the dashboard successfully', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[data-testid="parent-dashboard"]').should('be.visible');
      cy.contains('Parent Dashboard').should('be.visible');
      cy.contains('Manage your children\'s meal subscriptions and track deliveries').should('be.visible');
    });

    it('displays loading skeletons initially', () => {
      cy.visit('/school-tiffin/parent-dashboard');
      
      // Should show skeleton loading before API responses
      cy.get('[data-testid="skeleton"]').should('have.length.at.least', 4);
    });

    it('navigates between tabs correctly', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      // Overview tab should be active by default
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Overview');
      
      // Click on Children tab
      cy.get('[role="tab"]').contains('Children').click();
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Children');
      cy.contains('Your Children').should('be.visible');
      
      // Click on Subscriptions tab
      cy.get('[role="tab"]').contains('Subscriptions').click();
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Subscriptions');
      cy.contains('Active Subscriptions').should('be.visible');
      
      // Click on Deliveries tab
      cy.get('[role="tab"]').contains('Deliveries').click();
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Deliveries');
      cy.contains('Delivery Tracking').should('be.visible');
      
      // Click on Meal Plans tab
      cy.get('[role="tab"]').contains('Meal Plans').click();
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Meal Plans');
      cy.wait('@getMealPlans');
      cy.wait('@getSchools');
      cy.contains('Available Meal Plans').should('be.visible');
    });
  });

  describe('Statistics Cards', () => {
    it('displays correct statistics from API data', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      // Check statistics cards
      cy.contains('Total Children').parent().should('contain', '2');
      cy.contains('Active Subscriptions').parent().should('contain', '3');
      cy.contains('Today\'s Deliveries').parent().should('contain', '2');
      cy.contains('Monthly Spend').parent().should('contain', '₹3,000');
    });

    it('shows pending deliveries count', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.contains('1 pending').should('be.visible');
    });
  });

  describe('Quick Actions Alert', () => {
    it('shows alert for upcoming deliveries', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="alert"]').should('contain', 'You have 1 deliveries scheduled for today');
      cy.get('[role="alert"]').contains('Track deliveries').should('be.visible');
    });

    it('allows clicking track deliveries link', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="alert"]').contains('Track deliveries').click();
      // Should switch to deliveries tab
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Deliveries');
    });
  });

  describe('Children Management', () => {
    it('displays children in the children tab', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Children').click();
      
      // Should display child profile cards
      cy.get('[data-testid="child-profile-card"]').should('have.length', 2);
      cy.contains('Alice Doe').should('be.visible');
      cy.contains('Bob Doe').should('be.visible');
      cy.contains('Delhi Public School').should('be.visible');
      cy.contains('Modern School').should('be.visible');
    });

    it('shows add child button', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Children').click();
      cy.contains('Add Child').should('be.visible');
    });

    it('opens child profile dropdown menu', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Children').click();
      
      // Hover over first child card to show dropdown
      cy.get('[data-testid="child-profile-card"]').first().trigger('mouseover');
      cy.get('[data-testid="child-profile-card"]').first().find('button[aria-haspopup="menu"]').click();
      
      // Check dropdown menu items
      cy.contains('Edit Profile').should('be.visible');
      cy.contains('New Subscription').should('be.visible');
      cy.contains('Remove Child').should('be.visible');
    });
  });

  describe('Subscription Management', () => {
    it('displays active subscriptions', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Subscriptions').click();
      
      // Should display subscription list
      cy.get('[data-testid="parent-subscription-list"]').should('be.visible');
      cy.contains('3 subscriptions').should('be.visible');
    });

    it('shows new subscription button', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Subscriptions').click();
      cy.contains('New Subscription').should('be.visible');
    });
  });

  describe('Delivery Tracking', () => {
    it('displays delivery tracker', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Deliveries').click();
      
      // Should display delivery tracker
      cy.get('[data-testid="delivery-tracker"]').should('be.visible');
      cy.contains('2 deliveries').should('be.visible');
    });

    it('shows delivery count badge', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Deliveries').click();
      cy.contains('2 deliveries today').should('be.visible');
    });
  });

  describe('Meal Plan Browser', () => {
    it('displays meal plan browser', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Meal Plans').click();
      cy.wait('@getMealPlans');
      cy.wait('@getSchools');
      
      // Should display meal plan browser
      cy.get('[data-testid="meal-plan-browser"]').should('be.visible');
      cy.contains('Meal Plans').should('be.visible');
    });

    it('shows view favorites button', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[role="tab"]').contains('Meal Plans').click();
      cy.wait('@getMealPlans');
      cy.wait('@getSchools');
      
      cy.contains('View Favorites').should('be.visible');
    });
  });

  describe('Header Actions', () => {
    it('displays notifications and settings buttons', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.contains('Notifications').should('be.visible');
      cy.contains('Settings').should('be.visible');
    });

    it('allows clicking header action buttons', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.contains('Notifications').click();
      cy.contains('Settings').click();
      // These would typically open modals or navigate to other pages
    });
  });

  describe('Responsive Design', () => {
    it('works on mobile viewport', () => {
      cy.viewport('iphone-x');
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      // Dashboard should still be functional on mobile
      cy.get('[data-testid="parent-dashboard"]').should('be.visible');
      cy.contains('Parent Dashboard').should('be.visible');
      
      // Statistics cards should stack vertically
      cy.get('[data-testid="stats-card"]').should('be.visible');
      
      // Tabs should still work
      cy.get('[role="tab"]').contains('Children').click();
      cy.contains('Your Children').should('be.visible');
    });

    it('works on tablet viewport', () => {
      cy.viewport('ipad-2');
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      // Dashboard should be functional on tablet
      cy.get('[data-testid="parent-dashboard"]').should('be.visible');
      cy.contains('Parent Dashboard').should('be.visible');
      
      // Should have good layout on tablet
      cy.get('[data-testid="stats-card"]').should('be.visible');
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', () => {
      // Mock API error
      cy.intercept('GET', '/v2/parents/profile', {
        statusCode: 500,
        body: { success: false, message: 'Internal server error' }
      }).as('getParentProfileError');

      cy.visit('/school-tiffin/parent-dashboard');
      cy.wait('@getParentProfileError');
      
      // Should still render the page structure
      cy.contains('Parent Dashboard').should('be.visible');
      
      // Error should be logged to console (can be checked in browser dev tools)
    });

    it('handles network failures', () => {
      // Mock network failure
      cy.intercept('GET', '/v2/parents/profile', { forceNetworkError: true }).as('getParentProfileNetworkError');

      cy.visit('/school-tiffin/parent-dashboard');
      cy.wait('@getParentProfileNetworkError');
      
      // Should still render the page structure
      cy.contains('Parent Dashboard').should('be.visible');
    });
  });

  describe('Performance', () => {
    it('loads within acceptable time', () => {
      const startTime = Date.now();
      
      cy.visit('/school-tiffin/parent-dashboard');
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      cy.get('[data-testid="parent-dashboard"]').should('be.visible').then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(5000); // Should load within 5 seconds
      });
    });

    it('handles multiple rapid tab switches', () => {
      cy.wait(['@getParentProfile', '@getChildren', '@getActiveSubscriptions', '@getTodayDeliveries']);
      
      // Rapidly switch between tabs
      cy.get('[role="tab"]').contains('Children').click();
      cy.get('[role="tab"]').contains('Subscriptions').click();
      cy.get('[role="tab"]').contains('Deliveries').click();
      cy.get('[role="tab"]').contains('Meal Plans').click();
      cy.get('[role="tab"]').contains('Overview').click();
      
      // Should still be responsive
      cy.get('[role="tab"][aria-selected="true"]').should('contain', 'Overview');
      cy.contains('Recent Activity').should('be.visible');
    });
  });
});
