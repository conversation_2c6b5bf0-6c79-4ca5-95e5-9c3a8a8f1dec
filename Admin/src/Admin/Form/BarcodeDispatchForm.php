<?php
/**
 * This File mainly used to create the front form.
 * All the fields needed to create front form are described here.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: FrontForm.php 2014-06-19 $
 * @package Front/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form FrontEnd>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;
use Zend\Form\Form;

class BarcodeDispatchForm extends Form
{
	/**
	 * It has an instance of Adpter libray of Zend.
	 * This variable contains all the database credentials
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 *This is constructor and it has the list of form field defined in it.
	 *
	 * @param string $name
	 * @return void
	 */
	public function __construct($name = FALSE)
	{
		parent::__construct('frontlogin');
		$this->setAttribute('method', 'post');
		$this->add(array(
			'name'=>'barcode',
			'type' => 'Zend\Form\Element\Text',
			'attributes'=>array(
				'class'=>'m-wrap',
				'placeholder' => 'Enter Barcode',
				'id'	=> 'barcode',
				'required' => 'required'
			),
			'options'=>array(
				'label'=>'Barcode'
			)
		));
		
		$this->add(array(
			'name'=>'submit',
			'attributes'=>array(
				'id'=>'submit',
				'type'=>'button',
				'value'=>'Dispatch',
				'class'=>'button pull-right tiny',
			)
			
		));
	}

}