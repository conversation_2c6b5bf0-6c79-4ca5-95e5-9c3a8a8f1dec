<?php
/**
 * This file manages the cms on fooddialer system
 * The activity includes add,update and delete cms pages
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CmsController.php 2017-06-19 
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\JsonModel;
use Zend\View\Model\ViewModel;

use Zend\Db\Adapter\Adapter;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\Utility;
use Lib\S3;

use Admin\Form\CmsForm;
use QuickServe\Model\CmsValidator;


class CmsController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\CmsTable model
	 *
	 * @var QuickServe\Model\CmsTable $cmsTable
	 */
	protected $cmsTable;

	/**
	 * It has an instance of QuickServe\Model\ImageTable model
	 *
	 * @var QuickServe\Model\ImageTable $imageTable
	 */
	protected $imageTable;

    /**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	
	//protected $activitylogTable;
	
	/**
	 * This function used to display the list of cms
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction(){
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container('setting');
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Website Manager",'description'=>"Manage website content pages",'breadcrumb'=>"Website Manager"));
		
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
		
	
	}

	public function ajxCmsAction() {

        if (!$this->authservice) {
            $this->authservice = $this->getServiceLocator()
                ->get('AuthService');
        }

        $iden = $this->authservice->getIdentity();

        $session_setting = new Container("setting");
        $setting = $session_setting->setting;

        $utility = Utility::getInstance();

        $layout = $this->layout();
        $acl = $layout->acl;

        $viewModel = new ViewModel();

        $loggedUser = $layout->loggedUser;

        $select = new QSelect();

        $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0' => array('column' => 0, 'dir' => 'desc'));
        $arrColumns = array('0' => 'title', '1' => 'url_name');

        $order_by = $arrColumns[$arrOrder[0]['column']];
        $order = $arrOrder[0]['dir'];

        $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

        $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
        $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0;
        $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1;
        $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
        $search = ($arrSearch['value'] != "") ? $arrSearch['value'] : "";

        $status = $this->params()->fromQuery('status');

        $columns = $this->params()->fromQuery('columns');

        $select->where(
            new \Zend\Db\Sql\Predicate\PredicateSet(
            array(
            new \Zend\Db\Sql\Predicate\Operator('title', 'LIKE', '%' . $search . '%'),
            new \Zend\Db\Sql\Predicate\Operator('url_name', 'LIKE', '%' . $search . '%'),
            ),
            // optional; OP_AND is default
            \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )
        );

        $select->order($order_by . ' ' . $order);

        $cmss = $this->getCmsTable()->fetchAll($select, $page);
        
        $cmss->setCurrentPageNumber($page)
            ->setItemCountPerPage($itemsPerPage)
            ->setPageRange(7);

        $returnVar = array();
        $returnVar['draw'] = $draw;
        $returnVar['recordsTotal'] = $cmss->getTotalItemCount();
        $returnVar['recordsFiltered'] = $cmss->getTotalItemCount();
        $returnVar['data'] = array();

        foreach ($cmss as $cms) {

            $arrTmp = array();

            array_push($arrTmp, $cms['title']);
            array_push($arrTmp, $cms['url_name']);
            $status = ($cms['status'] == "1" ) ? '<span class="active">Active</span>' : '<span class="inactive">Inactive</span>';
            array_push($arrTmp, $status);

            $str = "";

            if ($acl->isAllowed($loggedUser->rolename, 'cms', 'edit')) {

                $textadd = ($cms['status']) == "0" ? 'Activate' : 'Deactivate';
				$str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\'' . $this->url()->fromRoute('cms', array('action' => 'edit', 'id' => $cms['cms_id'])) . '\'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
            }

            if ($acl->isAllowed($loggedUser->rolename, 'cms', 'delete')) {
                $str .= ' <a href="' . $this->url()->fromRoute('cms', array('action' => 'delete', 'id' => $cms['cms_id'])) . '" onclick="return confirm(\'Are you sure you want to ' . $textadd . ' this cms page ?\')">';
                if ($textadd == 'Deactivate') {
                    $str .= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"  data-text-swap="Wait.."><i class="fa fa-ban"></i></button>';
                } else {
                    $str.='<button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
                }
                $str.= '</a>';
            }

            array_push($arrTmp, $str);

            array_push($returnVar['data'], $arrTmp);
        }

        return new JsonModel($returnVar);
    }
	/**
	 * To add new cms
	 *
	 * @return \Admin\Form\CmsForm
	 */
	public function addAction(){
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$sm = $this->getServiceLocator();
		
		
		$adapt = $sm->get('Write_Adapter');
		$libCommon = Qscommon::getInstance($sm);
		$config_variables = $sm->get('config');
		$form = new CmsForm($sm);

		$form->get('submit')->setAttribute('value', 'Add');

		$request = $this->getRequest();
		if ($request->isPost()) {
			
			$cms = new CmsValidator();
			$cms->getInputFilter()->get('title')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'cms',
					'field'     => 'title',
					'adapter'   => $adapt,
					'message'   => 'Cms page is already exists',
			)
			));

			//$cms->setAdapter($adapt);
			$form->setInputFilter($cms->getInputFilter());
			$form->setData($request->getPost());
			
			if ($form->isValid()) {
				 
				$cms->exchangeArray($form->getData());
				
				$data_cms = $this->getCmsTable()->saveCms($cms);
				
				($data_cms) ?$this->flashMessenger()->addSuccessMessage("Website Page added successfully"):$this->flashMessenger()->addErrorMessage("Error adding page.");
				if($data_cms)
				{
		
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$title=$cms->cms;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'title';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "CMS : New cms $title created.";
					$libCommon->saveActivityLog($activity_log_data);
					
				}
				// Redirect to list of albums
				return $this->redirect()->toRoute('cms');
			}

		}
		
		$this->layout()->setVariables(array('page_title'=>"Add Website Page",'breadcrumb'=>"Add Website Page"));
		return array('form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages']
		);
	}

	/**
	 * To delete the cms of given cms id
	 *
	 * @param int id
	 * @return route cms
	 */
	public function deleteAction() {
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('cms');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$select = new QSelect();
		$select->where(array('cms_id'=>$id));
		$cms = $this->getCmsTable()->fetchAll($select);
		$arrcms=$cms->toArray();
		$title=$arrcms[0]['title'];
		$status=($arrcms[0]['status'])=='1'?'deactivated':'activated';
				
		$data_cms=$this->getCmsTable()->deleteCms($id);
		

		if($data_cms)
		{

			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'cms';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "Cms : Website $title $title_status.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
		return $this->redirect()->toRoute('cms');
	}
	/**
	 * Get instance of QuickServe\Model\CmsTable
	 *
	 * @return QuickServe\Model\CmsTable
	 */
	public function getCmsTable(){
		if (!$this->cmsTable) {
			$sm = $this->getServiceLocator();
			$this->cmsTable = $sm->get('QuickServe\Model\CmsTable');
		}
		return $this->cmsTable;
	}
	/**
	 * Get instance of QuickServe\Model\ImageTable
	 *
	 * @return QuickServe\Model\ImageTable
	 */
    public function getImageTable() {
    	if (!$this->imageTable) {
            $sm = $this->getServiceLocator();
            $this->imageTable = $sm->get('QuickServe\Model\ImageTable');
    	}
    	return $this->imageTable;
    }

	/**
	 * To update the home page of given cms id
	 *
	 * @return \Admin\Form\CmsForm
	 */
	public function editAction(){
echo "gotcha here...."; die;
		$session_setting = new Container('setting');
    	$setting = $session_setting->setting;

		$id = (int) $this->params('id');

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$cms = $this->getCmsTable()->getCms($id);
		$urlName = $cms['url_name'];
dd($urlName);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
        $config = $sm->get('Config');
        
        $s3 = $sm->get('S3');
        $hostname = $s3->getHostname();
        
    	$config_variables = $sm->get('config');
    	$bucketName = $s3::$bucketInfo['bucket'];
    	$bucketFolder = $setting['S3_BUCKET_URL'];
    	
    	$images = $this->getImageTable()->getImage($id);
    	$arrImages = array();
    	foreach ($images as $imgkey => $imgvalue) {
    		$arrImages[$imgvalue['position']] = $imgvalue;
    	}

		$libCommon = Qscommon::getInstance($sm);		
		$form = new CmsForm($adapt);
		
		$form->bind($cms);

		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $cms->url_name;
		$request = $this->getRequest();

		if ($request->isPost()){

			$cms = new CmsValidator();
			$cms->getInputFilter()->get('title')
			->getValidatorChain()                 
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'cms',
					'field'     => 'url_name',
					'adapter'   => $adapt,
					'message'   => 'Cms page is already exists',
					'exclude' => array(
							'field' => 'url_name',
							'value' => $val,
					)
			)
			));

			//$cms->setAdapter($adapt);
			$form->setInputFilter($cms->getInputFilter());
			$form->setData($request->getPost());

 			$data = array_merge(
            		$this->getRequest()->getPost()->toArray(),
            		$this->getRequest()->getFiles()->toArray()
            );
          	
            $form->setData($data);

            if ($_FILES['image_path']['error'] == 0 && isset($_FILES['image_path'])) {
				$cms->addImageFilter();
			}

			$image_path =  $cms->image_path;
			$images_path   = $data['image_path'];			
			 
            if ($form->isValid()) {

				$cms->exchangeArray($form->getData());

				$data_cms = $this->getCmsTable()->saveCms($cms);

				if(isset($data['image_path']) || isset($data['image_title']) || isset($data['description']) || isset($data['image_alt_tag']))
                {
                	//dd($data);
                    foreach($data['image_path'] as $n=>$image) {
                    	
                    	$title = $data['image_title'][$n];
                    	$desc = $data['description'][$n];
                    	$imgtag = $data['image_alt_tag'][$n];
                        $image_name = '';
                        if($image['error']==0)
                        {
                        	
                        	$ext = substr($image['name'], strrpos( $image['name'], '.' ));
                            $image_name = $image['name'];
                            $uploadFile = $image['tmp_name'];
							$contentType    =   $_FILES['file']['type'];

                            $uri = $bucketFolder."/cms/".$image_name;
                           
                            if($s3->putObjectFile($uploadFile, $bucketName, $uri, S3::ACL_PUBLIC_READ)){
                                if(file_exists($uploadFile)){
                                    unlink($uploadFile);
                                }else{
                                    exit("\nError: No such file for delete: $uploadFile\n\n");
                                }
                            }else{
                                exit("\nError: Uploading file : $uploadFile on aws\n\n");
                            }                			
                        }
                        if(isset($image_name) || isset($title) || isset($desc) || isset($imgtag)){

	                       	$sql = new QSql($sm);				    			
							$update = $sql->update('image'); 
							$updateImage = array();
							if(!empty($image_name)){
								$updateImage['image_path'] = $image_name;
							}

							$updateImage['image_title'] = $title;
							$updateImage['description'] = $desc;
							$updateImage['image_alt_tag'] = $imgtag;

							$update->set($updateImage);
							$update->where(array("image_id "=>$n));
							$selectString = $sql->getSqlStringForSqlObject($update);
							//dd($selectString);
							$adapt->query($selectString, Adapter::QUERY_MODE_EXECUTE);	
						}							
                    }//die;
                }

				$full_name=$loguser->first_name." ".$loguser->last_name;
				$cms_name=$cms->title;
				
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'cms';
				$activity_log_data['action']= 'edit';
				$activity_log_data['description']= "Cms : Cms $cms_name updated.";						
				$libCommon->saveActivityLog($activity_log_data);

				$this->flashMessenger()->addSuccessMessage("Cms updated successfully");
				return $this->redirect()->toRoute('cms');
			}

			
		}
		$this->layout()->setVariables(array('page_title'=>"Edit Page",'breadcrumb'=>"Edit Page"));
		
		$view = new ViewModel();

		$view->setTemplate('/admin/cms/edit-'.$urlName.'.phtml');
		
		$view->setVariables(array(
			'id' => $id,
			'form' => $form,
			'images' => $arrImages,
			'aws_folder'=>$bucketFolder,
            'aws_bucket_url' => $hostname
		));

		return $view;
	}

}
