openapi: 3.0.3
info:
  title: Order Cancel API
  description: |
    Order cancellation API with time-based refund policies and timing validation.
    
    ## Time-Based Refund Policies:
    1. **Before Cutoff Time** → 100% refund + unlock wallet amount
    2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
    3. **After 08:00:00** → No cancellation allowed
    
    ## Edge Case Handling:
    - Request timestamp validation for network delays
    - Prevents unfair rejections (e.g., request at 07:59:59 processed at 08:00:01)
    
  version: 2.0.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>

servers:
  - url: http://************:8003/api/v2
    description: Development server
  - url: https://api.quickserve.com/v2
    description: Production server

paths:
  /order-management/cancel/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Cancel order with time-based refund processing
      description: |
        Advanced order cancellation with time-based refund policies and wallet management.

        **Time-Based Refund Policies:**
        1. **Before Cutoff Time** → 100% refund + unlock wallet amount
        2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
        3. **After 08:00:00** → No cancellation allowed

        **Enhanced Features:**
        - ✅ Time-based refund calculation based on meal type
        - ✅ Automatic wallet unlocking for locked amounts
        - ✅ Settings-driven cancellation policies
        - ✅ Meal-specific filtering (breakfast, lunch, dinner)
        - ✅ Detailed refund breakdown in response
        - ✅ Request timestamp validation for edge cases
        - ✅ Only cancels current or future date orders
        - ✅ Prevents cancellation of prepared/dispatched orders
        
        **Wallet Integration:**
        - Unlocks previously locked wallet amounts
        - Credits refund amount to customer wallet
        - Maintains detailed transaction history

        **Edge Case Handling:**
        - Validates against request timestamp (not processing time)
        - Handles network delays and processing delays
        - Prevents unfair rejections due to timing issues

      parameters:
        - name: orderNo
          in: path
          required: true
          description: Order number to cancel
          schema:
            type: string
            example: "**********"

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderRequest'
            examples:
              single_date_cancellation:
                summary: Cancel single delivery date
                value:
                  reason: "Customer requested cancellation"
                  cancel_dates: ["2025-08-06"]
              multiple_dates_cancellation:
                summary: Cancel multiple delivery dates
                value:
                  reason: "Cancel specific delivery dates"
                  cancel_dates: ["2025-08-06", "2025-08-13", "2025-08-20"]
              meal_type_filter:
                summary: Cancel specific meal type for dates
                value:
                  reason: "Cancel only lunch orders for these dates"
                  cancel_dates: ["2025-08-06", "2025-08-13"]
                  meal_type: "lunch"
              edge_case_timing:
                summary: Edge case with request timestamp
                value:
                  reason: "Testing edge case timing"
                  cancel_dates: ["2025-08-06"]
                  request_timestamp: "2025-01-28 07:59:59"

      responses:
        '200':
          description: Order cancelled successfully with refund processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderResponse'
              examples:
                successful_cancellation:
                  summary: Successful cancellation with refund
                  value:
                    success: true
                    message: "Orders cancelled successfully with time-based refund policy"
                    data:
                      cancelled_orders: 2
                      cancelled_order_ids: [127810, 127811]
                      total_refund_amount: 125.50
                      wallet_credited: true
                      wallet_unlocked: 200.00
                      customer_code: 1
                      order_no: "**********"
                      refund_breakdown:
                        - order_id: 127810
                          order_date: "2025-08-06"
                          meal_type: "lunch"
                          base_amount: 125.00
                          refund_percentage: 50
                          refund_amount: 62.50
                          wallet_unlocked: 125.00
                          policy_type: "partial_refund_window"
                          cutoff_time: "00:01:00"

        '400':
          description: Timing validation failed or invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TimingValidationError'
              examples:
                timing_validation_failed:
                  summary: Timing validation failed
                  value:
                    success: false
                    message: "Cancellation request failed due to timing restrictions. Your request was submitted at a valid time, but processing was delayed beyond the cancellation window. Please try again or contact support."
                    error_code: "TIMING_VALIDATION_FAILED"
                    details:
                      request_time: "2025-01-28 07:59:59"
                      processing_time: "2025-01-28 08:00:02"
                      delay_seconds: 3
                      failed_orders:
                        - order_id: 127810
                          meal_type: "lunch"
                          request_time_policy: "partial_refund_window"
                          processing_time_policy: "no_cancellation_after_8am"
                          request_time_cancellable: true
                          processing_time_cancellable: false
                      critical_time_window: true

        '404':
          description: No eligible orders found for cancellation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "No eligible orders found for cancellation. Orders can only be cancelled for current or future dates."

        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'

components:
  schemas:
    CancelOrderRequest:
      type: object
      required:
        - reason
        - cancel_dates
      properties:
        reason:
          type: string
          maxLength: 500
          description: "Reason for order cancellation"
          example: "Customer requested cancellation due to change in plans"
        cancel_dates:
          type: array
          minItems: 1
          items:
            type: string
            format: date
          description: "Required: Specific dates to cancel. Since one order_no can span multiple days (5 days, 20 days, etc.), you must specify which delivery dates to cancel"
          example: ["2025-08-06", "2025-08-13", "2025-08-20"]
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional: Filter cancellation by specific meal type"
          example: "lunch"
        request_timestamp:
          type: string
          format: date-time
          description: "Optional: Client-side timestamp when request was initiated (for edge case validation)"
          example: "2025-01-28 07:59:59"

    CancelOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Orders cancelled successfully with time-based refund policy"
        data:
          type: object
          properties:
            cancelled_orders:
              type: integer
              description: "Number of orders cancelled"
              example: 2
            cancelled_order_ids:
              type: array
              items:
                type: integer
              description: "List of cancelled order IDs"
              example: [127810, 127811]
            total_refund_amount:
              type: number
              format: decimal
              description: "Total refund amount credited to wallet (after applying time-based policies)"
              example: 125.50
            wallet_credited:
              type: boolean
              description: "Whether refund was successfully credited to wallet"
              example: true
            wallet_unlocked:
              type: number
              format: decimal
              description: "Total wallet amount unlocked from locked state"
              example: 200.00
            customer_code:
              type: integer
              description: "Customer code for the cancelled orders"
              example: 1
            order_no:
              type: string
              description: "Order number that was cancelled"
              example: "**********"
            refund_breakdown:
              type: array
              description: "Detailed breakdown of refund calculation for each cancelled order"
              items:
                $ref: '#/components/schemas/RefundBreakdownItem'

    RefundBreakdownItem:
      type: object
      properties:
        order_id:
          type: integer
          example: 127810
        order_date:
          type: string
          format: date
          example: "2025-08-06"
        meal_type:
          type: string
          example: "lunch"
        base_amount:
          type: number
          format: decimal
          example: 125.00
        refund_percentage:
          type: integer
          example: 50
        refund_amount:
          type: number
          format: decimal
          example: 62.50
        wallet_unlocked:
          type: number
          format: decimal
          example: 125.00
        policy_type:
          type: string
          enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
          example: "partial_refund_window"
        cutoff_time:
          type: string
          format: time
          example: "00:01:00"

    TimingValidationError:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Cancellation request failed due to timing restrictions"
        error_code:
          type: string
          example: "TIMING_VALIDATION_FAILED"
        details:
          type: object
          properties:
            request_time:
              type: string
              format: date-time
              example: "2025-01-28 07:59:59"
            processing_time:
              type: string
              format: date-time
              example: "2025-01-28 08:00:02"
            delay_seconds:
              type: integer
              example: 3
            failed_orders:
              type: array
              items:
                type: object
            critical_time_window:
              type: boolean
              example: true

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"

    ValidationError:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          example:
            reason: ["The reason field is required."]

tags:
  - name: Order Management
    description: "Core order management operations"
