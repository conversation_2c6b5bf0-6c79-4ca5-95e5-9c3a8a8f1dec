import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id] } from '@/components/catalogue-service-v12/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]')).toBeInTheDocument();
  });
});