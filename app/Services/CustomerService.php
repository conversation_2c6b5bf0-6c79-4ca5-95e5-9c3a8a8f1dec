<?php

namespace App\Services;

use App\DTOs\Customer\CustomerDTO;
use App\DTOs\Customer\AddressDTO;
use App\Events\Customer\CustomerCreated;
use App\Events\Customer\CustomerUpdated;
use App\Events\Customer\CustomerDeleted;
use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Customer\DuplicateCustomerException;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\CustomerWallet;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;
use Exception;

/**
 * Customer Service
 * 
 * This service encapsulates all customer-related business logic.
 */
class CustomerService
{
    /**
     * @var Dispatcher
     */
    protected $events;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Constructor
     * 
     * @param Dispatcher $events
     * @param LoggerInterface $logger
     */
    public function __construct(
        Dispatcher $events,
        LoggerInterface $logger
    ) {
        $this->events = $events;
        $this->logger = $logger;
    }

    /**
     * Get all customers
     *
     * @param array $filters
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllCustomers(array $filters = [], int $perPage = 15)
    {
        $query = Customer::query();

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['company_id'])) {
            $query->where('company_id', $filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->where('unit_id', $filters['unit_id']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email_address', 'like', "%{$search}%");
            });
        }

        // Order by
        $orderBy = $filters['order_by'] ?? 'pk_customer_code';
        $orderDir = $filters['order_dir'] ?? 'desc';
        $query->orderBy($orderBy, $orderDir);

        // Paginate
        return $query->paginate($perPage);
    }

    /**
     * Get customer by ID
     *
     * @param int $id
     * @return Customer
     * @throws CustomerNotFoundException
     */
    public function getCustomerById(int $id): Customer
    {
        $customer = Customer::find($id);

        if (!$customer) {
            throw new CustomerNotFoundException("Customer with ID {$id} not found");
        }

        return $customer;
    }

    /**
     * Get customer by phone
     *
     * @param string $phone
     * @return Customer|null
     */
    public function getCustomerByPhone(string $phone): ?Customer
    {
        return Customer::where('phone', $phone)->first();
    }

    /**
     * Get customer by email
     *
     * @param string $email
     * @return Customer|null
     */
    public function getCustomerByEmail(string $email): ?Customer
    {
        return Customer::where('email_address', $email)->first();
    }

    /**
     * Create a new customer
     *
     * @param CustomerDTO $customerDTO
     * @return Customer
     * @throws DuplicateCustomerException
     */
    public function createCustomer(CustomerDTO $customerDTO): Customer
    {
        try {
            // Check if customer with same phone or email already exists
            $existingCustomer = $this->getCustomerByPhone($customerDTO->phone);
            if ($existingCustomer) {
                throw new DuplicateCustomerException("Customer with phone {$customerDTO->phone} already exists");
            }

            if ($customerDTO->email) {
                $existingCustomer = $this->getCustomerByEmail($customerDTO->email);
                if ($existingCustomer) {
                    throw new DuplicateCustomerException("Customer with email {$customerDTO->email} already exists");
                }
            }

            // Start transaction
            DB::beginTransaction();

            // Create customer
            $customer = new Customer();
            $customer->customer_name = $customerDTO->name;
            $customer->phone = $customerDTO->phone;
            $customer->email_address = $customerDTO->email;
            $customer->food_preference = $customerDTO->foodPreference;
            $customer->city = $customerDTO->city;
            $customer->city_name = $customerDTO->cityName;
            $customer->company_name = $customerDTO->companyName;
            $customer->group_code = $customerDTO->groupCode;
            $customer->group_name = $customerDTO->groupName;
            $customer->registered_on = now();
            $customer->registered_from = $customerDTO->registeredFrom;
            $customer->status = $customerDTO->status;
            $customer->otp = $customerDTO->otp ?? Str::random(6);
            $customer->password = $customerDTO->password ? Hash::make($customerDTO->password) : null;
            $customer->phone_verified = $customerDTO->phoneVerified;
            $customer->email_verified = $customerDTO->emailVerified;
            $customer->subscription_notification = $customerDTO->subscriptionNotification;
            $customer->source = $customerDTO->source;
            $customer->referer = $customerDTO->referer;
            $customer->alt_phone = $customerDTO->altPhone;
            $customer->company_id = $customerDTO->companyId;
            $customer->unit_id = $customerDTO->unitId;
            $customer->isguest = $customerDTO->isGuest;
            $customer->delivery_note = $customerDTO->deliveryNote;
            $customer->save();

            // Create wallet
            $wallet = new CustomerWallet();
            $wallet->customer_code = $customer->pk_customer_code;
            $wallet->balance = 0;
            $wallet->status = 1;
            $wallet->company_id = $customer->company_id;
            $wallet->unit_id = $customer->unit_id;
            $wallet->save();

            // Create addresses if provided
            if (!empty($customerDTO->addresses)) {
                foreach ($customerDTO->addresses as $addressDTO) {
                    $this->addCustomerAddress($customer->pk_customer_code, $addressDTO);
                }
            }

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerCreated($customer));

            // Log
            $this->logger->info('Customer created', [
                'customer_id' => $customer->pk_customer_code,
                'phone' => $customer->phone
            ]);

            return $customer;
        } catch (DuplicateCustomerException $e) {
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            $this->logger->error('Error creating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Update an existing customer
     *
     * @param int $id
     * @param CustomerDTO $customerDTO
     * @return Customer
     * @throws CustomerNotFoundException
     * @throws DuplicateCustomerException
     */
    public function updateCustomer(int $id, CustomerDTO $customerDTO): Customer
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($id);

            // Check if phone is being changed and if it's already in use
            if ($customerDTO->phone !== $customer->phone) {
                $existingCustomer = $this->getCustomerByPhone($customerDTO->phone);
                if ($existingCustomer && $existingCustomer->pk_customer_code !== $id) {
                    throw new DuplicateCustomerException("Customer with phone {$customerDTO->phone} already exists");
                }
            }

            // Check if email is being changed and if it's already in use
            if ($customerDTO->email && $customerDTO->email !== $customer->email_address) {
                $existingCustomer = $this->getCustomerByEmail($customerDTO->email);
                if ($existingCustomer && $existingCustomer->pk_customer_code !== $id) {
                    throw new DuplicateCustomerException("Customer with email {$customerDTO->email} already exists");
                }
            }

            // Start transaction
            DB::beginTransaction();

            // Update customer
            $customer->customer_name = $customerDTO->name;
            $customer->phone = $customerDTO->phone;
            $customer->email_address = $customerDTO->email;
            $customer->food_preference = $customerDTO->foodPreference;
            $customer->city = $customerDTO->city;
            $customer->city_name = $customerDTO->cityName;
            $customer->company_name = $customerDTO->companyName;
            $customer->group_code = $customerDTO->groupCode;
            $customer->group_name = $customerDTO->groupName;
            $customer->status = $customerDTO->status;
            if ($customerDTO->password) {
                $customer->password = Hash::make($customerDTO->password);
            }
            $customer->phone_verified = $customerDTO->phoneVerified;
            $customer->email_verified = $customerDTO->emailVerified;
            $customer->subscription_notification = $customerDTO->subscriptionNotification;
            $customer->alt_phone = $customerDTO->altPhone;
            $customer->company_id = $customerDTO->companyId;
            $customer->unit_id = $customerDTO->unitId;
            $customer->isguest = $customerDTO->isGuest;
            $customer->delivery_note = $customerDTO->deliveryNote;
            $customer->save();

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerUpdated($customer));

            // Log
            $this->logger->info('Customer updated', [
                'customer_id' => $customer->pk_customer_code,
                'phone' => $customer->phone
            ]);

            return $customer;
        } catch (CustomerNotFoundException $e) {
            throw $e;
        } catch (DuplicateCustomerException $e) {
            DB::rollBack();
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            $this->logger->error('Error updating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Delete a customer
     *
     * @param int $id
     * @return bool
     * @throws CustomerNotFoundException
     */
    public function deleteCustomer(int $id): bool
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($id);

            // Start transaction
            DB::beginTransaction();

            // Delete customer
            $customer->delete();

            // Commit transaction
            DB::commit();

            // Fire event
            $this->events->dispatch(new CustomerDeleted($id));

            // Log
            $this->logger->info('Customer deleted', [
                'customer_id' => $id
            ]);

            return true;
        } catch (CustomerNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            $this->logger->error('Error deleting customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Add a customer address
     *
     * @param int $customerId
     * @param AddressDTO $addressDTO
     * @return CustomerAddress
     * @throws CustomerNotFoundException
     */
    public function addCustomerAddress(int $customerId, AddressDTO $addressDTO): CustomerAddress
    {
        try {
            // Get customer
            $customer = $this->getCustomerById($customerId);

            // Create address
            $address = new CustomerAddress();
            $address->customer_code = $customer->pk_customer_code;
            $address->address_type = $addressDTO->type;
            $address->address = $addressDTO->address;
            $address->landmark = $addressDTO->landmark;
            $address->location_code = $addressDTO->locationCode;
            $address->location_name = $addressDTO->locationName;
            $address->city = $addressDTO->city;
            $address->city_name = $addressDTO->cityName;
            $address->state = $addressDTO->state;
            $address->country = $addressDTO->country;
            $address->pincode = $addressDTO->pincode;
            $address->latitude = $addressDTO->latitude;
            $address->longitude = $addressDTO->longitude;
            $address->is_default = $addressDTO->isDefault;
            $address->company_id = $customer->company_id;
            $address->unit_id = $customer->unit_id;
            $address->save();

            // If this is the default address, update customer's default location
            if ($addressDTO->isDefault) {
                $customer->location_code = $addressDTO->locationCode;
                $customer->location_name = $addressDTO->locationName;
                $customer->customer_Address = $addressDTO->address;
                $customer->save();
            }

            // Log
            $this->logger->info('Customer address added', [
                'customer_id' => $customer->pk_customer_code,
                'address_id' => $address->pk_customer_address_id
            ]);

            return $address;
        } catch (CustomerNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            $this->logger->error('Error adding customer address', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
