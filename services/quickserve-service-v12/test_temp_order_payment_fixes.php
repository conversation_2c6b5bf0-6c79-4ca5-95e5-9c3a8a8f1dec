<?php

/**
 * Test script to verify temp_order_payment fixes:
 * 1. order_menu should show actual meal type (breakfast/lunch/dinner) instead of "meal"
 * 2. amount should be base price excluding tax (matching older records)
 */

echo "🧪 Testing temp_order_payment Fixes\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test data simulation
$testMeals = [
    [
        'product_code' => 342,
        'product_name' => 'International Breakfast Subscription',
        'product_description' => 'Healthy breakfast meals',
        'quantity' => 1,
        'unit_price' => 75.00,
        'total_amount' => 75.00
    ],
    [
        'product_code' => 335,
        'product_name' => 'Premium Lunch Box',
        'product_description' => 'Nutritious lunch meals',
        'quantity' => 1,
        'unit_price' => 85.00,
        'total_amount' => 85.00
    ]
];

echo "📋 Test 1: Meal Type Classification\n";
echo "-" . str_repeat("-", 50) . "\n";

// Simulate the groupMealsByType logic
function classifyMealType($meal) {
    $productName = strtolower($meal['product_name'] ?? '');
    $productDescription = strtolower($meal['product_description'] ?? '');
    
    $mealType = 'meal'; // Default
    
    if (strpos($productName, 'breakfast') !== false || strpos($productDescription, 'breakfast') !== false) {
        $mealType = 'breakfast';
    } elseif (strpos($productName, 'lunch') !== false || strpos($productDescription, 'lunch') !== false) {
        $mealType = 'lunch';
    } elseif (strpos($productName, 'dinner') !== false || strpos($productDescription, 'dinner') !== false) {
        $mealType = 'dinner';
    } elseif (strpos($productName, 'snack') !== false || strpos($productDescription, 'snack') !== false) {
        $mealType = 'snack';
    }
    
    // If still generic, try to use product_code ranges
    if ($mealType === 'meal') {
        $productCode = $meal['product_code'];
        if ($productCode >= 340 && $productCode <= 349) {
            $mealType = 'breakfast';
        } elseif ($productCode >= 330 && $productCode <= 339) {
            $mealType = 'lunch';
        } elseif ($productCode >= 350 && $productCode <= 359) {
            $mealType = 'dinner';
        }
    }
    
    return $mealType;
}

$mealsByType = [];
foreach ($testMeals as $meal) {
    $mealType = classifyMealType($meal);
    $mealsByType[$mealType][] = $meal;
    
    echo "Product: {$meal['product_name']}\n";
    echo "  Code: {$meal['product_code']}\n";
    echo "  Classified as: {$mealType}\n";
    echo "  Amount: ₹{$meal['total_amount']}\n\n";
}

echo "📊 Test 2: Primary Meal Type Determination\n";
echo "-" . str_repeat("-", 50) . "\n";

// Simulate the determinePrimaryMealType logic
function determinePrimaryMealType($mealsByType) {
    $mealTypes = array_keys($mealsByType);
    
    if (count($mealTypes) === 1) {
        return $mealTypes[0];
    }
    
    $mealTypePriority = ['breakfast' => 1, 'lunch' => 2, 'dinner' => 3, 'snack' => 4, 'meal' => 5];
    
    usort($mealTypes, function($a, $b) use ($mealTypePriority) {
        $priorityA = $mealTypePriority[$a] ?? 99;
        $priorityB = $mealTypePriority[$b] ?? 99;
        return $priorityA <=> $priorityB;
    });
    
    return $mealTypes[0];
}

$primaryMealType = determinePrimaryMealType($mealsByType);

echo "Available meal types: " . implode(', ', array_keys($mealsByType)) . "\n";
echo "Primary meal type: {$primaryMealType}\n\n";

echo "💰 Test 3: Amount Calculation (Base vs Total)\n";
echo "-" . str_repeat("-", 50) . "\n";

// Calculate amounts
$baseAmount = 0;
$taxRate = 18; // 18% GST
$applyTax = true;

foreach ($mealsByType as $mealType => $meals) {
    $mealAmount = array_sum(array_column($meals, 'total_amount'));
    $baseAmount += $mealAmount;
    
    echo "Meal Type: {$mealType}\n";
    echo "  Base Amount: ₹{$mealAmount}\n";
}

$taxAmount = $applyTax ? round($baseAmount * $taxRate / 100, 2) : 0;
$totalAmountWithTax = $baseAmount + $taxAmount;

echo "\nSummary:\n";
echo "Base Amount (excluding tax): ₹{$baseAmount}\n";
echo "Tax Amount ({$taxRate}%): ₹{$taxAmount}\n";
echo "Total Amount (including tax): ₹{$totalAmountWithTax}\n\n";

echo "🔧 Test 4: temp_order_payment Record Simulation\n";
echo "-" . str_repeat("-", 50) . "\n";

// Simulate the fixed temp_order_payment record
$tempOrderPayment = [
    'temp_preorder_id' => 12345,
    'amount' => $baseAmount, // ✅ Fixed: Base amount without tax
    'order_menu' => $primaryMealType, // ✅ Fixed: Actual meal type instead of "meal"
    'status' => 'pending',
    'type' => 'online',
    'recurring_status' => '1'
];

echo "Fixed temp_order_payment record:\n";
foreach ($tempOrderPayment as $key => $value) {
    echo "  {$key}: {$value}\n";
}

echo "\n📋 Test 5: Comparison with Old vs New\n";
echo "-" . str_repeat("-", 50) . "\n";

echo "OLD (Before Fix):\n";
echo "  order_menu: 'meal' (generic)\n";
echo "  amount: {$totalAmountWithTax} (including tax)\n\n";

echo "NEW (After Fix):\n";
echo "  order_menu: '{$primaryMealType}' (specific meal type)\n";
echo "  amount: {$baseAmount} (base price excluding tax)\n\n";

echo "✅ Verification Results:\n";
echo "-" . str_repeat("-", 50) . "\n";

$orderMenuFixed = $primaryMealType !== 'meal';
$amountFixed = $baseAmount < $totalAmountWithTax;

echo "1. order_menu shows actual meal type: " . ($orderMenuFixed ? "✅ FIXED" : "❌ NOT FIXED") . "\n";
echo "2. amount excludes tax (base price): " . ($amountFixed ? "✅ FIXED" : "❌ NOT FIXED") . "\n";

if ($orderMenuFixed && $amountFixed) {
    echo "\n🎉 All fixes verified successfully!\n";
} else {
    echo "\n⚠️  Some fixes need attention.\n";
}

echo "\n📝 Expected Database Changes:\n";
echo "-" . str_repeat("-", 50) . "\n";
echo "When creating a new order, temp_order_payment should have:\n";
echo "- order_menu: '{$primaryMealType}' (not 'meal')\n";
echo "- amount: {$baseAmount} (not {$totalAmountWithTax})\n";
echo "- This matches the pattern in older records\n";

echo "\n🧪 Test completed!\n";
