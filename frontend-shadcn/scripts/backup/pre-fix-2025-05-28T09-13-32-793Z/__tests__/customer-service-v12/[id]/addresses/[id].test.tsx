import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Addresses[id] } from '@/components/customer-service-v12/[id]/addresses/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Addresses[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Addresses[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Addresses[id]')).toBeInTheDocument();
  });
});