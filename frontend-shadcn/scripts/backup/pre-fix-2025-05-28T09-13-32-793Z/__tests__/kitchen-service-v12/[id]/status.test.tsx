import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]Status } from '@/components/kitchen-service-v12/[id]/status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]Status', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]Status />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]Status')).toBeInTheDocument();
  });
});