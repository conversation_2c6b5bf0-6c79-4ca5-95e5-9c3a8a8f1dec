<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_order_no,
            'order_no' => $this->order_no,
            'customer_code' => $this->customer_code,
            'customer_name' => $this->customer_name,
            'customer_phone' => $this->customer_phone,
            'ship_address' => $this->ship_address,
            'order_date' => $this->order_date,
            'delivery_status' => $this->delivery_status,
            'order_status' => $this->order_status,
            'delivery_person' => $this->delivery_person,
            'location_code' => $this->location_code,
            'location' => new DeliveryLocationResource($this->whenLoaded('location')),
            'amount' => $this->amount,
            'tax' => $this->tax,
            'delivery_charges' => $this->delivery_charges,
            'applied_discount' => $this->applied_discount,
            'total' => $this->total,
            'payment_mode' => $this->payment_mode,
            'amount_paid' => $this->amount_paid,
            'fk_kitchen_code' => $this->fk_kitchen_code,
            'order_menu' => $this->order_menu,
            'delivery_type' => $this->delivery_type,
            'delivery_time' => $this->delivery_time,
            'delivery_end_time' => $this->delivery_end_time,
            'tp_delivery_order_id' => $this->tp_delivery_order_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
