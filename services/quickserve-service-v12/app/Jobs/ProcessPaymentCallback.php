<?php

declare(strict_types=1);

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessPaymentCallback implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 180;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private array $paymentData,
        private string $orderNo
    ) {
        $this->onQueue('payment-processing');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing payment callback in background', [
                'order_no' => $this->orderNo,
                'payment_data' => $this->paymentData,
                'job_id' => $this->job->getJobId()
            ]);

            DB::transaction(function () {
                $this->updatePaymentTransactionBatch();
                $this->createActualOrdersFromTemp();
                $this->updateWalletBatch();
                $this->createInvoiceRecords();
            });

            // Send notifications outside transaction
            $this->sendPaymentNotifications();

            Log::info('Payment callback processing completed', [
                'order_no' => $this->orderNo
            ]);

        } catch (Exception $e) {
            Log::error('Failed to process payment callback', [
                'order_no' => $this->orderNo,
                'payment_data' => $this->paymentData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Update payment transaction records in batch
     */
    private function updatePaymentTransactionBatch(): void
    {
        $status = $this->paymentData['status'] === 'success' ? 'completed' : 'failed';

        DB::table('payment_transaction')
            ->where('pre_order_id', $this->orderNo)
            ->update([
                'status' => $status,
                'gateway_transaction_id' => $this->paymentData['gateway_transaction_id'] ?? $this->paymentData['payment_service_transaction_id'],
                'gateway_response' => json_encode($this->paymentData),
                'modified_date' => now()
            ]);

        Log::info('Payment transaction updated', [
            'order_no' => $this->orderNo,
            'status' => $status
        ]);
    }

    /**
     * Create actual orders from temp pre-orders
     */
    private function createActualOrdersFromTemp(): void
    {
        if ($this->paymentData['status'] !== 'success') {
            return; // Only create orders for successful payments
        }

        // Get temp pre-order data
        $tempOrders = DB::table('temp_pre_orders')
            ->where('order_no', $this->orderNo)
            ->get();

        if ($tempOrders->isEmpty()) {
            Log::warning('No temp pre-orders found for order', [
                'order_no' => $this->orderNo
            ]);
            return;
        }

        $ordersToCreate = [];
        foreach ($tempOrders as $tempOrder) {
            $ordersToCreate[] = [
                'order_no' => $tempOrder->order_no,
                'customer_code' => $tempOrder->customer_id,
                'customer_name' => $tempOrder->customer_name,
                'phone' => $tempOrder->customer_phone,
                'email_address' => $tempOrder->customer_email,
                'product_code' => $tempOrder->product_code,
                'product_name' => $tempOrder->product_name,
                'quantity' => $tempOrder->quantity,
                'amount' => $tempOrder->amount,
                'order_date' => $tempOrder->order_date,
                'order_status' => 'Confirmed',
                'delivery_status' => 'Pending',
                'payment_mode' => $this->paymentData['gateway'] ?? 'online',
                'amount_paid' => 1, // Boolean flag
                'order_menu' => $tempOrder->meal_type ?? 'lunch',
                'ship_address' => $tempOrder->delivery_address,
                'created_date' => now(),
                'last_modified' => now()
            ];
        }

        // Bulk insert all orders
        DB::table('orders')->insert($ordersToCreate);

        Log::info('Actual orders created from temp orders', [
            'order_no' => $this->orderNo,
            'orders_created' => count($ordersToCreate)
        ]);
    }

    /**
     * Update customer wallet in batch
     */
    private function updateWalletBatch(): void
    {
        if ($this->paymentData['status'] !== 'success') {
            return;
        }

        // Check if wallet amount was used
        $paymentTransaction = DB::table('payment_transaction')
            ->where('pre_order_id', $this->orderNo)
            ->first();

        if (!$paymentTransaction || $paymentTransaction->wallet_amount <= 0) {
            return;
        }

        // Deduct wallet amount
        DB::table('customer_wallet')->insert([
            'customer_code' => $paymentTransaction->customer_id,
            'amount_type' => 'dr',
            'wallet_amount' => $paymentTransaction->wallet_amount,
            'reference_no' => $this->orderNo,
            'context' => 'order_payment',
            'description' => "Wallet amount used for order payment {$this->orderNo}",
            'created_at' => now()
        ]);

        Log::info('Customer wallet updated for payment', [
            'order_no' => $this->orderNo,
            'wallet_amount' => $paymentTransaction->wallet_amount
        ]);
    }

    /**
     * Create invoice records
     */
    private function createInvoiceRecords(): void
    {
        if ($this->paymentData['status'] !== 'success') {
            return;
        }

        try {
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $this->orderNo)
                ->first();

            if (!$paymentTransaction) {
                return;
            }

            // Create invoice
            $invoiceId = DB::table('invoices')->insertGetId([
                'customer_id' => $paymentTransaction->customer_id,
                'order_no' => $this->orderNo,
                'amount' => $paymentTransaction->payment_amount,
                'tax_amount' => $paymentTransaction->payment_amount * 0.18, // 18% GST
                'total_amount' => $paymentTransaction->payment_amount,
                'status' => 'paid',
                'created_at' => now()
            ]);

            // Create invoice payment record
            DB::table('invoice_payments')->insert([
                'invoice_id' => $invoiceId,
                'payment_transaction_id' => $paymentTransaction->id,
                'amount' => $paymentTransaction->payment_amount,
                'payment_method' => $this->paymentData['gateway'] ?? 'online',
                'transaction_id' => $this->paymentData['gateway_transaction_id'] ?? $this->paymentData['payment_service_transaction_id'],
                'status' => 'completed',
                'created_at' => now()
            ]);

            Log::info('Invoice records created', [
                'order_no' => $this->orderNo,
                'invoice_id' => $invoiceId
            ]);

        } catch (Exception $e) {
            Log::warning('Failed to create invoice records', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage()
            ]);
            // Don't fail the job for invoice issues
        }
    }

    /**
     * Send payment notifications
     */
    private function sendPaymentNotifications(): void
    {
        try {
            if ($this->paymentData['status'] === 'success') {
                // Send payment success notifications
                Log::info('Payment success notifications queued', [
                    'order_no' => $this->orderNo
                ]);
            } else {
                // Send payment failure notifications
                Log::info('Payment failure notifications queued', [
                    'order_no' => $this->orderNo
                ]);
            }

        } catch (Exception $e) {
            Log::warning('Failed to send payment notifications', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Payment callback job failed', [
            'order_no' => $this->orderNo,
            'payment_data' => $this->paymentData,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Mark payment as failed if all retries exhausted
        DB::table('payment_transaction')
            ->where('pre_order_id', $this->orderNo)
            ->update([
                'status' => 'failed',
                'modified_date' => now()
            ]);
    }
}
