import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Avatar } from '@/components/customer-service-v12/[id]/avatar';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Avatar', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Avatar />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Avatar')).toBeInTheDocument();
  });
});