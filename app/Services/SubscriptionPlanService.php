<?php

namespace App\Services;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Collection;

class SubscriptionPlanService
{
    /**
     * Get all subscription plans.
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllPlans(array $filters = []): Collection
    {
        $query = SubscriptionPlan::query();

        if (isset($filters['status'])) {
            $query->where('plan_status', $filters['status']);
        }

        if (isset($filters['type'])) {
            $query->where('plan_type', $filters['type']);
        }

        if (isset($filters['show_to_customer'])) {
            $query->where('show_to_customer', $filters['show_to_customer']);
        }

        if (isset($filters['start_date'])) {
            $query->where('plan_start_date', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('plan_end_date', '<=', $filters['end_date']);
        }

        return $query->get();
    }

    /**
     * Get a subscription plan by ID.
     *
     * @param int $id
     * @return \App\Models\SubscriptionPlan|null
     */
    public function getPlanById(int $id): ?SubscriptionPlan
    {
        return SubscriptionPlan::find($id);
    }

    /**
     * Get active subscription plans.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActivePlans(): Collection
    {
        return SubscriptionPlan::active()->get();
    }

    /**
     * Get subscription plans visible to customers.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCustomerVisiblePlans(): Collection
    {
        return SubscriptionPlan::active()->visibleToCustomers()->get();
    }

    /**
     * Get subscription plans by type.
     *
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPlansByType(string $type): Collection
    {
        return SubscriptionPlan::active()->ofType($type)->get();
    }

    /**
     * Create a new subscription plan.
     *
     * @param array $data
     * @return \App\Models\SubscriptionPlan
     */
    public function createPlan(array $data): SubscriptionPlan
    {
        return SubscriptionPlan::create($data);
    }

    /**
     * Update a subscription plan.
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\SubscriptionPlan|null
     */
    public function updatePlan(int $id, array $data): ?SubscriptionPlan
    {
        $plan = SubscriptionPlan::find($id);

        if (!$plan) {
            return null;
        }

        $plan->update($data);
        return $plan->fresh();
    }

    /**
     * Delete a subscription plan.
     *
     * @param int $id
     * @return bool
     */
    public function deletePlan(int $id): bool
    {
        $plan = SubscriptionPlan::find($id);

        if (!$plan) {
            return false;
        }

        // Check if the plan has any subscriptions
        if ($plan->subscriptions()->count() > 0) {
            // Instead of deleting, mark it as inactive
            $plan->update(['plan_status' => false]);
            return true;
        }

        return $plan->delete();
    }

    /**
     * Activate a subscription plan.
     *
     * @param int $id
     * @return \App\Models\SubscriptionPlan|null
     */
    public function activatePlan(int $id): ?SubscriptionPlan
    {
        $plan = SubscriptionPlan::find($id);

        if (!$plan) {
            return null;
        }

        $plan->update(['plan_status' => true]);
        return $plan->fresh();
    }

    /**
     * Deactivate a subscription plan.
     *
     * @param int $id
     * @return \App\Models\SubscriptionPlan|null
     */
    public function deactivatePlan(int $id): ?SubscriptionPlan
    {
        $plan = SubscriptionPlan::find($id);

        if (!$plan) {
            return null;
        }

        $plan->update(['plan_status' => false]);
        return $plan->fresh();
    }
}
