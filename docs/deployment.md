# CubeOneBiz Deployment Guide

This document provides comprehensive instructions for deploying the CubeOneBiz microservices architecture on AWS using Infrastructure as Code (IaC) tools.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Overview](#infrastructure-overview)
3. [Deployment Process](#deployment-process)
   - [Terraform Deployment](#terraform-deployment)
   - [Ansible Configuration](#ansible-configuration)
   - [CI/CD Integration](#cicd-integration)
4. [Environment Variables](#environment-variables)
5. [Monitoring and Logging](#monitoring-and-logging)
6. [Backup and Disaster Recovery](#backup-and-disaster-recovery)
7. [Troubleshooting](#troubleshooting)
8. [Maintenance Procedures](#maintenance-procedures)

## Prerequisites

Before deploying the infrastructure, ensure you have the following:

- AWS Account with appropriate permissions
- AWS CLI installed and configured
- Terraform v1.5.7 or later
- Ansible v2.15.0 or later
- kubectl v1.28.0 or later
- Helm v3.12.0 or later
- Docker v24.0.0 or later
- GitHub account with repository access

## Infrastructure Overview

The CubeOneBiz infrastructure consists of the following components:

- **EKS Cluster**: Managed Kubernetes cluster for running microservices
- **RDS MySQL**: Managed database for persistent storage
- **ElastiCache Redis**: In-memory cache for performance optimization
- **Amazon MQ (RabbitMQ)**: Message broker for event-driven architecture
- **VPC**: Virtual Private Cloud with public and private subnets
- **Route53**: DNS management for service discovery
- **CloudWatch**: Monitoring and logging
- **S3**: Object storage for files and backups

### Architecture Diagram

```
                                   ┌─────────────┐
                                   │   Route53   │
                                   └──────┬──────┘
                                          │
                                   ┌──────▼──────┐
                                   │  Kong API   │
                                   │   Gateway   │
                                   └──────┬──────┘
                                          │
                 ┌───────────────────────┼───────────────────────┐
                 │                        │                       │
        ┌────────▼─────────┐    ┌─────────▼────────┐    ┌────────▼─────────┐
        │  Auth Service    │    │  Customer Service│    │  Payment Service  │
        └────────┬─────────┘    └─────────┬────────┘    └────────┬─────────┘
                 │                        │                       │
        ┌────────▼─────────┐    ┌─────────▼────────┐    ┌────────▼─────────┐
        │ Analytics Service│    │   Meal Service   │    │Subscription Service
        └────────┬─────────┘    └─────────┬────────┘    └────────┬─────────┘
                 │                        │                       │
                 └───────────────────────┼───────────────────────┘
                                          │
                 ┌───────────────────────┼───────────────────────┐
                 │                        │                       │
        ┌────────▼─────────┐    ┌─────────▼────────┐    ┌────────▼─────────┐
        │      RDS         │    │    ElastiCache   │    │     Amazon MQ    │
        │     MySQL        │    │      Redis       │    │     RabbitMQ     │
        └──────────────────┘    └──────────────────┘    └───────────────────┘
```

## Deployment Process

### Terraform Deployment

1. **Initialize Terraform**:

   ```bash
   cd terraform
   terraform init
   ```

2. **Create a `terraform.tfvars` file with your configuration**:

   ```hcl
   aws_region           = "us-east-1"
   environment          = "production"
   project_name         = "cubeonebiz"
   cluster_name         = "cubeonebiz-cluster"
   cluster_version      = "1.28"
   vpc_cidr             = "10.0.0.0/16"
   rds_instance_class   = "db.t3.medium"
   rds_allocated_storage = 20
   rds_name             = "cubeonebiz"
   rds_username         = "admin"
   rds_password         = "your-secure-password"
   elasticache_node_type = "cache.t3.small"
   mq_instance_type     = "mq.t3.micro"
   mq_username          = "admin"
   mq_password          = "your-secure-password"
   domain_name          = "cubeonebiz.com"
   cert_manager_email   = "<EMAIL>"
   ```

3. **Plan the deployment**:

   ```bash
   terraform plan -out=tfplan
   ```

4. **Apply the deployment**:

   ```bash
   terraform apply tfplan
   ```

5. **Verify the deployment**:

   ```bash
   terraform output
   ```

### Ansible Configuration

1. **Update the inventory file**:

   ```bash
   cd ansible
   ```

2. **Run the bastion setup playbook**:

   ```bash
   ansible-playbook playbooks/setup_bastion.yml
   ```

3. **Run the EKS nodes setup playbook**:

   ```bash
   ansible-playbook playbooks/setup_eks_nodes.yml
   ```

4. **Deploy Kong API Gateway**:

   ```bash
   ansible-playbook playbooks/deploy_kong.yml
   ```

5. **Deploy microservices**:

   ```bash
   ansible-playbook playbooks/deploy_microservices.yml
   ```

### CI/CD Integration

The repository includes GitHub Actions workflows for automating the deployment process:

1. **Terraform Infrastructure**: Automatically applies infrastructure changes when Terraform files are modified.
2. **Ansible Configuration**: Automatically applies configuration changes when Ansible files are modified.
3. **Microservice CI/CD**: Automatically builds, tests, and deploys microservices when code is pushed to the repository.

To set up the CI/CD pipeline, add the following secrets to your GitHub repository:

- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `RDS_PASSWORD`: Password for the RDS database
- `MQ_PASSWORD`: Password for the Amazon MQ broker
- `MQ_USERNAME`: Username for the Amazon MQ broker
- `RDS_USERNAME`: Username for the RDS database
- `DOCKERHUB_USERNAME`: DockerHub username
- `DOCKERHUB_TOKEN`: DockerHub token
- `SSH_PRIVATE_KEY`: SSH private key for accessing the bastion host

## Environment Variables

Each microservice requires the following environment variables:

- `APP_ENV`: Application environment (production, staging, development)
- `APP_DEBUG`: Enable debug mode (true/false)
- `APP_URL`: Application URL
- `DB_CONNECTION`: Database connection type (mysql)
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_DATABASE`: Database name
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `RABBITMQ_HOST`: RabbitMQ host
- `RABBITMQ_PORT`: RabbitMQ port
- `RABBITMQ_USER`: RabbitMQ username
- `RABBITMQ_PASSWORD`: RabbitMQ password
- `RABBITMQ_VHOST`: RabbitMQ virtual host
- `QUEUE_CONNECTION`: Queue connection type (rabbitmq)
- `REDIS_HOST`: Redis host
- `REDIS_PORT`: Redis port
- `REDIS_PASSWORD`: Redis password
- `APP_KEY`: Application encryption key

These variables are stored as Kubernetes secrets and injected into the microservices at runtime.

## Monitoring and Logging

The infrastructure includes comprehensive monitoring and logging:

- **CloudWatch**: Collects logs and metrics from all AWS resources
- **Prometheus**: Collects metrics from Kubernetes resources
- **Grafana**: Visualizes metrics from Prometheus
- **ELK Stack**: Collects and analyzes logs from microservices

To access the monitoring dashboards:

1. **Grafana**:
   - URL: https://grafana.cubeonebiz.com
   - Username: admin
   - Password: Retrieve from AWS Secrets Manager

2. **Kibana**:
   - URL: https://kibana.cubeonebiz.com
   - Username: elastic
   - Password: Retrieve from AWS Secrets Manager

## Backup and Disaster Recovery

The infrastructure includes the following backup and disaster recovery mechanisms:

- **RDS Automated Backups**: Daily backups with 7-day retention
- **S3 Versioning**: Versioning enabled for all S3 buckets
- **Multi-AZ Deployment**: RDS and ElastiCache deployed across multiple availability zones
- **EKS Node Auto-Scaling**: Automatically scales nodes based on demand

To perform a manual backup:

```bash
# Backup RDS database
aws rds create-db-snapshot --db-instance-identifier cubeonebiz-mysql --db-snapshot-identifier manual-backup-$(date +%Y%m%d)

# Backup EKS resources
kubectl get all --all-namespaces -o yaml > eks-backup-$(date +%Y%m%d).yaml
```

## Troubleshooting

### Common Issues

1. **EKS Cluster Creation Fails**:
   - Check IAM permissions
   - Verify VPC and subnet configuration
   - Check CloudTrail logs for detailed error messages

2. **Microservice Deployment Fails**:
   - Check pod logs: `kubectl logs -n cubeonebiz <pod-name>`
   - Check deployment status: `kubectl describe deployment -n cubeonebiz <deployment-name>`
   - Verify environment variables and secrets

3. **Database Connection Issues**:
   - Check security group rules
   - Verify database credentials
   - Check network connectivity from EKS to RDS

4. **API Gateway Issues**:
   - Check Kong logs: `kubectl logs -n kong <kong-pod-name>`
   - Verify route configuration
   - Check SSL certificate status

### Logs and Diagnostics

To access logs:

```bash
# EKS control plane logs
aws eks update-cluster-config --name cubeonebiz-cluster --logging '{"clusterLogging":[{"types":["api","audit","authenticator","controllerManager","scheduler"],"enabled":true}]}'

# Pod logs
kubectl logs -n <namespace> <pod-name>

# CloudWatch logs
aws logs get-log-events --log-group-name /aws/eks/cubeonebiz-cluster/cluster --log-stream-name <log-stream-name>
```

## Maintenance Procedures

### Updating Kubernetes Version

1. Update the `cluster_version` variable in `terraform.tfvars`
2. Run `terraform plan` and `terraform apply`
3. Verify the cluster status: `kubectl get nodes`

### Scaling Resources

1. Update the relevant variables in `terraform.tfvars` (e.g., `rds_instance_class`, `elasticache_node_type`)
2. Run `terraform plan` and `terraform apply`
3. Verify the resource status in the AWS Console

### Adding a New Microservice

1. Add the microservice to the `microservices` list in `ansible/playbooks/deploy_microservices.yml`
2. Create the necessary Kubernetes secrets
3. Run the deployment playbook: `ansible-playbook playbooks/deploy_microservices.yml`
4. Configure the API Gateway routes
