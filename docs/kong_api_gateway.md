# Kong API Gateway Configuration

This document describes the Kong API Gateway configuration for the microservices architecture.

## Overview

Kong API Gateway is used to route requests between the microservices and the legacy Zend application components. It provides the following features:

- Path-based routing with the pattern '/v2/{service-name}/*'
- Rate limiting and request size validation
- CORS configuration
- Health check endpoints
- Consistent error responses
- Logging and monitoring

## Architecture

The following diagram shows the architecture of the API Gateway:

```
                                  ┌─────────────────┐
                                  │                 │
                                  │  Auth Service   │
                                  │                 │
                                  └─────────────────┘
                                          ▲
                                          │
                                          │ /v2/auth/*
                                          │
┌─────────────┐                 ┌─────────────────┐
│             │                 │                 │
│   Client    │ ───────────────▶│  Kong Gateway   │
│             │                 │                 │
└─────────────┘                 └─────────────────┘
                                          │
                                          │ /v2/customers/*
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │                 │
                                  │Customer Service │
                                  │                 │
                                  └─────────────────┘
                                          ▲
                                          │
                                          │ /v2/meals/*
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │                 │
                                  │  Meal Service   │
                                  │                 │
                                  └─────────────────┘
                                          ▲
                                          │
                                          │ /api/*
                                          │
                                          ▼
                                  ┌─────────────────┐
                                  │                 │
                                  │ Legacy Zend App │
                                  │                 │
                                  └─────────────────┘
```

## Services

The following services are configured in Kong:

1. **Auth Service**: Handles authentication and user management
   - Path: `/v2/auth/*`
   - Health check: `/v2/health/auth`

2. **Customer Service**: Handles customer management
   - Path: `/v2/customers/*`
   - Health check: `/v2/health/customer`

3. **Meal Service**: Handles meal management
   - Path: `/v2/meals/*`
   - Health check: `/v2/health/meal`

4. **Legacy API**: Handles legacy API requests
   - Path: `/api/*`

## Configuration Files

The Kong configuration is split into multiple files:

- `services/gateway/kong/kong.yaml`: Main configuration file
- `services/gateway/kong/auth-service.yaml`: Auth Service configuration
- `services/gateway/kong/customer-service.yaml`: Customer Service configuration
- `services/gateway/kong/meal-service.yaml`: Meal Service configuration

## Plugins

The following plugins are configured in Kong:

1. **Rate Limiting**: Limits the number of requests per minute/hour
   - Configuration: 60 requests per minute, 1000 requests per hour

2. **CORS**: Configures Cross-Origin Resource Sharing
   - Configuration: Allows all origins, methods, and headers

3. **Request Transformer**: Adds headers to requests
   - Configuration: Adds `X-Service` header with the service name

4. **Response Transformer**: Adds headers to responses
   - Configuration: Adds `X-Powered-By` and `X-Service` headers

5. **Prometheus**: Collects metrics for monitoring
   - Configuration: Collects status code, latency, bandwidth, and upstream health metrics

6. **HTTP Log**: Logs requests to a logging service
   - Configuration: Logs to `http://logging-service:8000/logs`

## Health Checks

Each service has a health check endpoint that returns the status of the service. The health check endpoints are:

- Auth Service: `/v2/health/auth`
- Customer Service: `/v2/health/customer`
- Meal Service: `/v2/health/meal`

The health check endpoints return a JSON response with the following structure:

```json
{
  "status": "ok",
  "service": "auth-service",
  "version": "12.0",
  "timestamp": "2023-06-15T12:34:56Z",
  "database": "connected"
}
```

## Deployment

To deploy the Kong API Gateway, run the following command:

```bash
cd services/gateway
./deploy-kong.sh
```

This will:
1. Copy the configuration files to the Kong container
2. Start the Kong API Gateway
3. Apply the configuration
4. Display the routes, services, and plugins

## Testing

To test the Kong API Gateway, run the following commands:

```bash
cd services/gateway/tests
./test-kong-routes.sh
./test-health-endpoints.sh
```

This will test the routes and health check endpoints for all services.

## OpenAPI Specification

The API Gateway is documented using the OpenAPI Specification (OAS) 3.1.0. The specification is available in the `services/gateway/openapi.yaml` file.

## Monitoring

The Kong API Gateway is monitored using Prometheus. The metrics are available at `http://localhost:8001/metrics`.

## Troubleshooting

If you encounter issues with the Kong API Gateway, check the following:

1. Make sure Kong is running:
   ```bash
   curl -s http://localhost:8001/status
   ```

2. Check the Kong logs:
   ```bash
   docker logs kong
   ```

3. Check the health of the services:
   ```bash
   cd services/gateway
   ./health-check.sh
   ```

4. Check the routes:
   ```bash
   curl -s http://localhost:8001/routes | jq
   ```

5. Check the services:
   ```bash
   curl -s http://localhost:8001/services | jq
   ```

6. Check the plugins:
   ```bash
   curl -s http://localhost:8001/plugins | jq
   ```

## Next Steps

The following steps should be taken to further enhance and expand the Kong API Gateway implementation:

1. **Implement JWT Authentication for All Services**
   - Configure JWT authentication for all microservices
   - Create a shared secret for JWT token validation
   - Implement token refresh mechanism
   - Add JWT claims validation for role-based access control

2. **Set Up Canary Deployments**
   - Configure Kong to support canary deployments
   - Implement traffic splitting between different versions of services
   - Create automated rollback mechanisms based on error rates

3. **Enhance Monitoring and Observability**
   - Set up Grafana dashboards for Kong metrics
   - Configure alerting based on error rates and latency
   - Implement distributed tracing with OpenTelemetry
   - Set up centralized logging with ELK stack

4. **Implement API Versioning Strategy**
   - Define API versioning strategy (URI, header, or content negotiation)
   - Configure Kong to route requests based on API version
   - Document versioning strategy in OpenAPI specification

5. **Automate Kong Configuration**
   - Implement Infrastructure as Code (IaC) for Kong configuration
   - Set up CI/CD pipeline for Kong configuration changes
   - Implement automated testing for Kong configuration

6. **Implement Advanced Security Features**
   - Set up IP restriction for admin API
   - Implement bot detection and prevention
   - Configure advanced rate limiting based on user roles
   - Set up API key rotation policies

7. **Expand Service Mesh Capabilities**
   - Evaluate Kong Mesh for service mesh capabilities
   - Implement service discovery
   - Configure circuit breaking and retries
   - Implement mutual TLS (mTLS) for service-to-service communication

8. **Develop API Developer Portal**
   - Set up Kong Developer Portal
   - Create API documentation
   - Implement self-service API key management
   - Create usage dashboards for API consumers

9. **Implement Subscription Service Integration**
   - Configure routes for the Subscription Service
   - Implement health checks for the Subscription Service
   - Set up rate limiting and authentication for the Subscription Service

10. **Performance Testing and Optimization**
    - Conduct load testing on Kong API Gateway
    - Optimize Kong configuration for performance
    - Implement caching strategies
    - Configure connection pooling
