# Systematic Test Coverage Improvement Plan for OneFoodDialer 2025

## **Executive Summary**
Implementing systematic test coverage improvements for QuickServe, Catalogue, and Analytics services using proven fix patterns to achieve >95% pass rate and <200ms API response times.

## **Current Baseline Status**
- **QuickServe Service**: 157/223 tests passing (70%) - Priority 1
- **Catalogue Service**: 22/78 tests passing (28%) - Priority 2  
- **Analytics Service**: ~10/70 tests passing (14%) - Priority 3
- **Overall Target**: +92 additional passing tests

## **Proven Fix Patterns (From Payment Service Success)**

### **Pattern 1: Mockery Fix Pattern**
```php
// Ensure Payment objects have proper IDs
$payment = new Payment();
$payment->id = 1;
$payment->amount = 100.00;

// Repository return types
$mockRepository->shouldReceive('update')
    ->andReturn(true); // Not object for update methods
```

### **Pattern 2: Event Model Consistency**
```php
// Align events to use consistent models
Event::fake([
    PaymentCreated::class,
    PaymentSucceeded::class,
]);

// Ensure event data consistency
$event = new PaymentCreated($payment);
$this->assertInstanceOf(Payment::class, $event->payment);
```

### **Pattern 3: Gateway Identification**
```php
// Add test_gateway support
'gateway' => 'test_gateway',
'test_mode' => true,

// Mock gateway responses
Http::fake([
    'test-gateway.com/*' => Http::response(['status' => 'success'], 200)
]);
```

### **Pattern 4: Laravel Configuration Fixes**
```php
// Sanctum guard configuration
'guards' => [
    'sanctum' => ['driver' => 'sanctum', 'provider' => 'users'],
],

// Facade root issues
use Tests\TestCase; // Not PHPUnit\Framework\TestCase
```

## **Phase 1: QuickServe Service (Priority 1)**

### **Target**: 157/223 → 200/223 (+43 tests)
### **Focus Areas**:
1. **Core Business Logic Tests** (15 tests)
   - Order lifecycle management
   - Product catalog operations
   - Meal customization logic
   - Inventory tracking

2. **API Endpoint Tests** (18 tests)
   - RESTful CRUD operations
   - Request validation
   - Response formatting
   - Error handling

3. **Event-Driven Communication** (10 tests)
   - RabbitMQ integration
   - Event publishing/consuming
   - Message queue handling
   - Dead letter queues

### **Implementation Strategy**:
1. Apply Mockery Fix Pattern to failing service tests
2. Implement Event Model Consistency for order events
3. Configure Sanctum authentication properly
4. Add comprehensive API endpoint coverage

## **Phase 2: Catalogue Service (High-Impact Target)**

### **Target**: 22/78 → 70/78 (+48 tests)
### **Focus Areas**:
1. **Product Catalog Management** (20 tests)
   - Product CRUD operations
   - Category management
   - Menu organization
   - Price management

2. **Inventory Operations** (15 tests)
   - Stock tracking
   - Availability management
   - Supplier integration
   - Reorder logic

3. **Integration Tests** (13 tests)
   - QuickServe integration
   - Payment service integration
   - Customer service integration
   - Analytics integration

### **Implementation Strategy**:
1. Fix FeatureFlagService caching issues
2. Implement proper transaction management
3. Add comprehensive checkout flow tests
4. Apply proven fix patterns from Payment Service

## **Phase 3: Analytics Service (Quick Wins)**

### **Target**: 10/70 → 45/70 (+35 tests)
### **Focus Areas**:
1. **Data Aggregation** (15 tests)
   - Sales analytics
   - Customer behavior analysis
   - Performance metrics
   - KPI calculations

2. **Reporting APIs** (12 tests)
   - Dashboard endpoints
   - Export functionality
   - Real-time metrics
   - Historical data

3. **Event Processing** (8 tests)
   - Order event handling
   - Customer event processing
   - Payment event analysis
   - System metrics

### **Implementation Strategy**:
1. Fix UpdateAnalyticsData listener tests
2. Implement proper mock configurations
3. Add comprehensive API endpoint tests
4. Configure RabbitMQ event processing

## **Success Criteria**
- **Pass Rate**: >95% (target: 546/575 tests)
- **API Response Time**: <200ms
- **Code Coverage**: >90% for each service
- **Zero Critical Vulnerabilities**
- **PHPStan Level 8**: <10 errors per service

## **Timeline**
- **Week 1**: QuickServe Service completion
- **Week 2**: Catalogue Service implementation  
- **Week 3**: Analytics Service and integration testing
- **Week 4**: Performance optimization and final validation

## **Monitoring & Validation**
- Continuous test execution with `scripts/run-all-tests.sh`
- Real-time coverage reporting
- Performance monitoring with <200ms targets
- Automated regression testing
