<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RabbitMQ Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for RabbitMQ.
    |
    */
    
    // RabbitMQ connection settings
    'host' => env('RABBITMQ_HOST', 'localhost'),
    'port' => env('RABBITMQ_PORT', 5672),
    'user' => env('RABBITMQ_USER', 'guest'),
    'password' => env('RABBITMQ_PASSWORD', 'guest'),
    'vhost' => env('RABBITMQ_VHOST', '/'),
    
    // Exchange settings
    'exchange' => env('RABBITMQ_EXCHANGE', 'fooddialer'),
    'exchange_type' => env('RABBITMQ_EXCHANGE_TYPE', 'topic'),
    
    // Queue settings
    'queues' => [
        'delivery_events' => [
            'name' => env('RABBITMQ_DELIVERY_QUEUE', 'delivery_events'),
            'routing_key' => 'delivery.#',
            'durable' => true,
            'auto_delete' => false
        ],
        'order_events' => [
            'name' => env('RABBITMQ_ORDER_QUEUE', 'order_events'),
            'routing_key' => 'order.#',
            'durable' => true,
            'auto_delete' => false
        ]
    ],
    
    // Routing keys
    'routing_keys' => [
        'order_delivered' => 'order.delivered',
        'delivery_assigned' => 'delivery.assigned'
    ]
];
