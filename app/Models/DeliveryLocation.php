<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryLocation extends Model
{
    use HasFactory;
    
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_location_code';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'location',
        'city',
        'sub_city_area',
        'pin',
        'delivery_charges',
        'delivery_time',
        'is_default',
        'status'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
        'status' => 'boolean',
        'delivery_charges' => 'decimal:2'
    ];
    
    /**
     * Get the users associated with this location.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userLocations(): HasMany
    {
        return $this->hasMany(UserLocation::class, 'location_id', 'pk_location_code');
    }
    
    /**
     * Get the orders associated with this location.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'location_code', 'pk_location_code');
    }
}
