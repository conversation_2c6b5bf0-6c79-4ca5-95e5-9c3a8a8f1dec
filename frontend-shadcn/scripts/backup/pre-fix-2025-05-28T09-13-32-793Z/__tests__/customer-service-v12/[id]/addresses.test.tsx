import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Addresses } from '@/components/customer-service-v12/[id]/addresses';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Addresses', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Addresses />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Addresses')).toBeInTheDocument();
  });
});