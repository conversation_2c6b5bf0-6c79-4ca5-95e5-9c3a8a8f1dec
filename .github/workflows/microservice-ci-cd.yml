name: "Microservice CI/CD"

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'services/**'
  pull_request:
    branches:
      - main
      - develop
    paths:
      - 'services/**'

jobs:
  detect-changes:
    name: "Detect Changed Services"
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v35
        with:
          files: services/**

      - name: Set matrix
        id: set-matrix
        run: |
          SERVICES=()
          for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
            SERVICE=$(echo $file | grep -o 'services/[^/]*' | sed 's/services\///')
            if [[ ! " ${SERVICES[@]} " =~ " ${SERVICE} " ]]; then
              SERVICES+=("$SERVICE")
            fi
          done
          
          if [ ${#SERVICES[@]} -eq 0 ]; then
            echo "No services changed"
            echo "matrix={\"service\":[]}" >> $GITHUB_OUTPUT
          else
            JSON_ARRAY=$(printf '%s\n' "${SERVICES[@]}" | jq -R . | jq -s .)
            echo "matrix={\"service\":$JSON_ARRAY}" >> $GITHUB_OUTPUT
          fi

  test:
    name: "Test"
    needs: detect-changes
    if: ${{ needs.detect-changes.outputs.matrix != '{"service":[]}' }}
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{ fromJson(needs.detect-changes.outputs.matrix) }}
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, zip, pdo, sqlite, pdo_sqlite, bcmath, intl, gd, exif, iconv
          coverage: xdebug

      - name: Get composer cache directory
        id: composer-cache
        working-directory: services/${{ matrix.service }}
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        working-directory: services/${{ matrix.service }}
        run: composer install --prefer-dist --no-progress

      - name: Run tests
        working-directory: services/${{ matrix.service }}
        run: vendor/bin/phpunit --coverage-clover=coverage.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: services/${{ matrix.service }}/coverage.xml
          flags: unittests
          name: ${{ matrix.service }}
          fail_ci_if_error: true

  build:
    name: "Build"
    needs: [detect-changes, test]
    if: ${{ needs.detect-changes.outputs.matrix != '{"service":[]}' && github.event_name == 'push' }}
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{ fromJson(needs.detect-changes.outputs.matrix) }}
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract branch name
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./services/${{ matrix.service }}
          push: true
          tags: |
            cubeonebiz/${{ matrix.service }}:latest
            cubeonebiz/${{ matrix.service }}:${{ env.BRANCH_NAME }}-${{ github.sha }}

  deploy:
    name: "Deploy"
    needs: [detect-changes, build]
    if: ${{ needs.detect-changes.outputs.matrix != '{"service":[]}' && github.event_name == 'push' }}
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{ fromJson(needs.detect-changes.outputs.matrix) }}
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kube config
        run: aws eks update-kubeconfig --name cubeonebiz-cluster --region us-east-1

      - name: Extract branch name
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV

      - name: Set environment
        run: |
          if [ "${{ env.BRANCH_NAME }}" == "main" ]; then
            echo "ENVIRONMENT=production" >> $GITHUB_ENV
          else
            echo "ENVIRONMENT=staging" >> $GITHUB_ENV
          fi

      - name: Deploy to Kubernetes
        run: |
          kubectl set image deployment/${{ matrix.service }} ${{ matrix.service }}=cubeonebiz/${{ matrix.service }}:${{ env.BRANCH_NAME }}-${{ github.sha }} -n cubeonebiz

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/${{ matrix.service }} -n cubeonebiz --timeout=300s
