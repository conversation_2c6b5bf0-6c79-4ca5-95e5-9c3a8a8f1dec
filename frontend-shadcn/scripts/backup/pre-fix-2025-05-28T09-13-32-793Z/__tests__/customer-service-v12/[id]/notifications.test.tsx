import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Notifications } from '@/components/customer-service-v12/[id]/notifications';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Notifications', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Notifications />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Notifications')).toBeInTheDocument();
  });
});