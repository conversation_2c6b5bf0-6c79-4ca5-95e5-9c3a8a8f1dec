<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->pk_location_code,
            'location' => $this->location,
            'city' => $this->city,
            'sub_city_area' => $this->sub_city_area,
            'pin' => $this->pin,
            'delivery_charges' => $this->delivery_charges,
            'delivery_time' => $this->delivery_time,
            'is_default' => $this->is_default,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
