# 🔄 ORDER SWAP API - COMPLETE cURL TEST COMMANDS

## 📋 **OpenAPI Specification Updated**

**✅ File Location:** `services/quickserve-service-v12/order-management-openapi.yaml`

**✅ Updated Sections:**
- Added `/order-management/swap/{orderNo}` endpoint
- Added `SwapOrderRequest` schema
- Added `SwapOrderResponse` schema
- Updated API documentation with comprehensive examples

---

## 🚀 **Complete cURL Test Commands**

### **1. Valid Breakfast Item Swap (Should Succeed)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 342,
    "reason": "Customer wants to change from Poha to Upma",
    "meal_type": "breakfast"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order swapped successfully",
  "data": {
    "order_id": 127810,
    "order_no": "QA93250725",
    "order_date": "2025-09-03",
    "swap_details": {
      "old_product": {
        "code": 341,
        "name": "Poha",
        "price": 150.00
      },
      "new_product": {
        "code": 342,
        "name": "Upma",
        "price": 200.00
      },
      "price_difference": 50.00,
      "swap_charges": 25.00,
      "total_amount_change": 75.00,
      "new_order_amount": 225.00
    },
    "reason": "Customer wants to change from Poha to Upma"
  }
}
```

---

### **2. Cross-Category Swap Test (Should Fail)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 355,
    "reason": "Try to change breakfast to lunch item"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response (400 Error):**
```json
{
  "success": false,
  "message": "Products are not in the same category or not swappable",
  "data": {
    "current_product": {
      "code": 341,
      "name": "Poha",
      "category": "Breakfast",
      "type": "Meal"
    },
    "new_product": {
      "code": 355,
      "name": "Biryani",
      "category": "Lunch",
      "type": "Meal"
    }
  }
}
```

---

### **3. Past Date Swap Test (Should Fail)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-07-20",
    "new_product_code": 342,
    "reason": "Try to change past order"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response (400 Error):**
```json
{
  "success": false,
  "message": "Order cannot be swapped due to its current status",
  "data": {
    "order_id": 127810,
    "order_status": "New",
    "delivery_status": "Pending"
  }
}
```

---

### **4. Non-Existent Order Test (Should Fail)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/NONEXISTENT123' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 342,
    "reason": "Test with non-existent order"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response (404 Error):**
```json
{
  "success": false,
  "message": "Order not found for the specified date",
  "data": {
    "order_no": "NONEXISTENT123",
    "order_date": "2025-09-03",
    "meal_type": null
  }
}
```

---

### **5. Invalid Product Code Test (Should Fail)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 99999,
    "reason": "Test with invalid product code"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response (404 Error):**
```json
{
  "success": false,
  "message": "New product not found",
  "data": {
    "new_product_code": 99999
  }
}
```

---

### **6. Validation Error Test (Should Fail)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "reason": "Missing required fields"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

**Expected Response (422 Error):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "order_date": ["The order date field is required."],
    "new_product_code": ["The new product code field is required."]
  }
}
```

---

### **7. Lunch Item Swap Test**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 356,
    "reason": "Customer prefers different lunch option",
    "meal_type": "lunch"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

---

### **8. Minimal Request Test (Only Required Fields)**

```bash
curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "order_date": "2025-09-03",
    "new_product_code": 342
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

---

## 🔍 **Verification Commands**

### **Check Updated Order in Database:**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/details/QA93250725' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\n"
```

### **Check Customer Orders:**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/customer/1' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\n"
```

---

## 📊 **Database Verification Queries**

### **Check Updated Order:**
```sql
SELECT pk_order_no, order_no, product_code, product_name, amount, tax, remark, last_modified
FROM orders 
WHERE order_no = 'QA93250725' AND order_date = '2025-09-03';
```

### **Check Updated Order Details:**
```sql
SELECT pk_order_detail_id, ref_order_no, product_code, product_name, product_amount, product_tax
FROM order_details 
WHERE ref_order_no = 'QA93250725' AND order_date = '2025-09-03';
```

### **Check Product Categories:**
```sql
SELECT pk_product_code, name, product_category, category, product_type, food_type, is_swappable, swap_charges
FROM products 
WHERE pk_product_code IN (341, 342, 355, 356);
```

---

## 🎯 **Test Execution Script**

Save this as `test_order_swap_api.sh`:

```bash
#!/bin/bash

echo "🔄 Testing Order Swap API"
echo "========================="

BASE_URL="http://************:8000/api/v2/order-management"

echo "1. Testing valid breakfast swap..."
curl -X POST "${BASE_URL}/swap/QA93250725" \
  -H 'Content-Type: application/json' \
  -d '{"order_date":"2025-09-03","new_product_code":342,"reason":"Valid breakfast swap test","meal_type":"breakfast"}' \
  -w "\nStatus: %{http_code}\n\n"

echo "2. Testing cross-category swap (should fail)..."
curl -X POST "${BASE_URL}/swap/QA93250725" \
  -H 'Content-Type: application/json' \
  -d '{"order_date":"2025-09-03","new_product_code":355,"reason":"Cross-category test"}' \
  -w "\nStatus: %{http_code}\n\n"

echo "3. Testing validation error (should fail)..."
curl -X POST "${BASE_URL}/swap/QA93250725" \
  -H 'Content-Type: application/json' \
  -d '{"reason":"Missing required fields"}' \
  -w "\nStatus: %{http_code}\n\n"

echo "✅ Test execution completed!"
```

**Make executable:** `chmod +x test_order_swap_api.sh`
**Run tests:** `./test_order_swap_api.sh`

---

## 🎉 **Summary**

**✅ OpenAPI Specification Updated:** `services/quickserve-service-v12/order-management-openapi.yaml`

**✅ New Endpoint Added:** `POST /api/v2/order-management/swap/{orderNo}`

**✅ Complete Test Suite:** 8 different test scenarios with expected responses

**✅ Verification Commands:** Database queries and API calls to verify changes

**✅ Automated Test Script:** Ready-to-run bash script for comprehensive testing

**Status: READY FOR TESTING!** 🚀
