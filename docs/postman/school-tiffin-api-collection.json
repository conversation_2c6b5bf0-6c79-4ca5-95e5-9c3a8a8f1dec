{"info": {"name": "OneFoodDialer 2025 - School Tiffin API", "description": "Comprehensive API collection for the school tiffin meal subscription system supporting dual business models", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "parent_id", "value": "1", "type": "string"}, {"key": "child_id", "value": "1", "type": "string"}, {"key": "school_id", "value": "1", "type": "string"}, {"key": "meal_plan_id", "value": "1", "type": "string"}, {"key": "subscription_id", "value": "1", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/v2/auth/login", "host": ["{{base_url}}"], "path": ["v2", "auth", "login"]}}, "response": []}]}, {"name": "Parent Management", "item": [{"name": "Get Parent Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/parents/profile", "host": ["{{base_url}}"], "path": ["v2", "parents", "profile"]}}, "response": []}, {"name": "Update Parent Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"Updated Parent Name\",\n  \"phone\": \"9876543210\",\n  \"address\": \"Updated Address\",\n  \"emergency_contact_name\": \"Emergency Contact\",\n  \"emergency_contact_phone\": \"9876543211\"\n}"}, "url": {"raw": "{{base_url}}/v2/parents/profile", "host": ["{{base_url}}"], "path": ["v2", "parents", "profile"]}}, "response": []}]}, {"name": "Children Management", "item": [{"name": "Get Children List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/parents/children", "host": ["{{base_url}}"], "path": ["v2", "parents", "children"]}}, "response": []}, {"name": "Add Child Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"<PERSON>\",\n  \"school_id\": 1,\n  \"grade_level\": \"5th\",\n  \"grade_section\": \"A\",\n  \"roll_number\": \"12345\",\n  \"date_of_birth\": \"2014-05-15\",\n  \"dietary_restrictions\": [\"nuts\", \"dairy\"],\n  \"medical_conditions\": [\"asthma\"],\n  \"emergency_contact_relationship\": \"grandmother\"\n}"}, "url": {"raw": "{{base_url}}/v2/parents/children", "host": ["{{base_url}}"], "path": ["v2", "parents", "children"]}}, "response": []}, {"name": "Update Child Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"<PERSON> Updated\",\n  \"grade_level\": \"6th\",\n  \"dietary_restrictions\": [\"nuts\"]\n}"}, "url": {"raw": "{{base_url}}/v2/parents/children/{{child_id}}", "host": ["{{base_url}}"], "path": ["v2", "parents", "children", "{{child_id}}"]}}, "response": []}, {"name": "Delete Child Profile", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v2/parents/children/{{child_id}}", "host": ["{{base_url}}"], "path": ["v2", "parents", "children", "{{child_id}}"]}}, "response": []}]}, {"name": "Schools", "item": [{"name": "Get Available Schools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/parents/schools/available", "host": ["{{base_url}}"], "path": ["v2", "parents", "schools", "available"]}}, "response": []}, {"name": "Get School Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/parents/schools/{{school_id}}", "host": ["{{base_url}}"], "path": ["v2", "parents", "schools", "{{school_id}}"]}}, "response": []}]}, {"name": "Meal Plans - Direct Model", "item": [{"name": "Get All Meal Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/meal-plans?meal_type=lunch&dietary_restrictions=vegetarian&available_only=true", "host": ["{{base_url}}"], "path": ["v2", "meal-plans"], "query": [{"key": "meal_type", "value": "lunch"}, {"key": "dietary_restrictions", "value": "vegetarian"}, {"key": "available_only", "value": "true"}]}}, "response": []}, {"name": "Get Meal Plan Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/meal-plans/{{meal_plan_id}}", "host": ["{{base_url}}"], "path": ["v2", "meal-plans", "{{meal_plan_id}}"]}}, "response": []}]}, {"name": "Meal Plans - School Partnership Model", "item": [{"name": "Get School Meal Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/meal-plans/school/{{school_id}}?meal_type=lunch&available_only=true", "host": ["{{base_url}}"], "path": ["v2", "meal-plans", "school", "{{school_id}}"], "query": [{"key": "meal_type", "value": "lunch"}, {"key": "available_only", "value": "true"}]}}, "response": []}]}, {"name": "Subscriptions", "item": [{"name": "Get Active Subscriptions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions?status=active", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions"], "query": [{"key": "status", "value": "active"}]}}, "response": []}, {"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"parent_customer_id\": 1,\n  \"child_profile_id\": 1,\n  \"meal_plan_id\": 1,\n  \"start_date\": \"2024-02-01\",\n  \"end_date\": \"2024-07-31\",\n  \"subscription_type\": \"monthly\",\n  \"billing_cycle\": \"monthly\",\n  \"delivery_days\": [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\"],\n  \"preferred_break_time\": \"lunch_break\",\n  \"meal_customizations\": {\n    \"portion_size\": \"regular\",\n    \"extra_items\": [\"fruit\"],\n    \"special_requests\": [\"less spicy\"]\n  },\n  \"dietary_accommodations\": [\"nut-free\"],\n  \"spice_level\": \"mild\",\n  \"special_notes\": \"Please ensure nut-free preparation\",\n  \"auto_renew\": true\n}"}, "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions"]}}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"delivery_days\": [\"monday\", \"wednesday\", \"friday\"],\n  \"special_notes\": \"Updated special instructions\",\n  \"auto_renew\": false\n}"}, "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions", "{{subscription_id}}"]}}, "response": []}, {"name": "Pause Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pause_reason\": \"vacation\",\n  \"pause_until\": \"2024-03-15\"\n}"}, "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions/{{subscription_id}}/pause", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions", "{{subscription_id}}", "pause"]}}, "response": []}, {"name": "Resume Subscription", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions/{{subscription_id}}/resume", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions", "{{subscription_id}}", "resume"]}}, "response": []}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cancellation_reason\": \"not needed\",\n  \"effective_date\": \"2024-03-01\"\n}"}, "url": {"raw": "{{base_url}}/v2/school-meal-subscriptions/{{subscription_id}}/cancel", "host": ["{{base_url}}"], "path": ["v2", "school-meal-subscriptions", "{{subscription_id}}", "cancel"]}}, "response": []}]}, {"name": "Delivery Tracking", "item": [{"name": "Get Today's Deliveries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/school/batches?delivery_date={{$isoTimestamp}}&status=scheduled", "host": ["{{base_url}}"], "path": ["v2", "school", "batches"], "query": [{"key": "delivery_date", "value": "{{$isoTimestamp}}"}, {"key": "status", "value": "scheduled"}]}}, "response": []}, {"name": "Get School Deliveries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/school/schools/{{school_id}}/batches?delivery_date={{$isoTimestamp}}", "host": ["{{base_url}}"], "path": ["v2", "school", "schools", "{{school_id}}", "batches"], "query": [{"key": "delivery_date", "value": "{{$isoTimestamp}}"}]}}, "response": []}, {"name": "Update Delivery Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_transit\",\n  \"delivery_person_name\": \"<PERSON>\",\n  \"delivery_person_phone\": \"9876543210\",\n  \"estimated_arrival_time\": \"12:15\",\n  \"current_location\": {\n    \"latitude\": 28.5355,\n    \"longitude\": 77.3910\n  },\n  \"notes\": \"On the way to school\"\n}"}, "url": {"raw": "{{base_url}}/v2/school/batches/{{batch_id}}/status", "host": ["{{base_url}}"], "path": ["v2", "school", "batches", "{{batch_id}}", "status"]}}, "response": []}]}, {"name": "Favorites", "item": [{"name": "Get Favorite Meal Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/parents/favorites", "host": ["{{base_url}}"], "path": ["v2", "parents", "favorites"]}}, "response": []}, {"name": "Add to Favorites", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"meal_plan_id\": {{meal_plan_id}}\n}"}, "url": {"raw": "{{base_url}}/v2/parents/favorites", "host": ["{{base_url}}"], "path": ["v2", "parents", "favorites"]}}, "response": []}, {"name": "Remove from Favorites", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/v2/parents/favorites/{{meal_plan_id}}", "host": ["{{base_url}}"], "path": ["v2", "parents", "favorites", "{{meal_plan_id}}"]}}, "response": []}]}, {"name": "Analytics & Reports", "item": [{"name": "Get Subscription Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/analytics/subscriptions?period=monthly&school_id={{school_id}}", "host": ["{{base_url}}"], "path": ["v2", "analytics", "subscriptions"], "query": [{"key": "period", "value": "monthly"}, {"key": "school_id", "value": "{{school_id}}"}]}}, "response": []}, {"name": "Get Delivery Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/analytics/delivery-performance?period=weekly&school_id={{school_id}}", "host": ["{{base_url}}"], "path": ["v2", "analytics", "delivery-performance"], "query": [{"key": "period", "value": "weekly"}, {"key": "school_id", "value": "{{school_id}}"}]}}, "response": []}]}, {"name": "Health Checks", "item": [{"name": "API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v2/health", "host": ["{{base_url}}"], "path": ["v2", "health"]}}, "response": []}]}]}