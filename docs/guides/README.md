# User Guides

This section provides step-by-step guides for common tasks and workflows in the QuickServe frontend application.

## Table of Contents

- [Getting Started](getting-started.md)
- [Development Guidelines](development-guidelines.md)
- [Adding a New Feature](adding-a-new-feature.md)
- [Working with Feature Flags](working-with-feature-flags.md)
- [Testing Guidelines](testing-guidelines.md)
- [Deployment Guidelines](deployment-guidelines.md)
- [Troubleshooting](troubleshooting.md)

## Overview

These guides are designed to help developers understand how to work with the QuickServe frontend application. They provide step-by-step instructions for common tasks and workflows.

## Getting Started

The [Getting Started](getting-started.md) guide provides instructions for setting up the development environment and running the application locally.

## Development Guidelines

The [Development Guidelines](development-guidelines.md) guide provides guidelines for developing new features and making changes to the application.

## Adding a New Feature

The [Adding a New Feature](adding-a-new-feature.md) guide provides step-by-step instructions for adding a new feature to the application.

## Working with Feature Flags

The [Working with Feature Flags](working-with-feature-flags.md) guide provides instructions for using feature flags to control the availability of features.

## Testing Guidelines

The [Testing Guidelines](testing-guidelines.md) guide provides guidelines for writing and running tests for the application.

## Deployment Guidelines

The [Deployment Guidelines](deployment-guidelines.md) guide provides instructions for deploying the application to different environments.

## Troubleshooting

The [Troubleshooting](troubleshooting.md) guide provides solutions for common issues that may arise during development.
