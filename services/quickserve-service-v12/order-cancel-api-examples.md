# Order Cancel API - Complete Documentation

## 🔗 **API Endpoint**

```
POST http://192.168.1.16:8003/api/v2/order-management/cancel/{orderNo}
```

## 📋 **Request Body Schema**

```json
{
  "reason": "string (required, max 500 chars)",
  "cancel_dates": ["array of dates (optional)"],
  "meal_type": "string (optional: breakfast, lunch, dinner)",
  "request_timestamp": "string (optional: YYYY-MM-DD HH:MM:SS)"
}
```

## 🧪 **API Examples**

### **Example 1: Basic Cancellation**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Customer requested cancellation"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Orders cancelled successfully with time-based refund policy",
  "data": {
    "cancelled_orders": 2,
    "cancelled_order_ids": [127810, 127811],
    "total_refund_amount": 125.50,
    "wallet_credited": true,
    "wallet_unlocked": 200.00,
    "customer_code": 1,
    "order_no": "**********",
    "refund_breakdown": [
      {
        "order_id": 127810,
        "order_date": "2025-08-06",
        "meal_type": "lunch",
        "base_amount": 125.00,
        "refund_percentage": 50,
        "refund_amount": 62.50,
        "wallet_unlocked": 125.00,
        "policy_type": "partial_refund_window",
        "cutoff_time": "00:01:00"
      }
    ]
  }
}
```

### **Example 2: Cancel Specific Dates**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Cancel specific delivery dates",
    "cancel_dates": ["2025-08-06", "2025-08-13"]
  }'
```

### **Example 3: Cancel Specific Meal Type**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Cancel only lunch orders",
    "meal_type": "lunch"
  }'
```

### **Example 4: Edge Case with Request Timestamp**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Testing edge case timing",
    "request_timestamp": "2025-01-28 07:59:59"
  }'
```

## ❌ **Error Responses**

### **Timing Validation Failed (HTTP 400)**
```json
{
  "success": false,
  "message": "Cancellation request failed due to timing restrictions. Your request was submitted at a valid time, but processing was delayed beyond the cancellation window. Please try again or contact support.",
  "error_code": "TIMING_VALIDATION_FAILED",
  "details": {
    "request_time": "2025-01-28 07:59:59",
    "processing_time": "2025-01-28 08:00:02",
    "delay_seconds": 3,
    "failed_orders": [
      {
        "order_id": 127810,
        "meal_type": "lunch",
        "request_time_policy": "partial_refund_window",
        "processing_time_policy": "no_cancellation_after_8am",
        "request_time_cancellable": true,
        "processing_time_cancellable": false
      }
    ],
    "critical_time_window": true
  }
}
```

### **No Eligible Orders (HTTP 404)**
```json
{
  "success": false,
  "message": "No eligible orders found for cancellation. Orders can only be cancelled for current or future dates."
}
```

### **Validation Error (HTTP 422)**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "reason": ["The reason field is required."],
    "cancel_dates.0": ["The cancel_dates.0 must be a date after or equal to today."]
  }
}
```

## ⏰ **Time-Based Refund Policies**

### **Policy 1: Before Cutoff Time**
- **Time:** Before 00:01:00 on cutoff day
- **Refund:** 100% for all meals
- **Wallet:** Unlock full amount

### **Policy 2: Partial Refund Window (00:01:00 - 08:00:00)**
- **Breakfast:** 0% refund
- **Lunch:** 50% refund
- **Wallet:** Unlock full amount

### **Policy 3: No Cancellation (After 08:00:00)**
- **Cancellation:** Not allowed
- **Refund:** 0%
- **Wallet:** Remains locked

## 🔧 **Request Parameters**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `reason` | string | ✅ Yes | Reason for cancellation (max 500 chars) |
| `cancel_dates` | array | ❌ No | Specific dates to cancel (YYYY-MM-DD format) |
| `meal_type` | string | ❌ No | Filter by meal type (breakfast, lunch, dinner) |
| `request_timestamp` | string | ❌ No | Client timestamp for edge case validation |

## 📊 **Response Fields**

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Whether the operation was successful |
| `message` | string | Human-readable message |
| `cancelled_orders` | integer | Number of orders cancelled |
| `cancelled_order_ids` | array | List of cancelled order IDs |
| `total_refund_amount` | decimal | Total refund amount after policies |
| `wallet_credited` | boolean | Whether refund was credited to wallet |
| `wallet_unlocked` | decimal | Total wallet amount unlocked |
| `refund_breakdown` | array | Detailed breakdown per order |

## 🎯 **Edge Case Handling**

### **Critical Timing Window (07:59:30 - 08:00:30)**
- System validates against request timestamp
- Prevents unfair rejections due to processing delays
- Detailed error messages for timing issues

### **Example Edge Case:**
```
User submits at:     07:59:59 ✅ (Valid)
Server processes at: 08:00:01 ❌ (Past cutoff)
System validates:    Uses 07:59:59 for policy check
Result:              Request accepted with partial refund
```

## 🧪 **Testing Commands**

### **Test Valid Request:**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{"reason": "Testing valid cancellation"}'
```

### **Test Edge Case:**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Testing edge case",
    "request_timestamp": "2025-01-28 07:59:59"
  }'
```

### **Test Invalid Request:**
```bash
curl -X POST 'http://192.168.1.16:8003/api/v2/order-management/cancel/**********' \
  -H 'Content-Type: application/json' \
  -d '{
    "reason": "Testing invalid request",
    "request_timestamp": "2025-01-28 08:00:01"
  }'
```

## ✅ **Implementation Features**

- ✅ Time-based refund policies
- ✅ Meal-specific cancellation rules
- ✅ Wallet locking/unlocking system
- ✅ Request timestamp validation
- ✅ Edge case handling for timing
- ✅ Detailed refund breakdown
- ✅ Settings-driven configuration
- ✅ Comprehensive error handling
- ✅ Backward compatibility

## 🎉 **Ready for Use**

The Order Cancel API is fully implemented with sophisticated time-based policies, edge case handling, and comprehensive validation. Use the examples above to integrate with your application.
