import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthPerformance } from '@/components/auth-service-v12/performance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthPerformance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthPerformance />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthPerformance')).toBeInTheDocument();
  });
});