<?php

namespace App\Http\Controllers\Api;

use App\DTOs\Customer\AddressDTO;
use App\DTOs\Customer\CustomerDTO;
use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Customer\DuplicateCustomerException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customer\AddAddressRequest;
use App\Http\Requests\Customer\CreateCustomerRequest;
use App\Http\Requests\Customer\UpdateCustomerRequest;
use App\Services\CustomerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Customer Controller
 * 
 * This controller handles customer-related requests.
 */
class CustomerController extends Controller
{
    /**
     * @var CustomerService
     */
    protected $customerService;
    
    /**
     * Constructor
     * 
     * @param CustomerService $customerService
     */
    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }
    
    /**
     * Get all customers
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'company_id', 'unit_id', 'search', 'order_by', 'order_dir']);
            $perPage = $request->input('per_page', 15);
            
            $customers = $this->customerService->getAllCustomers($filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting customers', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving customers'
            ], 500);
        }
    }
    
    /**
     * Get a customer by ID
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->getCustomerById($id);
            
            return response()->json([
                'success' => true,
                'data' => $customer
            ]);
        } catch (CustomerNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the customer'
            ], 500);
        }
    }
    
    /**
     * Create a new customer
     * 
     * @param CreateCustomerRequest $request
     * @return JsonResponse
     */
    public function store(CreateCustomerRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Create addresses DTOs if provided
            $addresses = [];
            if (isset($data['addresses']) && is_array($data['addresses'])) {
                foreach ($data['addresses'] as $address) {
                    $addresses[] = AddressDTO::fromArray($address);
                }
            }
            
            // Create customer DTO
            $customerDTO = new CustomerDTO(
                $data['name'],
                $data['phone'],
                $data['email'] ?? null,
                $data['food_preference'] ?? null,
                $data['city'] ?? null,
                $data['city_name'] ?? null,
                $data['company_name'] ?? null,
                $data['group_code'] ?? null,
                $data['group_name'] ?? null,
                $data['registered_from'] ?? null,
                $data['status'] ?? true,
                $data['otp'] ?? null,
                $data['password'] ?? null,
                $data['phone_verified'] ?? false,
                $data['email_verified'] ?? false,
                $data['subscription_notification'] ?? false,
                $data['source'] ?? null,
                $data['referer'] ?? null,
                $data['alt_phone'] ?? null,
                $data['company_id'] ?? 1,
                $data['unit_id'] ?? 1,
                $data['isguest'] ?? false,
                $data['delivery_note'] ?? null,
                $addresses
            );
            
            // Create customer
            $customer = $this->customerService->createCustomer($customerDTO);
            
            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => $customer
            ], 201);
        } catch (DuplicateCustomerException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 409);
        } catch (\Exception $e) {
            Log::error('Error creating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the customer'
            ], 500);
        }
    }
    
    /**
     * Update a customer
     * 
     * @param UpdateCustomerRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateCustomerRequest $request, int $id): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Create customer DTO
            $customerDTO = new CustomerDTO(
                $data['name'],
                $data['phone'],
                $data['email'] ?? null,
                $data['food_preference'] ?? null,
                $data['city'] ?? null,
                $data['city_name'] ?? null,
                $data['company_name'] ?? null,
                $data['group_code'] ?? null,
                $data['group_name'] ?? null,
                null,
                $data['status'] ?? true,
                null,
                $data['password'] ?? null,
                $data['phone_verified'] ?? false,
                $data['email_verified'] ?? false,
                $data['subscription_notification'] ?? false,
                null,
                null,
                $data['alt_phone'] ?? null,
                $data['company_id'] ?? 1,
                $data['unit_id'] ?? 1,
                $data['isguest'] ?? false,
                $data['delivery_note'] ?? null
            );
            
            // Update customer
            $customer = $this->customerService->updateCustomer($id, $customerDTO);
            
            return response()->json([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => $customer
            ]);
        } catch (CustomerNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (DuplicateCustomerException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 409);
        } catch (\Exception $e) {
            Log::error('Error updating customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the customer'
            ], 500);
        }
    }
    
    /**
     * Delete a customer
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->customerService->deleteCustomer($id);
            
            return response()->json([
                'success' => true,
                'message' => 'Customer deleted successfully'
            ]);
        } catch (CustomerNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error deleting customer', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the customer'
            ], 500);
        }
    }
    
    /**
     * Add an address to a customer
     * 
     * @param AddAddressRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function addAddress(AddAddressRequest $request, int $id): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Create address DTO
            $addressDTO = new AddressDTO(
                $data['type'],
                $data['address'],
                $data['landmark'] ?? null,
                $data['location_code'] ?? null,
                $data['location_name'] ?? null,
                $data['city'] ?? null,
                $data['city_name'] ?? null,
                $data['state'] ?? null,
                $data['country'] ?? null,
                $data['pincode'] ?? null,
                $data['latitude'] ?? null,
                $data['longitude'] ?? null,
                $data['is_default'] ?? false
            );
            
            // Add address
            $address = $this->customerService->addCustomerAddress($id, $addressDTO);
            
            return response()->json([
                'success' => true,
                'message' => 'Address added successfully',
                'data' => $address
            ], 201);
        } catch (CustomerNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error adding address', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the address'
            ], 500);
        }
    }
}
