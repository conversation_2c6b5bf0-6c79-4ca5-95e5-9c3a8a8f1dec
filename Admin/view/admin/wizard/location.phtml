<section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
            </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Your portal is almost ready to go live. Now lets enable the locations where you would like to serve.</h4>
         </div>
       </div>
       <div class="mt-10 form-box">
          <div class="row">
             <div class="col-sm-12">
                 <div class="clearfix form-head">
                   <div class="pull-left">
                      <h4 class="txt-color title">Enable locations</h4>
                   </div>
                   <div class="pull-right">
                      <p class="txt-color title">Step 5 of 5</p>
                   </div>
                 </div>

                <div class="col-md-6 col-sm-6 col-xs-6 mt-10 mb-10">
                      <div class="form-group float-label-control">
                         <input type="text" class="form-control empty" name="search-location" id="search-location" data-page="<?php echo $this->page; ?>" value="">
                         <label for="">Search</label>
                       </div>
                </div>
                <div class="col-md-6 col-sm-6 col-xs-6 mt-10 mb-10">
                   <a href="#" class="location-btn" id="add-location-btn" >Add Location</a>
                </div>

                 <div class="col-sm-12" id="list-container">
                 <?php echo $this->partial('admin/wizard/location-list.phtml', array( 'paginator' => $this->paginator, 'paginatorControl' => $this->paginationControl));?>
                 </div>
             </div>

          </div> 

       </div>
    </div>
 </section>
<!-- Modal -->
<div class="modal fade" id="location-modal" role="dialog">
  <div class="modal-dialog">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title theme-color">Add your location </h4>
      </div>
      <div class="modal-body">
            <div class="row">
                <form name="add-location-form" id="add-location-form">
                    <div class="col-sm-12">
                       <div class="form-group float-label-control">
                          <input type="text" class="form-control empty" name="location-name">
                          <label for="">Location Name</label>
                          <span class="add-location-error text-error"></span>
                       </div>
                       <div class="form-group float-label-control">
                          <input type="text" class="form-control empty" name="location-charges">
                          <label for="">Delivery Charges</label>
                          <span class="add-charges-error text-error"></span>
                       </div>
                    </div>
                    <div class="col-sm-12">
                       <div class="form-group float-label-control">
                          <input type="text" class="form-control empty" name="location-time">
                          <label for="">Delivery Time (in minutes)</label>
                          <span class="add-time-error text-error"></span>
                       </div>
                    </div>
                    <div class="col-sm-12">
                       <a href="#" id="save-location" class="add-loc-btn">Add</a>
                    </div>
                </form>
            </div>
      </div>
    </div>

  </div>
</div>

<div class="modal fade" id="common-modal1" role="dialog">
    <div class="modal-dialog">
    
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title  theme-color">Successful!</h4>
        </div>
        <div class="modal-body">
          <p id="common-content1"></p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      
    </div>
</div>


<script>
$(document).ready(function() {
    $('#next-btn').hide();
    $("#search-location").on('input', function() {
        
        var page = $(this).attr('data-page');
        
        $.ajax({
            url:"<?php echo $this->url('wizard',array('action' => 'search-location')); ?>",
            type: "POST",
            data: { page : page, search : $(this).val() },
            success:function(response)
            {
                $("#list-container").html('').append(response);
            }
        });
    });
    
    $(".update-location").on('click', function(){

        var tr = $(this).closest('tr');
        
        var location_id = tr.attr('data-location-id');
        
        var name = $('input[name="location['+location_id+'][\'name\']"]').val();
        var status = $('input[name="location['+location_id+'][\'status\']"]:checked').val();
        var del_charges = $('input[name="location['+location_id+'][\'delivery_charges\']"]').val();
        var time = $('input[name="location['+location_id+'][\'delivery_time\']"]').val();
        $(tr).find('.location-error, .status-error, .charges-error, .time-error').html('');
        
        $.ajax({
            url:"<?php echo $this->url('wizard',array('action' => 'edit-location')); ?>",
            type: "POST",
            data: { pk_location_code : location_id, name : name, status : status, charges : del_charges, time : time },
            success:function(response)
            {             
                if(response.status == 'error'){
                    
                    $.each(response.data, function(key, msg){
                        
                        $(tr).find('.'+key+'-error').html(msg);                        
                    });
                    
                }else{
                    $("#common-content1").html('').append(response.data);
                     //window.location.reload();
                    $('#common-modal1').modal('show');                      
                }                 
            }
        });
    });
    
    $("#add-location-btn").on('click', function(){
        
        $('.add-location-error, .add-charges-error, .add-time-error').html('');
        
        $("#location-modal").modal('show');
    })
    
    $("#save-location").on('click', function(){
    
        var params = $('#add-location-form').serializeArray();
        
        $('.add-location-error, .add-charges-error, .add-time-error').html('');
        $.ajax({
            url:"<?php echo $this->url('wizard',array('action' => 'add-location')); ?>",
            type: "POST",
            data: { params : JSON.stringify(params) },
            success:function(response)
            {                
                if(response.status == 'error'){
                    $.each(response.data, function(key, msg){
                        
                        $('.add-'+key+"-error").html(msg);
                    });
                    
                }else{
                    $('#location-modal').modal('hide');
                    $("#add-location-form").trigger("reset");
                    $("#common-content1").html('').append(response.data);                   
                    $('#common-modal1').modal('show');                          
                }
            }
        });
    })
    
});
</script> 
