import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueHealthDetailed } from '@/components/catalogue-service-v12/health/detailed';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueHealthDetailed', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueHealthDetailed />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueHealthDetailed')).toBeInTheDocument();
  });
});