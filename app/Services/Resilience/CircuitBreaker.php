<?php

declare(strict_types=1);

namespace App\Services\Resilience;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Circuit Breaker Pattern Implementation
 * 
 * Prevents cascading failures by monitoring service health
 * and temporarily blocking requests when failure threshold is reached
 */
class CircuitBreaker
{
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';

    private string $serviceName;
    private int $failureThreshold;
    private int $recoveryTimeout;
    private array $expectedExceptions;
    private string $cacheKeyPrefix;

    public function __construct(string $serviceName, array $config = [])
    {
        $this->serviceName = $serviceName;
        $this->failureThreshold = $config['failure_threshold'] ?? 5;
        $this->recoveryTimeout = $config['recovery_timeout'] ?? 60;
        $this->expectedExceptions = $config['expected_exceptions'] ?? [];
        $this->cacheKeyPrefix = "circuit_breaker_{$serviceName}";
    }

    /**
     * Execute callable with circuit breaker protection
     */
    public function call(callable $callback)
    {
        $state = $this->getState();

        if ($state === self::STATE_OPEN) {
            if ($this->shouldAttemptReset()) {
                $this->setState(self::STATE_HALF_OPEN);
                Log::info("Circuit breaker transitioning to half-open", [
                    'service' => $this->serviceName,
                ]);
            } else {
                throw new CircuitBreakerOpenException(
                    "Circuit breaker is open for service: {$this->serviceName}"
                );
            }
        }

        try {
            $result = $callback();
            $this->onSuccess();
            return $result;
        } catch (\Throwable $e) {
            $this->onFailure($e);
            throw $e;
        }
    }

    /**
     * Handle successful execution
     */
    private function onSuccess(): void
    {
        $state = $this->getState();
        
        if ($state === self::STATE_HALF_OPEN) {
            $this->setState(self::STATE_CLOSED);
            $this->resetFailureCount();
            Log::info("Circuit breaker closed after successful recovery", [
                'service' => $this->serviceName,
            ]);
        } elseif ($state === self::STATE_CLOSED) {
            $this->resetFailureCount();
        }
    }

    /**
     * Handle failed execution
     */
    private function onFailure(\Throwable $exception): void
    {
        if (!$this->isExpectedException($exception)) {
            return;
        }

        $failureCount = $this->incrementFailureCount();
        $state = $this->getState();

        Log::warning("Circuit breaker recorded failure", [
            'service' => $this->serviceName,
            'failure_count' => $failureCount,
            'threshold' => $this->failureThreshold,
            'exception' => $exception->getMessage(),
        ]);

        if ($failureCount >= $this->failureThreshold && $state !== self::STATE_OPEN) {
            $this->setState(self::STATE_OPEN);
            $this->setLastFailureTime(time());
            
            Log::error("Circuit breaker opened due to failure threshold", [
                'service' => $this->serviceName,
                'failure_count' => $failureCount,
                'threshold' => $this->failureThreshold,
            ]);
        }
    }

    /**
     * Check if exception should trigger circuit breaker
     */
    private function isExpectedException(\Throwable $exception): bool
    {
        if (empty($this->expectedExceptions)) {
            return true;
        }

        foreach ($this->expectedExceptions as $expectedClass) {
            if ($exception instanceof $expectedClass) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if circuit breaker should attempt reset
     */
    private function shouldAttemptReset(): bool
    {
        $lastFailureTime = $this->getLastFailureTime();
        return $lastFailureTime && (time() - $lastFailureTime) >= $this->recoveryTimeout;
    }

    /**
     * Get current circuit breaker state
     */
    private function getState(): string
    {
        return Cache::get("{$this->cacheKeyPrefix}_state", self::STATE_CLOSED);
    }

    /**
     * Set circuit breaker state
     */
    private function setState(string $state): void
    {
        Cache::put("{$this->cacheKeyPrefix}_state", $state, 3600);
    }

    /**
     * Get failure count
     */
    private function getFailureCount(): int
    {
        return Cache::get("{$this->cacheKeyPrefix}_failures", 0);
    }

    /**
     * Increment failure count
     */
    private function incrementFailureCount(): int
    {
        $key = "{$this->cacheKeyPrefix}_failures";
        $count = Cache::get($key, 0) + 1;
        Cache::put($key, $count, 3600);
        return $count;
    }

    /**
     * Reset failure count
     */
    private function resetFailureCount(): void
    {
        Cache::forget("{$this->cacheKeyPrefix}_failures");
    }

    /**
     * Get last failure time
     */
    private function getLastFailureTime(): ?int
    {
        return Cache::get("{$this->cacheKeyPrefix}_last_failure");
    }

    /**
     * Set last failure time
     */
    private function setLastFailureTime(int $timestamp): void
    {
        Cache::put("{$this->cacheKeyPrefix}_last_failure", $timestamp, 3600);
    }

    /**
     * Get circuit breaker metrics
     */
    public function getMetrics(): array
    {
        return [
            'service' => $this->serviceName,
            'state' => $this->getState(),
            'failure_count' => $this->getFailureCount(),
            'failure_threshold' => $this->failureThreshold,
            'last_failure_time' => $this->getLastFailureTime(),
            'recovery_timeout' => $this->recoveryTimeout,
        ];
    }

    /**
     * Reset circuit breaker to closed state
     */
    public function reset(): void
    {
        $this->setState(self::STATE_CLOSED);
        $this->resetFailureCount();
        Cache::forget("{$this->cacheKeyPrefix}_last_failure");
        
        Log::info("Circuit breaker manually reset", [
            'service' => $this->serviceName,
        ]);
    }
}

/**
 * Exception thrown when circuit breaker is open
 */
class CircuitBreakerOpenException extends \Exception
{
    //
}
