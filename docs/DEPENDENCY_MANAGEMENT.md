# 📦 OneFoodDialer 2025 - Dependency Management

**Status:** ✅ CONSOLIDATED  
**Date:** December 23, 2025  
**Root Configuration:** `composer.json`

## 🎯 Consolidated Dependency Management

The **primary and authoritative** dependency configuration for OneFoodDialer 2025 is:

```
composer.json (root level)
```

### 📊 Configuration Summary
- **PHP Version:** ^8.2 (Laravel 12 requirement)
- **Framework:** Laravel 12 microservices architecture
- **Package Manager:** Composer 2.x
- **Autoloading:** PSR-4 standard

### 🏗️ Shared Dependencies

#### Production Dependencies
```json
{
  "php": "^8.2",
  "laravel/framework": "^12.0",
  "laravel/sanctum": "^4.1",
  "laravel/tinker": "^2.10.1",
  "guzzlehttp/guzzle": "^7.8",
  "promphp/prometheus_client_php": "^2.14"
}
```

#### Development Dependencies
```json
{
  "fakerphp/faker": "^1.23",
  "larastan/larastan": "^3.4",
  "laravel/pail": "^1.2.2",
  "laravel/pint": "^1.13",
  "laravel/sail": "^1.41",
  "mockery/mockery": "^1.6",
  "nunomaduro/collision": "^8.6",
  "phpunit/phpunit": "^11.5.3",
  "rector/rector": "^2.0",
  "phpstan/phpstan": "^2.1"
}
```

## 📁 Service-Specific Dependencies

Each microservice maintains its own `composer.json` for service-specific dependencies:

```
services/auth-service-v12/composer.json
services/customer-service-v12/composer.json
services/payment-service-v12/composer.json
services/quickserve-service-v12/composer.json
services/kitchen-service-v12/composer.json
services/delivery-service-v12/composer.json
services/analytics-service-v12/composer.json
services/catalogue-service-v12/composer.json
services/meal-service-v12/composer.json
services/misscall-service-v12/composer.json
```

**⚠️ Important:** Service-specific files should only contain dependencies unique to that service. Common dependencies are managed at the root level.

## 🔧 Usage Guidelines

### Installing Dependencies

#### Root Level (Shared Dependencies)
```bash
# Install all shared dependencies
composer install

# Install all service dependencies
composer run install-services

# Update shared dependencies
composer update
```

#### Service Level (Service-Specific Dependencies)
```bash
# Install dependencies for a specific service
composer install --working-dir=services/auth-service-v12

# Add a service-specific dependency
cd services/auth-service-v12
composer require vendor/package
```

### Running Scripts

#### Quality Assurance
```bash
# Run all quality checks
composer run quality

# Static analysis
composer run analyse

# Code style check
composer run style-test

# Code style fix
composer run style

# Refactoring
composer run refactor
```

#### Testing
```bash
# Run all tests
composer run test-all

# Run service tests only
composer run test-services

# Run integration tests only
composer run test-integration
```

## 🏗️ Autoloading Structure

### PSR-4 Namespaces
```json
{
  "OneFoodDialer\\": "src/",
  "OneFoodDialer\\Shared\\": "packages/shared/src/",
  "OneFoodDialer\\Resilience\\": "packages/resilience/src/"
}
```

### Development Namespaces
```json
{
  "Tests\\": "tests/",
  "OneFoodDialer\\Tests\\": "tests/"
}
```

## 📦 Package Repositories

### Local Packages
```json
{
  "repositories": [
    {
      "type": "path",
      "url": "packages/shared"
    },
    {
      "type": "path", 
      "url": "packages/resilience"
    }
  ]
}
```

## 🔄 Maintenance

### Adding Shared Dependencies
1. Add to root `composer.json` require/require-dev section
2. Run `composer install` to update lock file
3. Update service-specific composer.json files if needed
4. Test all services to ensure compatibility

### Adding Service-Specific Dependencies
1. Navigate to service directory
2. Use `composer require vendor/package`
3. Ensure dependency doesn't conflict with shared dependencies
4. Test the specific service

### Updating Dependencies
```bash
# Update all shared dependencies
composer update

# Update specific shared dependency
composer update vendor/package

# Update service-specific dependencies
composer update --working-dir=services/service-name
```

## 🚨 Migration Notes

### From Legacy Zend Framework
- **Old:** `zendframework/zendframework ^2.5.0` (PHP 7.2)
- **New:** `laravel/framework ^12.0` (PHP 8.2+)
- **Backup:** Legacy configuration saved in `composer-backup/composer-legacy-zend.json`

### Breaking Changes
- PHP version requirement: 7.2 → 8.2+
- Framework: Zend Framework 2 → Laravel 12
- Autoloading: PSR-0 → PSR-4
- Testing: PHPUnit 5.7 → PHPUnit 11.5.3

## 📚 Related Documentation
- [Consolidation Implementation Plan](../CONSOLIDATION_IMPLEMENTATION_PLAN.md)
- [Docker Configuration](../Dockerfile)
- [API Specification](openapi/unified-api-specification.yaml)

---

**Last Updated:** December 23, 2025  
**Consolidation Status:** ✅ COMPLETED  
**Next Review:** January 2026
