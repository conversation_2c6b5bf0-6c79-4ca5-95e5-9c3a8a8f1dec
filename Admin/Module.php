<?php
/**
 * This File is the most important file of Admin Module
 * This file executed initially while loading Admin module.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Module.php 2014-06-19 $
 * @package Admin
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Module Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */

namespace Admin;

use Zend\ModuleManager\Feature\AutoloaderProviderInterface;
use Zend\ModuleManager\ModuleManager;
use Zend\Mvc\ModuleRouteListener;
use Zend\Mvc\MvcEvent;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\TableGateway\TableGateway;
use Zend\Session\Container;
use Admin\Model\ThemeMasterTable;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as QSCommon;

class Module implements AutoloaderProviderInterface
{
	
	/**
	 * This function returns the list of files to configuration file to be loaded initially.
	 *
	 * @see \Zend\ModuleManager\Feature\AutoloaderProviderInterface::getAutoloaderConfig()
	 * @return array
	 */
    public function getAutoloaderConfig()
    {
    
        return array(
            'Zend\Loader\ClassMapAutoloader' => array(
                __DIR__ . '/autoload_classmap.php',
            ),
            'Zend\Loader\StandardAutoloader' => array(
                'namespaces' => array(
		   			// if we're in a namespace deeper than one level we need to fix the \ in the path
                   // __NAMESPACE__ => __DIR__ . '/src/' . str_replace('\\', '/' , __NAMESPACE__),
                		__NAMESPACE__ => __DIR__ . '/src/' . __NAMESPACE__,
                ),
            ),
        );
    }

    /**
     * This function calls the configuration file of the front module
     *
     * @return array module.config.php
     */
    public function getConfig()
    {
        return include __DIR__ . '/config/module.config.php';
    }
    /**
     * This function called before redering the view
     *
     * @param MvcEvent $e
     * @return void
     */
    public function onBootstrap(MvcEvent $e)
    {
       
    	$app = $e->getApplication();
    	$sm  = $app->getServiceManager();
    	//$dbAdapter = $sm->get('Zend\Db\Adapter\Adapter');
    	$utility = \Lib\Utility::getInstance();
        $libCommon = QSCommon::getInstance($sm);
    	 // You may not need to do this if you're doing it elsewhere in your
        // application
        $eventManager        = $e->getApplication()->getEventManager();
        $moduleRouteListener = new ModuleRouteListener();
        $moduleRouteListener->attach($eventManager);

        $setting_session = new Container('setting');
        $setting_session->setting = $libCommon->getSettings();

        if(!isset($setting_session->setting['GLOBAL_CURRENCY_ENTITY'])) {

            $cc = $setting_session->setting['GLOBAL_CURRENCY']; 
            $entity = $libCommon->getCurrencyEntityTable()->getCurrencyEntity($cc);
            $setting_session->setting['GLOBAL_CURRENCY_ENTITY'] = $entity['entity_code'];   
        }

        $eventManager->getSharedManager()->attach('Admin', 'dispatch', function($e) use ($utility) {
        	
        	$controller = $e->getTarget();
        	$route = $controller->getEvent()->getRouteMatch();
        	$controller->getEvent()->getViewModel()->setVariables(array(
        		'controller' => $route->getParam('controller'),
        		'action' => $route->getParam('action'),
        	));
        	
        	$cntl = $route->getParam('controller');
        	$action = $route->getParam('action');

        	if($cntl=='Admin\Controller\Order' || $cntl=='Admin\Controller\Preorders' || $cntl=='Admin\Controller\OrderDispatch'  || $cntl=='Admin\Controller\OrderConfirm' ){
        		
        		if(!$utility->checkSubscription('order_management','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}	
        	}
        	
        	if($cntl=='Admin\Controller\Report'){
        		
        		if($action=='sales' && !$utility->checkSubscription('report_sales','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
        		
        	}
        	
            if($cntl=='Admin\Controller\Analytics' ){
        		if(!$utility->checkSubscription('report_analytical','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
                
        	}
        	if($cntl=='Admin\Controller\User'){
        	
        		if(!$utility->checkSubscription('user_management','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
                
        	}
        	
        	
        	if($cntl=='Admin\Controller\Customer'){
        		 
        		if(!$utility->checkSubscription('customer_management','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
        		
        		if($action=='paymentoption' && !$utility->checkSubscription('customer_wallet','allowed')){
        			return $controller->redirect()->toUrl('/customer');
        		}
        	}
        	
        	if($cntl=='Admin\Controller\Product'){
        		 
        		if(!$utility->checkSubscription('product_management','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
        	}
            
            if($cntl=='Admin\Controller\Promocode'){
        	
                if(!$utility->checkSubscription('promocode','allowed')){
        			return $controller->redirect()->toUrl('/dashboard');
        		}
        	}
           
        	
        },100);
       // exit;
       //echo '<pre>';print_r($getEvent->getRouteMatch()->getParams()); exit;
    }

    /**
     * This function create all the services which needs through everywhere in front module
     *
     * @return array
     */
    public function getServiceConfig()
    {
      	return array(
    		'factories' => array(),
    	);
    }
}
