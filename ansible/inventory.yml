all:
  children:
    bastion:
      hosts:
        bastion.cubeonebiz.com:
          ansible_user: ec2-user
          ansible_ssh_private_key_file: ~/.ssh/cubeonebiz.pem
    
    eks_nodes:
      hosts:
        # These will be dynamically populated by the AWS dynamic inventory plugin
      vars:
        ansible_user: ec2-user
        ansible_ssh_private_key_file: ~/.ssh/cubeonebiz.pem
        ansible_ssh_common_args: '-o ProxyCommand="ssh -W %h:%p -q <EMAIL>"'
    
    eks_control_plane:
      hosts:
        # These will be dynamically populated by the AWS dynamic inventory plugin
      vars:
        ansible_user: ec2-user
        ansible_ssh_private_key_file: ~/.ssh/cubeonebiz.pem
        ansible_ssh_common_args: '-o ProxyCommand="ssh -W %h:%p -q <EMAIL>"'
