import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]PhoneVerify } from '@/components/customer-service-v12/[id]/phone/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]PhoneVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]PhoneVerify />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]PhoneVerify')).toBeInTheDocument();
  });
});