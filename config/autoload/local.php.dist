<?php
/**
 * Local Configuration Override
 *
 * This configuration override file is for overriding environment-specific and
 * security-sensitive configuration information. Copy this file without the
 * .dist extension at the end and populate values as needed.
 */

return array(
    // Global application settings
    'settings' => array(
        // Client settings
        'CLIENT_WEB_URL' => 'http://localhost:8888',
        
        // Merchant settings
        'MERCHANT_SUPPORT_EMAIL' => '<EMAIL>',
        'MERCHANT_WORKING_HOURS' => '9 AM - 5 PM',
        'MERCHANT_COMPANY_NAME' => 'Demo Company',
        'MERCHANT_SENDER_ID' => 'DEMO',
        
        // Global settings
        'GLOBAL_WEBSITE_PHONE' => '555-1234',
        'GLOBAL_AUTH_METHOD' => 'legacy', // Options: legacy, onesso
        
        // Signature settings
        'SIGNATURE_COMPANY_NAME' => 'Demo Company',
        
        // CMS settings
        'url_name' => 'default',
    ),
);
