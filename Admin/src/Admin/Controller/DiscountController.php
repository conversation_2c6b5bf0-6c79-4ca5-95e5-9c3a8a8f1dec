<?php
/**
 * This file manages the discounts on fooddialer system
 * The activity includes add,update and delete discount
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DiscountController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;
use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\CommonConfig as Qscommon;
use QuickServe\Model\DiscountValidator;
use Admin\Form\DiscountForm;

class DiscountController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\DiscountTable model
	 *
	 * @var QuickServe\Model\DiscountTable $discountTable
	 */
	protected $discountTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of discounts
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice) {
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New Select();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'pk_discount_code';
		$order = $this->params()->fromRoute('order')?
				 $this->params()->fromRoute('order'): Select::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$discount = $this->getDiscountTable()->fetchAll($select->order($order_by . ' ' . $order));
		$returnvar = $discount->toArray();
		$itemsPerPage = 2;

		$discount->current();
		$paginator = new Paginator(new paginatorIterator($discount));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Discount",'description'=>"Running Discount details",'breadcrumb'=>"Discount"));
		
		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages(),
		));
	}
	/**
	 * To add new discount
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function addAction()
	{
		//echo "Enter"; exit;
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new DiscountForm($sm);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		
		$form->get('submit')->setAttribute('value', 'Add');

		$request = $this->getRequest();
		if ($request->isPost()) {
			
			
			$discount = new DiscountValidator();

			if($request->getPost('discount_for') && $request->getPost('discount_for') == 'Qty')
			{
				$discount->getInputFilter()->get('product_id')->setAllowEmpty(false);
				$discount->getInputFilter()->get('quantity')->setAllowEmpty(false);
				$discount->getInputFilter()->get('group_code')->setAllowEmpty(true);
			}
			elseif($request->getPost('discount_for') && $request->getPost('discount_for') == 'Group')
			{
				$discount->getInputFilter()->get('product_id')->setAllowEmpty(true);
				$discount->getInputFilter()->get('quantity')->setAllowEmpty(true);
				$discount->getInputFilter()->get('group_code')->setAllowEmpty(false);
			}

			$discount->getInputFilter()->get('discount_name')
			->getValidatorChain()             // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'discounts',
					'field'     => 'discount_name',
					'adapter'   => $adapt,
					'message'   => 'Discount Name Already exists',
			)
			));

			//$discount->setAdapter($adapt);
			$form->setInputFilter($discount->getInputFilter());
			$form->setData($request->getPost());
			//echo "<pre>"; print_r($request->getPost()); exit;
			if ($form->isValid()) {
				//echo "<pre>"; print_r($form->getData()); exit;
				$discount->exchangeArray($form->getData());
				//echo "<pre>"; print_r($location); exit;
				$data_discount = $this->getDiscountTable()->saveDiscount($discount);
				($data_discount) ?$this->flashMessenger()->addSuccessMessage("Discount added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Discount.");
				
				if($data_discount)
				{
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$discount_name=$data_discount['discount_name'];
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'discount';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "Discount : New Discount '$discount_name' created.";
					//$activity_log_data['description']= "'$discount_name' discount added by $full_name";
					$libCommon->saveActivityLog($activity_log_data);
				}
				
				$this->flashMessenger()->addMessage('Record saved successfully!');

				// Redirect to list of albums
				return $this->redirect()->toRoute('discount');
			}/* else{
			echo '<pre>';print_r($form->getMessages());exit;
			} */
		}
		//$form->get('number_of_pages')->setOptions();
		$this->layout()->setVariables(array('page_title'=>"Add Discount",'breadcrumb'=>"Add Discount"));
		return array('form' => $form);
	}
	/**
	 * To update the discount of given discount id
	 *
	 * @return \Admin\Form\DiscountForm
	 */
	public function editAction()
	{

		$id = (int) $this->params('id');

		if (!$id) {
			return $this->redirect()->toRoute('discount', array('action' => 'add'));
		}

		$discount = $this->getDiscountTable()->getDiscount($id);

		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		$form = new DiscountForm($sm);
		//echo '<pre>';print_r($form);exit();
		
		$libCommon = Qscommon::getInstance($sm);
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$form->bind($discount);
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $discount->discount_name;

		$request = $this->getRequest();

		if ($request->isPost())
		{
			$discount = new DiscountValidator();
			
			if($request->getPost('discount_for') && $request->getPost('discount_for') == 'Qty')
			{
				$discount->getInputFilter()->get('product_id')->setAllowEmpty(false);
				$discount->getInputFilter()->get('quantity')->setAllowEmpty(false);
				$discount->getInputFilter()->get('group_code')->setAllowEmpty(true);
			}
			elseif($request->getPost('discount_for') && $request->getPost('discount_for') == 'Group')
			{
				$discount->getInputFilter()->get('product_id')->setAllowEmpty(true);
				$discount->getInputFilter()->get('quantity')->setAllowEmpty(true);
				$discount->getInputFilter()->get('group_code')->setAllowEmpty(false);
			}
			

			//$discount->getInputFilter()->get('quantity')->setRequired(false);
			//$discount->getInputFilter()->get('group_code')->setRequired(false);
			$discount->getInputFilter()->get('discount_name')
			->getValidatorChain() // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'discounts',
					'field'     => 'discount_name',
					'adapter'   => $adapt,
					'message'   => 'Discount Name Already exists',
					'exclude' => array(
							'field' => 'discount_name',
							'value' => $val,
					)
			)
			));

			//$discount->setAdapter($adapt);
			$form->setInputFilter($discount->getInputFilter());
			$form->setData($request->getPost());
			//echo '<pre>';print_r($request->getPost());exit;
			if ($form->isValid()) {
				//echo "hh";exit;
				$discount->exchangeArray($form->getData());
				$data_discount=$this->getDiscountTable()->saveDiscount($discount);
				
				if($data_discount){
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$discount_name=$discount->discount_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'discount';
					$activity_log_data['action']= 'edit';
					$activity_log_data['description']= "Discount : Discount '$discount_name' updated.";
						
					//$activity_log_data['description']= "'$discount_name' discount updated by $full_name";
					$libCommon->saveActivityLog($activity_log_data);
				}
				

			 $this->flashMessenger()->addSuccessMessage("Discount updated successfully");

				// Redirect to list of albums
				return $this->redirect()->toRoute('discount');
			}
			//	echo '<pre>';print_r($form->getMessages());exit;
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Discount",'breadcrumb'=>"Edit Discount"));
		return array(
				'id' => $id,
				'form' => $form
		);
	}
	/**
	 * To delete the discount of given discount id
	 *
	 * @param int id
	 * @return route discount
	 */
	public function deleteAction() {
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$id = (int) $this->params('id');
		
		
		$select = new Select();
		$select->where(array('pk_discount_code'=>$id));
		$discounts = $this->getDiscountTable()->fetchAll($select);
		$arrdiscounts=$discounts->toArray();
// 		echo "<pre> data =";print_r($arrdiscounts);die;
		$discount_name=$arrdiscounts[0]['discount_name'];
		$discount_status=($arrdiscounts[0]['group_status'])=='1'?'deactivated':'activated';
		
// 		echo $discount_status; exit();	
		
		if (!$id) {
			return $this->redirect()->toRoute('discount');
		}
		$data_discount=$this->getDiscountTable()->deleteDiscount($id);
		
		if($data_discount)
		{
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'discount';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "Discount : Discount '$discount_name' $discount_status.";
				
			//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
			$libCommon->saveActivityLog($activity_log_data);
		}
		
		
				($data_discount) ?$this->flashMessenger()->addSuccessMessage("Discount updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Discount.");
		return $this->redirect()->toRoute('discount');
	}
	/**
	 * Get instance of QuickServe\Model\DiscountTable
	 *
	 * @return QuickServe\Model\DiscountTable
	 */
	public function getDiscountTable()
	{
		if (!$this->discountTable) {
			$sm = $this->getServiceLocator();
			$this->discountTable = $sm->get('QuickServe\Model\DiscountTable');
		}
		return $this->discountTable;
	}

}