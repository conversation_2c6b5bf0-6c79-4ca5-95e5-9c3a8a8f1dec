/**
 * Authentication Context Tests
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '@/contexts/keycloak-context';

// Mock Keycloak module
jest.mock('@/lib/auth/keycloak', () => ({
  initKeycloak: jest.fn(),
  login: jest.fn(),
  logout: jest.fn(),
  getUserInfo: jest.fn(),
  getToken: jest.fn(),
}));

// Test component to access auth context
const TestComponent: React.FC = () => {
  const { user, isAuthenticated, isLoading, login, logout } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading.toString()}</div>
      <div data-testid="authenticated">{isAuthenticated.toString()}</div>
      <div data-testid="user">{user ? JSON.stringify(user) : 'null'}</div>
      <button data-testid="login-btn" onClick={login}>Login</button>
      <button data-testid="logout-btn" onClick={logout}>Logout</button>
    </div>
  );
};

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock window.location
delete (window as unknown as { location: unknown }).location;
window.location = { href: '' } as Location;

describe('AuthProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);

    // Mock NODE_ENV as development
    process.env.NODE_ENV = 'development';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should render children and provide auth context', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('loading')).toBeInTheDocument();
    expect(screen.getByTestId('authenticated')).toBeInTheDocument();
    expect(screen.getByTestId('user')).toBeInTheDocument();
  });

  it('should start with loading state', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Initially should be loading
    expect(screen.getByTestId('loading')).toHaveTextContent('true');

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
    expect(screen.getByTestId('user')).toHaveTextContent('null');
  });

  it('should handle development authentication from localStorage', async () => {
    const mockAuthData = {
      user: {
        id: '1',
        email: '<EMAIL>',
        username: 'test',
        firstName: 'Test',
        lastName: 'User',
        fullName: 'Test User',
        roles: ['admin']
      },
      authenticated: true,
      expiresAt: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockAuthData));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
    expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockAuthData.user));
  });

  it('should handle expired development authentication', async () => {
    const mockAuthData = {
      user: {
        id: '1',
        email: '<EMAIL>',
        username: 'test'
      },
      authenticated: true,
      expiresAt: Date.now() - 1000 // Expired 1 second ago
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockAuthData));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
    expect(screen.getByTestId('user')).toHaveTextContent('null');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('dev_auth');
  });

  it('should handle invalid localStorage data', async () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-json');

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
    expect(screen.getByTestId('user')).toHaveTextContent('null');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('dev_auth');
  });

  it('should handle login in development mode', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
    });

    act(() => {
      screen.getByTestId('login-btn').click();
    });

    expect(window.location.href).toBe('/auth/sign-in');
  });

  it('should handle logout in development mode', async () => {
    const mockAuthData = {
      user: { id: '1', email: '<EMAIL>' },
      authenticated: true,
      expiresAt: Date.now() + 24 * 60 * 60 * 1000
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockAuthData));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
    });

    act(() => {
      screen.getByTestId('logout-btn').click();
    });

    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('dev_auth');
    expect(window.location.href).toBe('/auth/sign-in');
  });
});
