import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthHealthDetailed } from '@/components/auth-service-v12/health/detailed';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthHealthDetailed', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthHealthDetailed />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthHealthDetailed')).toBeInTheDocument();
  });
});