 <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'new-customer')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		     
     	 ?>
      
      <!-- END PAGE HEADER-->
  	<div id="content">
      <div class="large-6 columns">
		<?php     
		if(trim($errStr)!=""){
			echo '<div class="alert-box alert">'.$errStr.'</div>';
		}
		?>     
        <?php echo $this->form()->openTag($form); ?>
          <fieldset>
    		<legend>Customer Info</legend>
        		<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('customer_name')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                 	<?php
		              echo $this->formElement($form->get('check_mobile_verification'));
		              	echo $this->formElement($form->get('customer_name'));
						echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('customer_name')); 
					?>            
                  	</div>
              	</div>
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('customer_Address')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                       	<?php echo $this->formElement($form->get('customer_Address'));
							echo $this->formElementErrors($form->get('customer_Address')); ?>
                  	</div>
              	</div>
              	
              	<div class="row">
         	     <div class="large-4 medium-4 small-4 columns">
	              		&nbsp;
	              	</div>
	              	<div class="large-8 medium-8 small-8 columns">
	              		<input type="checkbox" name="sameadd" id="sameadd" <?php echo ($checkedflg)?'checked':''?>> <label for="sameadd">Copy Address to Lunch & Dinner </label>
	              	</div>
              	</div>
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('company_name')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php echo $this->formElement($form->get('company_name'));
							echo $this->formElementErrors($form->get('company_name')); ?>              
                  	</div>
              	</div>
              	
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php echo $this->formElement($form->get('phone'));
						echo $this->formElementErrors($form->get('phone')); ?>          
                  	</div>
              	</div>
             
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('email_address')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php echo $this->formElement($form->get('email_address'));
						echo $this->formElementErrors($form->get('email_address')); ?>
                  	</div>
              	</div>
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('location_code')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                   <?php echo $this->formElement($form->get('location_code'));?>
					<?php echo $this->formElementErrors($form->get('location_code')); ?>
                  	</div>
                  		
              	</div>
              	  <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('city')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                   <?php echo $this->formElement($form->get('city'));
						echo $this->formElementErrors($form->get('city')); ?>
                  	</div>
              	</div>
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('group_code')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                     <?php 
                     	echo $this->formElement($form->get('group_code'));
						echo $this->formElementErrors($form->get('group_code'));
					?>          
                  	</div>
              	</div>
              	 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('registered_on')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
		                     echo $this->formElement($form->get('registered_on'));
							 echo $this->formElementErrors($form->get('registered_on'));
						?>         
                  	</div>
              	</div>
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('thirdparty')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
		                     echo $this->formElement($form->get('thirdparty'));
							 echo $this->formElementErrors($form->get('thirdparty'));
						?>         
                  	</div>
              	</div>
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                      	echo $this->formElement($form->get('status'));
						echo $this->formElementErrors($form->get('status'));
						echo $this->formElement($form->get('csrf')); 
					 ?>     
                  	</div>
              	</div>
               
              
            </fieldset>
            <div class="clearfix mb15"></div>
            
            <fieldset>
            	<legend>Delivery Location</legend>
                	
              	 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('lunch_code')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('lunch_code'));
						echo $this->formElementErrors($form->get('lunch_code'));	
					 ?>             
                  	</div>
              	</div> 
              	
              	 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('lunch_add')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('lunch_add'));
						echo $this->formElementErrors($form->get('lunch_add'));	
					 ?>             
                  	</div>
              	</div> 
              	
              	<?php 
              	$dabbawala_code_str = ($this->dabbawala_code_type=='') ? "text" : $this->dabbawala_code_type
              	?>
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right">Dibbawala Code</label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
	                      <div class="large-4 left">
		                      <label for="dabbawala_code_type_1">
								<input <?php echo ($dabbawala_code_str=='text') ? "checked" : "" ;?> name="dabbawala_code_type" type="radio" id="dabbawala_code_type_1" value="text">
								 <span class="custom radio"></span> Add Text
							  </label>
						 </div>
						 <div class="large-4 left">
						 	 <label for="dabbawala_code_type_2">
								<input <?php echo ($dabbawala_code_str=='image') ? "checked" : "" ;?> name="dabbawala_code_type" type="radio" id="dabbawala_code_type_2"  value="image">
								 <span class="custom radio"></span> Add Image
							 </label>
						</div>
                  	</div>
              	</div>
              	
              	<div class="row" id="dabbawala_code_text">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_code')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('dabbawala_code'));
						echo $this->formElementErrors($form->get('dabbawala_code'));	
					 ?>             
                  	</div>
              	</div> 
              	
              	<div class="row" id="dabbawala_code_image">
              	
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_image')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('dabbawala_image'));
						echo $this->formElementErrors($form->get('dabbawala_image'));
					 ?>             
                  	</div>
              	</div> 
              	              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('dinner_code')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('dinner_code'));
						echo $this->formElementErrors($form->get('dinner_code'));	
					 ?>             
                  	</div>
              	</div> 
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('dinner_add')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('dinner_add'));
						echo $this->formElementErrors($form->get('dinner_add'));	
					 ?>             
                  	</div>
              	</div> 
            </fieldset>
                
             <div class="clearfix mb15"></div>
                
              <div class="large-12 columns pl0 pr0">
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">&nbsp;</div>
	                  	<div class="large-8 small-8 medium-8 columns pr0">   
		                  	<div class="right">    
		                    	<?php echo $this->formElement($form->get('csrf')); ?>
		            		 	<?php echo $this->formElement($form->get('submit_button')); ?>
		            		 	<button type="submit" class="button left tiny left5 redBg" id="cancelbutton">Cancel &nbsp;<i class="fa fa-ban"></i></button>
		            		 </div>
	                	</div>
                 </div>
              </div> 
              <?php 
                 	echo $this->formElement($form->get('backurl'));
				 ?>
               
                
            <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
          </div>
          
        </div>
       
      </div>
       

    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">

$(document).ready(function(){

    showDabbawala = function(type){

		$("#dabbawala_code_text").hide();
		$("#dabbawala_code_image").hide();

		$("#dabbawala_code_"+type).show();
			
	}
	

$('#datepicker').datepicker();
$("#datepicker").datepicker({dateFormat:"yy/mm/dd"}).datepicker("setDate",new Date());

$('#sameadd').change(function() {
	//alert("dddd");
	   if($(this).is(':checked'))
    	{
		   $('#lunch_add').val( $('#customer_Address').val());
		   $('#dinner_add').val( $('#customer_Address').val());

	    }
	    else
	    {
	 	   $('#lunch_add').val('');
		   $('#dinner_add').val('');
		}
 });

$("input:radio[name='dabbawala_code_type']").on('click',function(){

	showDabbawala($(this).val());
	
});

showDabbawala($("input:radio[name='dabbawala_code_type']:checked").val());


});

</script> 