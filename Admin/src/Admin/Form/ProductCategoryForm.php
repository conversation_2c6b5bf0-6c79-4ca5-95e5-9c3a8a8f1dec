<?php
/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use QuickServe\Model\ProductCategoryTable;

class ProductCategoryForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->adapter = $adapter;
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'product_category_id',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'product_category_id',
        		),
        		'options' => array(
      		),
        ));

        $this->add(array(
            'name' => 'product_category_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'product_category_name',
                'placeholder' => 'Enter product category',
                'required' => 'required',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Menu Category<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'type',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'type',
                'required' => 'required',
                'value' => 'product',
            ),
            'options' => array(
              'label' => 'Category for<span class="red">*</span>',
              'label_options' => array('disable_html_escape' => true),
              'value_options' => $this->getProductCategoryType(),
            ),
        ));

        $this->add(array(
        		'name' => 'description',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'class'=> 'mediuminput',
        				'id' => 'description',
        				'placeholder' => 'Enter description here',
        				//  'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Description',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        /* $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
					'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive'
						)
				)
		)); */
        
        $this->add(array(
        		'name' => 'status',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes' => array(
        				'id' => 'status',
        				//   'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Status',
        				'value_options' => array(
        						array(
        								'value' => '1',
        								'label' => 'Active',
        								'selected' => true,
        						),
        						array(
        								'value' => '0',
        								'label' => 'Inactive',
        						),
        				),
        		),
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Go',
        				'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/product-category',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
    }

    /**
     * The function `getProductCategoryType` is used to fetch the types of product categories.
     * 
     * @method getProductCategoryType
     * @access private
     * @return array ProductCategoryTable::$types
     */
    private function getProductCategoryType() {
		return ProductCategoryTable::$types;
    }
}