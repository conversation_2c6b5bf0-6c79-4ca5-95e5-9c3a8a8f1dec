<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
<style> #collectionlist{display:block; } </style>
  <div class="maincontentinner">
        <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="widgets"><span>Invoice</span></h2>

          </div>
          <!--contenttitle-->

          <div class="dataTables_add" id="dyntable_filter">
        	  <a href="collectionlist" id="collectionlist"  target="_blank" class="btn btn_document" ><span>Print Collection List</span></a>
              <a id="innvoicebill" target="_blank" href="#" class="btn btn_document" style="float:left;"><span>Print Invoice</span></a>
  		  </div>
		<form id="frminvoice" class="" action="<?php echo $this->url('invoice',array('action' => 'bill'));?>" method="post" >
          <table class="stdtable"  id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <th class="head1" style="width: 20px !important" >Select</th>
                <th class="head1">Invoice No.</th>
                <th class="head0">Name</th>
                <th class="head1">Address</th>
                <th class="head1">Contact No</th>
               <!--  <th class="head1">Total Orders</th> -->
              	<th class="head0">Total Amount</th>
                <th class="head0">Status</th>
              </tr>
            </thead>
            <tfoot>
               <tr>
                <th class="head1" style="width: 20px !important">Select</th>
                <th class="head1">Invoice No.</th>
                <th class="head0" >Name</th>
                <th class="head1">Address</th>
                <th class="head1">Contact No</th>
                <!-- <th class="head1">Total Orders</th> -->
              	<th class="head0">Total Amount</th>
                <th class="head0">Status</th>
              </tr>
            </tfoot>

				<tbody>
				  <?php $is_data = false; foreach ($paginator as $invoice) : $is_data = true;?>
				  <tr>
					<td style="width: 20px !important"><input type="checkbox" class="sel_invoice" name="selectinvoice[]" id="<?php echo $this->escapeHtml($invoice->invoice_id); ?>" value=<?php echo $this->escapeHtml($invoice->invoice_id); ?> /></td>
					<td><?php echo $this->escapeHtml($invoice->invoice_no); ?></td>
					<td ><?php echo $this->escapeHtml($invoice->cust_name); ?></td>
					<td><?php echo $this->escapeHtml($invoice->customer_Address);?></td>
					<td><?php echo $this->escapeHtml($invoice->phone);?></td>
					<!-- <td><?php //echo $this->escapeHtml($invoice->quantity); ?></td> -->
					<td><?php echo $this->escapeHtml($invoice->invoice_amount) ;?></td>
					<td><?php echo $invoice->status=="1"?'Paid':'Unpaid';?>
						<!-- <a class="btn btn5 btn_pencil5" href="order_summary_details_edit.html" style=""></a>  -->
					</td>
				  </tr>
				<?php endforeach; ?>
				</tbody>

          </table>
		</form>
        </div>
        <!--content-->

      </div>
      <!--maincontentinner-->
	<style>
	#collectionlist { float: left; <?php if(!$is_data){ echo 'display:none !important';}  ?>}
	</style>
	 <script>

		$(document).ready(function(){
		  $("#innvoicebill").click(function(){
			  var ctr = 0;
			  $('.sel_invoice:checked').each(function() {
				  ctr++ ;
			  });
			  if(ctr > 0 )
			  {

			  	$("#frminvoice").submit();
			  }
			  else
			  {
				 alert("please select invoice to print");
				return false;
			  }
		  });
		});
		</script>
