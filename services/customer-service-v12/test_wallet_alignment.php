<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Container\Container;
use Illuminate\Events\Dispatcher;
use Illuminate\Log\LogManager;

// Set up database connection
$capsule = new DB;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '*************',
    'database' => 'onefooddialer_2025',
    'username' => 'root',
    'password' => 'root',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "🔍 Testing Customer Wallet Alignment for Customer ID 1\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // Test 1: Check if customer_wallet table exists and has correct structure
    echo "📋 Test 1: Database Table Structure\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $columns = DB::select("DESCRIBE customer_wallet");
    echo "Table columns found:\n";
    foreach ($columns as $column) {
        echo "  - {$column->Field} ({$column->Type})\n";
    }
    echo "\n";

    // Test 2: Check existing wallet data for customer ID 1
    echo "💰 Test 2: Existing Wallet Data for Customer ID 1\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $walletRecords = DB::table('customer_wallet')
        ->where('fk_customer_code', 1)
        ->orderBy('customer_wallet_id', 'desc')
        ->limit(10)
        ->get();
    
    if ($walletRecords->count() > 0) {
        echo "Found {$walletRecords->count()} wallet records:\n";
        foreach ($walletRecords as $record) {
            echo "  ID: {$record->customer_wallet_id}, Amount: {$record->wallet_amount}, Type: {$record->amount_type}, Date: {$record->created_at}\n";
        }
    } else {
        echo "No wallet records found for customer ID 1\n";
    }
    echo "\n";

    // Test 3: Calculate wallet balance using legacy logic
    echo "🧮 Test 3: Wallet Balance Calculation (Legacy Logic)\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $balanceData = DB::table('customer_wallet')
        ->select([
            DB::raw("SUM(CASE WHEN amount_type = 'cr' THEN wallet_amount ELSE 0 END) as total_credit"),
            DB::raw("SUM(CASE WHEN amount_type = 'dr' THEN wallet_amount ELSE 0 END) as total_debit"),
            DB::raw("SUM(CASE WHEN amount_type = 'lock' THEN wallet_amount ELSE 0 END) as total_locked")
        ])
        ->where('fk_customer_code', 1)
        ->first();

    $totalCredit = (float) ($balanceData->total_credit ?? 0);
    $totalDebit = (float) ($balanceData->total_debit ?? 0);
    $totalLocked = (float) ($balanceData->total_locked ?? 0);
    $availableBalance = $totalCredit - $totalDebit;

    echo "Credit Amount: ₹{$totalCredit}\n";
    echo "Debit Amount: ₹{$totalDebit}\n";
    echo "Locked Amount: ₹{$totalLocked}\n";
    echo "Available Balance: ₹{$availableBalance}\n";
    echo "Usable Balance: ₹" . ($availableBalance - $totalLocked) . "\n";
    echo "\n";

    // Test 4: Compare with screenshot values
    echo "📸 Test 4: Screenshot Comparison\n";
    echo "-" . str_repeat("-", 40) . "\n";
    echo "Expected from screenshot:\n";
    echo "  - Available Balance: ₹2,000.00\n";
    echo "  - Usable Balance: ₹1,790.00\n";
    echo "  - Locked Balance: ₹210.00\n";
    echo "\n";
    echo "Actual calculated values:\n";
    echo "  - Available Balance: ₹{$availableBalance}\n";
    echo "  - Usable Balance: ₹" . ($availableBalance - $totalLocked) . "\n";
    echo "  - Locked Balance: ₹{$totalLocked}\n";
    echo "\n";

    // Test 5: Check records before June 15th
    echo "📅 Test 5: Records Before June 15th, 2024\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $oldRecords = DB::table('customer_wallet')
        ->where('fk_customer_code', 1)
        ->where('created_at', '<', '2024-06-15')
        ->orderBy('created_at', 'desc')
        ->get();
    
    if ($oldRecords->count() > 0) {
        echo "Found {$oldRecords->count()} records before June 15th, 2024:\n";
        foreach ($oldRecords->take(5) as $record) {
            echo "  {$record->created_at}: ₹{$record->wallet_amount} ({$record->amount_type}) - {$record->description}\n";
        }
        if ($oldRecords->count() > 5) {
            echo "  ... and " . ($oldRecords->count() - 5) . " more records\n";
        }
    } else {
        echo "No records found before June 15th, 2024\n";
    }
    echo "\n";

    echo "✅ Wallet alignment test completed successfully!\n";

} catch (Exception $e) {
    echo "❌ Error during wallet alignment test: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
