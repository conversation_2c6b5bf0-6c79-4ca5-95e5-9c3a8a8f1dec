
<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>

               
                <div class="content">
                    
                   
                    <div class="contenttitle radiusbottom0">
                    	<h2 class="image"><span>Development</span></h2>
                    </div><!--contenttitle-->
                    
                    <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('development_crud', array('action'=>'add'));?>" class="btn btn_add"><span>Add Record</span></a></div>
                    <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
                        <colgroup>
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
														<col class="con1" />
                        </colgroup>
                        <thead>
                            <tr>
																<td class="head0">Development Plan</td>
																<td class="head1">Desc</td>
																<td class="head0">Price Plan</td>
																<td class="head1">Pages</td>
																<td class="head0">Time Required</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
																<td class="head0">Development Plan</td>
																<td class="head1">Desc</td>
																<td class="head0">Price Plan</td>
																<td class="head1">Pages</td>
																<td class="head0">Time Required</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </tfoot>
                        <tbody>
                             <?php foreach ($paginator as $development) :?>
						        <tr>
						            <td><?php echo $this->escapeHtml($development['development_name']); ?></td>
						            <?php $desc = (strlen($development['description']) > 53) ? substr($development['description'],0,50).'...' :$development['description']; ?>
						            <td><?php echo $this->escapeHtml($desc); ?></td>
						            <td><?php echo $this->escapeHtml($development['price_name']); ?></td>
						            <td><?php echo $this->escapeHtml($development['number_of_pages']); ?></td>
						             <td><?php echo $this->escapeHtml($development['time_required']); ?></td>
						             <td><?php echo ($development['development_status'])?'Active':'<span class="red">Inactive</span>';?></td>
						            <td class="center">
						            <a href="<?php echo $this->url('development_crud', array('action' => 'edit', 'id' => $development['pk_development_id'])); ?>" class="btn btn5 btn_pencil5"></a>&nbsp;
						             <?php $textadd = ($development['development_status'])? 'Suspend' :'Activate'; ?>
						            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this development plan ?')" href="<?php echo $this->url('development_crud', array('action' => 'delete', 'id' => $development['pk_development_id']));
						        	?>" class="btn btn5 btn_trash5"></a></td>
						               
						            </td>
						        </tr>
					 <?php endforeach; ?>
      
                    </table>
                    
                    <br /><br />
                    
                </div><!--content-->
                
            <