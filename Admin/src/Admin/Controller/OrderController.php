<?php
/**
 * This file manages the orders on fooddialer system
 * It shows today's order,cancelled order,unbilled order
 * Admin can also cancel order
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;
use Zend\Validator\InArray;
use Zend\View\View;
use Zend\Db\Adapter\Adapter;
use DOMPDFModule\View\Model\PdfModel;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\S3;
use Lib\Utility;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Catalogue as QSCatalogue;
use Lib\QuickServe\Wallet as QSWallet;

use Admin\Form\orderForm;
class OrderController extends AbstractActionController
{
    /**
     * It has an instance of QuickServe\Model\OrderTable model
     *
     * @var QuickServe\Model\OrderTable $ordertable
     */
    protected $ordertable;
    protected  $orderdispatchTable;
    /**
     * It has an instance of AuthService model
     *
     * @var AuthService $authservice
     */
    protected $authservice;

    /**
     * It has an instance of Utility Model
     *
     * @var AuthService $authservice
     */
    protected $utility;
    /**
     * 
     */
    protected $locationTable;
    protected $TPDelivery;

    /**
     * This function used to validate an email
     *
     * @param string $email
     * @return boolean
     */
    private function is_proper_email($email) {
        // First, we check that there's one @ symbol, and that the lengths are right
        if (!ereg("^[^@]{1,64}@[^@]{1,255}$", $email)) {
                // Email invalid because wrong number of characters in one section, or wrong number of @ symbols.
                return false;
        }
        // Split it into sections to make life easier
        $email_array = explode("@", $email);
        $local_array = explode(".", $email_array[0]);
        for ($i = 0; $i < sizeof($local_array); $i++) {
                if (!ereg("^(([A-Za-z0-9!#$%&#038;'*+/=?^_`{|}~-][A-Za-z0-9!#$%&#038;'*+/=?^_`{|}~\.-]{0,63})|(\"[^(\\|\")]{0,62}\"))$", $local_array[$i])) {
                        return false;
                }
        }
        if (!ereg("^\[?[0-9\.]+\]?$", $email_array[1])) { // Check if domain is IP. If not, it should be valid domain name
                $domain_array = explode(".", $email_array[1]);
                if (sizeof($domain_array) < 2) {
                        return false; // Not enough parts to domain
                }
                for ($i = 0; $i < sizeof($domain_array); $i++) {
                        if (!ereg("^(([A-Za-z0-9][A-Za-z0-9-]{0,61}[A-Za-z0-9])|([A-Za-z0-9]+))$", $domain_array[$i])) {
                                return false;
                        }
                }
        }
        return true;
    }
	
    public function __construct(){
            $this->utility = \Lib\Utility::getInstance();
    }
    /**
     * To view orders
     *
     * @return \Zend\View\Model\ViewModel
     */
    public function indexAction()
    {	

        $sm = $this->getServiceLocator();
        $kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
        $location_data=$this->getOrderTable()->getLocationData();
        $deliverypersons=$this->getuserTable()->getAllDeliveryPerson();

        $config_variables = $sm->get('config');
        $languages=$config_variables['supported_nonenglish_languages'];

        $menuSelected = $this->params()->fromQuery('menu','');

        $setting_session = new Container('setting');

        $menus = $setting_session->setting['MENU_TYPE'];

        $sess_acceptedMenu = new Container('acceptedMenu');

        if($menuSelected ==""){
            $menuSelected = $sess_acceptedMenu->menu['default'];
        }
		
        if (! $this->authservice)
        {
        $this->authservice = $this->getServiceLocator()
        ->get('AuthService');
     	}
        
        $skipKitchenCheck = $setting_session->setting['GLOBAL_SKIP_KITCHEN'];
     	
     	$view = $this->params('view');
        
     	$qryParams = $this->params()->fromQuery();
     	$deliveryStatus = strtolower($qryParams['dst']);
     	
     	$condition='';
        
     	if(isset($qryParams['id']) && $qryParams['id'] =='1')
     	{
            $condition = "order_status IN ('New','Complete')";
     	}
     	
     	if(!empty($deliveryStatus))
     	{
     	    if($condition !=""){
     	       $condition .= " AND "; 
     	    }
     		$condition .= " delivery_status IN ('".$deliveryStatus."') ";
     	}
     	
     	$from = $this->params('from');
     	
     	$dayofweek = date('w', strtotime(date('Y-m-d')));
     	
     	$tmr_date = $this->getOrderTable()->getNextAvailableOrderDate()->toArray()[0];
     	$date = date('d F Y',strtotime($tmr_date['order_date']));

     	if(isset($view) && $view == "today"){
     	    $which_order="Today's";
     	    $this->layout()->which = "today";
     	    $this->layout()->setVariables(array('page_title'=>"Today's Orders",'description'=>"Booked Orders",'breadcrumb'=>"Today's Orders"));
     	}elseif(isset($view) && $view == "cancel"){
     	    $which_order="Cancelled";
     	    $this->layout()->which = "cancel";
     	    $this->layout()->setVariables(array('page_title'=>"Cancelled Orders",'description'=>"Details and more",'breadcrumb'=>"Cancelled Orders"));
     	}elseif(isset($view) && $view == "unbill"){
     	    $which_order="Un-billed";
     	    $this->layout()->which = "unbill";
     	    $this->layout()->setVariables(array('page_title'=>"Unbilled Orders",'description'=>"Payment Pending",'breadcrumb'=>"Unbilled Orders"));
     	}elseif(isset($view) && $view == "nextday")
     	{
            $which_order="Next Day's";
            $this->layout()->which = "nextday";
     	
            $this->layout()->setVariables(array('page_title'=>"Next Days Orders",'breadcrumb'=>"Next Days Orders"));
     	}elseif(isset($view) && $view == "preorder")
     	{
            $which_order="Preorders";
            $this->layout()->which = "preorder";
            $this->layout()->setVariables(array('page_title'=>"Pre Orders",'description'=>"to be delivered",'breadcrumb'=>"Pre Orders"));
     	}else{
     	    $which_order="All";
     	    $this->layout()->which = "all";
     	    $this->layout()->setVariables(array('page_title'=>"Orders",'description'=>"All Orders",'breadcrumb'=>"Orders"));
     	}
        
     	$tblDispatched = $this->getServiceLocator()->get("QuickServe\Model\OrderDispatchTable");
     	
     	$select = new QSelect();
     	
     	$select->where(array("date"=>$tmr_date,"total_order > 0"));
     	
     	if(isset($_SESSION['adminkitchen'])){
     		$select->where(array('fk_kitchen_code'=>$_SESSION['adminkitchen']));
     	}
     	
     	$kitchen_details = $tblDispatched->fetchAll($select);
     	
     	if ( count($kitchen_details) > 0 ) {
     		 
            foreach ( $menus as $menu ) {
                $new_array_menus[$menu]=array();

                foreach( $kitchen_details as $key => $value ){
                    if( strtolower($kitchen_details[$key][order_menu]) == $menu ){
                            $new_array_menus[$menu][$value['fk_product_code']][$kitchen_details[$key][product_name]] = (isset($new_array_menus[$menu][$value['fk_product_code']][$key][$kitchen_details[$key][product_name]])) ? $kitchen_details[$key][total_order] + $new_array_menus[$menu][$value['fk_product_code']][$kitchen_details[$key][product_name]] : $kitchen_details[$key][total_order];
                    }
                }
            }
     	
            foreach ($menus as $dynamenu){
                    $viewData['new_array_'.$dynamenu]=$new_array_menus[$dynamenu];
            }
     	}
     	
            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser;
		
        /*
         * date picker for next day orders 
         */
            $libCommon = Qscommon::getInstance($sm);
            $holidays  = $libCommon->fetchHolidaysList('holiday');

            $weekOff=$libCommon->fetchHolidaysList('weekoff');

            $holidays_array= array();

            foreach ($holidays as $key=>$val){
                $holidays_array[$key]="'".date('Y/m/d',strtotime($val['holiday_date']))."'";
                $holiday_array[$key]=strval(date('Y/m/d',strtotime($val['holiday_date'])));
            }
            
            $holidays = implode(",",$holidays_array);
            
            $viewData=array(
                'acl' => $acl,
                'loggedUser' => $loguser,
                'which_order'=>$which_order,
                'view'=>$view,
                'condition' => $condition,
                'menus' => $menus,
                'menuSelected' => $menuSelected,
                'kitchen_data' =>$kitchen_data,
                'location_data'=>$location_data,
                'languages' =>$languages,
                'new_menu_array' => $new_array_menus,
                'deliverypersons' =>$deliverypersons,
                'nextdate' => $date,
                'showTax'=>$setting_session->setting['GLOBAL_APPLY_TAX'],
                'holidays' => $holidays, 'weekOff'=> $weekOff[0]['holiday_description'],
                'setting' => $setting_session->setting,
                'delivery_status'=>$deliveryStatus,
                'skipKitchenCheck'=>$skipKitchenCheck
            );	
            
            
        return new ViewModel($viewData);
		
    }
	
    /**
     * 
     * @return \Admin\Controller\JsonModel
     */
    public function ajxOrderAction()
    {

        $setting_session = new Container('setting');

        $menus = $setting_session->setting['MENU_TYPE'];

        $menuSelected = $this->params()->fromQuery('menu');

        if (! $this->authservice)
        {
            $this->authservice = $this->getServiceLocator()->get('AuthService');
        }
	
	    $iden = $this->authservice->getIdentity();

        $session_setting = new Container("setting");
     	$setting = $session_setting->setting;

     	$utility = Utility::getInstance();

     	$sm = $this->getServiceLocator();
	    
	    $cutofftime = $sm->get('Config')['cutofftime'];

	    $libOrder = QSOrder::getInstance($sm);

	    $libCommon = QSCommon::getInstance($sm);
	    
	    $layout = $this->layout();
	    $acl = $layout->acl;
	
	    $viewModel = new ViewModel();
	
	    $loggedUser = $layout->loggedUser;
	
	    $select = new QSelect();
	    $qryParams = $this->params()->fromQuery();
	    $qryPost = $this->params()->fromPost();
	    
	    $view = $this->params()->fromQuery('view');
	    $condition = $this->params()->fromQuery('condition');
	    $kitchenscreen = $_SESSION['adminkitchen'];
	    $location_code = $this->params()->fromQuery('location_code');
	    $orderstatus = $this->params()->fromQuery('orderstatus','');
	    $fromdate = $this->params()->fromQuery('minDate');
	    $todate = $this->params()->fromQuery('maxDate');
	    $deliveryperson = $this->params()->fromQuery('deliveryperson');
	    $dateFilter = $this->params()->fromQuery('date_filter',null);
	    $delivery_type = $this->params()->fromQuery('delivery_type',null);
	    $delivery_status = $this->params()->fromQuery('delivery_status',null);
            
	    $date = date("Y-m-d",strtotime($dateFilter));
	    
	    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
	    if($view =='preorder'){
	    	$arrColumns = array('0'=>'pk_order_no','1'=>'order_no','2'=>'customer_name','3'=>'location','4'=>'mealnames','5'=>'promo_code','6'=>'amount','7'=>'tax','8'=>'applied_discount','9'=>'delivery_charges','10'=>'service_charges','11'=>'net_amount','12'=>'order_status','13'=>'order_menu','14'=>'start_date','15'=>'end_date');
	    }else{
	    	$arrColumns = array('0'=>'pk_order_no','1'=>'order_no','2'=>'customer_name','3'=>'location','4'=>'mealnames','5'=>'amount','6'=>'tax','7'=>'applied_discount','8'=>'delivery_charges','9'=>'service_charges','10'=>'net_amount','11'=>'order_status','12'=>'order_menu','13'=>'delivery_status','14'=>'order_date');
	    }
	     
	    $order_by = $arrColumns[$arrOrder[0]['column']];
	    $order = $arrOrder[0]['dir'];
	    
	    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
	    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
	    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
	    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
	    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
	    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	
	    $columns = $this->params()->fromQuery('columns');
	    
	    $today = date("Y-m-d");
	 
	    switch($view){
	    	
	    	case "preorder":
	    		
                $expires_on = $this->params()->fromQuery('expires_on');
                
                $havingCnd = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";

                $havingCnd1 = "";
	    		
	    	    if(isset($expires_on) && $expires_on !=""){
	    
                        switch($expires_on){

                            case "today":

                                $havingCnd1 = " end_date = '$today' ";
                                break;

                            case "tommorrow":

                                $tom = date("Y-m-d",strtotime("+1 day"));
                                $havingCnd1 = " end_date = '$tom' ";
                                break;

                            case "next2day":

                                $endDate = date("Y-m-d",strtotime("+2 day"));
                                $havingCnd1 = " end_date <= '$endDate' ";
                                break;

                            case "thisweek":

                                $expireDate = date("Y-m-d",strtotime("next week -1 day"));
                                $havingCnd1 = " end_date <= '$expireDate' ";
                                break;

                            case "nextweek":

                                $startDate = date("Y-m-d",strtotime("monday next week"));
                                $endDate = date("Y-m-d",strtotime("sunday next week"));

                                $havingCnd1 = " end_date >= '$startDate' AND end_date <= '$endDate' ";
                                break;

                            case "nextmonth":

                                $startDate = date('Y-m-d',strtotime("first day of next month"));
                                $endDate = date('Y-m-d',strtotime("last day of next month"));

                                $havingCnd1 = " end_date >= '$startDate' AND end_date <= '$endDate' ";
                                break;

                        }
			    	
                        $havingCnd .= " AND ( ".$havingCnd1." ) ";
			    
                    }
			    
                    $select->having($havingCnd);

                    $select->where("orders.delivery_status !='Reordered'");

                    break;
	    		
    		case "nextday":
                    if(empty($dateFilter)){
                            $tmr_date = $this->getOrderTable()->getNextAvailableOrderDate()->toArray()[0];
                            $date = $tmr_date['order_date'];
                    }

                    foreach($menus as $k=>$v){
                            $v = "'$v'";
                            $menus[$k]=$v;
                    }
                            $menu = implode(",",$menus);     		

                    $select->where("orders.order_menu IN ($menu)");
                    $select->where(" orders.order_date = '$date'");
                    $select->where("orders.order_status IN ('New','UnDelivered','Rejected')");

                    //echo $select->getSqlString(); die;

                    break;
    			
	    	case "today":
	    		if(isset($condition) && $condition!="")
	    	    {
	    	    	$select->where($condition);
	    	    }
	    		
	    	    $select->where("orders.order_date='$today'");
	    	    $select->where("orders.order_status != 'Cancel'");
	    		
	    	    break;
	    	case "unbill":
	    	    $select->where("invoice_status ='Unbill'  AND delivery_status='Delivered'");
	    	    break;
	    	    
	    	case "cancel":
	    	    $select->where("order_status ='Cancel'");
               
	    	    break;
	    	    
                case "process":
                    $select->where("orders.order_status ='New' AND orders.order_date='$today' AND orders.delivery_status='Dispatched'");
                    break;
    	    	
                case "delivered":
                    $select->where("orders.order_status ='Complete' AND orders.order_date='$today' AND orders.delivery_status='Delivered'");
                    break;

                    default:
	    	
	    	    break;
	    }	
	    
	    if(!empty($menuSelected)){
	    	$select->where("order_menu=". "'$menuSelected'");
	    }

	    if(isset($search) && $search !=""){
	    
    		$select->where(
    				 
                    new \Zend\Db\Sql\Predicate\PredicateSet(
                        array(
                            new \Zend\Db\Sql\Predicate\Operator('orders.order_no', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.customer_name', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.group_name', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.phone', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.location_name', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.product_name', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.amount', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('applied_discount', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.order_status', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.order_menu', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.ship_address', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.remark', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.delivery_status', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.invoice_status', 'LIKE', '%'.$search.'%')

                        ),
                        // optional; OP_AND is default
                        \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
                    )
    
    		);
	    }	  
	 	
	    if($kitchenscreen!='all')
	    {
			$select->where("fk_kitchen_code = $kitchenscreen");
		}else{
			$select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
		}
	    
	    if($location_code!='all' && $location_code !='')
	    {
	    	$select->where("orders.location_code = '$location_code'");
	    }
	    if($orderstatus!='all' && $orderstatus!='')
	    {
	    	$select->where("orders.order_status = '$orderstatus'");
	    }
	    if($deliveryperson!="" && $deliveryperson!="all")
	    {
	    	$select->where(array('orders.delivery_person'=>$deliveryperson));
	    }
            
        // $delivery_type 14 july sankalp
        if($delivery_type != "" && $delivery_type != "all"){
            $select->where(array('orders.delivery_type'=>$delivery_type));
        }
        
	    if($delivery_status != "" && $delivery_status != "all"){
            $select->where(array('orders.delivery_status'=>$delivery_status));
        }
	    
	    if(isset($fromdate) && $fromdate!='')
	    {
	    	$fromdate = date('Y-m-d',strtotime($fromdate));
	    	$select->where("orders.order_date >= '$fromdate'");
	    }
	    
	    if(isset($todate) && $todate!='')
	    {
	    	$todate = date('Y-m-d',strtotime($todate));
	    	$select->where("orders.order_date <= '$todate'");
	    }
	
        /* balancedmeal issue . remove when fixed*/
        if(!$view || $view == 'preorder'){
           $select->where("orders.order_date >= DATE_SUB(NOW(),INTERVAL 1 YEAR)");
        }
        /* balancedmeal issue fixed */    
	    
        $select->order($order_by . ' ' . $order);
        //echo $select->getSqlString(); die;
	    
	    //$orders = $this->getOrderTable()->fetchAll($select,$page,$view,null,false);

//	    $orders = $libOrder->orderfetchAll($select,$page,$view);

	    $orders = $libOrder->orderfetchAll($select,$page,$view, null , null, false, false, true); // added - sankalp 
        
        $orders->setCurrentPageNumber($page)
	    ->setItemCountPerPage($itemsPerPage)
	    ->setPageRange(7);
        
	    $returnVar = array();
	    $returnVar['draw'] = $draw;
	    $returnVar['recordsTotal'] = $orders->getTotalItemCount();
	    $returnVar['recordsFiltered'] = $orders->getTotalItemCount();
	    $returnVar['data'] = array();
            
        $thirdpartyArray = array();            
        if(array_key_exists('GLOBAL_THIRDPARTY_DELIVERY', $setting) && !empty($setting['GLOBAL_THIRDPARTY_DELIVERY']) ){
            $thirdpartyArray          = explode(',',$setting['GLOBAL_THIRDPARTY_DELIVERY']); // value => yourguy,roadrunner
        }
            
	    foreach($orders as $order)
	    {
            //echo '<pre> order start date: '; print_r($order); echo '</pre>';
	    	$cur_date = date('Y-m-d');

        	$arrTmp = array();
        	
        	if(isset($view) && $view != "")
        	{
                    if($view!="preorder")
                    {	 
                            $pk_order_no = $order['pk_order_no'];
                            //array_push($arrTmp,$pk_order_no);
                    }

                    if($view!="preorder")
                    {
                            $order_no = "<a href=". $this->url()->fromRoute('order', array('action' => 'detail', 'id' => $order['order_no'], 'view' => $view, 'date' => $order['order_date'])).">".$pk_order_no."</a>";
                    }
                    else 
                    {
                            $order_no = "<a href=". $this->url()->fromRoute('order', array('action' => 'detail', 'id' => $order['order_no'], 'view' => $view)).">".$order['order_no']."</a>";
                    }
        	}
        	else
        	{
        		$pk_order_no = $order['pk_order_no'];
        		//array_push($arrTmp,$pk_order_no);
        		
        		$order_no = "<a href=". $this->url()->fromRoute('order', array('action' => 'detail', 'id' => $order['order_no'], 'date' => $order['order_date'])).">".$pk_order_no."</a>";
        	}
        	
        	if($order['order_status']=='Cancel')
        	{
        		$order['order_status']="Cancelled"; 
        	}
        	
        	if($view !='preorder'){
        		
                    $delivery_status = "";
                    if(!empty($order['delivery_status'])){
                            $delivery_status = $order['delivery_status'];
                    }elseif(empty($order['delivery_status']) && $order['order_status'] == "New"){
                            $delivery_status = "Pending";
                    }elseif(empty($order['delivery_status']) && $order['order_status'] == "UnDelivered"){
                            $delivery_status = "UnDelivered";
                    }elseif(empty($order['delivery_status']) && $order['order_status'] == "Cancelled"){
                            $delivery_status = "Cancelled";
                    }elseif(empty($order['delivery_status']) && $order['order_status'] == "Complete"){
                            $delivery_status = "Delivered";
                    }
	        	
        	}	
                
        	$strOrderIcon = $libCommon->getSourceIcon($order['order_source']);
                
                /* added delivery type - pradeep 07 march*/
                
                if(array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ){
                    
                    $strHtml = '';
                    if($order['delivery_type'] == 'pickup'){
                        $strHtml = '<button class="smBtn yellowBg has-tip tip-top" data-selector="tooltip-ir5zd4uo2" title="'.$order['delivery_type'].'">
                                <i class="fa fa-map-marker" aria-hidden="true"></i>
                        </button>';
                    }else{
                        $strHtml = '<button class="smBtn greenBg has-tip tip-top" data-selector="tooltip-ir5zd4uo0" title="'.$order['delivery_type'].'">
                                <i class="fa fa-truck" aria-hidden="true"></i>
                        </button>';
                    }
                    
                    //array_push($arrTmp, $strHtml);
                }
                $strIcons = $strHtml." ".$strOrderIcon;
                array_push($arrTmp,$strIcons);
                
        	$order_no = $order_no;
        	array_push($arrTmp,$order_no);
        	
        	$customerName = "<a href='/customer/order/".$order['customer_code']."'>".$order['customer_name']."</a>";
        	array_push($arrTmp,$customerName);
        	array_push($arrTmp,$order['location']);
        	
        	
                
        	$strDetails = "<div class='mb5'>Price: ".$utility->getLocalCurrency($order['price'])."</div>";
        	
        		
        	$strDetails .= "<div class='mb5'>Tax: ".$utility->getLocalCurrency($order['tax'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Discount : ".$utility->getLocalCurrency($order['applied_discount'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Delivery Charges : ".$utility->getLocalCurrency($order['delivery_charges'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Service Charges  : ".$utility->getLocalCurrency($order['service_charges'])." </div>";
        	
                if($order['tp_aggregator']){
                    $strDetails .= "<div class='mb5'>Third-Party Aggregator Commission  : ".$utility->getLocalCurrency($order['tp_aggregator_charges'])." (".$order['tp_aggregator_charges_type'].")</div>";
                }
                
                if($order['tp_delivery']){
                    $strDetails .= "<div class='mb5'>Third-Party Delivery Commission  : ".$utility->getLocalCurrency($order['tp_delivery_charges'])." (".$order['tp_delivery_charges_type'].") </div>";
                }
                
        	$strAmt = $utility->getLocalCurrency($order['net_amount']);
        	$strAmt .= '<span  data-title="'.$strDetails.'" >';
        	$strAmt .= '<i class="pl5 fa fa-info-circle" aria-hidden="true"></i></span>';
        	
        	array_push($arrTmp,$strAmt);
            array_push($arrTmp,$order['mealnames']);
        	array_push($arrTmp,$order['payment_mode']);
        	
        	$test = isset($order['all_status'])?explode(',',$order['all_status']):'';

            //Add timeslot if present
            if(isset($order['delivery_time']) && !empty($order['delivery_time']) && $order['delivery_end_time'] && !empty($order['delivery_end_time']) ) {
                $time_slot = '<div class="mb5">Time Slot: '.date('h:i a', strtotime($order['delivery_time'])).'-'.date('h:i a', strtotime($order['delivery_end_time'])).'</div>';
            }            
        	
        	if($view !="preorder"){
        		array_push($arrTmp,$order['order_status']);
        	}

        	array_push($arrTmp,ucfirst($order['order_menu']));
        	
        	if($view =='preorder'){
        		array_push($arrTmp,$utility->displayDate($order['start_date'],$setting['DATE_FORMAT']).$time_slot);
        		array_push($arrTmp,$utility->displayDate($order['end_date'],$setting['DATE_FORMAT']));
        	}else{
        		array_push($arrTmp,$delivery_status);
        	}
        	
        	if($view !='preorder'){
        		array_push($arrTmp,$utility->displayDate($order['order_date'],$setting['DATE_FORMAT']).$time_slot);
        	}
                
                /* deliveryPerson drop Down -pradeep 08march17- */
                $deliveryPersons = $this->getUserTable()->getAllDeliveryPerson();
                
                if($view=="today") {
                    
                    if(!empty($deliveryPersons)) {
                        $strH ='';                      
                            $strH .='<select name="deliveryBoy"  data-orderno='.$order['order_no'].' data-date='.$order['order_date'].' class="deliveryBoy" style="margin: 0px;">';

                            $strH .='<option>--Select--</option>';
                            foreach($deliveryPersons as $deliveryPerson){
                                 $selected = "";
                                if($order['delivery_person']==$deliveryPerson['pk_user_code']){
                                        $selected = "selected";
//                                }elseif($order['prefered_delivery_person_id']==$deliveryPerson['pk_user_code']){
//                                        $selected = "selected";                                    
                                }
                            $strH .= "<option ".$selected." value=".$deliveryPerson['pk_user_code'].">".$deliveryPerson['first_name']." ".$deliveryPerson['last_name']."</option> ";
                            }
                            $strH .= "</select>";
                    }
                    array_push($arrTmp,$strH);
                }
                /* end */ 
                
                /* added delivery type - sankalp 14July*/
        	
                //$confirmMsg = "Are you sure do you really want to perform this operation?";
                $confirmMsg = "Are you sure to perform this operation?";

	        if($view=="today" || $view=="nextday"){

                $str = ""; // <td> removed from str

                if(isset($setting['GLOBAL_ENABLE_RECURRING_ORDER']) && $setting['GLOBAL_ENABLE_RECURRING_ORDER'] == 'yes' && isset($order['recurring_status']) ) {

                    //$str = "";
                    $strCls = ($order['recurring_status'] == 1) ? 'fa-stop' : 'fa-repeat';
                    $strTitle = ($order['recurring_status'] == 1) ? 'Stop Recurring' : 'Start Recurring';

                    if($acl->isAllowed($loggedUser->rolename,'order','shiftcancelorder')) {
                       $str .= '<button id="'.$order['order_no'].'" class="smBtn grayBg has-tip tip-top recurringorder" onclick="javsacript:confirm(\'Do You Want To '.$strTitle.' Order?\')" data-orderno="'.$order['order_no'].'" data-tooltip  title="'.$strTitle.'"><i class="fa '.$strCls.'"></i></button>';
                       //$str .= '<button class="smBtn redBg has-tip tip-top btnCancel" onclick="javsacript:getConfirmation(\''.$order['recurring_status'].'\',\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_menu'].'\',\''.$order['order_date'].'\',\''.$order['fk_kitchen_code'].'\',\''.$view.'\');" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button></td>';
                    } 
                }

				//if (time() < strtotime($cutofftime) && $acl->isAllowed($loggedUser->rolename,'order','cancelorder') && $order['delivery_status']==""  ){				                
                if ($acl->isAllowed($loggedUser->rolename,'order','cancelorder') && $order['delivery_status']=="Pending" && $order['order_status']=='New'){	//&& $order['delivery_status']==""

	            	//$str .= '<button class="smBtn redBg has-tip tip-top" onclick="javsacript:if(confirm(\''.$confirmMsg.'\')){location.href=\''.$this->url()->fromRoute('order', array('action'=>'cancelorder','id' => $order['order_no'],'c_id' => $order['customer_code'],'menu'=>$order['order_menu'],'date'=>$order['order_date'])).'\'}" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button>';
                    $str .= '<button class="smBtn redBg has-tip tip-top btnCancel" onclick="javsacript:getConfirmation(\''.$order['recurring_status'].'\',\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_menu'].'\',\''.$order['order_date'].'\',\''.$order['fk_kitchen_code'].'\',\''.$view.'\');" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button>';                   
                    if($view == "today"){

                        $cut_offday = $setting['K'.$kitchenscreen.'_'.strtoupper($order['order_menu']).'_ORDER_CUT_OFF_DAY'];
                        $cut_offtime = $setting['K'.$kitchenscreen.'_'.strtoupper($order['order_menu']).'_ORDER_CUT_OFF_TIME'];                      

                        $show_shift_icon = false;

                        if($cut_offday == 0 && time() < strtotime($cut_offtime) ) {
                            $show_shift_icon = true;
                        }

                        if($acl->isAllowed($loggedUser->rolename,'order','shiftcancelorder') && $order['delivery_status']=="Pending" && $order['order_status']=='New' && $show_shift_icon == true) {                            
                            $str .= '<button id="manualshift" class="smBtn greenBg has-tip tip-top autoplaceorder" onclick="loadModel(\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_date'].'\',\''.$order['order_status'].'\',\''.$order['order_menu'].'\');" data-tooltip  title="Shift Order" value="menualshift"><i class="fa fa-step-forward"></i></button>';
                        }                        
                    }
				}else if ($acl->isAllowed($loggedUser->rolename,'order','rejectundelivered') && $order['order_status']=="New" && $order['delivery_status']=="Dispatched" ){
					
					$str .= '<button class="smBtn redBg has-tip tip-top btnRejected"  onclick="javsacript:if(confirm(\''.$confirmMsg.'\')){location.href=\''.$this->url()->fromRoute('order', array('action'=>'rejectundelivered','id' => $order['order_no'],'c_id' => $order['customer_code'],'order_status'=>'Rejected','date'=>$order['order_date'])).'\'}" data-tooltip  title="Rejected"><i class="fa fa-thumbs-down"></i></button>';
					$str .= '<button class="smBtn redBg has-tip tip-top btnUndeliverd"  onclick="location.href=\''.$this->url()->fromRoute('order', array('action'=>'rejectundelivered','id' => $order['order_no'],'c_id' => $order['customer_code'],'order_status'=>'UnDelivered','date'=>$order['order_date'])).'\'" data-tooltip  title="Un Delivered"><i class="fa fa-undo"></i></button>';
				}
				else if ($acl->isAllowed($loggedUser->rolename,'order','cancelorder') && $order['order_menu'] !='instantorder' && ( $order['order_status']=="Rejected" || $order['order_status']=="UnDelivered" ) && ($order['delivery_status']=="UnDelivered" || $order['delivery_status']=="Rejected" )){
					$str .= '<button class="smBtn greenBg has-tip tip-top autoplaceorder" onclick="loadModel(\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_date'].'\',\''.$order['order_status'].'\',\''.$order['order_menu'].'\');" data-tooltip  title="Shift Order"><i class="fa fa-step-forward"></i></button>';                    
				}                               
                
                // added 28march16 - sankalp
                if($thirdpartyArray){
                    
                    if( ($order['order_status'] == 'New') && in_array($order['third_party_name'], $thirdpartyArray) ){

                        $display = (!is_null($order['tp_system_order_id']) ) ? 'show':'hide';

                        if($display == 'hide'){
                            $str .= '<button class="smBtn greenBg has-tip tip-top" id="thirdparty-book" data-thirdparty="yourguy" data-id="'.$order['pk_order_no'].'" data-tpid="'.$order['tp_system_order_id'].'" data-tooltip  title="Book pickup [yourguy]"><i class="fa fa-truck"></i></button>';
                        }
                        $str .= '<button class="smBtn blueBg has-tip tip-top '.$display.'" id="thirdparty-cancel" data-thirdparty="yourguy" data-id="'.$order['tp_system_order_id'].'" data-tooltip  title="Cancel pickup [yourguy]"><i class="fa fa-times"></i></button>';
                        $str .= '<button class="smBtn yellowBg has-tip tip-top '.$display.'" id="thirdparty-getStatus" data-thirdparty="yourguy" data-id="'.$order['tp_system_order_id'].'"  data-order_id="'.$order['pk_order_no'].'" data-tooltip  title="Delivery status [yourguy]"><i class="fa fa-clock-o"></i></button>';

                    }
                }
                
                array_push($arrTmp,$str);
                 
            }            

            if($view=="preorder") {
                //echo '<pre>'; print_r($order);
                $str = "";
  
		        if(isset($setting['GLOBAL_ENABLE_RECURRING_ORDER']) && $setting['GLOBAL_ENABLE_RECURRING_ORDER'] == 'yes' && isset($order['recurring_status']) ) {

	               
        	        $strCls = ($order['recurring_status'] == 1) ? 'fa-stop' : 'fa-repeat';
                	$strTitle = ($order['recurring_status'] == 1) ? 'Stop Recurring' : 'Start Recurring';

			        if($acl->isAllowed($loggedUser->rolename,'order','shiftcancelorder')) {
        	           $str .= '<button id="'.$order['order_no'].'" class="smBtn grayBg has-tip tip-top recurringorder" onclick="javsacript:confirm(\'Do You Want To '.$strTitle.' Order?\')" data-orderno="'.$order['order_no'].'" data-tooltip  title="'.$strTitle.'"><i class="fa '.$strCls.'"></i></button>';
                	   $str .= '<button class="smBtn redBg has-tip tip-top btnCancel" onclick="javsacript:getConfirmation(\''.$order['recurring_status'].'\',\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_menu'].'\',\''.$order['order_date'].'\',\''.$order['fk_kitchen_code'].'\',\''.$view.'\');" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button></td>';
                    } 
		        }

            	if( $order['start_date'] > $today && $order['order_status'] == "New") {
           	        //$str .= '<button class="smBtn redBg has-tip tip-top btnCancel" onclick="javsacript:getConfirmation(\''.$order['recurring_status'].'\',\''.$order['order_no'].'\',\''.$order['customer_code'].'\',\''.$order['order_menu'].'\',\''.$order['order_date'].'\',\''.$order['fk_kitchen_code'].'\',\''.$view.'\');" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button></td>';
                    $str .= '<button class="smBtn redBg has-tip tip-top" onclick="javsacript:if(confirm(\''.$confirmMsg.'\')){location.href=\''.$this->url()->fromRoute('order', array('action'=>'cancelorder','id' => $order['order_no'],'c_id' => $order['customer_code'],'menu'=>$order['order_menu'])).'\'}" data-tooltip  title="Cancel order"><i class="fa fa-ban"></i></button></td>';
                }
            	
            	array_push($arrTmp,$str);
            }
            
            
            array_push($returnVar['data'],$arrTmp);
	    }//end of foreach
	    //die;
	    return new JsonModel($returnVar);
	}
	
        
	/**
	 * @return load vide to render model for autoshift
	 */
	function loadshiftmodelAction(){

		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		$libCatalogue = QSCatalogue::getInstance($sm);
		$utility = Utility::getInstance();
				
		$holidays  = $libCommon->fetchHolidaysList('holiday');
	
		$weekOff = $libCommon->fetchHolidaysList('weekoff');
	
		foreach ($holidays as $key=>$val){
			$holidays_array[$key]="'".date('Y/m/d',strtotime($val['holiday_date']))."'";
			$holiday_array[$key]=strval(date('Y/m/d',strtotime($val['holiday_date'])));
		}
		
		
		$settings = $libCommon->getSettings();
		$request = $this->getRequest();
		
		$date = $this->params()->fromRoute('date');
		$ordno = $this->params()->fromRoute('ordno');
		$custid = $this->params()->fromRoute('custid'); 
        $ordstatus = $this->params()->fromRoute('ordstatus'); 
        $menu = $this->params()->fromRoute('menu');
        
		$lastdate = $libOrder->getLastDateofOrder_new($ordno);
		$order = $libOrder->getviewOrder($ordno,'reference',$date);
        
		$lastdate = date('Y/m/d', strtotime('+1 day', strtotime($lastdate)));
		//$lastdate = $newdate;
		        
		if($request->isPost()){

            $ordno = $request->getPost("ordno");
            $newdate = $request->getPost("newdate");
            $custid = $request->getPost("custid");
            $orddate = $request->getPost("orddate");
            $ordstatus = $request->getPost("ordstatus");
            $menu = $request->getPost("menu");
			
            try{

                    ///////////// fetch order info ////////////
                $condition = "AND order_status='Dispatch'";
                $order = $libOrder->getviewOrder($ordno,'referencegroup',$orddate,$condition);
                $orderArr = $order->toArray();

                $kitchen = $orderArr[0]['fk_kitchen_code'];
                $order_menu = $orderArr[0]['order_menu'];
                
                ///////////// get week offs in string format (e.g 1,2,3 etc...) ///////////////
                $strWeekOffs = $weekOff[0]['holiday_description'];

                $arr = [0,1,2,3,4,5,6];

                if(!empty($orderArr[0]['days_preference'])){
                        $arrWeekOffs = array_diff($arr,explode(',',$orderArr[0]['days_preference']));
                        $strWeekOffs = implode(",",$arrWeekOffs);
                }else{

                    $kitchenwise_menu_weekoff_setting = $settings['K'.$kitchen.'_'.  strtoupper($orderArr[0]['order_menu']) .'_WEEKOFFS'];

                    if($kitchenwise_menu_weekoff_setting != null && $kitchenwise_menu_weekoff_setting != ""){ // change by shil to allow 0 as true value.
                        $strWeekOffs =  $kitchenwise_menu_weekoff_setting;
                    }
                }

                ////////// fetch new order details ////////////
                $orddate = date('Y/m/d', strtotime(($orddate)));

                $tempOrders = $libOrder->getNewOrders($custid,$ordno,false,array($orddate));



                ////////////////////////////////////////////////

                $availableDates = array();

                // Check if calendar based or non calendar based.
                if($settings['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){

                        $today = date("Y-m-d");

                        $arrWeekOffs = explode(",",$strWeekOffs);

                        $filterAvailableDates = array();
                        $availableDatesProducts = array();

                        $pCnt = 0;

                        foreach ($tempOrders as $rowData){

                            //echo "<pre>";print_r($rowData);echo "</pre>";

                            if($rowData['product_type']=='Meal'){

                                $pCnt++;

                                $availableDatesProducts[$rowData['product_code']] = $libCatalogue->getCalendarProducts($rowData['product_code'],1,true,$order_menu,$kitchen);

                                $availableDates = array_keys($availableDatesProducts[$rowData['product_code']]);

                                //echo "<pre>";print_r($availableDates);echo "</pre>";

                                if(empty($availableDates)){

                                    return new JsonModel(array('status'=>'error','msg'=>"Menu is not available yet for {$rowData["product_name"]} ."));
                                }

                                $aCnt = 0;
                                // If menu prepared then check for how many days it is prepared.
                                foreach($availableDates as $aDate){

                                    if(!in_array(date('w', strtotime($aDate)), $arrWeekOffs) && !in_array($aDate, $holidays_list)
                                        && $aDate == $lastdate){
                                        array_push($filterAvailableDates,$aDate);
                                        $aCnt++;
                                    }

                                }

                                if($aCnt != 1){

                                return new JsonModel(array('status'=>'error','msg'=>"{$rowData["product_name"]} menu is not available."));
                                }

                            }

                        }

                        //echo "<pre>";print_r($filterAvailableDates);echo "</pre>";

                        // If order contains more than one meals then order should shift to common available dates.
                        if($pCnt > 1){

                            // Find out common dates amongs meals available dates , these dates should be more than or equal to cancel days...
                            if(!empty($filterAvailableDates)){

                                $withoutDuplicates = array_unique($filterAvailableDates);

                                $duplicates = array_diff_assoc($filterAvailableDates, $withoutDuplicates);

                                if(count($duplicates) != 1){

                                        return new JsonModel(array('status'=>'error','msg'=>" Menu is not available. "));
                                }

                                $filterAvailableDates = $duplicates;

                            }else{

                                return new JsonModel(array('status'=>'error','msg'=>"No dates available"));

                            }

                        }

                }

                ///////////////////////////////////////////////

                $newD = array();

                $newDates = array(date('Y/m/d', strtotime(($newdate))));

                foreach (array($orddate) as $key=>$vals){

                    $newDates[strval(date('Y-m-d',strtotime($vals)))] = date('Y-m-d',strtotime($newDates[$key]));

                    $newD[$key] = strval(date('Y-m-d',strtotime($newDates[$key])));

                    unset($newDates[$key]);

                }

                $existingOrderDates = array();

                foreach ($tempOrders as $key=>$vals){

                    foreach ($newDates as $keys=>$val){

                        if(strval($vals['order_date'])==$keys){

                                $existingOrderDates[$newDates[$keys]] = $tempOrders[$key]['order_date'];

                                $tempOrders[$key]['order_date']=$newDates[$keys];
                        }
                    }
                }

                //$tempOrdersDetails = $libOrder->getNewOrderDetails($id,$cancelDays);

                if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){

                    $tempOrdersDetails = array();

                    foreach($tempOrders as $oProduct){

                        foreach ($existingOrderDates as $key=>$vals){

                            foreach ($availableDatesProducts[$oProduct['product_code']][$key] as $aProduct){

                                $tmpDetails = array();
                                $tmpDetails['ref_order_no'] = $id;
                                $tmpDetails['meal_code'] = $aProduct->fk_product_code;
                                $tmpDetails['product_code'] = $aProduct->product_code;
                                $tmpDetails['product_name'] = $aProduct->product_name;
                                $tmpDetails['quantity'] = $oProduct['quantity'] * $aProduct->product_qty;
                                $tmpDetails['product_type'] = $oProduct['product_type'];
                                $tmpDetails['order_date'] = $key;

                                array_push($tempOrdersDetails,$tmpDetails);

                            }

                        }

                    }

                    }else{

                        $tempOrdersDetails = $libOrder->getNewOrderDetails($ordno,array($orddate));

                        foreach ($tempOrdersDetails as $key=>$vals){

                                foreach ($newDates as $keys=>$val){

                                        if(strval($vals['order_date'])==$keys){

                                                $tempOrdersDetails[$key]['order_date'] = $newDates[$keys];

                                        }
                                }
                        }

                    }
				
				////////////////// tax added by shilbhushan on 22nd Apr 16. /////////////////////////
	    		$arrBills = $libOrder->getOrderTable()->getOrderBillNos(array($ordno),array($orddate));
	    		
	    		$arrTaxDetails = array();
	    		
	    		$arrTaxDetails['new_order_dates'] = $newDates;
	    		$arrTaxDetails['new_taxes'] = array();

	    		foreach($arrBills as $okey=>$obill){
	    			$tempTaxDetails = $libOrder->getOrderTable()->getOrderTaxDetails($id,array($obill));
	    			$tempTaxDetails = $tempTaxDetails->toArray();
	    			
	    			foreach($tempTaxDetails as $txDetail){
	    				
	    				$date = $newDates[$txDetail['order_date']];
	    				
	    				if(!isset($arrTaxDetails['new_taxes'][$date])){
	    					
	    					$arrTaxDetails['new_taxes'][$date] = array();
	    					$arrTaxDetails['new_taxes'][$date][] = $txDetail;
	    				}else{
	    					$arrTaxDetails['new_taxes'][$date][] = $txDetail;
	    				}
	    			}
	    			
	    			//print_r($tempTaxDetails->toArray());die;
	    		}

	    		///////////////////////// End /////////////////////////////////////

	    		//echo "<pre>";print_r($tempOrders);die;

				$result_msg3 = $libOrder->performKitchenOpertation($tempOrders,$custid,$ordno,$tempOrders[0]['order_menu'],$tempOrders[0]['fk_kitchen_code'],array($orddate),$newDates,$newD,false);

				if($orderArr[0]['recurring_status'] != 1) {
                    $res = $libOrder->changeOrderStatus($custid,$ordno,$tempOrders[0]['order_menu'],array(date('Y-m-d', strtotime(($orddate)))),'Reordered');    
                }

				$result_msg2 = $libOrder->AutoPlaceOrders($tempOrders,$tempOrdersDetails,$arrTaxDetails);

				if(is_array($result_msg3))
				{
					
					$kitchenexceed.=implode(",",array_unique($result_msg3)).",";
					
				}
					
				if($kitchenexceed!=""){
					
					$msg="Order shifted successfully Please Increase the capacity of Kitchen on Dates :".$kitchenexceed;
				
					return new JsonModel(array('status'=>true,'msg'=>$msg));
					
				}else{
					
					$msg="Order shifted successfully";
					
					return new JsonModel(array('status'=>true,'msg'=>$msg));
					
				}
				
			}catch (\Exception $e){
				
				return new JsonModel(array('status'=>false,'msg'=>$msg));
			}

		}

		$details = (array(
				'ordDate' => $date,
				'lDate' => $lastdate,
				'custid' => $custid,
				'ordNo' => $ordno,   
                'ordstatus' => $ordstatus,
                'menu' => $menu,
				'preferredDays' => $utility->getWeekOfDaysByNum($order->toArray()[0]['days_preference']),
				'holiday'=>implode(",",$holidays_array),
				'weekOff'=>$weekOff[0]['holiday_description'],
		));
		
		
		$view = new ViewModel($details);
		$view->setTerminal(true);
		return $view;
		
	}
	
	/**
	 * Print Todays Order
	 */
	
	public function printorderAction()
	{
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;
        $printLabel = $setting_session->setting['PRINT_LABEL']; 

        $s3 = $sm->get('S3');
        $bucketFolder = $setting['S3_BUCKET_URL'];
        $hostname = $s3->getHostname();
                
        $menus = $setting_session->setting['MENU_TYPE'];
                
        $sess_acceptedMenu = new Container('acceptedMenu');
    	
		$menuSelected = $this->params()->fromRoute('menu');
        $language_array = $this->params()->fromRoute('language');
		$location_id=$this->params()->fromRoute('location');
                
		$deliveryperson = $this->params()->fromQuery('deliveryperson');
		$delivery_type = $this->params()->fromQuery('delivery_type');
                
		$kitchen_screen = $this->params()->fromRoute('kitchen');
		
		$delivery_codes = $this->getLocationTable()->fetchAll();

        $location_ids = array();
		//echo "location name".$location_id; exit();
		if($location_id=='all')
		{
			foreach ($delivery_codes as $code){
				$location_ids[] = $code['pk_location_code'];
			}
		}
		else
		{
			array_push($location_ids,$location_id);
		}

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		
		
		$config = $this->getServiceLocator()->get("config");
		
		$today = date("Y-m-d");
		$select  =  new QSelect();
		$select->where(array("orders.order_date"=>$today));
		$select->where("orders.order_status != 'Cancel'");
		
		if($menuSelected !='' && !empty($menuSelected))
		{	
			$select->where(array("orders.order_menu"=>$menuSelected));
		}
       
		if($_SESSION['adminkitchen'] != 'all'){
			$select->where(array("orders.fk_kitchen_code"=>$_SESSION['adminkitchen']));
        }else{
            $select->where->in("orders.fk_kitchen_code", array_column($loguser->kitchens, 'fk_kitchen_code'));
        }
		
		$select->where->in('orders.location_code',$location_ids);
		
		if($deliveryperson!="" && $deliveryperson!="all")
		{
			$select->where(array('orders.delivery_person'=>$deliveryperson));
		}
		if($delivery_type!="" && $delivery_type!="all")
		{
			$select->where(array('orders.delivery_type'=>$delivery_type));
		}

        $select->where(array('orders.product_type'=>"Meal")); //added by pratik
		
        $print_data = $this->getOrderTable()->fetchAll($select,null,'printorder', 'history', null, true, true); // true added by sankalp
		
        //echo "<pre>"; print_r($print_data->toArray()); echo "</pre>"; die();
		
		if($print_data)
		{
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'order';
			$activity_log_data['action']= 'printorder';
			$activity_log_data['description']= "Print : Order printed.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
				
                $libOrder = QSOrder::getInstance($sm);

                $tmp = array();
                // parsing array to get count of orders/ delivery person
                foreach($print_data as $value){
                     
                    $deliverPersonName = ($value['delivery_person']) ? $value['delivery_person'] : 'None Assigned';
                    
                    $bill =  $libOrder->getOrderBillNos(array($value['order_no']),$value['order_date']);
                    
                    $billNo = reset($bill);
                    $value['bill_no'] = $billNo;
                    $tmp[$deliverPersonName][] = $value; 
                }
                
		$details = (array(
                    'print_data' =>  $tmp,  //$print_data,
                    'root_url' => $config['root_url'],
                    'menu' => $menuSelected,	
                    'print_label' => $printLabel,
                    'aws_url' => $hostname."/".$bucketFolder,
                    'show_delivery_type' => ((array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ) && (array_key_exists('PRINT_LABEL_SHOW_DELIVERY_TYPE', $setting) && ($setting['PRINT_LABEL_SHOW_DELIVERY_TYPE'] == 'yes') ) )
		));
		//echo'<pre>';print_r($tmp);die();
		$view = new ViewModel($details);
		$view->setTerminal(true);
		return $view;
	}
	
        
        /*
         * Print Packaging
         */   
    public function printpackagingAction()
	{
            
		$layoutviewModel    = $this->layout();
		$acl                = $layoutviewModel->acl;
		$loguser            = $layoutviewModel->loggedUser;

		$sm                 = $this->getServiceLocator();
		$libCommon          = Qscommon::getInstance($sm);
		
		$session_setting    = new Container("setting");
		$setting            = $session_setting->setting;
		$menulabel          = $_GET['menu'];
		$kitchenscreen      = $_GET['screen'];
		$language           = $_GET['lang'];
		
		$config             = $this->getServiceLocator()->get("config");
	
		$dateFilter = date('Y-m-d',strtotime($_GET['date']));
                
        $heading = ($dateFilter == date('Y-m-d')) ? "Today's Packaging" : "Next day's Packaging";
                
        $select = new QSelect();
                
		if($menulabel!='')
		{
			$select->where(array('order_menu' => $menulabel));
		}
		
		if($kitchenscreen!='all'){
			$select->where(array('fk_kitchen_code'=> $kitchenscreen));
        }else{
            $select->where->in('fk_kitchen_code' , array_column($loguser->kitchens, 'fk_kitchen_code') );            
        }
                
        $result = $this->getOrderTable()->printPackaging($select, $dateFilter);                
//                echo "<pre>"; print_r($result); die();
	
		$count = count($result);

        if($result)
		{
			$full_name = $loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'order';
			$activity_log_data['action']= 'printpackaging';
			$activity_log_data['description']= "Print : Packaging Meals for '$count'.";
                        
			$libCommon->saveActivityLog($activity_log_data);
		}
	
		$view_Data=array(
        	'heading' => $heading,
            'dateFilter' => $dateFilter,
			'root_url' => $config['root_url'],
            'menu' => ($menulabel)? ucfirst($menulabel): 'All',
            'result'  => $result->toArray()
		);
//		echo "<pre>"; print_r($view_Data); die();
		$view = new ViewModel($view_Data);
		$view->setTerminal(true);
		return $view;
	}
        
        
	/**
	 * Print KTO List
	 */
	
	public function printkotlistAction()
	{
        $select = new QSelect();
        $this->table = "products";
        $layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$sm = $this->getServiceLocator();
		$libCommon = Qscommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);

        $session_setting = new Container("setting");
		$setting = $session_setting->setting;
		$menu = $setting['MENU_TYPE'];
		/*$menulabel = $_GET['menu'];
		$kitchenscreen = $_GET['screen'];
		$language = $_GET['lang'];*/
		
		$menulabel = $this->params()->fromQuery('menu');
		$kitchenscreen = $this->params()->fromQuery('screen');
		$language = $this->params()->fromQuery('lang');
		$subaction = $this->params()->fromQuery('s');
		$date = $this->params()->fromQuery('date');

		//$menuSelected = $this->params()->fromRoute('menu');
		
		$config = $this->getServiceLocator()->get("config");
	
		$tblDispatched = $this->getServiceLocator()->get("QuickServe\Model\OrderDispatchTable");
		
		$tblKitchenMaster = $this->getServiceLocator()->get("QuickServe\Model\KitchenMasterTable");
	
        //$tblProducts = $this->getServiceLocator()->get("QuickServe\Model\ProductTable");
       
		$date = date("Y-m-d",strtotime($date));
		
        $libOrder->migrationProcess($date); //KOT matching
		//$today_date = date('Y-m-d');
	    $sm = $this->getServiceLocator();
        $adapter = $sm->get('Write_Adapter');
        $sql=new QSql($sm);
        
		$select = new QSelect();

	
		$select->where("kitchen.date = '$date' AND kitchen.total_order > 0 ");
        $select->join(array('p' => "products"), 'p.pk_product_code = kitchen.fk_product_code', array("description"));
        $select->from("kitchen");
       // echo $select->getSqlString(); die();
		
		if($menulabel!='')
		{
			$select->where(array('kitchen.order_menu'=>$menulabel));
		}
		
		if($kitchenscreen!='all'){
			$select->where(array('kitchen.fk_kitchen_code'=>$kitchenscreen));
//			$kitchen_screen_details = $tblKitchenMaster->getKitchen($kitchenscreen);
//			
//			$kitchen_screen_name = $kitchen_screen_details->kitchen_name;
		}else{
            $select->where->in("kitchen.fk_kitchen_code", array_column($loguser->kitchens, 'fk_kitchen_code'));
        }
		//dd($select->getSqlString());
		$kitchen_details = $tblDispatched->fetchAll($select,$language);
		

		$kitchen_data = $kitchen_details;

		$count = count($kitchen_data);
	
		if($kitchen_data)
		{
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'order';
			$activity_log_data['action']= 'printktolist';
			$activity_log_data['description']= "Print : KOT printed for '$count'.";
	
			$libCommon->saveActivityLog($activity_log_data);
		}

		$exportData = array();
	
		$cnt = 0; 
		foreach ($menu as $dynamenu){
		
			$new_array_menus[$dynamenu]=array();

			foreach($kitchen_data as $product){
				//echo "<pre>"; print_r($product);
				if($product['order_menu']==$dynamenu){
					//$new_array_menus[$dynamenu][$product['product_name']] = $product['total_order'];
					//$new_array_menus[$dynamenu]['weight'] = ($product['total_order'] * $product['gen_quantity']).' '.$product['gen_unit'];
					$new_array_menus[$dynamenu][$cnt]['product_name'] = $product['product_name'];
                    $new_array_menus[$dynamenu][$cnt]['description'] = $product['description'];
					$new_array_menus[$dynamenu][$cnt]['product_qty'] = $product['total_order'];
					$new_array_menus[$dynamenu][$cnt]['product_wt'] = $product['total_quantity'] * $product['total_order'];
					$temp = array();
					$temp['menu'] = $dynamenu;
					$temp['item'] = $product['product_name'];
                    $temp['description'] = $product['description'];
					$temp['qty'] = $product['total_order'];
					$temp['weight'] = $product['total_quantity'] * $product['total_order'];
					$exportData[] = $temp;
					$cnt++;
				}
			}
			//die;
		}

		if($subaction=='export'){
			
			$formData['status'] = "";
			$formData['selected_columns'] = "menu,item,qty,weight";
			$formData['export_type'] = "xls";
			$formData['export_filename'] = "KOT_list_kitchen".$kitchenscreen;
			
			if($menulabel != ''){
				$formData['export_filename'] .= "_".$menulabel;
			}
			
			$formData['export_filename'] .= "_".$date;
			return $this->forward()->dispatch('Admin\Controller\Report', array(
				'printData' => $exportData,
				'action'    => 'printReport',
				'formData'  => $formData
			));
			exit;
		}
		
		$view_Data = array(
			'root_url' => $config['root_url'],
		);
		
		$view_Data['new_menu_array'] = $new_array_menus;
		$view_Data['menu'] = $menulabel;
		$view_Data['date'] = $date;
		
		//dd($view_Data);
		
		$view = new ViewModel($view_Data);
		$view->setTerminal(true);
		return $view;
		
	}
	
	
	public function printnextdayorderAction()
	{
       
		$sm = $this->getServiceLocator();
		$storage_adapter = $sm->get("Write_Adapter");
		
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		$menu = $setting['MENU_TYPE'];
		$menuSelected = $this->params()->fromRoute('menu');
		$date = $this->params()->fromQuery('date',null);
		$kitchenscreen = $_SESSION['adminkitchen'];
		$dateFilter = date('Y-m-d',strtotime($date));
		$libOrder->migrationProcess($dateFilter);
		//echo "<pre>"; print_r($menuSelected); exit;
		$language = $_GET['lang'];
		$delivery_codes = $this->getLocationTable()->fetchAll();
		
		
		$location_id=$this->params()->fromRoute('location');
		$location_ids = array();
		//echo "location name".$location_id; exit();
		if($location_id=='all')
		{
			foreach ($delivery_codes as $code){
				$location_ids[] = $code['pk_location_code'];
			}
		}
		else
		{
			array_push($location_ids,$location_id);
		}
	
		$config = $this->getServiceLocator()->get("config");
	
		$tblDispatched = $this->getServiceLocator()->get("QuickServe\Model\OrderDispatchTable");
	
		//$tmr_date = date('Y-m-d',strtotime("+1 days"));
		
		if(empty($date)){		
			$tmr_date = $this->getOrderTable()->getNextAvailableOrderDate()->toArray()[0];
			$dateFilter = date('Y-m-d',strtotime($tmr_date['order_date']));
		}
		

		$select = new QSelect();
	
		$select->where(array("date"=>$dateFilter,"total_order > 0"));

		// if(isset($_SESSION['adminkitchen'])){
		// 	$select->where(array('fk_kitchen_code'=>$_SESSION['adminkitchen']));
		// }
		
        if($kitchenscreen!='all'){
            $select->where(array('fk_kitchen_code'=>$kitchenscreen));
        }else{
            $select->where->in("fk_kitchen_code", array_column($loguser->kitchens, 'fk_kitchen_code'));
        }
		
		if($menuSelected !='')
		{
			$select->where(array("order_menu" => $menuSelected));
		}
		
		$kitchen_details = $tblDispatched->fetchAll($select,$language);
		$kitchen_data = $kitchen_details;
	
 		//echo "<pre>"; print_r($kitchen_details); 
		
		if ( count($kitchen_details) > 0 ) {
		
		foreach ($menu as $dynamenu){
				
			$new_array_menus[$dynamenu]=array();
				
			/* foreach($kitchen_data as $product){
	
				if($product['order_menu']==$dynamenu){
	
					$new_array_menus[$dynamenu][$product['product_name']] = $product['total_order'];
				}
			} */
			
			foreach( $kitchen_details as $key => $value ){
				//      				echo $key."<br/>";
				//echo "data :".$kitchen_product->order_menu;
				
				if( strtolower($kitchen_details[$key]['order_menu']) == $dynamenu ){
// 					echo $kitchen_details[$key]['order_menu']."====".$dynamenu.'<br/>';
					//echo  strtolower($kitchen_details[$key][order_menu]);
					$new_array_menus[$dynamenu][$value['fk_product_code']][$kitchen_details[$key][product_name]] = (isset($new_array_menus[$dynamenu][$value['fk_product_code']][$kitchen_details[$key][product_name]])) ? $kitchen_details[$key][total_order] + $new_array_menus[$dynamenu][$value['fk_product_code']][$kitchen_details[$key][product_name]] : $kitchen_details[$key][total_order];

				}
			}
			
			}
		}
		
// 		foreach ($menu as $dynamenu){
// 			$view_Data['new_array_'.$dynamenu]=$new_array_menus[$dynamenu];
// 		}
		
		
// 		echo "<pre>"; print_r($new_array_menus); die;
		
		if($kitchen_data)
		{
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'order';
			$activity_log_data['action']= 'printnextdayorder';
			$activity_log_data['description']= "Print : Order printed.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
		
		$view_Data=array(
			'root_url' => $config['root_url'],
			'date_filter' => $dateFilter
		);	
	
		$view_Data['new_menu_array']=$new_array_menus;
		$view_Data['menu']=$menuSelected;
	
		$view = new ViewModel($view_Data);
		
		$view->setTerminal(true);
		return $view;
	}
	

	/**
	 * To view today's unbilled order &cancelled orer
	 *
	 * @return \Zend\View\Helper\ViewModel
	 */
	public function old_detailAction()
	{
        $sm = $this->getServiceLocator();	
	    $libOrder = QSOrder::getInstance($sm);

		$id = $this->params('id');
		$date = $this->params('date',null);
	
		if (!$id) {
			return $this->redirect()->toRoute('order', array('action' => 'add'));
		}
	
		$condition = "AND order_status='New'";
		$order = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
		$rerult = $order->toArray();
	
		$condition = null;
		$customer = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
		$rerult1 = $customer->toArray();
	
		$orderDetails = $libOrder->getOrderDetails($id,null,$date);
	
		$this->layout()->setVariables(array('page_title'=>"Order Information",'description'=>"Update Order",'breadcrumb'=>"Order Information"));
	
		//$order= $order->current();
		return array(
				'id' => $id,
				'page_price' => $config_variables['page_price'],
				'orders'=>$rerult,
				'orderDetails'=>$orderDetails,
				'customer' => $rerult1
		);
	}
	
	
	
	/**
	 * To view today's unbilled order &cancelled orer
	 *
	 * @return \Zend\View\Helper\ViewModel
	 */
	public function detailAction()
	{
        
		$holidays_list = "";
		$sm = $this->getServiceLocator();
		$storage_adapter = $sm->get("Write_Adapter");
		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		
		$settings = $libCommon->getSettings();
		
		$utility = Utility::getInstance();
		
		$id = $this->params('id');
		$view = $this->params('view');
	
		$date = $this->params('date',null);
	
		if (!$id) {
			return $this->redirect()->toRoute('order', array('action' => 'add'));
		}
	
		$getHolidays = $libCommon->fetchHolidaysList('holiday');
	
		$weekOff=$libCommon->fetchHolidaysList('weekoff');
		
		foreach ($getHolidays as $key=>$vals){
			$holidays_list .= "'".Date('Y/m/d',strtotime($vals['holiday_date']))."',";
		}
	
		$holidays=rtrim($holidays_list,",");
		
		$condition = "AND order_status IN ('New','Complete')";
		$order = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
           
		$rerult = $order->toArray();
		
		$order_days = (isset($rerult[0]['order_days']))?$rerult[0]['order_days']:'';
		$rerult[0]['order_dates'] = explode(",",$order_days);
		
		$temp_date = (isset($rerult[0]['order_days']))?explode(",",$rerult[0]['order_days']):'';

		$r_date=array();
		if(is_array($temp_date)){
			foreach ($temp_date as $key=>$val_date){
				
				$r_date[$key] = $utility->displayDate($val_date,$settings['DATE_FORMAT']);
				
			}
			
		
			$rerult[0]['order_days']=implode(",", $r_date);

		}else{
			$rerult[0]['order_days']='';
		}

		$lastdate=$libOrder->getLastDateofOrder_new($id);
		
		$lastdate =date('Y/m/d', strtotime('+1 day', strtotime($lastdate)));
	
		$condition = null;
		
		if($view=='preorder'){
	       $condition = " AND order_status IN ('New','Complete','Cancel') AND delivery_status !='Reordered'";
		}
	    
		$customer = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
        
		$rerult1 = $customer->toArray();
		
		$rerult1[0]['order_date']=$utility->displayDate($rerult1[0]['order_date'],$settings['DATE_FORMAT']);
		
		$excludeCan = true;
		
		if($view !='preorder'){
		    if($rerult1[0]['order_status']=='Cancel'){
		        $excludeCan = false;
		    }
		}
		
		$orderDetails = $libOrder->getOrderDetails($id,'reference',$date,$excludeCan);
   
		//////////////////////////////// //////////////////////
                
		$select = new QSelect();
	    	
		$select->where("order_status IN ('New','Complete') AND delivery_status IN ('Pending','Delivered')");
		$select->where("orders.order_no = '$id'"); 

		if(!empty($date)){

			$select->where("orders.order_date = '$date'");	
		}
		
		//echo $select->getSqlString();die;
		
	    $allOrders = $libOrder->orderfetchAll($select);
       
	    $arrAllOrders = $allOrders->toArray();
                           
        //dd($arrAllOrders);
		//echo "results<pre>"; print_r($arrAllOrders); exit();

		//////////////////////////////////////////////////////

		$arrMealsToUpdate = array();

		foreach($arrAllOrders as $oDetail){

			//if(!isset($arrMealsToUpdate[$oDetail['order_date']])){
				$arrMealsToUpdate[$oDetail['order_date']][] = $oDetail;	
			//}
			
		}

		//echo "<pre>"; print_r($arrMealsToUpdate); exit();
	
		$this->layout()->setVariables(array('page_title'=>"Order Information",'description'=>"Update Order",'breadcrumb'=>"Order Information"));
	
		//$order= $order->current();
		return array(
				'id' => $id,
				'page_price' => (isset($config_variables['page_price']))?$config_variables['page_price']:'',
				'orders'=>$rerult,
				'order_days'=>$order_days,
				'weekOff'=>$weekOff[0]['holiday_description'],
				'orderDetails'=>$orderDetails,
				'customer' => $rerult1,
				'holidays'=>$holidays,
				'lastDate'=>$lastdate,
				'view'  => $view,
				'date_format' => $settings['DATE_FORMAT'],
				'meals_to_update' => $arrMealsToUpdate,
                'pickup_enabled' => (array_key_exists('GLOBAL_DELIVERY_TYPE', $settings) && (strpos($settings['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false)) ? true : false

		);
	}
	
	
	/**
	 * To delete order of given order id
	 *
	 * @param int id
	 * @return route order
	 */
	public function deleteAction()
	{
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('order');
		}
		$this->getOrderTable()->deleteOrder($id);
		return $this->redirect()->toRoute('order');
	}
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
	/**
	 *Cancel My Order
	 *
	 * @param int id
	 * @param int c_id
	 * @return route order
	 */
	public function cancelorderAction(){

        $sm = $this->getServiceLocator();
		$storage_adapter = $sm->get("Write_Adapter");
		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		$id = $this->params()->fromRoute('id', 0);
        
        $customer_id = (int) $this->params()->fromRoute('c_id', 0);
		$order_menu = $this->params()->fromRoute('menu');
		$date = $this->params()->fromRoute('date',null);
        $recurring_status = $this->params()->fromRoute('recurring_status',null);
		
		$today = date("Y-m-d H:i:s");
		$request = $this->getRequest();
		
		//$date = $this->params('date',null);
 		$condition = "AND order_status='New'";
		$order = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
		$orderArr = $order->toArray();
   
		$kitchen = $orderArr[0]['fk_kitchen_code'];
		$keyCancel = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_TIME";
		$keyCancelBeforeDay = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_DAY";
		
		$cancelTime = $libOrder->getSetting($keyCancel)->value;
        
		$cancelBeforeDay = $libOrder->getSetting($keyCancelBeforeDay)->value;

        //$recurring_status = null;
		$cancelDays = array();
    
		if($date != null){
			array_push($cancelDays,$date);
            
		}else{
			
			if($request->isPost()){
				$data = $request->getPost();
                //dd($data);
                $id = $data['id'];
                $customer_id = $data['c_id'];
                $order_menu = $data['menu'];
                $kitchen = $data['fk_kitchen_code'];
				$cancelDays = $data['cancelDays'];
                //array_push($cancelDays,$data['date']);
			    $recurring_status = $data['recurring_status']; 
                
                $ordersd = $libOrder->getviewOrder($id,'reference',$cancelDays);
				$ordersd = $ordersd->toArray();
			}
            
			if(empty($cancelDays))
			{
				if($ordersd[0]['order_status']=='Cancel'){
					
					$message['error']="The selected order is already cancelled";
					$this->flashmessenger()->addMessage($message);
					return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'preorder'));
				}
				if(isset($ordersd[0]['order_days'])){
					$cancelDays = explode(",",$ordersd[0]['order_days']);
				}
			}
		}
	
		//check if TOTAL ORDERED == TOTAL PREPARED
        
		$res = $libOrder->check_ordered_prepared($order_menu,$cancelDays);
      
		$orderd_preapred = $res->toArray();
        
        /* code Commented for cancel order from todays order if order is prepared PradeepM 24jan18*/
		//if($orderd_preapred[0]['total_count']=="1"){
           
            if(!empty($data)) {

            }
            
			$result_msg = $libOrder->canceltodaysorder($customer_id,$id,$order_menu,$kitchen,$cancelDays,$recurring_status);
			
			if(is_array($result_msg))
			{
				$message['error'] = $result_msg['error'];
				$msg = $result_msg['error'];
				$status = "error";
			}else{
				
				$message['success'] = "Order ID : $id is cancelled";
				$msg = $message['success'];
				$status = "success";
				
				$getCustomer = $libCustomer->getCustomer($customer_id,'id');
				
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'order';
				$activity_log_data['action']= 'cancelorder';
				$activity_log_data['description']= "Order : '$id' canceled for $getCustomer->customer_name by Admin Manager.";
				
				//$activity_log_data['description']= "'$id' order cancelled by $full_name";
				$libCommon->saveActivityLog($activity_log_data);
				
				
				$message['success'] = "Order $id with Dates : ".implode(",", $cancelDays)." is cancelled ";
				$msg = $message['success'];
				$status = "success";
			}
			$this->flashmessenger()->addMessage($message);
          /* code Commented for cancel order from todays order if order is prepared PradeepM 24jan18*/
			/*}else{
				$message['error'] = "The selected order is already prepared so can not be cancelled";
				$msg = $message['error'];
				$status = "error";
				$this->flashmessenger()->addMessage($message);                
			}*/
			/*code ends here */  
            //send sms
            $mailer = new \Lib\Email\Email();
            $sm = $this->getServiceLocator();
            $mailer->setAdapter($sm);
            //get sms configuration
            $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
            //SET sms configuration to mailer
            $mailer->setSMSConfiguration($sms_config);
            
            $sms_common = $libCommon->getSmsConfig($setting);
            $mailer->setMobileNo($ordersd[0]['phone']);
            $mailer->setMerchantData($sms_common);

            $sms_array = array(
                        'order_no'	=> $id,
                        'website'	=> $sms_common['Website'],
                        
            );
            $message1 = $libCommon->getSMSTemplateMsg('order_cancellation',$sms_array);	

            if($message1){
                $mailer->setSMSMessage($message1);

                $sms_returndata = $mailer->sendmessage();
            }
            //end sms send
            
			SHOWMSG:
		
			if ( ! $request->isXmlHttpRequest()){
				
				if($date != null){
			
					return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'today'));
			
				}else{
			
					return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'preorder'));
				}
				
			}else{
				//return new JsonModel(array('success'=>true));
				return new JsonModel(array('status'=>$status,'msg'=>$msg));
					
			}
	}
        /**
        * This function books a shipment on third party delivery system.
        * @return json $result
        */
        public function manualDeliveryAction()
        {
            $request            = $this->getRequest();
           
            $id                 = $request->getPost('id');
            $third_party        = $request->getPost('thirdparty');
            
            $data               = $this->getOrderTable()->getOrder($id, true)->toArray();
            
            $setting_session    = new Container('setting');
            $setting            = $setting_session->setting;
            
            $sm                 = $this->getServiceLocator();
            
            $sm->get($third_party)->setConfig($setting);
            
            $result             = $sm->get($third_party)->book($data[0]);
        
            echo json_encode($result); exit();
        }
        
        /**
        * This function cancels a third party shipment.
        * @return json $result
        */
        public function cancelDeliveryAction()
        {
            
            $request            = $this->getRequest();
           
            $order_id           = $request->getPost('id');
            $third_party        = $request->getPost('thirdparty');
            
            $setting_session    = new Container('setting');
            $setting            = $setting_session->setting;
            
            $sm                 = $this->getServiceLocator();
            
            $sm->get($third_party)->setConfig($setting);
                 
            $result             = $sm->get($third_party)->cancel($order_id);

            echo json_encode($result); exit();
        }

        /**
        * This function updated status for recurring orders
        * @param varchar order no
        * @returns json object
        */
        public function cancelRecurringOrderAction() {

            $request = $this->getRequest();
            $order_id = $request->getPost('orderid');

            try{
                $updateRecurringTable = $this->getOrderTable()->updateRecurringOrder($order_id);
            }
            catch(\Exception $e){
                $message = $e->getMessage();
                return new JsonModel(array("status"=>"error","msg"=>$message));
            }

            return new JsonModel(array('status'=>'success','msg'=>'Recurring disabled successfully.'));
        }
        
        /**
        * This function returns the status of a third party shipment.
        * @return json $status
        */
        public function getDeliveryStatusAction()
        {
            $request            = $this->getRequest();
           
            $tp_id              = $request->getPost('id');
            $order_id           = $request->getPost('order_id');
            $third_party        = $request->getPost('thirdparty');
            
            $data               = $this->getOrderTable()->getOrder($order_id, true)->toArray();
            
            $setting_session    = new Container('setting');
            $setting            = $setting_session->setting;
            
            $sm                 = $this->getServiceLocator();
            
            $sm->get($third_party)->setConfig($setting);
                 
            $status             = $sm->get($third_party)->getStatus($tp_id, $data[0]);

            echo json_encode($status); exit();
        }
       
        
	/**
	 * To Reject the undelivered Order
	 * This function used to update status of undelivered order
	 *
	 * @param int id
	 *  @param int c_id
	 *  @return route order
	 */
	public function rejectundeliveredAction()
	{
		$id = $this->params()->fromRoute('id', 0);
		$customer_id = (int) $this->params()->fromRoute('c_id', 0);
		$order_status = $this->params()->fromRoute('order_status', 0);
		$date = $this->params()->fromRoute('date', null);
		$sm = $this->getServiceLocator();
		$libOrder = QSOrder::getInstance($sm);
		
		$libOrder->rejectundeliveredtodaysorder($customer_id,$id,$order_status,$date);

		$message['success']="Order ID : $id is marked as $order_status";
		$this->flashmessenger()->addMessage($message);
		return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'today',));
	}
    
    public function getitemname($item_id)
    {
    	$item_name=$this->getOrderTable()->getItemName($item_id);
    	return $item_name;
    }
    
    public function printnextdaylabelAction(){
		
        if (! $this->authservice){
	        $this->authservice = $this->getServiceLocator()->get('AuthService');
	    }
	
	    $iden = $this->authservice->getIdentity();
        $sm = $this->getServiceLocator();
        $libCommon = QSCommon::getInstance($sm);
    	$setting_session = new Container('setting');
    	$setting = $setting_session->setting;
    	$date_format = $setting['DATE_FORMAT'];
    	
    	$menus = $setting_session->setting['MENU_TYPE'];
    	
    	$delivery_codes = $this->getLocationTable()->fetchAll();
    	$location_id=$this->params()->fromRoute('location');
    	$date = $this->params()->fromQuery('date');
    	
    	$dateFilter = date('Y-m-d',strtotime($date));
    	
    	$location_ids = array();
    	//echo "location name".$location_id; exit();
    	if($location_id=='all'){
	    	foreach ($delivery_codes as $code){
	    		$location_ids[] = $code['pk_location_code'];
	    	}
    	}else{
    		array_push($location_ids,$location_id);
    	}
    
    	$menu = $this->params()->fromRoute("menu","");
    	
        $delivery_type = $this->params()->fromRoute('delivery_type', 'all');
        
    	$kitchen_screen = $this->params()->fromRoute('kitchen');
        
    	$language_array = $this->params()->fromRoute('language');    	
    	
    	$sms_common = $libCommon->getSmsConfig($setting);
        
    	$this->getOrderTable()->setServiceLocator($sm);
    	
    	$select = new QSelect();
    	
    	//echo $dateFilter;die;
    	
    	if($menu !=""){
    		$select->where(array("order_menu" => $menu));
    	}
        
        if($delivery_type != 'all' && $delivery_type != ''){
            $select->where(array("delivery_type" => $delivery_type));
        }
    	
    	$printLableOrderBy = $setting['PRINT_LABEL_ORDER_BY'];
    	
    	if($kitchen_screen!="all"){
    		$print_data = $this->getOrderTable()->getTodaysorder($location_ids,$menu,$kitchen_screen,$dateFilter,$printLableOrderBy);
    	}else {
            
    		$print_data = $this->getOrderTable()->getTodaysorder($location_ids,$menu,array_column($iden->kitchens, 'fk_kitchen_code'),$dateFilter,$printLableOrderBy);
    	}

     
    	if(count($print_data['printData'])==0)
    	{
    		$message['error']="No next days orders";
			$this->flashmessenger()->addMessage($message);
    		return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'preorder',));
    	}
    	$barcodeArr = $print_data['barcodeArr'];
        $setting_session = new Container('setting');
    	$setting = $setting_session->setting;
    	
    	$print_location = $setting['PRINT_LOCATION'];
    
    	$orders = array_keys($print_data['printData']);

    	if($language_array!='all'){

    		$multilingualCodeSupport = $this->getServiceLocator()->get('QuickServe\Model\MultilingualCodeSupportTable');
	    	foreach($print_data['printData'] as $key=>$val){
	    		$location_language = $language_array;
	    		$multilingual_array = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $val[0]['product_code'], 'location_id' => $val[0]['location_code'], 'language_code' => $location_language));

                if( is_array($multilingual_array) && !empty($multilingual_array) ) {
                                
	    			$print_data['printData'][$key][0]['product_short_code'] = ( isset($multilingual_array[0]['context_code']) && !empty($multilingual_array[0]['context_code']) ) ? $multilingual_array[0]['context_code'] : null;
					$print_data['printData'][$key][0]['name'] = ( isset($multilingual_array[0]['context_name']) && !empty($multilingual_array[0]['context_name']) ) ? $multilingual_array[0]['context_name'] : $print_data['printData'][$key][0]['name'];
					if( strtolower($print_data['printData'][$key][0]['dabbawala_code_type']) == 'text') {
	    				$print_data['printData'][$key][0]['dabbawala_code'] = ( isset($multilingual_array[0]['location_code']) && !empty($multilingual_array[0]['location_code']) ) ? $multilingual_array[0]['location_code'] : $print_data['printData'][$key][0]['dabbawala_code'];
	    			}
	    		}
	    		
	    		$order_items_array = $this->getOrderTable()->getOrdered_items($val[0]['order_no'],$val[0]['order_date'],$val[0]['product_code']);
	    		
	    		$item_arranged = array();
	    		
	    		if( is_array($order_items_array) && !empty($order_items_array) ) {
					$item_array = array();
					
					foreach( $order_items_array as $meal_id=>$items) {
						
						$strProductDesc = "";
						
						$cnt = 0;
						
	    				foreach($items as $product_id=>$item_value){
	    					
	    					if($meal_id != $product_id && $cnt==0){
	    						
	    						$arrMultilingualMeal = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $meal_id, 'language_code' => $location_language));
	    						
	    						if( is_array($arrMultilingualMeal) && !empty($arrMultilingualMeal) ) {
	    							$meal_name = ( isset($arrMultilingualMeal[0]['context_name']) && !empty($arrMultilingualMeal[0]['context_name']) ) ? $arrMultilingualMeal[0]['context_name'] : $item_value['meal_name'];
	    							if( !empty($meal_name)) {
	    								//array_push($item_array, $item_name.'('.$item_value['quantity'].')');
	    								$strProductDesc .= $meal_name.' ('.$item_value['meal_quantity'].') [ ';
	    							}
	    						}else{
	    								
	    							$strProductDesc .= $item_value['meal_name'].' ('.$item_value['meal_quantity'].')';
	    						}
	    						
	    						if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
	    							$strProductDesc .= ' [';
	    						}else{
	    							$strProductDesc .= ',';
	    						}
	    					}
	    					
	    					if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
	    						
			    				if( !empty($product_id) && !empty($item_value['quantity']) ) {
			    					
				    				$multilingual_array = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $product_id, 'language_code' => $location_language));
				    				if( is_array($multilingual_array) && !empty($multilingual_array) ) {
				    					$item_name = ( isset($multilingual_array[0]['context_name']) && !empty($multilingual_array[0]['context_name']) ) ? $multilingual_array[0]['context_name'] : $item_value['product_name'];
				    					if( !empty($item_name)) {
				    						//array_push($item_array, $item_name.'('.$item_value['quantity'].')');
				    						
				    						$strProductDesc .= $item_name.' ('.$item_value['quantity'].'),';
				    					}
				    				}
				    				else {
	
				    					$strProductDesc .= $item_value['product_name'].' ('.$item_value['quantity'].'),';
				    				}
			    				}
	    					}
		    				
		    				$cnt++;
		    				
		    				if(count($items) == $cnt){
		    					
		    					$strProductDesc  = rtrim($strProductDesc,",");
		    					
		    					if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
		    						$strProductDesc .= " ] ";
		    					}
		    				
		    				}
		    				 
	    				}
	    				
	    			}//end of foreach
					
					
					//$order_items_string = implode(',', $item_array);
					
	    			$order_items_string = $strProductDesc;
	    			
					if( !empty($order_items_string) ) {
						$print_data['printData'][$val['0']['order_no']][0]['product_description'] = $order_items_string;
					}
				}
	    		
				array_push($orders,$val['0']['order_no']);

	    	}
    	}
		
    	if(strtolower($setting['PRINT_LABEL_SHOW_BARCODE'])=='yes'){
    		
	   		$orderBarcodeArr = array_combine($orders,$barcodeArr);
            
	   		$saveRes = $this->getOrderTable()->saveOrderBarcode($orderBarcodeArr ,$dateFilter ,$print_data);
    	}
    	
    	//PDF GENERATE
    	$path = realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))));

        $pdfDir = $path . "/public/data/order_dispatch/";
		
    	if (!is_dir($pdfDir)) {
    		mkdir($pdfDir);
    	}
    	$today = date('d_m_Y');
    	$outfile = $pdfDir.$loc_name."_".$today.".pdf";
    	$filename = $loc_name."_".$today.".pdf";
    	
    	$tblSetting = $sm->get("QuickServe\Model\SettingTable");
    	$settingLocation = $tblSetting->getSetting("PRINT_LOCATION");
    	
    	$lable_setting = $setting['PRINT_LABLE_TEMPLATE'];
   	
    	$details = (array(
    		'print_data' => $print_data['printData'],
    		'print_location' => $settingLocation->value,
    		'comp_data' =>$sms_common,
    		'date_format' => $date_format,
    	));
    	
    	$layoutviewModel = $this->layout();
    	$acl = $layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	    	
    	$count = count($print_data['printData']);
    	$full_name=$loguser->first_name." ".$loguser->last_name;
    	$activity_log_data=array();
    	$activity_log_data['context_ref_id']=$loguser->pk_user_code;
    	$activity_log_data['context_name']= $full_name;
    	$activity_log_data['context_type']= 'user';
    	$activity_log_data['controller']= 'order';
    	$activity_log_data['action']= 'printnextdaylabel';
    	$activity_log_data['description']= "Print : label printed for $count in next day orders.";
    	 
    	//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
    	$libCommon->saveActivityLog($activity_log_data);
    	
    	$libPrintLabel = \Lib\QuickServe\PrintLabel::getInstance($setting,$sm);
    	//echo "<pre>";print_r($print_data);die;
    	$config = $this->getServiceLocator()->get("config");
    	 
    	// Set SMS config and application config data.
    	$libPrintLabel->setConfig($config);
    	//$libPrintLabel->setShowBarcode('yes');
    	$libPrintLabel->setData($print_data['printData']);
		
		error_reporting(0);
		ini_set("display_errors","Off");

    	try{
			$libPrintLabel->renderLabels();die;
			/*$pdf = new PdfModel();
			$pdf->setOption('filename', 'print-labels'); // Triggers PDF download, automatically appends ".pdf"
        	$pdf->setOption('paperSize', 'A4'); // Defaults to "8x11"
        	$pdf->setOption('paperOrientation', 'portrait'); // Defaults to "portrait"
			
			$htmlPrintLabels = $libPrintLabel->generatePdf();
			

			//echo htmlentities($htmlPrintLabels);die;
			//$pdf = new ViewModel();	
			// To set view variables
			$pdf->setVariables(array(
				'htmlPrintLabels' => $htmlPrintLabels
			));

			$pdf->setTemplate('admin/order/print-label-nextday.phtml');

			return $pdf;*/

    	}catch(\Exception $e){
    	
    		echo $e->getMessage();
    	}

		die;
    }
    
    /**
     *shift My Order
     *
     * @param int id
     * @param int c_id
     * @return route order
     */
    
    public function shiftcancelorderAction(){

    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
        
    	$sm = $this->getServiceLocator();
    	//$storage_adapter = $sm->get("Write_Adapter");
    	$libCustomer = QSCustomer::getInstance($sm);
    	$libCommon = QSCommon::getInstance($sm);
    	$libOrder = QSOrder::getInstance($sm);
    	$libCatalogue = QSCatalogue::getInstance($sm);
    	
    	$id = $this->params()->fromRoute('id', 0);
    
    	$customer_id = (int) $this->params()->fromRoute('c_id', 0);
    	$order_menu = $this->params()->fromRoute('menu');
    	$date = $this->params()->fromRoute('date',null);
    
    	$today = date("Y-m-d H:i:s");
    	$request = $this->getRequest();
    
    	$setting_session = new Container('setting');
    	$setting = $setting_session->setting;
    	
    	$setting = $libCommon->getSettings();
    	
    	$getHolidays = $libCommon->fetchHolidaysList('holiday');
    	$holidays_list=array();
    	$weekOff = $libCommon->fetchHolidaysList('weekoff');
    
    	foreach ($getHolidays as $key=>$vals){
    
    		$holidays_list[$key] .= strval(Date('Y/m/d',strtotime($vals['holiday_date'])));
    	}
    	
    	
    	////////////////////////////////////////////////////////////////////
    	 
    	$date = $this->params('date',null);
    	$condition = "AND order_status='New'";
       
        $order = $libOrder->getviewOrder($id,'referencegroup',$date,$condition);
    	$orderArr = $order->toArray();
    	
    	$kitchen = $orderArr[0]['fk_kitchen_code'];
    	
    	
    	///////////// get week offs in string format (e.g 1,2,3 etc...) /////////////// 
    	$strWeekOffs = $weekOff[0]['holiday_description'];
    	
    	$arr = [0,1,2,3,4,5,6];
    	
    	if($orderArr[0]['days_preference'] !="" ){
    		$arrWeekOffs = array_diff($arr,explode(',',$orderArr[0]['days_preference']));
    		$strWeekOffs = implode(",",$arrWeekOffs);
    	}else{
    		 
    		$kitchenwise_menu_weekoff_setting = $setting['K'.$orderArr[0]['fk_kitchen_code'].'_'.  strtoupper($orderArr[0]['order_menu']) .'_WEEKOFFS'];
    		 
    		if($kitchenwise_menu_weekoff_setting != null && $kitchenwise_menu_weekoff_setting != ""){ // change by shil to allow 0 as true value.
    			$strWeekOffs =  $kitchenwise_menu_weekoff_setting;
    		}
    	}
    	
    	//echo "<pre>"; print_r($order->toArray()); exit();
    	
    	$keyCancel = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_TIME";
    	$keyCancelBeforeDay = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_DAY";
    		
    	$cancelTime = $libOrder->getSetting($keyCancel)->value;
    	$cancelBeforeDay = $libOrder->getSetting($keyCancelBeforeDay)->value;
    	$cancelDays = array();
        
    	if($date != null){
    		array_push($cancelDays,$date);
    	}else{
    
    		if($request->isPost()){
    			$data = $request->getPost();
    			$cancelDays = $data['cancelDays'];
    			if(isset($data['date_selected'])){
    				$lastdate_selected = $data['date_selected'];
    			}
    		}
          
    		//echo "<pre>"; print_r($cancelDays); exit();
    		if(empty($cancelDays))
    		{
              
    			$ordersd = $libOrder->getviewOrder($id,'reference',$cancelDays);
               
    			$ordersd = $ordersd->toArray();
    
    			$cancelDays = array();
    			foreach($ordersd as $keyorder=>$keyvalue)
    			{
    				if($keyvalue['order_status']!='Cancel'){
    					
    					$today = date('Y-m-d');
    					
    					if($keyvalue['order_date']!=$today)
    					{
    						array_push($cancelDays,$keyvalue['order_date']);
    					}
    				}
    			}
    		}
    
    	}
    	
    	$cancelDays = array_unique($cancelDays);
    	
    	$tempOrders = $libOrder->getNewOrders($customer_id,$id,$order_menu,$cancelDays);
       
    	//check if TOTAL ORDERED == TOTAL PREPARED
    	$res = $libOrder->check_ordered_prepared($order_menu,$cancelDays);
    	$orderd_preapred = $res->toArray();
    	//echo "res<pre>"; print_r($orderd_preapred); exit();

    	//dd($data);die;

    	if($orderd_preapred[0]['total_count']=="1"){
    
    		$flag_date = false;
    			
    		if(isset($data['date_selected'])){
    			$lastdate = $lastdate_selected;
    			$flag_date = true;
    		}else{
    			$lastdate = $libOrder->getLastDateofOrder($customer_id,$id,$order_menu);
                
    		}
    		//echo "checking for settings";
    		
    		$availableDates = array();
    		
    		// Check if calendar based or non calendar based.
    		if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){
    			
    			$today = date("Y-m-d");
    			
    			$arrWeekOffs = explode(",",$strWeekOffs);
    			
    			$filterAvailableDates = array();
    			$availableDatesProducts = array();
    			
    			$pCnt = 0;
    			
    			foreach ($tempOrders as $rowData){
    				
    				//echo "<pre>";print_r($rowData);echo "</pre>";
    				
    				if($rowData['product_type']=='Meal'){
    			
    					$pCnt++;
    					
    					$availableDatesProducts[$rowData['product_code']] = $libCatalogue->getCalendarProducts($rowData['product_code'],1,true,$order_menu,$kitchen);
	    				
	    				$availableDates = array_keys($availableDatesProducts[$rowData['product_code']]);
	    				
	    				//echo "<pre>";print_r($availableDates);echo "</pre>";
	    				
	    				if(empty($availableDates)){
	    				
	    					return new JsonModel(array('status'=>'error','msg'=>"Menu is not available yet for {$rowData["product_name"]} ."));
	    				}
	    				
	    				$aCnt = 0;
	    				// If menu prepared then check for how many days it is prepared.
	    				foreach($availableDates as $aDate){
	    					
	    					if(isset($data['date_selected'])){
	    						
		    					if(!in_array(date('w', strtotime($aDate)), $arrWeekOffs) && !in_array($aDate, $holidays_list) 
		    					&& $aDate == $lastdate && !in_array($aDate,$cancelDays) ){
		    						array_push($filterAvailableDates,$aDate);
		    						$aCnt++;
		    					}
		    					
	    					}else{
	    						if(!in_array(date('w', strtotime($aDate)), $arrWeekOffs) && !in_array($aDate, $holidays_list)
	    								&& $aDate > $lastdate && !in_array($aDate,$cancelDays) ){
	    							array_push($filterAvailableDates,$aDate);
	    							$aCnt++;
	    						}
	    					}
	    				
	    				}
	    				
	    				if($aCnt < count($cancelDays)){
	    					
	    					return new JsonModel(array('status'=>'error','msg'=>"{$rowData["product_name"]} menu is not available for ".count($cancelDays)." day(s)"));
	    				}
	    			
    				}
	    			
    			}
    			
    			// If order contains more than one meals then order should shift to common available dates.
    			if($pCnt > 1){ 
    				
	    			// Find out common dates amongs meals available dates , these dates should be more than or equal to cancel days...
	    			if(!empty($filterAvailableDates)){
	    				
	    				$withoutDuplicates = array_unique($filterAvailableDates);
	    				
	    				$duplicates = array_diff_assoc($filterAvailableDates, $withoutDuplicates);
	    				
	    				if(count($duplicates) < count($cancelDays)){
	    					
	    					return new JsonModel(array('status'=>'error','msg'=>" Menu is not available for ".count($cancelDays)." day(s)"));
	    				}
	    				
	    				$filterAvailableDates = $duplicates;
	    				
	    			}else{
	    				
	    				return new JsonModel(array('status'=>'error','msg'=>"No dates available"));
	    				
	    			}
    			
    			}
    			
    		}
    		
    		$newDates = $libOrder->getNewDates($lastdate,count($cancelDays),$holidays_list,$flag_date,$strWeekOffs,$filterAvailableDates);
    		
    		$newD = array();
    		
    		foreach ($cancelDays as $key=>$vals){
    			$newDates[strval(date('Y-m-d',strtotime($vals)))] = date('Y-m-d',strtotime($newDates[$key]));
    			$newD[$key] = strval(date('Y-m-d',strtotime($newDates[$key])));
    			unset($newDates[$key]);
    		}
    
    		$existingOrderDates = array();
    		
    		foreach ($tempOrders as $key=>$vals){
    			
    			foreach ($newDates as $keys=>$val){
    
    				if(strval($vals['order_date'])==$keys){
    					
    					$existingOrderDates[$newDates[$keys]] = $tempOrders[$key]['order_date'];
    					
    					$tempOrders[$key]['order_date']=$newDates[$keys];
    				}
    			}
    		}
    		
    		if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){
    			
    			$tempOrdersDetails = array();
    		
	    		foreach($tempOrders as $oProduct){
	    		
		    		foreach ($existingOrderDates as $key=>$vals){
		    			
		    			foreach ($availableDatesProducts[$oProduct['product_code']][$key] as $aProduct){
		    			
			    			$tmpDetails = array();
			    			$tmpDetails['ref_order_no'] = $id;
			    			$tmpDetails['meal_code'] = $aProduct->fk_product_code;
			    			$tmpDetails['product_code'] = $aProduct->product_code;
			    			$tmpDetails['product_name'] = $aProduct->product_name;
			    			$tmpDetails['quantity'] = $oProduct['quantity'] * $aProduct->product_qty;
			    			$tmpDetails['product_type'] = $oProduct['product_type'];
			    			$tmpDetails['order_date'] = $key;
			    			
			    			array_push($tempOrdersDetails,$tmpDetails);
		    			
		    			}
		    			
		    		}
	    		
	    		}
    		
    		}else{
    		
	    		$tempOrdersDetails = $libOrder->getNewOrderDetails($id,$cancelDays);
	    		foreach ($tempOrdersDetails as $key=>$vals){
	    			 
	    			foreach ($newDates as $keys=>$val){
	    		
	    				if(strval($vals['order_date'])==$keys){
	    		
	    					$tempOrdersDetails[$key]['order_date'] = $newDates[$keys];
	    		
	    				}
	    			}
	    		}
    		
    		}
    		
    		//echo "<pre>";print_r($tempOrders);
    		//echo "<pre>";print_r($tempOrdersDetails);die;
    		
    		////////////////// tax added by shilbhushan on 22nd Apr 16. /////////////////////////
    		$arrBills = $libOrder->getOrderTable()->getOrderBillNos(array($id),$existingOrderDates);
    		$arrTaxDetails = array();
    		
    		$arrTaxDetails['new_order_dates'] = $newDates;
    		$arrTaxDetails['new_taxes'] = array();
    		
    		foreach($arrBills as $okey=>$obill){
    			$tempTaxDetails = $libOrder->getOrderTable()->getOrderTaxDetails($id,array($obill));
    			$tempTaxDetails = $tempTaxDetails->toArray();
    			
    			foreach($tempTaxDetails as $txDetail){
    				
    				$date = $newDates[$txDetail['order_date']];
    				
    				if(!isset($arrTaxDetails['new_taxes'][$date])){
    					
    					$arrTaxDetails['new_taxes'][$date] = array();
    					$arrTaxDetails['new_taxes'][$date][] = $txDetail;
    				}else{
    					$arrTaxDetails['new_taxes'][$date][] = $txDetail;
    				}
    			}
    			
    			//print_r($tempTaxDetails->toArray());die;
    		}
    		
    		
    		///////////////////////// End /////////////////////////////////////
    		
	         $result_msg3 = $libOrder->performKitchenOpertation($tempOrders,$customer_id,$id,$order_menu,$kitchen,$cancelDays,$newDates,$newD);
	         //die;
	         
	    	 if(is_array($result_msg3))
	    	 {
    			$message['error'] = $result_msg3['error'];
    			$msg = $result_msg3['error'];
    			$status = "error";
    
    			return new JsonModel(array('status'=>$status,'msg'=>$msg));
	    	}

	    		// work from here..
	    		
	    		$result_msg = $libOrder->canceltodaysorder($customer_id,$id,$order_menu,$kitchen,$cancelDays);
	    		
	    		$res = $libOrder->changeOrderStatus($customer_id,$id,$order_menu,$cancelDays,'Reordered');
	    		
                $result_msg2 = $libOrder->AutoPlaceOrders($tempOrders,$tempOrdersDetails,$arrTaxDetails);
				
	    		if(is_array($result_msg)){
					
	    			$message['error'] = $result_msg['error'];
	    			$msg = $result_msg['error'];
	    			$status = "error";
	    			
	    		}else{
						
	    			$getCustomer = $libCustomer->getCustomer($customer_id,'id');
	    				
	    			$full_name=$loguser->first_name." ".$loguser->last_name;
	    			$activity_log_data=array();
	    			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
	    			$activity_log_data['context_name']= $full_name;
	    			$activity_log_data['context_type']= 'user';
	    			$activity_log_data['controller']= 'order';
	    			$activity_log_data['action']= 'cancelorder';
	    			$activity_log_data['description']= "Order : '$id'  with Dates : ".implode(",", $cancelDays)." modified for $getCustomer->customer_name by Admin Manager.";
	    				
	    			//$activity_log_data['description']= "'$id' order cancelled by $full_name";
	    			$libCommon->saveActivityLog($activity_log_data);
	    				
	    			$message['success'] = "Order $id with Dates : ".implode(",", $cancelDays)." is cancelled And New Order has been placed";
					$msg = $message['success'];
	    			$status = "success";
				}
				
    			$this->flashmessenger()->addMessage($message);
    							
        }
        else{
    			
    			//SEND ERROR MESSAGE, CAN NOT CANCEL ORDERS
    
    			//$message['error']="Can not cancel Order ID : $id";
    			
    			$message['error'] = "The selected order is already prepared so can not be cancelled";
				$msg = $message['error'];
    			$status = "error";
    			$this->flashmessenger()->addMessage($message);
    		}
    
    		SHOWMSG:
    		
    		if ( ! $request->isXmlHttpRequest()){
    			if($date != null){
    				return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'today'));
    
    			}else{
    
    				return $this->redirect()->toRoute('order',array('action'=>'index','view' => 'preorder'));
				}
    		}else{
    			return new JsonModel(array('status'=>$status,'msg'=>$msg));
    					
    		}
    
    }
    
    public function dispatchOrderAction(){
    	
        $request = $this->getRequest();

        $orderDate = trim($request->getPost('d'));
        $menu = trim($request->getPost('menu'));
        $debug = 0;
        $kitchen = trim($request->getPost('k'));

        if(!empty($this->params()->fromQuery())){
        	$orderDate = trim($this->params()->fromQuery('d',''));
        	$menu = trim($this->params()->fromQuery('menu',''));
        	$debug = trim($this->params()->fromQuery('debug',0));
        	$kitchen = trim($this->params()->fromQuery('k',null));
		}
    
       	try{
    	
	    	if(empty($orderDate)){
	    		
	    		throw new \Exception("Order date not specified.");
	    	}
	    	
	    	$today = date("Y-m-d");
	    	
	    	if($orderDate > $today){
	    		
	    		throw new \Exception("Dispatch date should not be greater than today.");
	    	}
	    	
	    	if(empty($menu)){
	    	
	    		throw new \Exception("menu not specified.");
	    	}
	    	
	    	if($debug){
	    		
	    		echo "dispatching orders of date <b>".$orderDate."</b> and menu <b>".$menu."</b><br /><br />";
	    	}
	    	
	    	$sm = $this->getServiceLocator();
	    	$libOrder = QSOrder::getInstance($sm);
	    	
	    	$select = new QSelect();
	    	
	    	$select->where(array("orders.order_date"=>$orderDate,"order_menu"=>$menu,"order_status"=>"New","delivery_status"=>"Pending"));
	    	
	    	if(!empty($kitchen)){
	    		$select->where("fk_kitchen_code = '$kitchen'");
	    	}
	    	
	    	if($debug){
	    		
	    		echo $select->getSqlString()."<br /><br />";
	    	}
	    	
	    	$orders = $libOrder->orderfetchAll($select);
	    	
	    	$count = $orders->count();
	    	
	    	if($debug){
	    		echo "Found orders == ".$count."<br /><br />";
	    	}
	    	
	    	if($count > 0){
	    		
		    	$mainlocarray = array();
		    	$orderNos = array();
		    	
		    	foreach ($orders as $order)
		    	{
		    		array_push($orderNos,$order->order_no);
		    		
		    		$orderDetails = $libOrder->getOrderDetails($order->order_no,null,$order->order_date);
		    		
		    		//echo "<pre>";print_r($orderDetails);die;
		    		
		    		foreach($orderDetails as $detail){
		    		
			    		if(!isset($mainlocarray[$detail['fk_kitchen_code']][$detail['menu']][$detail['product_code']])){
			    			$mainlocarray[$detail['fk_kitchen_code']][$detail['menu']][$detail['product_code']] = $detail['quantity'];
			    			 
			    		}else{
			    			$mainlocarray[$detail['fk_kitchen_code']][$detail['menu']][$detail['product_code']] += $detail['quantity'];
			    		}
		    		
		    		}
		    	}
		    	
		    	if($debug){
		    		echo "Kitchen data to be updated ";
		    		echo "<pre>";print_r($mainlocarray);
		    	}
		    	
		    	$libOrder->dispatchedOrder($orderNos,$orderDate);
		    	$libOrder->updateKitchen($mainlocarray,$orderDate);
	    	
	    	}
	    	
    	
    	}catch(\Exception $e){
    		
    		$message = $e->getMessage();
    		return new JsonModel(array("status"=>"error","msg"=>$message));
    	}
    	
    	if($debug){
    		die;
    	}else{
    		return new JsonModel(array("status"=>"success"));
    	}


    	
    }
    
    
    public function deliverOrderAction(){
    	
        $request = $this->getRequest();

        $orderDate = trim($request->getPost('d'));
        $menu = trim($request->getPost('menu'));
        $debug = 0;
        $kitchen = trim($request->getPost('k'));

        $dispatch = $this->dispatchOrderAction();

        if(!empty($this->params()->fromQuery())){
            $orderDate = trim($this->params()->fromQuery('d',''));
            $menu = trim($this->params()->fromQuery('menu',''));
            $debug = trim($this->params()->fromQuery('debug',0));
            $kitchen = trim($this->params()->fromQuery('k',null));
        }
    	
    	$utility = Utility::getInstance();
    	
    	try{
    		 
    		if(empty($orderDate)){
    			 
    			throw new \Exception("Order date not specified.");
    		}
    	
    		$today = date("Y-m-d");
    	
    		if($orderDate > $today){
    			 
    			throw new \Exception("Order date to be delivered should not greater than today.");
    		}
    	
    		if(empty($menu)){
    	
    			throw new \Exception("menu not specified.");
    		}
    	
    		if($debug){
    			 
    			echo "delivering orders of date ".$orderDate." and menu ".$menu."<br /><br />";
    		}
    	
    		$sm = $this->getServiceLocator();
    		//$storage_adapter = $sm->get("Write_Adapter");
    		
    		//$libConfig= QSConfig::getInstance($sm);
    		$libOrder = QSOrder::getInstance($sm);
    		$libWallet = QSWallet::getInstance($sm);
    		
    		//$settings = $libConfig->getSettings();
    		
    		$select = new QSelect();
    				
    		$select->where(array(
    			"DATE(orders.order_date) = '".$orderDate."'",
    			'delivery_status'=>'Dispatched',
    			'order_status'=>'New',
    			'order_menu'=>$menu
    		));
    		
    		if(!empty($kitchen)){
    			$select->where("fk_kitchen_code = '$kitchen'");
    		}
    		
    		if($debug){
    			
    			echo $select->getSqlString()."<br /><br />";
    		}
    				
    		$orders = $libOrder->orderfetchAll($select);
    				
    		$orders = $orders->toArray();
    		
    		if($debug){
    			 
    			echo "Found Orders ".count($orders)."<br /><br />";
    		}
    		
    		//error_reporting(E_ALL);
    		//ini_set("display_errors","On");
    		
    		if(!empty($orders) && count($orders) > 0){
    		
    			foreach($orders as $order){
    				
    				$sql = new QSql($sm);
    				$update = $sql->update('orders'); // @return ZendDbSqlUpdate
    				$data = array(
    					'delivery_status' => "Delivered",
    					'order_status'	=> 'Complete',
    				);
    				
    				$update->set($data);
    				$update->where(array('order_no'=>$order['order_no'],'order_date'=>$orderDate));
					
					$sql->execQuery($update);
                    
    				if($debug){
    					
    					echo $order['customer_name']." == ".$order['order_no']."<br />";
    				}
    				
    				if($order['amount_paid']==1){
    					
    					$amount = $order['net_amount'] - $order['delivery_charges'];
                        $meal_amount = number_format($amount,2);
    					
    					$data = array(
    							'amount' =>	$meal_amount,
    							'date' => $today,
    							'id' => $order['customer_code'],
    							'description' => $utility->getLocalCurrency($meal_amount).' deducted against Bill No. '.$order['pk_order_no'].'of Order No. '.$order['order_no'],
    					);
    					
    					$libWallet->saveWalletTransaction($data,"debit",'admin');
    					
    					
    					
    					$delivery_charge = number_format($order['delivery_charges'],2);
    					$data1 = array(
    							'amount' =>	$order['delivery_charges'],
    							'date' => $today,
    							'id' => $order['customer_code'],
    							'description' => $utility->getLocalCurrency($delivery_charge).' delivery charges deducted against Bill No. '.$order['pk_order_no'].'of Order No. '.$order['order_no'],
    					);
    					
    					$libWallet->saveWalletTransaction($data1,"debit",'admin');
    					/* 
    					
    					
    					$data['amount'] = $order['net_amount'];
    					$data['date'] = $today;
    					$data['id'] =  $order['customer_code'];
    					$net_amount = number_format($order['net_amount'],2);
    					$data['description'] = 'Rs. '.$net_amount.' deducted against Order No. '.$order['order_no'];
    		    		$libWallet->saveWalletTransaction($data,"debit",'admin'); */
    		    		
    					if($debug){
    							
    						echo " Amount Deducted =  ".$data['amount']."<br />";
    					}
    					
    				}
    				
    				if($debug){
    					
    					echo "<br /><br />";
    				}

    			}
                
                $layoutviewModel = $this->layout();
                $loguser = $layoutviewModel->loggedUser;

                $libCommon = QScommon::getInstance($sm);

                $full_name=$loguser->first_name." ".$loguser->last_name;
                $activity_log_data=array();
                $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                $activity_log_data['context_name']= $full_name;
                $activity_log_data['context_type']= 'user';
                $activity_log_data['controller']= 'Past Delivery';
                $activity_log_data['action']= 'add';
                $activity_log_data['description']= "$menu orders has been delivered for $orderDate";                
                $libCommon->saveActivityLog($activity_log_data);
    		
    		}
    	
    		 
    	}catch(\Exception $e){
    	
    		$message = $e->getMessage();
    		return new JsonModel(array("status"=>"error","msg"=>$message));
    	}
    	 
    	if($debug){
    		die;
    	}else{
    		return new JsonModel(array("status"=>"success"));
    	}
    	 
    	
    }

    public function ajxUpdateQtyAction(){

    	//error_reporting(E_ALL);
    	//ini_set("display_errors", "On");

    	$request = $this->getRequest();

    	$sm = $this->getServiceLocator();
    	$libOrder = QSOrder::getInstance($sm);	

		try{

			if($request->isPost()){

	    		$params = $request->getPost();
	    		$mode = "";

	    		//\Lib\Utility::pr($params);

	    		// fetch orders of specified date and order no.

	    		$libOrder->updateOrderQuantity($params['meal_order_no'],$params['meal_order_date'],$params['meal_new'],$params['meal_new_qty']);

	    		
    		}

		}catch(\Exception $e){

				//throw new \Exception($e->getMessage());
				return new JsonModel(array("status"=>"error",'msg'=>$e->getMessage()));
		}
    	
    	return new JsonModel(array("status"=>"success"));
    }

	public function updateOrderAction(){

		$date = $this->params()->fromPost("date",null);
		$orderNo = $this->params()->fromPost("orderno",null);
		$dp = $this->params()->fromPost("dp",null);

		$sm = $this->getServiceLocator();
		$libOrder = QSOrder::getInstance($sm);

		$data = array();
		$where = array();

		if(!$orderNo){
			return new JsonModel(array("status"=>"error","msg"=>"Order not provided"));
		}

		$where['order_no'] = $orderNo;
		$where['order_date'] = $date;

		$data['delivery_person'] = $dp;

		try{
			$libOrder->getOrderTable()->updateOrderTable($data,$where);	
			return new JsonModel(array("status"=>"success","msg"=>"Order updated successfully"));			
		}catch(\Exception $e){
			return new JsonModel(array("status"=>"error","msg"=>$e->getMessage()));			
		}
		

	}

    public function getLocationTable()
    {
    	if (!$this->locationTable)
    	{
    		$sm = $this->getServiceLocator();
    		$this->locationTable = $sm->get('QuickServe\Model\LocationTable');
    	}
    	return $this->locationTable;
    }
    
	public function getOrderDispatchTable() {
		if (!$this->orderdispatchTable) {
			$sm = $this->getServiceLocator();
			$this->orderdispatchTable = $sm->get('QuickServe\Model\OrderDispatchTable');
		}
		return $this->orderdispatchTable;
	}
	/**
	 * Get instance of getUserTable
	 * @method getUserTable()
	 * @return QuickServe\Model\UserTable
	 */
	public function getUserTable()
	{
		if(!$this->userTable)
		{
			$sm = $this->getServiceLocator();
			$this->userTable = $sm->get('QuickServe\Model\UserTable');
		}
		return $this->userTable;
	}

    public function pastorderAction()
    {

        $layoutviewModel = $this->layout();
        $loguser = $layoutviewModel->loggedUser;

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCommon = QScommon::getInstance($sm);
        
        $form = new orderForm($sm);

        $form->get('submit')->setAttribute('value', 'Add');

        $utility = \Lib\Utility::getInstance();

        $request = $this->getRequest();

        $this->layout()->setVariables(array('page_title'=>"Past Order Delivery",'breadcrumb'=>"Past Order Delivery"));
        return array(
            'form' => $form
        );  
    }



        
}
