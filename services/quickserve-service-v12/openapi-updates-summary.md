# OpenAPI Specification Updates Summary

## 📋 **All Modifications Updated in OpenAPI Spec**

### **File Updated:** `services/quickserve-service-v12/order-management-openapi.yaml`

---

## 🔄 **Version Update**
- **Version:** Updated from `1.0.0` to `2.0.0`
- **Reason:** Major feature additions and breaking changes

---

## 📝 **Description Updates**

### **Enhanced API Description:**
```yaml
title: Order Management API
description: |
  Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies.

  ## Complete Order Journey (Updated with Temporary Tables & Wallet Integration)
  1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
  2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
  3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
  4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
  5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
  6. **Order Fulfillment** - All orders ready for delivery with status "New" (not "Confirmed")

  ## Advanced Cancellation System
  ### Time-Based Refund Policies:
  1. **Before Cutoff Time** → 100% refund + unlock wallet amount
  2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
  3. **After 08:00:00** → No cancellation allowed
  
  ### Settings-Based Configuration:
  - All cutoff times and refund percentages configurable via database settings
  - Kitchen-specific and meal-specific policies
  - Cutoff day calculation (0=same day, 1=day before, etc.)
```

---

## ⚙️ **New Configuration Section**

### **Added Cancellation Settings Documentation:**
```yaml
x-cancellation-settings:
  description: |
    Database settings that control the time-based cancellation policies.
    All settings are stored in the `settings` table and can be modified without code changes.
  
  cutoff-times:
    K1_BREAKFAST_ORDER_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CUT_OFF_TIME: "00:01:00" 
    K1_LUNCH_ORDER_CUT_OFF_DAY: "0"
    
  cancellation-cutoffs:
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_DAY: "0"
    
  refund-percentages:
    BREAKFAST_PARTIAL_REFUND_PERCENTAGE: "0"    # 0% refund for breakfast in partial window
    LUNCH_PARTIAL_REFUND_PERCENTAGE: "50"       # 50% refund for lunch in partial window
    
  time-windows:
    CANCELLATION_PARTIAL_REFUND_START_TIME: "00:01:00"
    CANCELLATION_PARTIAL_REFUND_END_TIME: "08:00:00"
    CANCELLATION_NO_REFUND_START_TIME: "08:00:01"
    
  feature-flags:
    ENABLE_TIME_BASED_CANCELLATION: "yes"
    ENABLE_WALLET_LOCKING: "yes"
    ENABLE_AUTOMATIC_REFUND: "yes"
```

---

## 🔗 **Endpoint Updates**

### **1. Enhanced Order Creation Endpoint**
**Path:** `/order-management/create`

**Updated Description:**
```yaml
**Order Placement Fixes (v2.0):**
- ✅ order_status = "New" (not "Confirmed")
- ✅ payment_mode = "online" (not gateway name like "razorpay")
- ✅ temp_pre_order.due_date = NULL (not current date)
- ✅ temp_pre_order.city_name = actual city name from database (not city ID)
- ✅ temp_pre_order.food_preference = "[]" (not "veg")
- ✅ temp_pre_order.delivery_time = NULL, delivery_end_time = NULL
- ✅ temp_pre_order.order_days = CSV format (not JSON array)
- ✅ temp_order_payment = separate records for each meal (not clubbed)
- ✅ Wallet locking during order creation for cancellation tracking
```

### **2. Enhanced Customer Orders Endpoint**
**Path:** `/order-management/customer/{customerId}`

**Updated Description:**
```yaml
summary: Get customer orders with cancellation status
description: |
  Retrieves paginated list of orders for a customer with real-time cancellation eligibility.
  
  **New Features:**
  - ✅ `is_cancellable` flag for each order based on time-based policies
  - ✅ `cancellation_policy` object with refund percentage and policy details
  - ✅ Real-time calculation based on current time and settings
  - ✅ Meal-specific cancellation policies (breakfast vs lunch)
  
  **Cancellation Policies:**
  - **Before Cutoff**: 100% refund allowed
  - **Partial Window (00:01-08:00)**: Breakfast 0%, Lunch 50%
  - **After 08:00**: No cancellation allowed
```

### **3. Enhanced Cancel Order Endpoint**
**Path:** `/order-management/cancel/{orderNo}`

**Updated Description:**
```yaml
summary: Cancel order with time-based refund processing
description: |
  Advanced order cancellation with time-based refund policies and wallet management.

  **Time-Based Refund Policies:**
  1. **Before Cutoff Time** → 100% refund + unlock wallet amount
  2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
  3. **After 08:00:00** → No cancellation allowed

  **Enhanced Features:**
  - ✅ Time-based refund calculation based on meal type
  - ✅ Automatic wallet unlocking for locked amounts
  - ✅ Settings-driven cancellation policies
  - ✅ Meal-specific filtering (breakfast, lunch, dinner)
  - ✅ Detailed refund breakdown in response
  - ✅ Only cancels current or future date orders
  - ✅ Prevents cancellation of prepared/dispatched orders
  
  **Wallet Integration:**
  - Unlocks previously locked wallet amounts
  - Credits refund amount to customer wallet
  - Maintains detailed transaction history
```

### **4. New Cancellation Settings Endpoint**
**Path:** `/order-management/cancellation-settings`

```yaml
get:
  tags:
    - Order Management
  summary: Get cancellation policy settings
  description: |
    Retrieves current cancellation policy settings from database.
    
    **Returns:**
    - Cutoff times for each meal type
    - Refund percentages for partial refund window
    - Time windows for different policies
    - Feature flags for cancellation system
  responses:
    '200':
      description: Cancellation settings retrieved successfully
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CancellationSettingsResponse'
```

---

## 📊 **Schema Updates**

### **1. Enhanced CancelOrderRequest Schema**
```yaml
CancelOrderRequest:
  type: object
  required:
    - reason
  properties:
    reason:
      type: string
      maxLength: 500
      description: "Reason for order cancellation"
      example: "Customer requested cancellation due to change in plans"
    cancel_dates:
      type: array
      items:
        type: string
        format: date
      description: "Optional: Specific dates to cancel. If not provided, all future orders will be cancelled"
      example: ["2025-08-18", "2025-08-20"]
    meal_type:
      type: string
      enum: [breakfast, lunch, dinner]
      description: "Optional: Filter cancellation by specific meal type"
      example: "lunch"
```

### **2. Enhanced CancelOrderResponse Schema**
```yaml
CancelOrderResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Orders cancelled successfully with time-based refund policy"
    data:
      type: object
      properties:
        cancelled_orders:
          type: integer
          description: "Number of orders cancelled"
          example: 3
        cancelled_order_ids:
          type: array
          items:
            type: integer
          description: "List of cancelled order IDs"
          example: [127733, 127734, 127735]
        total_refund_amount:
          type: number
          format: decimal
          description: "Total refund amount credited to wallet (after applying time-based policies)"
          example: 125.50
        wallet_credited:
          type: boolean
          description: "Whether refund was successfully credited to wallet"
          example: true
        wallet_unlocked:
          type: number
          format: decimal
          description: "Total wallet amount unlocked from locked state"
          example: 200.00
        customer_code:
          type: integer
          description: "Customer code for the cancelled orders"
          example: 1
        order_no:
          type: string
          description: "Order number that was cancelled"
          example: "XAJE250724"
        refund_breakdown:
          type: array
          description: "Detailed breakdown of refund calculation for each cancelled order"
          items:
            type: object
            properties:
              order_id:
                type: integer
                example: 127733
              order_date:
                type: string
                format: date
                example: "2025-08-06"
              meal_type:
                type: string
                example: "lunch"
              base_amount:
                type: number
                format: decimal
                example: 125.00
              refund_percentage:
                type: integer
                example: 50
              refund_amount:
                type: number
                format: decimal
                example: 62.50
              wallet_unlocked:
                type: number
                format: decimal
                example: 125.00
              policy_type:
                type: string
                enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
                example: "partial_refund_window"
              cutoff_time:
                type: string
                format: time
                example: "00:01:00"
```

### **3. Enhanced OrderSummary Schema**
```yaml
OrderSummary:
  type: object
  properties:
    # ... existing properties ...
    recurring_status:
      type: string
    is_cancellable:
      type: boolean
      description: "Whether this order can be cancelled based on current time and policies"
      example: true
    cancellation_policy:
      type: object
      description: "Cancellation policy details for this order"
      properties:
        refund_percentage:
          type: integer
          description: "Percentage of refund if cancelled now"
          example: 50
        policy_type:
          type: string
          enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
          description: "Type of cancellation policy currently applicable"
          example: "partial_refund_window"
        cutoff_time:
          type: string
          format: time
          description: "Cutoff time for this meal type"
          example: "00:01:00"
        cutoff_day:
          type: integer
          description: "Days before delivery when cutoff applies (0=same day, 1=day before)"
          example: 0
```

### **4. New CancellationSettingsResponse Schema**
```yaml
CancellationSettingsResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    data:
      type: object
      properties:
        cutoff_times:
          type: object
          properties:
            breakfast:
              type: object
              properties:
                cutoff_time:
                  type: string
                  format: time
                  example: "00:01:00"
                cutoff_day:
                  type: integer
                  example: 0
            lunch:
              type: object
              properties:
                cutoff_time:
                  type: string
                  format: time
                  example: "00:01:00"
                cutoff_day:
                  type: integer
                  example: 0
        refund_percentages:
          type: object
          properties:
            breakfast_partial_refund:
              type: integer
              example: 0
            lunch_partial_refund:
              type: integer
              example: 50
        time_windows:
          type: object
          properties:
            partial_refund_start:
              type: string
              format: time
              example: "00:01:00"
            partial_refund_end:
              type: string
              format: time
              example: "08:00:00"
            no_refund_start:
              type: string
              format: time
              example: "08:00:01"
        feature_flags:
          type: object
          properties:
            time_based_cancellation_enabled:
              type: boolean
              example: true
            wallet_locking_enabled:
              type: boolean
              example: true
            automatic_refund_enabled:
              type: boolean
              example: true
```

---

## ✅ **Summary of All Updates**

### **✅ Updated Sections:**
1. **API Version:** 1.0.0 → 2.0.0
2. **API Description:** Added cancellation system details
3. **Configuration:** Added x-cancellation-settings section
4. **Order Creation:** Added order placement fixes documentation
5. **Customer Orders:** Added isCancellable flag and cancellation_policy
6. **Cancel Order:** Enhanced with time-based policies and wallet integration
7. **New Endpoint:** Added cancellation-settings endpoint
8. **Request Schemas:** Added meal_type filter to CancelOrderRequest
9. **Response Schemas:** Enhanced with detailed refund breakdown
10. **Order Schema:** Added cancellation status fields

### **✅ New Features Documented:**
- Time-based refund policies
- Wallet locking/unlocking system
- Settings-based configuration
- Meal-specific cancellation rules
- Real-time cancellation eligibility
- Detailed refund breakdown
- Order placement fixes
- City name database lookup

### **✅ Backward Compatibility:**
- All existing endpoints remain functional
- New fields are optional or additive
- Legacy behavior preserved where applicable

---

## 🔄 **NEW API ENDPOINT: Order Swap**

### **Added:** `/order-management/swap/{orderNo}` (POST)

**Purpose:** Allows customers to swap their meal with another product from the same category.

**Key Features:**
- ✅ Product category validation - Only same category swaps allowed
- ✅ Order status validation - Only swappable orders (not delivered/cancelled)
- ✅ Price difference calculation - Automatic price adjustment
- ✅ Swap charges support - Additional charges for premium swaps
- ✅ Tax recalculation - Updated tax based on new amount
- ✅ Order details update - All related records updated
- ✅ Audit logging - Complete swap history tracking
- ✅ Transaction safety - Database rollback on errors

**Request Schema:** `SwapOrderRequest`
```yaml
SwapOrderRequest:
  type: object
  required:
    - order_date
    - new_product_code
  properties:
    order_date:
      type: string
      format: date
      description: "Date of the order to swap (YYYY-MM-DD)"
      example: "2025-09-03"
    new_product_code:
      type: integer
      description: "Product code of the new product to swap to"
      example: 342
    reason:
      type: string
      maxLength: 255
      description: "Optional reason for the swap"
      example: "Customer wants to change from Poha to Upma"
    meal_type:
      type: string
      enum: [breakfast, lunch, dinner]
      description: "Optional filter by meal type"
      example: "breakfast"
```

**Response Schema:** `SwapOrderResponse`
```yaml
SwapOrderResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "Order swapped successfully"
    data:
      type: object
      properties:
        order_id:
          type: integer
          example: 127810
        order_no:
          type: string
          example: "QA93250725"
        order_date:
          type: string
          format: date
          example: "2025-09-03"
        swap_details:
          type: object
          properties:
            old_product:
              type: object
              properties:
                code: 341
                name: "Poha"
                price: 150.00
            new_product:
              type: object
              properties:
                code: 342
                name: "Upma"
                price: 200.00
            price_difference: 50.00
            swap_charges: 25.00
            total_amount_change: 75.00
            new_order_amount: 225.00
        reason:
          type: string
          example: "Customer wants to change from Poha to Upma"
```

**Validation Rules:**
- Products must belong to the same category/type
- Order must not be Cancelled, Delivered, or Complete
- Order must not be Delivered, Failed, or Dispatched
- Cannot swap orders for past dates
- Both current and new products must be active
- New product must have is_swappable = true
- Veg/Non-veg classification must match

**Database Updates:**
- **Orders table:** product_code, product_name, amount, tax, remark
- **Order_details table:** All product fields updated proportionally

---

## 🎉 **OpenAPI Specification Fully Updated!**

**The OpenAPI specification now comprehensively documents all the sophisticated order cancellation system features, order placement fixes, wallet integration, and the new order swap functionality that we've implemented.**

**File Location:** `services/quickserve-service-v12/order-management-openapi.yaml`
