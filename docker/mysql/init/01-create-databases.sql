-- Create databases for microservices
CREATE DATABASE IF NOT EXISTS auth_service;
CREATE DATABASE IF NOT EXISTS user_service;
CREATE DATABASE IF NOT EXISTS payment_service;
CREATE DATABASE IF NOT EXISTS order_service;
CREATE DATABASE IF NOT EXISTS customer_service;
CREATE DATABASE IF NOT EXISTS quickserve_service;
CREATE DATABASE IF NOT EXISTS konga;

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON auth_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON user_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON payment_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON order_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON customer_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON quickserve_service.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON konga.* TO 'root'@'%';

FLUSH PRIVILEGES;
