import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthMetricsPerformance } from '@/components/auth-service-v12/metrics/performance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthMetricsPerformance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthMetricsPerformance />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthMetricsPerformance')).toBeInTheDocument();
  });
});