import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AdminIndex } from '@/components/admin-service-v12/index';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AdminIndex', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AdminIndex />
      </QueryClientProvider>
    );

    expect(screen.getByText('AdminIndex')).toBeInTheDocument();
  });
});