<?php
use Zend\View\Resolver\TemplateMapResolver;
/**
 * This File is the main configuration file of Admin Module.
 * It has all the configuration needed for the Admin module
 * It defines the list of controllers,routes information and the view manager inside this configuration.
 *
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: module.config.php 2014-06-19 $
 * @package Admin/Config
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Config Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */

return array(
    'controllers' => array(
        'invokables' => array(
            'Admin\Controller\Dashboard'        => 'Admin\Controller\DashboardController',
            'Admin\Controller\Customer'         => 'Admin\Controller\CustomerController',
            'Admin\Controller\Order'            => 'Admin\Controller\OrderController',
            'Admin\Controller\Preorders'        => 'Admin\Controller\PreordersController',
            'Admin\Controller\User'             => 'Admin\Controller\UserController',
            'Admin\Controller\Product'          => 'Admin\Controller\ProductController',
            'Admin\Controller\Location'         => 'Admin\Controller\LocationController',
            'Admin\Controller\City'             => 'Admin\Controller\CityController',
            'Admin\Controller\CustGroup'        => 'Admin\Controller\CustGroupController',
            'Admin\Controller\Discount'         => 'Admin\Controller\DiscountController',
            'Admin\Controller\PromoCode'        => 'Admin\Controller\PromoCodeController',
            'Admin\Controller\AllocateDelivery' => 'Admin\Controller\AllocateDeliveryController',
            'Admin\Controller\Backorder'        => 'Admin\Controller\BackorderController',
            'Admin\Controller\Printlabel'       => 'Admin\Controller\PrintlabelController',
            'Admin\Controller\OrderDispatch'    => 'Admin\Controller\OrderDispatchController',
            'Admin\Controller\Collection'       => 'Admin\Controller\CollectionController',
            'Admin\Controller\Invoice'          => 'Admin\Controller\InvoiceController',
            'Admin\Controller\Tax'              => 'Admin\Controller\TaxController',
            'Admin\Controller\Report'           => 'Admin\Controller\ReportController',
            'Admin\Controller\Setting'          => 'Admin\Controller\SettingController',
            'Admin\Controller\OrderConfirm'     => 'Admin\Controller\OrderConfirmController',
            'Admin\Controller\Thirdparty'       => 'Admin\Controller\ThirdpartyController',
            'Admin\Controller\EmailTemplate'    => 'Admin\Controller\EmailTemplateController',
            'Admin\Controller\SmsTemplate'      => 'Admin\Controller\SmsTemplateController',
            'Admin\Controller\BarcodeDispatch'  => 'Admin\Controller\BarcodeDispatchController',
            'Admin\Controller\ProductCategory'  => 'Admin\Controller\ProductCategoryController',
            'Admin\Controller\Kitchen'          => 'Admin\Controller\KitchenController',	
            'Admin\Controller\Role'             => 'Admin\Controller\RoleController',
            'Admin\Controller\Subscriptionlog'  => 'Admin\Controller\SubscriptionlogController',
            'Admin\Controller\Cms'              => 'Admin\Controller\CmsController',
            'Admin\Controller\Wizard'           => 'Admin\Controller\WizardController',
            'Admin\Controller\Timeslot'         => 'Admin\Controller\TimeslotController',
            'Admin\Controller\TrackTiffins'     => 'Admin\Controller\TrackTiffinsController',

        ),
    ),
    'router' => array(
        'routes' => array(
            'dashboard' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/dashboard[/:action][/:id]',
                    'constraints' => array(
                        'action' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Dashboard',
                        'action'     => 'index',
                    ),
                ),
            ),
            'collectionlist' => array(
                'type'    => 'Literal',
                'options' => array(
                    'route'    => '/collectionlist',
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Invoice',
                        'action'     => 'printcollectionlist',
                    ),
                ),
            ),
            'user_crud' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/users[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\User',
                        'action'     => 'index',
                    ),
                ),
            ),
            'backorder' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/backorder[/:action][/:id][/:menu]',
                    'constraints' => array(
                        'action' => '[a-zA-Z][a-zA-Z0-9_-]*',
                            'id'   => '[0-9]+',
                            'menu' => '[a-zA-Z][a-zA-Z0-9_-]*'
                        ),
                        'defaults' => array(
                            'controller' => 'Admin\Controller\Backorder',
                            'action'     => 'index',
                    ),
                ),
            ),
            'customer' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/customer[/:action][/:id][/amt/:amt][/description/:description][/hdnavailbal/:hdnavailbal][/page/:page][/order_by/:order_by][/:order][/menu/:menu][/location/:location][/city/:city][/deliverypers/:deliverypers][/order_date/:order_date][/flag/:flag]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Customer',
                        'action'     => 'index',
                    ),
                ),
            ),
            'product' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/product[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action'   => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'       => '[0-9]+',
                        'page'     => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order'    => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Product',
                        'action'     => 'index',
                    ),
                ),
            ),
            'meal' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/meal[/:action][/:id][/page/:page][/order_by/:order_by][/:order][/:kitchen]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Product',
                        'action'     => 'meal',
                    ),
                ),
            ),	 

            'location' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/location[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Location',
                        'action'     => 'index',
                    ),
                ),
            ),
            'city' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/city[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\City',
                        'action'     => 'index',
                    ),
                ),
            ),
            'custgroup' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/custgroup[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\custgroup',
                        'action'     => 'index',
                    ),
                ),
            ),
            'discount' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/discount[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\discount',
                        'action'     => 'index',
                    ),
                ),
            ),
            'promocode' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/promocode[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\promocode',
                        'action'     => 'index',
                    ),
                ),
            ),
            'order' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/order[/:action][/:id][/view/:view][/page/:page][/order_by/:order_by][/:order][/:c_id][/:order_status][/menu/:menu][/language/:language][/location/:location][/city/:city][/deliveryperson/:deliveryperson][/delivery_type/:delivery_type][/date/:date][/kitchen/:kitchen][/ordno/:ordno][/custid/:custid][/third-party/:third-party][/ordstatus/:ordstatus]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        /*'id'     => '[0-9A-Za-z]+',*/
                        'id'     => '[^preorder|^today|^unbill|^\/]+',
                        'view'=> '[a-zA-Z][a-zA-Z0-9_-]*',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                        'c_id' => '[0-9]+',
                        'order_status' => 'Rejected|UnDelivered',

                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Order',
                        'action'     => 'index',
                    ),
                ),
            ),
            'allocatedelivery' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/allocatedelivery[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',

                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\AllocateDelivery',
                        'action'     => 'index',
                    ),
                ),
            ),

            'preorders' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/preorders[/:action][/:id][/page/:page][/order_by/:order_by][/:order][/:c_id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                        'c_id' => '[0-9]+',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Preorders',
                        'action'     => 'index',
                    ),
                ),
            ),

            'printlabel' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/printlabel[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Printlabel',
                        'action'     => 'index',
                    ),
                ),
            ),

            'orderdispatch' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/orderdispatch[/:action][/:id][/location/:location][/purpose/:purpose]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'location' => '[0-9]+'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\OrderDispatch',
                        'action'     => 'index',
                    ),
                ),
            ),

             'orderconfirm' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/orderconfirm[/:action][/:id][/amt/:amt][/custcode/:custcode]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        //'location' => '[0-9]+'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\OrderConfirm',
                        'action'     => 'index',
                    ),
                ),
            ),
	    		
            'order_dispatch_pdf' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/pdf-order-dispatch',

                    'defaults' => array(
                        'controller' => 'Admin\Controller\OrderDispatch',
                        'action'     => 'orderdispatchget',
                    ),
                ),
            ),
            'collection' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/collection[/:action][/:id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'location' => '[0-9]+'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Collection',
                        'action'     => 'index',
                    ),
                ),
            ),

            'invoice' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/invoice[/view/:view][/:action][/:id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'location' => '[0-9]+'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Invoice',
                        'action'     => 'index',
                    ),
                ),
            ),

            'tax' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/tax[/:action][/:id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'location' => '[0-9]+'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Tax',
                        'action'     => 'index',
                    ),
                ),
            ),
            'report' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/report[/:action][/:id][/order_date/:order_date][/menu/:menu]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'location' => '[0-9]+',
                        //	'table' => '[a-zA-Z][a-zA-Z0-9_-]*',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Report',
                        'action'     => 'Orders',
                    ),
                ),
            ),
            'setting' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/setting[/:action][/:id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Setting',
                        'action'     => 'index',
                    ),
                ),
            ),
            'thirdparty' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/thirdparty[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Thirdparty',
                        'action'     => 'index',
                    ),
                ),
            ),

            'emailtemplate' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/emailtemplate[/:action][/:id][/:setid][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                        'templateid' => '[0-9]+',
                        'setid' => '[0-9]+',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\EmailTemplate',
                        'action'     => 'index',
                    ),
                ),
            ),

            'smstemplate' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/smstemplate[/:action][/:id][/:setid][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\SmsTemplate',
                        'action'     => 'index',
                    ),
                ),
            ),

            'barcodedispatch' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/barcodedispatch[/:action][/:id]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',

                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\BarcodeDispatch',
                        'action'     => 'index',
                    ),
                ),
            ),
            'product_category' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/product-category[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\ProductCategory',
                        'action'     => 'index'
                    )
                )
            ),
            'kitchen_master' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/kitchen-master[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC'
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Kitchen',
                        'action'     => 'index'
                    )
                )
            ),
            'role' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/role[/:action][/:id][/page/:page][/order_by/:order_by][/:order][/:kitchen]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Role',
                        'action'     => 'index',
                    ),
                ),
            ),

            'subscriptionlog' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/subscriptionlog[/:action][/:id][/date/:date]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Subscriptionlog',
                        'action'     => 'index',
                    ),
                ),
            ),

            'cms' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/cms[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Cms',
                        'action'     => 'index',
                    ),
                ),
            ),
            
            'wizard' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/wizard[/:action]',
                    'constraints' => array(
                        'action' => '[a-zA-Z][a-zA-Z0-9_-]*',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Wizard',
                        'action'     => 'index',
                    ),
                ),
                
//                'child_routes' => array(
//                    'location' => array(
//                        'type'    => 'segment',
//                        'options' => array(
//                            'route'    => '/location',
//                            'defaults' => array(
//                                'action' => 'location',
//                            ),
//                        ),
//                        'may_terminate' => true
//                    ),
//                )
            ),

            'timeslot' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/timeslot[/:action][/:id][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\Timeslot',
                        'action'     => 'index',
                    ),
                ),
            ),
            
            'tracktiffins' => array(
                'type'    => 'segment',
                'options' => array(
                    'route'    => '/tracktiffins[/:action][/:id][/menu/:menu][/page/:page][/order_by/:order_by][/:order]',
                    'constraints' => array(
                        'action' => '(?!\bpage\b)(?!\border_by\b)[a-zA-Z][a-zA-Z0-9_-]*',
                        'id'     => '[0-9]+',
                        'page' => '[0-9]+',
                        'order_by' => '[a-zA-Z][a-zA-Z0-9_-]*',
                        'order' => 'ASC|DESC',
                    ),
                    'defaults' => array(
                        'controller' => 'Admin\Controller\TrackTiffins',
                        'action'     => 'index',
                    ),
                ),
            ),             

        ),
    ),

    'view_manager' => array(
    	'display_not_found_reason' => true,
    	'display_exceptions'       => true,
    	'doctype'                  => 'HTML5',
    	'not_found_template'       => 'admin/error/404',
    	'exception_template'       => 'admin/error/index',
    	'template_map' => array(
            'layout/admin'        => __DIR__ . '/../view/admin/layout/layout.phtml',
            'layout/admin_new'        => __DIR__ . '/../view/admin/layout/layout_new.phtml',
            'layout/wizard'         => __DIR__ . '/../view/admin/layout/wizard.phtml',
            'layout/thermal'        => __DIR__ . '/../view/admin/layout/thermal.phtml',
            'paginator-slide-price-plan' => __DIR__ . '/../view/admin/layout/slidePaginatorPricePlan.phtml',
            'paginator-slide-order-summary' => __DIR__ . '/../view/admin/layout/slidePaginatorOrderSummary.phtml',
            'paginator-slide-order-services' => __DIR__ . '/../view/admin/layout/slidePaginatorServices.phtml',
            'paginator-slide-order-payments' => __DIR__ . '/../view/admin/layout/slidePaginatorPayments.phtml',
            'paginator-slide' => __DIR__ . '/../view/admin/layout/slidePaginatorCustomer.phtml',
                    'error/404'               => __DIR__ . '/../view/error/404.phtml',
            'error/index'             => __DIR__ . '/../view/error/index.phtml',
    	),
        'template_path_stack' => array(

            'admin' => __DIR__ . '/../view',
        ),
    	'strategies' => array(
            'ViewJsonStrategy',
    	),
    ),

	'module_layouts' => array(
            'Admin' => array(
                'default' => 'layout/admin_new',
                'wizard' => 'layout/wizard'
            )
        ),
        'service_manager' => array(
            'factories' => array(
                'translator' => 'Zend\I18n\Translator\TranslatorServiceFactory',
            ),
        ),
        'translator' => array(
            'locale' => 'en_US',
                'translation_file_patterns' => array(
                    array(
                        'type'     => 'gettext',
                        'base_dir' => __DIR__ . '/../language',
                        'pattern'  => '%s.mo',
                    ),
                ),
    ),
    'page_price' => array('content' => array('words_per_page' => 350)),

        'screen' => array(
            '1' => 'Screen 1','2' => 'Screen 2','3' => 'Screen 3',
        ),
);
