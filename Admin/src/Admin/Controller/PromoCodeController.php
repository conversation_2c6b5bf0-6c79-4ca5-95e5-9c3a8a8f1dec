<?php
/**
 * This File manages the promocodes on fooddialer system
 * It is used to add ,update delete promocode
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PromoCodeController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use QuickServe\Model\PromoCode;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as QScommon;

use Admin\Form\PromoCodeForm;

class PromoCodeController extends AbstractActionController
{
    
      protected $errorMessages = [];

	/**
	 * It has an instance of QuickServe\Model\PromoCodeTable model
	 *
	 * @var QuickServe\Model\PromoCodeTable $promocodeTable
	 */
    protected $promocodeTable;
	/**
	 * To display the list of promo codes
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
    public function indexAction() {
//         error_reporting(E_ALL);
//        ini_set('display_errors','On');
        $select = new QSelect();
		$order_by = $this->params()->fromRoute('order_by') ?
                $this->params()->fromRoute('order_by') : 'pk_promo_code';
        $order = $this->params()->fromRoute('order') ?
                $this->params()->fromRoute('order') : QSelect::ORDER_ASCENDING;
        $page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

        $albums = $this->getPromoCodeTable()->fetchAll($select->order($order_by . ' ' . $order));
        $returnvar = $albums->toArray();
        $itemsPerPage = 2;
        $albums->current();
        $paginator = new Paginator(new paginatorIterator($albums));
        $paginator->setCurrentPageNumber($page)
                ->setItemCountPerPage($itemsPerPage)
                ->setPageRange(7);

        $this->layout()->setVariables(array('page_title'=>"Promocode",'description'=>"Promocode Details",'breadcrumb'=>"Promocode"));
        
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        
        return new ViewModel(array(
        			'acl' => $acl,
                    'order_by' => $order_by,
                    'order' => $order,
                    'page' => $page,
                    'paginator' => $returnvar,
        			'loggedUser' => $loguser,
        			'flashMessages'=> $this->flashMessenger()->getMessages()
                ));
    }
	/**
	 * To add new promo code
	 *
	 * @return \Admin\Form\PromoCodeForm
	 */
    public function addAction() {
//        error_reporting(E_ALL);
//        ini_set("display_errors",'On');
        $sm = $this->getServiceLocator();
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$libCommon = QScommon::getInstance($sm);
        
    	$adapt = $sm->get('Write_Adapter');
        
    	$form = new PromoCodeForm($sm);
    	$form->get('submit')->setAttribute('value', 'Add');
    	 
    	$request = $this->getRequest();
       
       if ($request->isPost()) {
           
    		$form_data = $request->getPost();
             
            if($form_data['applied_on'] == 'order'){
                $form->setValidationGroup('promo_code', 'applied_on', 'promo_type','Product_order_quantity', 'product_code', 'discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'wallet'){
                $form->setValidationGroup('promo_code', 'applied_on', 'promo_type', 'wallet_amount', 'discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'menu'){
                $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','menu_type','discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'plan'){
                 $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','discount_type','amount', 'start_date', 'end_date', 'status');
            }
            else{
                 $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }
              
            $promo = new PromoCode();
            
    		$promo->getInputFilter()->get('promo_code')
    		->getValidatorChain()                  // Filters are run second w/ FileInput
    		->attach(new \Zend\Validator\Db\NoRecordExists(array(
    				'table'     => 'promo_codes',
    				'field'     => 'promo_code',
    				'adapter'   => $adapt,
    				'message'   => 'Promo Code Already exists',
                )
    		));
            
           
    		//$promo->setAdapter($adapt);
    		$form->setInputFilter($promo->getInputFilter());
    		$form->setData($request->getPost());
//           dd($request->getPost());
            $errorMsg = "";
            
        if ($form->isValid()) {
//                dd($form_data);
                $dis_amt=$form_data['amount'];
//                 $min_amt=$form_data['min_amount'];
                
                       //minimum amount validation
//                    if($dis_amt > $min_amt){
//                        $errorMsg =  "discount amount always less than minimum amount"; 
//                        goto END;                       
//                    }   
                    if($form_data['applied_on']=='menu'){
                        
                        $select = new QSelect();
                        $where = array(
//                     'promo_type' => $form_data['promo_type'],
//                                    'Product_order_quantity' => $form_data['Product_order_quantity'],
//                                    'amount'=>$form_data['amount'],
//                                        'min_amount'=>$form_data['min_amount'],
//                                        'discount_type'=>$form_data['discount_type'],
//                                        'applied_on'=>$form_data['applied_on'],
                                            'menu_type'=> (is_array($form_data['menu_type'])) ? implode(',', $form_data['menu_type']) : $form_data['menu_type'],
                    
                        );
              

                        $select->where($where);     

//                        dd($this->getPromoCodeTable()->fetchAll($select)->toArray());
                        $callback = new \Zend\Validator\Callback(function () use($select) {

                            return (!empty($this->getPromoCodeTable()->fetchAll($select)->toArray())) ?  false:true;

                        });

                        $callback->setMessage('promo code with same values already exists.');

                        $promo->getInputFilter()->get('promo_code')
                                ->getValidatorChain()
                                ->attach($callback);
                     } 
                     
                    $form->setInputFilter($promo->getInputFilter());
//                        dd($form_data); 
                    
                    if ($form->isValid()) { 
                        $obj = $form->getData();
                        
                        if(count($obj['menu_type']) > 1){
                            $obj['menu_operator'] = '&&';
                        }
                        
                        $promo->exchangeArray($obj);
                        
                         $data_promocode = $this->getPromoCodeTable()->savePromoCode($promo);
               
    
                        ($data_promocode) ?$this->flashMessenger()->addSuccessMessage("Promo Code added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Promo Code.");
    			
                        if($data_promocode)
                    {
                        $full_name=$loguser->first_name." ".$loguser->last_name;
                        $promo_code=$form_data->promo_code;
                        $activity_log_data=array();
                        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                        $activity_log_data['context_name']= $full_name;
                        $activity_log_data['context_type']= 'user';
                        $activity_log_data['controller']= 'promocode';
                        $activity_log_data['action']= 'add';
                        $activity_log_data['description']= "Promocode : New Promocode '$promo_code' created.";

                    }
               
    			return $this->redirect()->toRoute('promocode');
                        }   
                
            }else{
//                dd($form->getMessages() );
            }
            
    	}
       
       
//        END:
        $this->layout()->setVariables(array('page_title'=>"Add Promocode",'breadcrumb'=>"Add Promocode"));
    	return array('form' => $form,'errorMsg'=>$errorMsg);
    }
   
	/**
	 * To update promocode of given promocode id
	 *
	 * @param int id
	 * @return \Admin\Form\PromoCodeForm
	 */
    public function editAction() {
//        error_reporting(E_ALL);
//        ini_set("display_errors",'On');
    	$id = (int) $this->params('id');
    	if (!$id) {
    		return $this->redirect()->toRoute('promocode', array('action' => 'add'));
    	}
    
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	 
    	$libCommon = QScommon::getInstance($sm);
    
    	$promo = $this->getPromoCodeTable()->getPromoCode($id);
        
    	$promId=explode(",", $promo['product_code']);
       
    	$sql = new QSql($sm);
    	$select = $sql->select();
    	$select->from('products');
    	
    	 
    	$statement = $sql->prepareStatementForSqlObject($select);
    	$results = $statement->execute();
        
    	$selectData=array();
    
    	foreach ($results as $res) {
    		foreach($promId as $keys){
    			if($res['pk_product_code']== $keys)
    				$selectData[]= $res['pk_product_code']."@".$res['name'];
    		}   //concatinating Prod_code and Prod_name
    	}
        
    	$promo['product_code'] = $selectData;
        
        /*  auto apply promocode */
        if($promo->applied_on == 'menu'){
            $promoMenuTypes = explode(",", $promo['menu_type']);
            foreach($promoMenuTypes as $menu){

                $selectMenuData[]= $menu;
            }
            $promo['menu_type'] = $selectMenuData;
        }
        
    	$form = new PromoCodeForm($sm);
       
    	$form->bind($promo);
        
    	$form->get('submit')->setAttribute('value', 'Edit');
        
    	$val = $promo->promo_code;
        
        /* wallet promocode */
        $pk_promo_code = $promo->pk_promo_code;

        $request = $this->getRequest();
    	
        if ($request->isPost()) {
           
            $form_data = $request->getPost();
           
            if($form_data['applied_on'] == 'order'){
                $form->setValidationGroup('promo_code', 'applied_on', 'promo_type','Product_order_quantity', 'product_code', 'discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'wallet'){
                $form->setValidationGroup('promo_code', 'applied_on', 'promo_type', 'wallet_amount', 'discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'menu'){
                $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','menu_type','discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }elseif($form_data['applied_on'] == 'plan'){
                 $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','discount_type','amount', 'start_date', 'end_date', 'status');
            }
            else{
                 $form->setValidationGroup('promo_code','applied_on','promo_type','Product_order_quantity','discount_type','amount', 'promo_limit', 'start_date', 'end_date', 'status');
            }            
            
           
            $promo = new PromoCode();
    		$promo->getInputFilter()->get('promo_code')
    		->getValidatorChain()                  // Filters are run second w/ FileInput
    		->attach(new \Zend\Validator\Db\NoRecordExists(array(
    				'table'     => 'promo_codes',
    				'field'     => 'promo_code',
    				'adapter'   => $adapt,
    				'message'   => 'Promo Code Already exists',
    				'exclude' => array(
                        'field' => 'promo_code',
                        'value' => $val,
    				)
                )
    		));
            
    		//$promo->setAdapter($adapt);
    		$form->setInputFilter($promo->getInputFilter());
//            dd($request->getPost());
    		$form->setData($request->getPost());
             $errorMsg = "";
           
    		if ($form->isValid())
    		{ 
                $dis_amt=$form_data['amount'];
//                 $min_amt=$form_data['min_amount'];
                
//                    if($dis_amt > $min_amt){
//                        $errorMsg =  "discount amount must always be less than minimum amount"; 
//                       goto END;                       
//                    }   
                   
                    if($form_data['applied_on']=='menu'){      

                        $select = new QSelect();
                        
                        $where = array(
            //             'promo_type' => $form_data['promo_type'],
    //                                    'Product_order_quantity' => $form_data['Product_order_quantity'],
            //                            'amount'=>$form_data['amount'],
            //                                'min_amount'=>$form_data['min_amount'],
            //                                'discount_type'=>$form_data['discount_type'],
            //                                'applied_on'=>$form_data['applied_on'],
                                            'menu_type'=> (is_array($form_data['menu_type'])) ? implode(',', $form_data['menu_type']) : $form_data['menu_type'],
                            );


                        $select->where($where);
                        $select->where->notEqualTo('pk_promo_code', $pk_promo_code);
//                        dd($select->getSqlString());
//                         dd($this->getPromoCodeTable()->fetchAll($select)->toArray());
                        $callback = new \Zend\Validator\Callback(function () use($select) {

                            return (!empty($this->getPromoCodeTable()->fetchAll($select)->toArray())) ?  false:true;

                        });

                        $callback->setMessage('promo code with same values already exists.');

                        $promo->getInputFilter()->get('promo_code')
                                ->getValidatorChain()
                                ->attach($callback);
                    }    
                     
                    $form->setInputFilter($promo->getInputFilter());
                   
                    if ($form->isValid()) {
                       
                        $obj = $form->getData();
                        
                        $obj->pk_promo_code = $pk_promo_code;
                        
                        if($request->getPost('applied_on') == 'order'){
                            $obj->wallet_amount = NULL;
                            $obj->menu_type=NULL;
                            $obj->Product_order_quantity = $request->getPost('Product_order_quantity');
                        }else if($request->getPost('applied_on') == 'wallet'){
                            $obj->product_code = NULL;
                            $obj->Product_order_quantity = NULL;
                             $obj->menu_type=NULL;

                        }else if($request->getPost('applied_on') == 'menu'){
                            
                            if (count($form_data['menu_type']) > 1){
                                
                                $obj->menu_operator = '&&';
                            }
                           // $obj->product_code = NULL; $obj->Product_order_quantity = NULL;
                            $obj->wallet_amount = NULL;
                            $obj->Product_order_quantity = $request->getPost('Product_order_quantity');
                        }
                        else if($request->getPost('applied_on') == 'plan'){
                           // $obj->product_code = NULL; $obj->Product_order_quantity = NULL;
                            $obj->wallet_amount = NULL;
    //                        $obj->Product_order_quantity = NULL;
                        }    
                        
//                        dd($obj);
                            $promo->exchangeArray($obj);
                            
                            $data_promocode = $this->getPromoCodeTable()->savePromoCode($promo);
        //                    dd($data_Promocode);
                        if($data_promocode){

                            $full_name=$loguser->first_name." ".$loguser->last_name;
                            $promo_code=$promo->promo_code;
                            $activity_log_data=array();
                            $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                            $activity_log_data['context_name']= $full_name;
                            $activity_log_data['context_type']= 'user';
                            $activity_log_data['controller']= 'promocode';
                            $activity_log_data['action']= 'edit';
                            $activity_log_data['description']= "Promocode : Promocode '$promo_code' updated.";
                            //$activity_log_data['description']= "'$promo_code' promo code updated by $full_name";
                            $libCommon->saveActivityLog($activity_log_data);
                        }


                        $this->flashMessenger()->addSuccessMessage("Promo Code updated successfully");
                        // Redirect to list of albums
                        return $this->redirect()->toRoute('promocode');
                    }else{
//                        dd($form->getMessages());
                    }
    		}
    		else
    		{
//    			dd($form->getMessages());
    		}
    	}
//        END:
        $this->layout()->setVariables(array('page_title'=>"Edit Promocode",'breadcrumb'=>"Edit Promocode"));
    	return array(
    			'id' => $id,
    			'form' => $form,
            'errorMsg'=>$errorMsg
    	);
    }
	/**
	 * To delete promocode of given promocode id
	 *
	 * @param int id
	 * @return route promocode
	 */
    public function deleteAction()
    {
        $id = (int) $this->params('id');
        
        if (!$id)
        {
            return $this->redirect()->toRoute('promocode');
        }
        
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        
        $libCommon = QScommon::getInstance($sm);
        
        $select = new QSelect();
        $select->where(array('pk_promo_code'=>$id));
        $promocodes = $this->getPromoCodeTable()->fetchAll($select);
        
        $arrpromoceode=$promocodes->toArray();
        
        $promo_name=$arrpromoceode[0]['promo_code'];
        $promo_status=($arrpromoceode[0]['status'])=='1'?'deactivated':'activated';

        $data_promocode=$this->getPromoCodeTable()->deletePromoCode($id);
		($data_promocode) ?$this->flashMessenger()->addSuccessMessage("Promo Code updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Promo Code.");

		if($data_promocode)
		{
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'promocode';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "Promocode : Promocode '$promo_name' $promo_status.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
		
		
       return $this->redirect()->toRoute('promocode');
    }
	/**
	 * Get instance of QuickServe\Model\PromoCodeTable
	 *
	 * @return QuickServe\Model\PromoCodeTable
	 *
	 */
    public function getPromoCodeTable() {
        if (!$this->promocodeTable) {
            $sm = $this->getServiceLocator();
            $this->promocodeTable = $sm->get('QuickServe\Model\PromoCodeTable');
        }
        return $this->promocodeTable;
    }
    public function addErrorMessage($text)
    {
        $this->errorMessages[] = $text ;
        return $this; 
    }

}
