import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletBalance } from '@/components/customer-service-v12/[id]/wallet/balance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletBalance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletBalance />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletBalance')).toBeInTheDocument();
  });
});