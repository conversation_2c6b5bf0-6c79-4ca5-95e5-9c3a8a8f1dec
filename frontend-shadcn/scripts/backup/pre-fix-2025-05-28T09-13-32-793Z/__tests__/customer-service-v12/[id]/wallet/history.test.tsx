import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletHistory } from '@/components/customer-service-v12/[id]/wallet/history';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletHistory', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletHistory />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletHistory')).toBeInTheDocument();
  });
});