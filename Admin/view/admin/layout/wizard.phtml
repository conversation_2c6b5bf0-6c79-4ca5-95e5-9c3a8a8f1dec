<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title><?php echo ucfirst($_SESSION['tenant']['company_details']['company_name'])?> : Wizard</title>
		<link rel="shortcut icon" href="/images/favicon.ico" />
		
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/metro-bootstrap.css">
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/metro-bootstrap-responsive.css">
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/bootstrap.css">
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/font-awesome.min.css">
<!--        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/timepicki.css">-->
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/floatLabel.css">
        <link rel="stylesheet" type="text/css" href="/wizard_assets/css/default.css">

        <script src="/wizard_assets/js/jquery.min.js"></script>
		<script src="/wizard_assets/js/bootstrap.min.js"></script>
        
	</head>

	<body class="metro" id="body">

        
		<!-- topbar starts -->
		<div class="topbar wizard-topbar">
			<!-- mobile navigation starts -->

			<!-- mobile navigation ends -->
			<div class="grid">
				<div class="row">
					<div class="col-lg-2 col-md-2 col-sm-4 col-xs-3 pl6">
						<div class="logo1">
							<img src="/images/topbar.png" alt="<?php echo ucfirst($_SESSION['tenant']['company_details']['company_name'])?>"/>
						</div>
					</div>
					
					<div class="col-lg-8 col-md-8 col-sm-4 col-xs-5 building-name">
						<div><?php echo ucfirst($_SESSION['tenant']['company_details']['company_name']);?></div>
					</div>

					<div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 pl6">
						<div class="dropdown user-drop">
						  <button id="dLabel" class="user-prf" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						   	<span class="user-img"><img src="/images/default-user.png"></span><?php echo $_SESSION['Zend_Auth']['storage']->first_name.' '.$_SESSION['Zend_Auth']['storage']->last_name; ?>
						    <span class="caret"></span>
						  </button>
						  <ul class="dropdown-menu logout" aria-labelledby="dLabel">
						    <li><a href="/auth/logout">Logout</a></li>
						  </ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- topbar ends -->

		<!--wrapper starts -->
		<div id="wrapper" class="clearfix">

			<!-- page container starts -->
			<div class="page_container container page_container_wizard">
		        
		        <?php echo $this->content;?>
			</div>
			<!-- page container ends -->
		</div>
		
		<section class="sub-footer" style="position: fixed; bottom: 0px; z-index: 4">
				<div class="container ">
					<div class="row pre-next">
						<div class="col-sm-12">
							<div class="clearfix">
								<div class="pull-left">
									Powered by: <a href="#" class="poweredby">PROSIMERP</a>
								</div>
                                <div class="pull-right">
                                    <?php if($this->previous){?>
                                        <a href="/wizard/<?php echo $this->previous; ?>" class="pre-btn">&#171; Previous</a>
                                    <?php } 
                                    if($this->next){?>
                                        <!--<a href="/wizard/<?php // echo $this->next;?>" class="next-btn" >Next &#187;</a>-->
                                        <a href="javascript:void(0)" class="next-btn" data-page="<?php echo $this->page;?>"  data-next="<?php echo $this->next;?>" id='next-btn'>Next &#187;</a>
                                    <?php }
                                    if($this->page == 5){ ?>
                                        <a href="/wizard/finish" class="next-btn">Finish &#187;</a>
                                    <?php } ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>
<!-- Common success Modal -->
<div class="modal fade" id="common-modal" role="dialog">
    <div class="modal-dialog">
    
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title  theme-color">Successful!</h4>
        </div>
        <div class="modal-body">
          <p id="common-content"></p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      
    </div>
</div>

        <script src="/wizard_assets/js/jquery.customSelect.js"></script>
        <script src="/wizard_assets/js/floatLabel.js"></script> 
        
        
		<script>
            
			$(document).ready(function() {
                
				$("button[type=submit]").click(function() {
					$(this).text("Loading...");
					$(this).css("opacity", "0.8");
				});
			
			    var city = '<?php echo $_SESSION['wizard']['city'];?>';
			    if (city != null) {
			      $('#city-styled').val(city);
			    }

			    var plans = '<?php echo $_SESSION['wizard']['meal_plans'];?>';
			    if (plans != null) {
			      $('#meal_plans-styled').val(plans);
			    }

			});

		</script>
		<!-- left navigation script -->
		<script>
            
			$(".parent-menu li span a").on('click', function(e) {
                
				e.preventDefault();

				if ($(".submenu").hasClass("active") == true) {
					//close

				} else {
					//open

				}

				if ($(this).attr('class') == "selected") {
					$(".parent-menu li span a").removeClass("selected");

					// $(body).removeClass("slideout");
					timer2 = setTimeout(function() {

					}, 500);
					$(".submenu").animate({
						"left" : "-114px"
					}, 500, function() {
						$(".submenu").removeClass("active");
					});
					$(".page_container").animate({
						"left" : "13px"
					}, 500);

				} else if ($(this).attr('class') != "selected") {

					$(".parent-menu li span a").removeClass("selected")
					$(this).addClass("selected")
					$(body).addClass("slideout");
					$(".submenu").animate({
						"left" : "114px"
					}, 500, function() {

					});
					$(".page_container").animate({
						"left" : "241px"
					}, 500);
					$(".submenu").removeClass("active");
					$(this).parent().siblings(".submenu").addClass("active");

				}

			});
		</script>
		<!-- change script -->

		<script>          
			 $(window).bind("load", function() {

				var footerHeight = 0,
				    footerTop = 0,
				    $footer = $("#footer");
                    positionFooter();
				function positionFooter() {
					footerHeight = $footer.height();
					footerTop = ($(window).scrollTop() + $(window).height() - footerHeight - 0) + "px";

					if (($(document.body).height() + footerHeight) < $(window).height()) {
						$footer.css({
							position : "absolute",
							bottom : 0,
							left : 0

						})
					} else {
						$footer.css({
							position : "relative"
						})
					}
				}


				$(window).resize(positionFooter)
				$(document).ready(positionFooter)
				$(".page-container").mouseover(positionFooter)
				$(".admin").mouseover(positionFooter)

			});

		</script>
        
        <script>
            $("#next-btn").on('click', function(e) {
                
				e.preventDefault();

                var page = $(this).attr('data-page');
                var next = $(this).attr('data-next');
                
                if(page == 'index'){
                    window.location.href = '/wizard/'+next;
                }
                
                var formData = {}; 

				var products = [];

                $(".products").each(function(index, el) {
                	
                	var temp = {};
                	temp.id = $(this).find("select").val();
                	temp.quantity = $(this).find("input[name=quantity]").val();
                	products.push(temp);
                });

                $.each($('#form-container :input'), function() {
                    
                    if($(this).is(":checkbox")) {

                        if(!formData.hasOwnProperty(this.name)) formData[this.name] = [];
                    
                        var chkbox = this; 
                       
                        $.each($(this), function(){
                            if($(this).is(":checked")){
                                formData[chkbox.name].push(this.value);
                            }
                        });

                    }else if($(this).is(":radio")) {
                        
                        if($(this).is(":checked")){
                            formData[this.name] = this.value;
                        }
                    }else{
                      if(this.name=='product'){
                      	formData[this.name] = products; 
                      }else{
                      	formData[this.name] = this.value; 	
                      }
                      
                    }
                    $("#"+$(this).attr('name')+'_err').html('');
                });  
                
                $.ajax({

                    url:"<?php echo $this->url('wizard',array('action' => 'ajx-update-keys')); ?>",
                    type: "POST",
                    data: { page : page, formData : JSON.stringify(formData) },
                    success:function(response)
                    {
                        if(response.status == 'error'){
                            
                            $.each(response.messages, function(key, msg){
                                
                                $("#"+key+'_err').html(msg);
                            });
                            
                        }else if(response.status == 'success'){
                            $("#common-content").html('').append(response.data);
                            $('#common-modal').modal('show');   
                        }else{
                            console.debug("debugger on."); 
                        }
                        
                    }
               });
            });
            var url      = window.location.href;                    
            var value = url.substring(url.lastIndexOf('/') + 1);              
            
            $('#common-modal').on('hidden.bs.modal', function () {
                if(value == 'location'){
                    window.location.href = '/wizard/location';
                }else{
                    window.location.href = '/wizard/<?php echo $this->next; ?>';
                }
            })
            
        </script>


	</body>
</html>
