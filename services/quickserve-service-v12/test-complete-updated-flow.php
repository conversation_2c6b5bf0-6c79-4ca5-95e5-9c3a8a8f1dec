<?php
/**
 * Test complete updated order flow with new start_date and selected_days
 * This simulates the entire flow from order creation to payment success
 */

require_once __DIR__ . '/vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

$client = new Client([
    'base_uri' => 'http://*************:8003/api/',
    'timeout' => 30,
]);

echo "=== Testing Complete Updated Order Flow ===\n\n";

// Step 1: Create order with new structure
echo "Step 1: Creating order with new start_date and selected_days structure...\n";

$testData = [
    'customer_id' => 3802,
    'customer_name' => 'Complete Flow Test User',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '919998887781',
    'customer_address' => '789 Flow Test Street, Test Area, Test City - 400003',
    'location_code' => 1001,
    'location_name' => 'Test Location',
    'city' => 1,
    'city_name' => 'Mumbai',
    'product_code' => 342,
    'product_name' => 'International Breakfast Subscription',
    'product_type' => 'Meal',
    'quantity' => 1,
    'amount' => 200.00,
    'start_date' => '2025-07-28', // Monday
    'selected_days' => [1, 3, 5], // Monday, Wednesday, Friday
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'subscription_days' => 6 // Should create 6 orders: 2 Mon, 2 Wed, 2 Fri
];

try {
    $response = $client->post('v2/order-management/create', [
        'json' => $testData,
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ]
    ]);
    
    $responseData = json_decode($response->getBody(), true);
    
    if (!$responseData['success']) {
        echo "❌ Order creation failed: " . $responseData['message'] . "\n";
        exit(1);
    }
    
    $orderNo = $responseData['data']['order_no'];
    $transactionId = $responseData['data']['payment_service_transaction_id'];
    
    echo "✅ Order created successfully!\n";
    echo "Order No: $orderNo\n";
    echo "Transaction ID: $transactionId\n\n";
    
    // Step 2: Check pre-order status
    echo "Step 2: Checking pre-order status and delivery dates...\n";
    
    $statusResponse = $client->get("v2/order-management/pre-order-status/$orderNo");
    $statusData = json_decode($statusResponse->getBody(), true);
    
    if ($statusData['success']) {
        $orderDays = $statusData['data']['pre_order']['order_days'];
        echo "✅ Pre-order status retrieved successfully\n";
        echo "Calculated delivery dates: " . implode(', ', $orderDays) . "\n";
        echo "Total delivery days: " . count($orderDays) . "\n\n";
        
        // Verify the dates are correct (Monday, Wednesday, Friday only)
        $correctDays = true;
        foreach ($orderDays as $date) {
            $dayOfWeek = date('N', strtotime($date)); // 1=Monday, 3=Wednesday, 5=Friday
            if (!in_array($dayOfWeek, [1, 3, 5])) {
                $correctDays = false;
                echo "❌ Incorrect day found: $date (day of week: $dayOfWeek)\n";
            }
        }
        
        if ($correctDays) {
            echo "✅ All delivery dates are correct (Monday, Wednesday, Friday only)\n\n";
        }
    } else {
        echo "❌ Failed to get pre-order status\n";
        exit(1);
    }
    
    // Step 3: Simulate payment success
    echo "Step 3: Simulating payment success...\n";
    
    $paymentSuccessData = [
        'payment_service_transaction_id' => $transactionId,
        'gateway_transaction_id' => 'RAZORPAY_' . time(),
        'payment_amount' => 200.00,
        'gateway' => 'razorpay',
        'payment_status' => 'success'
    ];
    
    $paymentResponse = $client->post("v2/order-management/payment-success/$orderNo", [
        'json' => $paymentSuccessData
    ]);
    
    $paymentData = json_decode($paymentResponse->getBody(), true);
    
    if ($paymentData['success']) {
        echo "✅ Payment success processed!\n";
        echo "Orders created: " . $paymentData['data']['orders_created'] . "\n";
        echo "Order details created: " . $paymentData['data']['order_details_created'] . "\n\n";
    } else {
        echo "❌ Payment success failed: " . $paymentData['message'] . "\n";
        exit(1);
    }
    
    // Step 4: Verify final order status
    echo "Step 4: Verifying final order status...\n";
    
    $finalStatusResponse = $client->get("v2/order-management/pre-order-status/$orderNo");
    $finalStatusData = json_decode($finalStatusResponse->getBody(), true);
    
    if ($finalStatusData['success']) {
        $ordersCreated = $finalStatusData['data']['orders_created'];
        echo "✅ Final status retrieved\n";
        echo "Orders created count: " . $ordersCreated['count'] . "\n";
        echo "Order details count: " . $ordersCreated['order_details_count'] . "\n";
        echo "Status: " . $ordersCreated['status'] . "\n\n";
        
        if ($ordersCreated['count'] == 6 && $ordersCreated['status'] == 'completed') {
            echo "✅ All orders created successfully!\n";
        } else {
            echo "❌ Order creation incomplete\n";
        }
    }
    
    // Step 5: Check individual order details
    echo "Step 5: Checking individual order details...\n";
    
    $detailsResponse = $client->get("v2/order-management/details/$orderNo");
    $detailsData = json_decode($detailsResponse->getBody(), true);
    
    if ($detailsData['success']) {
        echo "✅ Order details retrieved\n";
        echo "Meal items count: " . count($detailsData['data']['meal_items']) . "\n";
        
        // Show meal items
        echo "Meal items:\n";
        foreach ($detailsData['data']['meal_items'] as $item) {
            echo "  - {$item['product_name']} (Code: {$item['product_code']}, Qty: {$item['quantity']})\n";
        }
    }
    
    echo "\n=== Test Completed Successfully ===\n";
    
} catch (RequestException $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "\n";
    
    if ($e->hasResponse()) {
        $errorResponse = $e->getResponse();
        $errorBody = json_decode($errorResponse->getBody(), true);
        echo "Error details:\n";
        echo json_encode($errorBody, JSON_PRETTY_PRINT) . "\n";
    }
}
