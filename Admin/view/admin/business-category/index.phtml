<?php
$title = 'Business Categories';
$this->headTitle ( $title );
?>
<div class="content">
	<div class="contenttitle radiusbottom0">
		<h2 class="image">
			<span><?php echo $this->escapeHtml($title); ?></span>
		</h2>
	</div>

	<div class="dataTables_add" id="dyntable_filter">
		<a href="<?php echo $this->url('business', array('action'=>'add'));?>"
			class="btn btn_add"><span>Add
			new category</span></a>
	</div>

	<table cellpadding="0" cellspacing="0" border="0" class="stdtable"
		id="dyntable">
		<colgroup>
			<col class="con0" />
			<col class="con1" />
			<col class="con0" />
			<col class="con1" />
			<col class="con0" />
			<col class="con1" />
			<col class="con0" />
			<col class="con1" />
		</colgroup>
		<thead>
			<tr>
				<td class="head0">Name</td>
				<td class="head1">Description</td>
				<td class="head1 center">Action</td>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td class="head0">Name</td>
				<td class="head1">Description</td>
				<td class="head1 center">Action</td>
			</tr>
		</tfoot>
		<tbody>
			<?php foreach ($paginator as $category) :
			//echo "<pre>";print_r($category);echo "</pre>";//die;
			?>
			<tr>
				<td><?php echo $this->escapeHtml($category->business_name);?></td>
				<td><?php echo $this->escapeHtml($category->business_description);?></td>
				<td><a
					href="<?php

				echo $this->url ( 'business', array (
				'action' => 'edit',
				'id' => $category->id
				) );
				?>" class="btn btn5 btn_pencil5"></a>
					<a
					href="#" class="btn btn5 btn_trash5" rel="category" id="cat_<?=$category->id?>"></a>&nbsp;
					<?php
						$img  = ($category->status=='1') ? "/admin/images/icons/Check.png" : "/admin/images/icons/Remove.png";
					?>
					<img style="border: 1px solid #CCCCCC;padding:4px;cursor:pointer;" src="<?=$img?>" class="catStatus" title="Status" rel="active_<?=$category->id?>_<?=$category->status?>" />
				</td>
			</tr>
	<?php endforeach;
	?>
	</tbody>
	</table>
	<br/>
	<?php
	echo $this->paginationControl(
    // the paginator object
    $this->paginator,
    // the scrolling style; see http://zf2.readthedocs.org/en/release-2.1.4/modules/zend.paginator.usage.html#rendering-pages-with-view-scripts
    'sliding',
    // the partial to use to render the control
    array('partial/paginator.phtml', 'Album'),
    // the route to link to when a user clicks a control link
    array(
        'route' => 'business'
    )
);
?>
	<br />
</div>
<script type="text/javascript">
(function ($) {

	$(document).ready(function(){

		$(".btn_trash5").on('click',function(){

		var catIdStr = $(this).attr("id");
		var catIdArr = catIdStr.split("_");
		var catId = catIdArr[1];

			if(confirm("Do you really want to delete category?")){
				$.ajax({
					type: "POST",
					url: "/business-category/delete/"+catId,
					data: { del: "Yes" }
				})
				.done(function( msg ) {
					alert(msg.msg);
					window.location.href = '/business-category';
					/*
					window.setTimeout(function() {
					    window.location.href = '/business-category';
					}, 2000);*/
				});
			}

			return false;

	});

	$(".catStatus").on('click',function() {

		var catIdStr = $(this).attr("rel");
		var catIdArr = catIdStr.split("_");
		var catId = catIdArr[1];
		var curStatus = catIdArr[2];

		var obj = $(this);

			if(confirm("Do you really want to change status?")){
				$.ajax({
					type: "POST",
					url: "/business-category/status/"+catId,
					data: { stat: "Yes",curStatus:curStatus }
				})
				.done(function( msg ) {
					alert(msg.msg);
					if(curStatus==1){
						obj.attr("src","/admin/images/icons/Remove.png");
						obj.attr("rel","active_"+catId+"_0");
					}else{
						obj.attr("src","/admin/images/icons/Check.png");
						obj.attr("rel","active_"+catId+"_1");
					}

				});
			}

			return false;

		});
	});
})(jQuery);
</script>