<?php

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Lib\QuickServe\CommonConfig as QSCommon;
use Zend\Session\Container;
use Zend\View\Model\JsonModel;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;


/**
 * This controller is used to set up the client portal.
 * All the BRD requirements are to be incorporated into the software 
 * with this controller.
 *
 * PHP 7
 *
 * Project name FoodDialer
 * @version 1.0
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> Tambe <<EMAIL>>
 * @since File available since Release 1.0
 */
class WizardController extends AbstractActionController{
    
    /**
     * locationTable object
     */
    public $locationTable;
    
    /**
     * sms set id
     */
    public $sms_set_id = 1;
    
    public $mealTable;
    
    public $payment_modes = ['wallet','cash','online'];
    
    public $payment_gateways = [
                'payu'          => ['GATEWAY_PAYU_MERCHANT_ID', 'GATEWAY_PAYU_MERCHANT_KEY', 'GATEWAY_PAYU_MERCHANT_SALT'],
                'instamojo'     => ['GATEWAY_INSTAMOJO_MERCHANT_KEY', 'GATEWAY_INSTAMOJO_MERCHANT_TOKEN'],
                'paytm'         => ['GATEWAY_PAYTM_MERCHANT_CHANNEL', 'GATEWAY_PAYTM_MERCHANT_INDUSTRY', 'GATEWAY_PAYTM_MERCHANT_KEY', 'GATEWAY_PAYTM_MERCHANT_MID', 'GATEWAY_PAYTM_MERCHANT_WEBSITE'],
                'payeezy' => ['PAYEEZY_HCO_LOGIN', 'PAYEEZY_HCO_TRANSACTION_KEY','GATEWAY_PAYEEZY_ID','GATEWAY_PAYEEZY_KEY','GATEWAY_PAYEEZY_SECRET','GATEWAY_PAYEEZY_HMAC_KEY'],
                'mobikwik' => ['GATEWAY_MOBIKWIK_MERCHANT_NAME', 'GATEWAY_MOBIKWIK_MERCHANT_ID','GATEWAY_MOBIKWIK_MERCHANT_KEY'],
                'paypal' => ['GATEWAY_PAYPAL_USER', 'GATEWAY_PAYPAL_SECRET','GATEWAY_PAYPAL_SIGNATURE'],
                'coverge' => ['GATEWAY_CONVERGE_MERCHANT_ID', 'GATEWAY_CONVERGE_USER_ID','GATEWAY_CONVERGE_PIN']
            ];
    
    /**
     * this returns the wizard view.
     * @return \Admin\Controller\ViewModel
     */
    public function indexAction() {
        
        $view = new ViewModel();
        
        $this->layout()->setVariables(array('next' => 'company', 'page' => 'index'));
        
        return $view;
    }
    
    /**
     * returns step1 details.
     * 
     */
    function companyAction(){
        
        $this->layout()->setVariables($this->getStepDetails(1));
        
        $setting_session = new Container('setting');
        $libCommon = QSCommon::getInstance($this->getServiceLocator());
    	$cities = $libCommon->getCity();
        
        return new ViewModel(array(
            'settings' => $setting_session->setting,
            'cities' => $cities
        ));
    }
    
    /**
     * returns step2 details.
     * 
     */
    function paymentAction(){
         
        $this->layout()->setVariables($this->getStepDetails(2));
        
        $setting_session = new Container('setting');
       // dd($setting_session->setting['GLOBAL_CUSTOMER_PAYMENT_MODE']);
        return new ViewModel(array(
            'payment_mode' => $this->payment_modes,
            'payment_gateway' => $this->payment_gateways,
            'settings' => $setting_session->setting,
//            'is_online_enabled' => (strpos($setting_session->setting['GLOBAL_CUSTOMER_PAYMENT_MODE'], 'online')  !== false)
            'is_online_enabled' => (in_array('online',$setting_session->setting['GLOBAL_CUSTOMER_PAYMENT_MODE']))
        ));
        
    }
    
    /**
     * returns step3.1 details.
     * 
     */
    function configAction(){
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
        $this->menu_type = $setting['MENU_TYPE'];
        // dd($this->menu_type);
        $this->food_type = $setting['FOOD_TYPE'];
        $this->layout()->setVariables($this->getStepDetails(3.1));
        $setting_session = new Container('setting');
        $configs = array('K1_MENU_TYPE', 'Food_type', 'WEEKOFF','GLOBAL_TAX_METHOD', 'GLOBAL_MIN_ORDER_PRICE','GLOBAL_MAX_ORDER_PRICE');
        
        return new ViewModel(array(
            'menu_types' => $this->menu_type,
            'food_type' => $this->food_type,
            'settings' => $setting_session->setting,
        ));
        
    }
    
    /**
     * returns step3.2 details.
     * 
     */
    function configAdvancedAction(){
       
        $this->layout()->setVariables($this->getStepDetails(3.2));
        
        $setting_session = new Container('setting');

        $libCommon = QSCommon::getInstance($this->getServiceLocator());

        //dd($libCommon->getLabelTemplates()->toArray());
        //$_SESSION['wizard']['template'] = $formData['PRINT_LABEL_TEMPLATE'];


        return new ViewModel(array(
            'settings' => $setting_session->setting,
            'label_templates' => $libCommon->getLabelTemplates()->toArray(),
            'themes' => $libCommon->getThemes(),
        ));
       
        //$_SESSION['wizard']['template'] = $formData['pk_template_id'];
        
    }
    
    /**
     * returns step3.3 details.
     * 
     */
    function configNotificationsAction(){
       
        $this->layout()->setVariables($this->getStepDetails(3.3));
                
        $setting_session = new Container('setting');
        
        if(!array_key_exists('notifications', $_SESSION['wizard']) ){
           
            $_SESSION['wizard']['notifications'] = array(
                'new_registration'  => 'no', 
                'order_booking'     => 'no', 
                'order_confirmation' => 'no', 
                'order_cancellation' => 'no', 
                'pre_order_renewal' => 'no'
            );
        }
        
        return new ViewModel(array(
            'settings' => $setting_session->setting,
        ));
    }
    
    /**
     * returns step4.1 details.
     * 
     */
    function catalogAction(){
        
        $view = new ViewModel();
        
        $this->layout()->setVariables($this->getStepDetails(4.1));
        
        return $view;
    }
    
    /**
     * returns step4.2 details.
     * 
     */
    function catalogFirstmealAction(){

        $this->layout()->setVariables($this->getStepDetails(4.2));
        
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
         
        $mealTableObj = $this->getServiceLocator()->get('QuickServe\Model\MealTable');
        $mealPlan = $this->getServiceLocator()->get('QuickServe\Model\PlanMasterTable');       
        $objProduct = $this->getServiceLocator()->get('QuickServe\Model\ProductTable');
        
        $select = new QSelect();
		$select->where("product_type IN ('Main','Extra') AND status='1' AND product_subtype='generic'");
		$select->order("name asc");
     	
        $mealProduct = $objProduct->fetchAll($select, null, null, null, null, null, $_SESSION['adminkitchen'], null, 'yes');
			
		$arrProds = array();		
		foreach($mealProduct as $prodkey){            
			$arrProds[$prodkey->pk_product_code] = $prodkey->name;
		}               
        
       $menu_type = $setting['MENU_TYPE'];
       
        $plans = $mealPlan->fetchAll();
        $mealItems = $mealTableObj->getMeal(336)->getItems();
        $meal = $mealTableObj->getMeal(336);
       // dd($meal);
        return new ViewModel(array(
            'settings' => $session_setting->setting,
            'meal' => $meal,
            'products' => $arrProds,
            'mealItems' => $mealItems,
            'plans' => $plans, 
            'menu_type' => $menu_type,
        ));
    }
    
    /**
     * returns step5 details.
     * 
     * @todo search in url query parameter. This is for using pagination in search
     */
    function locationAction(){
        
        $this->layout()->setVariables($this->getStepDetails(5));
        
        $page = (int)$this->params()->fromQuery('page', 1);
        
        $paginator = $this->getLocations($page);
        
        return new ViewModel(array(
            'paginator' => $paginator,
            'page' => $page
        ));
    }
    
    /**
     * returns finish page.
     * 
     */
    function finishAction(){
        
        $view = new ViewModel();
        
        $this->layout()->setVariables($this->getStepDetails('finish'));
        
        return $view;
    }
    
    /**
     * returns template, variables, etc for each step
     * 
     * @param mixed $step
     * @return array
     */
    function getStepDetails($step){
        
        $details = array('page' => $step);
        
        switch ($step) {
            case 1:
                $details['template']    = 'admin/wizard/company';
                $details['next']        = 'payment';
                break;
            case 2:
                $details['template']    = 'admin/wizard/payment';
                $details['next']        = 'config';
                $details['previous']    = 'company';
                break;
            case 3.1:
                $details['template']    = 'admin/wizard/config' ;
                $details['next']        = 'config-advanced';
                $details['previous']    = 'payment';
                break;
            case 3.2:
                $details['template']    = 'admin/wizard/config_advanced' ;
                $details['next']        = 'config-notifications';
                $details['previous']    = 'config';
                break;
            case 3.3:
                $details['template']    = 'admin/wizard/config_notifications' ;
                $details['next']        = 'catalog';
                $details['previous']    = 'config-advanced';
                break;
            case 4.1:
                $details['template']    = 'admin/wizard/catalog' ;
                $details['next']        = 'catalog-firstmeal';
                $details['previous']    = 'config-notifications';
                break;
            case 4.2:
                $details['template']    = 'admin/wizard/catalog_firstmeal' ;
                $details['next']        = 'location';
                $details['previous']    = 'catalog';
                break;
            case 5:
                $details['template']    = 'admin/wizard/location';
                $details['next']        = 'finish';
                $details['previous']    = 'catalog-firstmeal';
                break;
            case 'finish':
                $details['template']    = 'admin/wizard/finish';
                break;

            default:
                $details['template']    = 'admin/wizard/error';
                break;
        }
      
        return $details;
    }
    
    /**
     * updates all the keys in settings
     */
    function ajxUpdateKeysAction(){
         
        $param = $this->getRequest()->getPost();
      
        $response = array();
      
        $formData = json_decode(($param['formData']),true);

        switch ($param['page']) {
            case 1: // company
                $response = $this->setCompanyDetails($formData);
                break;
            case 2: /* payment */
                 $response = $this->setPaymentDetails($formData);
                break;
            case 3.1:
                 $response = $this->setConfigDetails($formData);
                break;
            case 3.2:
                 $response = $this->setConfigAdvancedDetails($formData);
                break;
            case 3.3:
                 $response = $this->setNotificationDetails($formData);
                break;
            case 4.1:
                break;
            case 4.2:
                 $mealTableObj = $this->getServiceLocator()->get('QuickServe\Model\MealTable');
                 $response = $this->setCatalogFirstmeal($formData, $mealTableObj->getMeal(336));
                break;
            case 5:
                break;
            case 'finish':
                
                break;
            default:
                
                break;
        }
        
        return new JsonModel($response);
       
    }
    
    /**
     * updates company fields in settings table
     * 
     * @param   array $formData         serialized form data
     * @return  array
     */
    function setCompanyDetails(array $formData){

        $status = 'success'; $messages = array();
        $validLength = new \Zend\Validator\StringLength(array('max' => 150));

        if(!array_key_exists('MERCHANT_COMPANY_NAME', $formData) || empty($formData['MERCHANT_COMPANY_NAME'])){
            $status = 'error';
            $messages['MERCHANT_COMPANY_NAME'] = 'Company name is required';
        }

        if(!array_key_exists('MERCHANT_POSTAL_ADDRESS', $formData) || empty($formData['MERCHANT_POSTAL_ADDRESS']) ){
            $status = 'error';
            $messages['MERCHANT_POSTAL_ADDRESS'] = 'Company address is required';
        }

        if(!$validLength->isValid($formData['MERCHANT_POSTAL_ADDRESS']) ){
            $status = 'error';
            $messages['MERCHANT_POSTAL_ADDRESS'] = 'Company address can not exceed 150 characters';
        }
        
        if(!array_key_exists('GLOBAL_WEBSITE_PHONE', $formData) || empty($formData['GLOBAL_WEBSITE_PHONE']) ){
            $status = 'error';
            $messages['GLOBAL_WEBSITE_PHONE'] = 'Contact Detail(Phone number is required)';
        }
        if (!preg_match('/^(?:\+)91\h*-\h*\d{10}$/', $formData['GLOBAL_WEBSITE_PHONE'])){
            $status = 'error';
            $messages['GLOBAL_WEBSITE_PHONE'] = 'Invalid Phone Number.(Valid Format - +91-xxxxxxxxxx)';
        }
        
        if(!array_key_exists('MERCHANT_SUPPORT_EMAIL', $formData) || empty($formData['MERCHANT_SUPPORT_EMAIL']) ){
            $status = 'error';
            $messages['MERCHANT_SUPPORT_EMAIL'] = 'Email Id is required';
        }
        if(!filter_var($formData['MERCHANT_SUPPORT_EMAIL'], FILTER_VALIDATE_EMAIL) ){
            $status = 'error';
            $messages['MERCHANT_SUPPORT_EMAIL'] = 'Invalid Email Id';
        }

        if(!array_key_exists('city', $formData) || empty($formData['city']) ){
            $status = 'error';
            $messages['city'] = 'Please select city';
        }
      
        if(!array_key_exists('MERCHANT_SENDER_ID', $formData) || empty($formData['MERCHANT_SENDER_ID']) ){
            $status = 'error';
            $messages['MERCHANT_SENDER_ID'] = 'SMS sender id is required';
        }
        
        $company_details = array('MERCHANT_COMPANY_NAME','MERCHANT_POSTAL_ADDRESS','GLOBAL_WEBSITE_PHONE','MERCHANT_SUPPORT_EMAIL','city','MERCHANT_SENDER_ID');

        foreach($company_details as $company){  
           
            if(!array_key_exists($company, $formData) || empty($formData[$company])){
                $status = 'error';
                $messages[$company] = str_replace('_', ' ', $company). " field is required";
            }
        }  
        if($status == 'success'){            
            /* 1. update $settings in database */
            $sm = $this->getServiceLocator();
            $sql = new QSql($sm);
           
            $query = "UPDATE settings "
                . "SET `value` = (case ";
            
            foreach($formData as $key => $value){
                
                  $query.=  " when `key` = '".$key."' then '".$value. "'";
            }
            
            $query .= " end) WHERE `key` in ("
                ."'". implode("','",array_keys($formData))."'" 
                . ")";
            
            $result2 = $sql->execQuery($query,'query');
           
            /* 2. update $_SESSION settings variable */

            $setting_session = new Container('setting');
            $setting_session->setting->offsetSet('MERCHANT_COMPANY_NAME',$formData ['MERCHANT_COMPANY_NAME']);   
            $setting_session->setting->offsetSet('GLOBAL_WEBSITE_PHONE', $formData ['GLOBAL_WEBSITE_PHONE']);   
            $setting_session->setting->offsetSet('MERCHANT_SUPPORT_EMAIL', $formData ['MERCHANT_SUPPORT_EMAIL']);   
            $_SESSION['wizard']['city'] = $formData['city'];
            $setting_session->setting->offsetSet('MERCHANT_SENDER_ID', $formData ['MERCHANT_SENDER_ID']);   
           
            return array('status' => 'success','data' => 'Company details updated successfully.');
        }
        /* error response */
        return array('status' => 'error', 'messages' => $messages);

    }

    /**
     * returns all locations for city.
     * 
     * @param integer $page
     * @param string $search
     * @return $object Paginator object.
     */
    function getLocations($page = 1, $search = null){
        
        $per_page = 5;
        
        $select = new \Lib\QuickServe\Db\Sql\QSelect();
        
        $select->where(array('delivery_locations.city' => 1));
        
        if($search){
            $select->where(
				new \Zend\Db\Sql\Predicate\PredicateSet(
                    array(
                        new \Zend\Db\Sql\Predicate\Operator('location', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('delivery_charges', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('delivery_time', 'LIKE', '%'.$search.'%'),
                    ),
                    \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
            );
        }
        
        $paginator = $this->getLocationTable()->fetchAll($select, true);
        
        return $paginator->setCurrentPageNumber($page)->setItemCountPerPage($per_page)->setPageRange(7);
        
    }
    
    /**
     * search location.
     * 
     * @return ViewModel
     */
    function searchLocationAction(){
        
        $param = $this->getRequest()->getPost();
        
        $locations = $this->getLocations(1, $param['search']);
        
        $view = new ViewModel();
        
        $view->setTemplate('admin/wizard/location-list.phtml')->setTerminal(true);
        
//        $view->setVariables(array( 'paginator' => $locations, 'paginatorControl' => $this->paginationControl));
        $view->setVariables(array( 'paginator' => $locations));
        
        return $view;
    }

    /**
     * updates location by id
     * @return JsonModel
     */
    function editLocationAction(){
        
        $param = $this->getRequest()->getPost();
        
        $location = $this->getLocationTable()->getLocation($param['pk_location_code']);

        list($status, $messages) = $this->locationValidation($param);

        if($status == 'success'){
            
            $location->location = $param['name'];
            $location->status = $param['status'];
            $location->delivery_charges = $param['charges'];
            $location->delivery_time = $param['time'];
            
            $locationValidator = new \QuickServe\Model\LocationValidator();

            $locationValidator->exchangeArray($location);
            
            $this->getLocationTable()->saveLocation($locationValidator);
            
            return new JsonModel(array('status' => $status, 'data' => 'Location updated successfully.'));
        }else{
            return new JsonModel(array('status' => $status, 'data' => $messages));
        }
    }
    
    /**
     * adds a new location
     * @return JsonModel
     */
    function addLocationAction(){
        
        $param =  json_decode(($this->getRequest()->getPost()['params']),true); $this->getRequest()->getPost();
        
        foreach($param as $field){
            $data[str_replace("location-", "", $field['name'])] = $field['value'];
        }
        
        list($status, $messages) = $this->locationValidation($data);
        
        if($status == 'success'){
            
            $location = new \QuickServe\Model\LocationValidator();
            
            $location->location = $data['name'];
            $location->delivery_charges = $data['charges'];
            $location->delivery_time = $data['time'];
            $location->status = 1;
            $location->city = 1;
            $location->fk_kitchen_code = 1;

            $this->getLocationTable()->saveLocation($location);
            
            return new JsonModel(array('status' => $status, 'data' => 'Location updated successfully.'));
        }else{
            return new JsonModel(array('status' => $status, 'data' => $messages));
        }
    }

    /**
     * validates a location entry.
     * 
     * @param array $param      array of parameters for location.
     * @return array
     */
    function locationValidation($param){
        
        $status = 'success'; $messages = array();
        
        if( empty($param['name']) ){
            $status = 'error';
            $messages['location'] = "Location name is required.";
        }
        
        if( empty($param['charges'])){
            $status = 'error';
            $messages['charges'] = "Delivery charges are required.";
        }else{
            if(!is_numeric($param['charges'])){
                $status = 'error';
                $messages['charges'] = "Charges should be a numeric value.";
            }
        }
        
        if( empty($param['time']) ){
            $status = 'error';
            $messages['time'] = "Delivery time is required.";
        }else{
            if(!is_numeric($param['time'])){
                $status = 'error';
                $messages['time'] = "Time should be a numeric value.";
            }

        }
        
        return array($status, $messages);
    }
    
    /**
	 * Get instance of QuickServe\Model\LocationTable
	 *
	 * @return QuickServe\Model\LocationTable
	 */
	public function getLocationTable(){
		if (!$this->locationTable) {
			$sm = $this->getServiceLocator();
			$this->locationTable = $sm->get('QuickServe\Model\LocationTable');
		}
		return $this->locationTable;
	}
    
    /**
     * set payment details.
     * 
     * @param array $formData
     * @return type
     */
    function setPaymentDetails(array $formData){
        
        $status = 'success'; $messages = array();
        
        if(!array_key_exists('GLOBAL_CUSTOMER_PAYMENT_MODE', $formData) || empty($formData['GLOBAL_CUSTOMER_PAYMENT_MODE'])){
            $status = 'error';
            $messages['GLOBAL_CUSTOMER_PAYMENT_MODE'] = 'Select atleast one payment mode.';
        }
        
        if(!array_key_exists('APPLY_GATEWAY_TRANSACTION_CHARGES', $formData) || empty($formData['APPLY_GATEWAY_TRANSACTION_CHARGES']) ){
            $status = 'error';
            $messages['APPLY_GATEWAY_TRANSACTION_CHARGES'] = 'This field is required.';
        }
        
        if($formData['APPLY_GATEWAY_TRANSACTION_CHARGES'] == 'yes' && (!array_key_exists('GATEWAY_TRANSACTION_CHARGES_AMOUNT', $formData) || empty($formData['GATEWAY_TRANSACTION_CHARGES_AMOUNT']) ) ){
            $status = 'error';
            $messages['GATEWAY_TRANSACTION_CHARGES_AMOUNT'] = 'Transaction charges is required.';
        }        
        /*
         * Payment Gateway Validation
         */
        $payment_gateway = array('GATEWAY_PAYU_MERCHANT_ID', 'GATEWAY_PAYU_MERCHANT_KEY', 'GATEWAY_PAYU_MERCHANT_SALT','GATEWAY_INSTAMOJO_MERCHANT_KEY', 'GATEWAY_INSTAMOJO_MERCHANT_TOKEN', 'GATEWAY_PAYTM_MERCHANT_CHANNEL', 'GATEWAY_PAYTM_MERCHANT_INDUSTRY', 'GATEWAY_PAYTM_MERCHANT_KEY', 'GATEWAY_PAYTM_MERCHANT_MID', 'GATEWAY_PAYTM_MERCHANT_WEBSITE');                
        if(!empty($formData['GLOBAL_CUSTOMER_PAYMENT_MODE']) && in_array('online', $formData['GLOBAL_CUSTOMER_PAYMENT_MODE']) ){
            
            if(empty($formData['ONLINE_PAYMENT_GATEWAY'])){
                $status = 'error';
                $messages['ONLINE_PAYMENT_GATEWAY'] = 'Select atleast one payment gateway.';
            }else{
                foreach($formData['ONLINE_PAYMENT_GATEWAY'] as $gateway){
                    foreach($payment_gateway as $gateway => $param){
                        if(!array_key_exists($param, $formData) || $formData[$param] == ''){
                            $status = 'error';
                            $messages[$param] = str_replace('_', ' ', str_replace('GATEWAY_' ,'', $param) ).' is required.';
                        }
                    }
                }
            }  
        }
        
        if($status == 'success'){
              
            /*1. update settings table */
            $this->massUpdateQuery($formData, 'settings', 'key', 'value');

            /* 2. update $_SESSION settings variable */
            $setting_session = new Container('setting');
            
            $setting_session->setting->offsetSet('GLOBAL_CUSTOMER_PAYMENT_MODE',  $formData['GLOBAL_CUSTOMER_PAYMENT_MODE']);
            $setting_session->setting->offsetSet('APPLY_GATEWAY_TRANSACTION_CHARGES',  $formData['APPLY_GATEWAY_TRANSACTION_CHARGES']);
            $setting_session->setting->offsetSet('GATEWAY_TRANSACTION_CHARGES_AMOUNT',  $formData['GATEWAY_TRANSACTION_CHARGES_AMOUNT']);
            
            if(!empty($formData['GLOBAL_CUSTOMER_PAYMENT_MODE']) && in_array('online', $formData['GLOBAL_CUSTOMER_PAYMENT_MODE']) ){
            
                foreach($formData['ONLINE_PAYMENT_GATEWAY'] as $gateway){
                    foreach($payment_gateway as $gateway => $param){
                        $setting_session->setting->offsetSet($param,  $formData[$param]);
                    }
                }
            }
            
            return array('status' => 'success','data' => 'Payment details updated successfully.');
        }
        /* error response */
        return array('status' => 'error', 'messages' => $messages);
        
    }
    
    function setConfigDetails($formData){
        
        $configs = array('K1_MENU_TYPE', 'food_type', 'WEEKOFF','GLOBAL_TAX_METHOD', 'GLOBAL_MIN_ORDER_PRICE','GLOBAL_MAX_ORDER_PRICE');

        $status = 'success'; $messages = array();
      
        foreach($configs as $config){

            if(!array_key_exists($config, $formData) || !isset($formData[$config])){
                $status = 'error';
                $messages[$config] = str_replace('_', ' ', $config). " field is required";
            }
        }
        
        $weekOffs = array(
    			'mf'=>'Monday - Friday',
    			'ms'=>'Monday - Saturday',
    			'msu'=>'Monday - Sunday',
    	);
       $_SESSION['wizard']['WEEKOFF']=$formData['WEEKOFF'];

         if($status == 'success'){
            
            $_SESSION['wizard'] = $formData;

            /* update setting */
            $this->massUpdateQuery($formData, 'settings', 'key', 'value');

            return array('status' => 'success', 'data' => 'Config updated successfully.');
        
        }
         /* error response */
        return array('status' => 'error','weekoffs' => $weekOffs,'setweekoff'=>(isset($weekOff[0]) && !empty($weekOff[0]))?$weekOff[0]:'', 'messages' => $messages);

    }

    function setConfigAdvancedDetails(array $formData){
        $status = 'success'; $messages = array();
         
        if(!array_key_exists('GLOBAL_ALLOW_MEAL_SWAP', $formData) || empty($formData['GLOBAL_ALLOW_MEAL_SWAP'])){
         
            $status = 'error';
            $messages['GLOBAL_ALLOW_MEAL_SWAP'] = 'Select meal swap option.';
        }
        
        if(!array_key_exists('GLOBAL_ALLOW_MEAL_ITEM_SWAP', $formData) || empty($formData['GLOBAL_ALLOW_MEAL_ITEM_SWAP'])){
          
            $status = 'error';
            $messages['GLOBAL_ALLOW_MEAL_ITEM_SWAP'] = 'Select customers to set item preference.';
        }
        
        if(!array_key_exists('GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION', $formData) || empty($formData['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'])){
         
            $status = 'error';
            $messages['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'] = 'Select extra item swap.';
        }
        
        if(!array_key_exists('GLOBAL_SKIP_KITCHEN', $formData) || empty($formData['GLOBAL_SKIP_KITCHEN'])){
          
            $status = 'error';
            $messages['GLOBAL_SKIP_KITCHEN'] = 'Select skip kitchen.';
        }
        
        if(!array_key_exists('GLOBAL_ALLOW_INSTANT_ORDER', $formData) || empty($formData['GLOBAL_ALLOW_INSTANT_ORDER'])){
         
            $status = 'error';
            $messages['GLOBAL_ALLOW_INSTANT_ORDER'] = 'Select Instant order.';
        }
        
        if(!array_key_exists('GLOBAL_DELIVERY_TYPE', $formData) || empty($formData['GLOBAL_DELIVERY_TYPE'])){
       
            $status = 'error';
            $messages['GLOBAL_DELIVERY_TYPE'] = 'Select delivery type.';
        }
        
        if(!array_key_exists('GLOBAL_THEME', $formData) || empty($formData['GLOBAL_THEME'])){
       
            $status = 'error';
            $messages['GLOBAL_THEME'] = 'Please Select Theme.';
        }

        if(!array_key_exists('PRINT_LABEL_TEMPLATE', $formData) || empty($formData['PRINT_LABEL_TEMPLATE'])){
       
            $status = 'error';
            $messages['PRINT_LABEL_TEMPLATE'] = 'Please Select Print Label Templates.';
        }
        
        if($status == 'success'){
            
            $sm = $this->getServiceLocator();
            $sql = new QSql($sm);

            //$update = $sql->update('settings');
            
            $query = "UPDATE settings "
                . "SET `value` = (case ";
          
            foreach($formData as $key => $value){
                
                  $query.=  " when `key` = '".$key."' then '".$value. "'";
            }
                $query .= " end) WHERE `key` in ("
                    ."'". implode("','",array_keys($formData))."'" 
                    . ")";
            
            $setting_session = new Container('setting');
            
            $setting_session->setting->offsetSet('GLOBAL_ALLOW_MEAL_SWAP', $formData['GLOBAL_ALLOW_MEAL_SWAP']);  
            $setting_session->setting->offsetSet('GLOBAL_ALLOW_MEAL_ITEM_SWAP', $formData['GLOBAL_ALLOW_MEAL_ITEM_SWAP']);
            $setting_session->setting->offsetSet('GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION', $formData['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION']);
            $setting_session->setting->offsetSet('GLOBAL_SKIP_KITCHEN', $formData['GLOBAL_SKIP_KITCHEN']);
            $setting_session->setting->offsetSet('GLOBAL_ALLOW_INSTANT_ORDER', $formData['GLOBAL_ALLOW_INSTANT_ORDER']);
            $setting_session->setting->offsetSet('GLOBAL_DELIVERY_TYPE', $formData['GLOBAL_DELIVERY_TYPE']);

            $setting_session->setting->offsetSet('PRINT_LABEL_TEMPLATE', $formData['PRINT_LABEL_TEMPLATE']);

            //dd($setting_session->setting);            
            //$_SESSION['wizard']['PRINT_LABEL_TEMPLATE'] = $formData['PRINT_LABEL_TEMPLATE'];
            //echo "<pre>"; echo $query; echo "</pre>"; die;             
            $result = $sql->execQuery($query,'query');
           
             return array('status' => 'success','data' => 'Additional settings updated successfully.');
        }
        /* error response */
        return array('status' => 'error', 'messages' => $messages);
    }
    
    function setCatalogFirstmeal($formData, $meal){
        $status = 'success'; $messages = array();

        if(!array_key_exists('meal_name', $formData) || empty($formData['meal_name'])){
            $status = 'error';
            $messages['meal_name'] = 'Meal name required';
        }
        
        if(!array_key_exists('meal_price', $formData) || empty($formData['meal_price'])){
            
            $status = 'error';
            $messages['meal_price'] = 'Meal price required';
        }
        if(!array_key_exists('veg', $formData) || empty($formData['veg'])){  
            
            $status = 'error';
            $messages['veg'] = 'Select atleast one food type';
        }
        if(!array_key_exists('meal_plans', $formData) || empty($formData['meal_plans'])){

            $status = 'error';
            $messages['meal_plans'] = 'Select atleast one plan type';
        }
        if(!array_key_exists('menu_type', $formData) || empty($formData['menu_type'])){

            $status = 'error';
            $messages['menu_type'] = 'Select menu type';
        }
        
        if(!array_key_exists('product', $formData) || empty($formData['product'])){
            $status = 'error';
            $messages['product'] = 'Select product.';
        }
        
        $_SESSION['wizard']['plans']=$formData['plans'];
        $_SESSION['wizard']['meal_plans']=$formData['meal_plans'];

        if($status == 'success'){

            $itemsId = [];
            $itemsQnt = [];
            foreach($formData['product'] as $key=>$data){
                $itemsId[] = $data['id'];
                $itemsQnt[] = $data['quantity'];
            }
            $product = array_combine($itemsId,$itemsQnt);
             
            //2. $meal update with form value
            $meal->name =  $formData['meal_name']; 
            $meal->unit_price = $formData['meal_price'];
            $meal->food_type = $formData['veg'];
            $meal->meal_plans = $formData['plans'].'@'.$formData['meal_plans'];
            $meal->category = implode(",",$formData['menu_type']);
            $meal->items = json_encode($product);
           
            // 3. save to database
            $meal = $this->getMealTable()->saveMeal($meal);
            return array('status' => 'success','data' => 'Meal updated successfully.');
        }
        /* error response */
        return array('status' => 'error', 'messages' => $messages);

    }
    
    public function getMealTable() {
       
    	if (!$this->mealTable) {
            $sm = $this->getServiceLocator();           
            $this->mealTable = $sm->get('QuickServe\Model\MealTable');
    	}
        
    	return $this->mealTable;
    }
    
    /**
     * set notification parameters in database.
     * email_template and sms_template tables need to be updated.
     * 
     * @param array $formData
     * @return array
     */
    function setNotificationDetails(array $formData){
        $templates = array('new_registration', 'order_booking', 'order_confirmation', 'order_cancellation', 'pre_order_renewal');

        $status = 'success'; $messages = array();
        
        foreach($templates as $template){
            if(!array_key_exists($template, $formData) || !isset($formData[$template])){
                $status = 'error';
                $messages[$template] = str_replace('_', ' ', $template). " field is required";
            }
        }
        
        if($status == 'success'){
            
            $_SESSION['wizard']['notifications'] = $formData;
            
            /* update email_template */
            $this->massUpdateQuery($formData, 'email_template', 'template_key', 'is_active');

            /* update sms_template */
            $this->massUpdateQuery($formData, 'sms_template', 'template_key', 'is_active');
            
            return array('status' => 'success', 'data' => 'Notifications updated successfully.');
        }
        
        return array('status' => 'error', 'messages' => $messages);
    }
    
    /**
     * updates multiple records
     * 
     * @param array $formData           array with key,value pair => (column,value)
     * @param string $table             table name
     * @param string $whereColumn       name of where column
     * @param string $setColumn         name of column to set
     */
    function massUpdateQuery(array $formData, string $table, string $whereColumn, string $setColumn){
        
        $sql = new QSql($this->getServiceLocator());
          
        $query = "UPDATE ".$table
            . " SET `".$setColumn."` = (case ";
        
        foreach($formData as $key => $value){
            
            $value =  ($table == 'email_template') ? ( ($value == 'yes') ? 1 : 0 ) : $value ;
            $query .=  " when `".$whereColumn."` = '".$key."' then '". (is_array($value) ? implode(',', $value) : $value ) . "'";
        }

        $query .= " end) WHERE `".$whereColumn."` in ("
            ."'". implode("','",array_keys($formData))."'" 
            . ")";

        $sql->execQuery($query,'query');
      
    }
    
}
