
<link rel="stylesheet" href="/admin/css/jquery-ui.css">
<script src="/admin/js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<?php 
$form = $this->form;
$form->setAttribute('action', $this->url('content_crud', array('action' => 'edit', 'id'=>$this->id))); 
$form->setAttribute('class','stdform');
$form->prepare();
?>
 <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Content Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
         <?php echo $this->form()->openTag($form) ;?>
            <p>
              <label><?php echo $this->formLabel($form->get('content_plan')); ?></label>
              <span class="field">
              <?php 
              echo $this->formElement($form->get('pk_content_id'));
              echo $this->formElement($form->get('content_plan'));
              echo $this->formElementErrors($form->get('content_plan'));
              ?>
            
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('content_description')); ?></label>
              <span class="field">
            <?php 
            echo $this->formElement($form->get('content_description'));
			echo $this->formElementErrors($form->get('content_description'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('number_of_pages')); ?></label>
              <span class="field">
            <?php 
            echo $this->formElement($form->get('number_of_pages'));
			echo $this->formElementErrors($form->get('number_of_pages'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('max_word_count')); ?></label>
              <span class="field">
            <?php 
            $max_word_value = ($form->get('max_word_count')->getValue()) ? $form->get('max_word_count')->getValue() : $page_price['content']['words_per_page'];
            $form->get('max_word_count')->setValue( $max_word_value );
            echo $this->formElement($form->get('max_word_count'));
			//echo $this->formElementErrors($form->get('max_word_count'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('price_plan')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('price_plan'));
					echo $this->formElementErrors($form->get('price_plan'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('time_required')); ?></label>
              <span class="field">
            <?php echo $this->formElement($form->get('time_required'));
					echo $this->formElementErrors($form->get('time_required'));echo ' Hours';
					echo $this->formElement($form->get('csrf'));?>
              </span> </p>
           
            <p class="stdformbutton">
             <?php echo $this->formInput($form->get('submit')); ?>
            </p>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form) ;?>
          <br clear="all" />
          <br />
        </div>
        <!--content--> 
<script type="text/javascript">
jQuery(document).ready(function(){
	jQuery('select[name="number_of_pages"]').bind('change', function() {
		var max_word_count = jQuery(this).find('option:selected').attr('value') * <?php echo $page_price['content']['words_per_page']; ?>;
		jQuery('input[name="max_word_count"]').attr('value', max_word_count);
	});
});
</script>