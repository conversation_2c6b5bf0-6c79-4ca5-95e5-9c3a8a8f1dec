<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Renaming\Rector\Name\RenameClassRector;
use <PERSON>\Renaming\Rector\Namespace_\RenameNamespaceRector;

return static function (RectorConfig $rectorConfig): void {
    // Rename namespaces from Zend module style to PSR-4
    $rectorConfig->ruleWithConfiguration(RenameNamespaceRector::class, [
        // Map SanAuth namespace to App\Auth
        'SanAuth' => 'App\Auth',
        'SanAuth\Controller' => 'App\Auth\Controller',
        'SanAuth\Model' => 'App\Auth\Model',
        'SanAuth\Service' => 'App\Auth\Service',
        'SanAuth\Form' => 'App\Auth\Form',
        'SanAuth\Validator' => 'App\Auth\Validator',
        'SanAuth\Factory' => 'App\Auth\Factory',
        'SanAuth\Middleware' => 'App\Auth\Middleware',
        'SanAuth\Session' => 'App\Auth\Session',

        // Map Api namespace to App\Api
        'Api' => 'App\Api',
        'Api\Controller' => 'App\Api\Controller',
        'Api\Model' => 'App\Api\Model',
        'Api\Service' => 'App\Api\Service',
        'Api\Middleware' => 'App\Api\Middleware',
        'Api\Factory' => 'App\Api\Factory',

        // Map QuickServe namespace to App\QuickServe
        'QuickServe' => 'App\QuickServe',
        'QuickServe\Model' => 'App\QuickServe\Model',
        'QuickServe\Controller' => 'App\QuickServe\Controller',
        'QuickServe\Event' => 'App\QuickServe\Event',
        'QuickServe\Factory' => 'App\QuickServe\Factory',
        'QuickServe\Listener' => 'App\QuickServe\Listener',
        'QuickServe\Service' => 'App\QuickServe\Service',

        // Map Lib namespace to App\Lib
        'Lib\QuickServe' => 'App\Lib\QuickServe',
        'Lib\Auth' => 'App\Lib\Auth',
        'Lib\Barcode' => 'App\Lib\Barcode',
        'Lib\CronHelper' => 'App\Lib\CronHelper',
        'Lib\Email' => 'App\Lib\Email',
        'Lib\ICICI' => 'App\Lib\ICICI',
        'Lib\TPDelivery' => 'App\Lib\TPDelivery',
        'Lib\Compatibility' => 'App\Lib\Compatibility',
    ]);
};
