  <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'index')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		    
?>
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
           <?php  echo $this->form()->openTag($form);?>
        		<div class="row">
            		<div class="large-4 columns">
                    	<label class="inline right">Enter Contact No./ Email ID<span class="red">*</span></label>
                  	</div>
                  	<div class="large-8 columns">                
                       <?php   echo $this->formElement($form->get('phone')); ?>
	      		  		<?php   
	      		  			echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('phone')); ?>        
                  	</div>
              	</div>
                
                
                
                <div class="row">
            		<div class="large-4 columns">&nbsp;</div>
                  	<div class="large-8 columns">     
                    	 <?php echo $this->formSubmit($form->get('submit')); ?>
                  	</div>
              	</div>              
             
        <?php echo $this->formElement($form->get('csrf')); ?>
        	<?php  echo $this->form()->closeTag(); ?>
        </div>
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">
$(document).ready(function() { 

/* 
	 $('#submitbtn').click(function(){
		 var phone = $('#phone').val()
		 
		 //alert(phone);
		//return false;
		 /* var loc = "/backorder/printorder/menu/"+menu;
		 window.open(loc, '_blank'); */
		  
	/*}); */
});
</script>