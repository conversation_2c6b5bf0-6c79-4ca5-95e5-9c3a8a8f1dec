import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletUnfreeze } from '@/components/customer-service-v12/[id]/wallet/unfreeze';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletUnfreeze', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletUnfreeze />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletUnfreeze')).toBeInTheDocument();
  });
});