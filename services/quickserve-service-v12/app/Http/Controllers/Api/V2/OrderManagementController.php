<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Services\PaymentOrderUpdateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

/**
 * Controller for comprehensive order management
 */
class OrderManagementController extends BaseController
{
    public function __construct(
        protected PaymentOrderUpdateService $paymentOrderUpdateService
    ) {}

    /**
     * Create a new pre-order with temporary tables and initiate payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createOrder(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'customer_id' => 'required|integer',
                'customer_address' => 'required|string|max:250',
                'location_code' => 'required|integer',
                'location_name' => 'required|string|max:45',
                'city' => 'required|integer',
                'city_name' => 'required|string|max:45',

                // Meals array structure
                'meals' => 'required|array|min:1',
                'meals.*.product_code' => 'required|integer',
                'meals.*.quantity' => 'required|integer|min:1',

                // Date-based structure
                'start_date' => 'required|date_format:Y-m-d|after_or_equal:today',
                'selected_days' => 'required|array|min:1|max:7', // Array of day numbers (0-6)
                'selected_days.*' => 'integer|min:0|max:6', // Each day must be 0-6 (Sunday-Saturday)
                'subscription_days' => 'required|integer|min:1|max:30', // Number of delivery days

                // Legacy support (optional)
                'days_preference' => 'nullable|string', // e.g., "1,2,3,4,5" - for backward compatibility

                'delivery_time' => 'nullable|string',
                'delivery_end_time' => 'nullable|string',
                'food_preference' => 'nullable|string|max:255',
            ]);

            // Fetch customer details from database
            $customerDetails = $this->getCustomerDetails($validated['customer_id']);
            if (!$customerDetails) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Process and validate meals
            $processedMeals = $this->processMeals($validated['meals']);
            if (empty($processedMeals)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid meals found'
                ], 400);
            }

            Log::info('Processed meals for order creation', [
                'processed_meals' => $processedMeals,
                'count' => count($processedMeals)
            ]);

            // Process selected days - convert legacy format if needed
            $selectedDays = $this->processSelectedDays($validated);

            // Calculate delivery dates based on start_date, selected_days, and subscription_days
            $deliveryDates = $this->calculateDeliveryDates(
                $validated['start_date'],
                $selectedDays,
                $validated['subscription_days']
            );

            // Add calculated data to validated data
            $validated['delivery_dates'] = $deliveryDates;
            $validated['selected_days_processed'] = $selectedDays;
            $validated['customer_details'] = $customerDetails;
            $validated['processed_meals'] = $processedMeals;

            DB::beginTransaction();

            // Generate unique order number
            $orderNo = $this->generateOrderNumber();

            // Step 1: Create temp_pre_orders records (one for each meal type)
            $tempPreOrderIds = $this->createTempPreOrders($orderNo, $validated, $selectedDays, $customerDetails, $processedMeals);

            // Calculate total amount with tax from all temp_pre_orders
            $totalAmountWithTax = array_sum(array_column($tempPreOrderIds, 'total_amount'));
            $validated['total_amount'] = $totalAmountWithTax;

            // Use first temp_pre_order for payment processing
            $primaryTempPreOrderId = $tempPreOrderIds[0]['id'];

            // Step 2: Create temp_order_payment record
            $tempOrderPaymentId = $this->createTempOrderPayment($primaryTempPreOrderId, $validated);

            // Step 3: Create payment_transaction with status 'initiated'
            $paymentTransactionId = $this->createInitialPaymentTransaction($primaryTempPreOrderId, $orderNo, $validated);

            // Commit transaction before calling Payment Service to avoid deadlocks
            DB::commit();

            // Step 4: Initiate payment with payment service (outside transaction)
            try {
                $paymentServiceResponse = $this->initiatePaymentWithPaymentService($orderNo, $validated, $paymentTransactionId);
            } catch (Exception $e) {
                // If Payment Service fails, mark the temp records as failed but don't rollback
                // The temp records are already created and can be retried later
                Log::error('Payment Service failed after temp records created', [
                    'order_no' => $orderNo,
                    'temp_pre_order_ids' => $tempPreOrderIds,
                    'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Payment initiation failed: ' . $e->getMessage(),
                    'data' => [
                        'order_no' => $orderNo,
                        'temp_pre_order_ids' => $tempPreOrderIds,
                        'can_retry' => true
                    ]
                ], 500);
            }

            Log::info('Pre-order created successfully', [
                'order_no' => $orderNo,
                'temp_pre_order_ids' => $tempPreOrderIds,
                'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                'temp_order_payment_id' => $tempOrderPaymentId,
                'payment_transaction_id' => $paymentTransactionId,
                'customer_id' => $validated['customer_id'],
                'amount' => $validated['total_amount'],
                'subscription_days' => $validated['subscription_days']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Pre-order created successfully',
                'data' => [
                    'temp_pre_order_ids' => $tempPreOrderIds,
                    'primary_temp_pre_order_id' => $primaryTempPreOrderId,
                    'order_no' => $orderNo,
                    'customer_id' => $validated['customer_id'],
                    'customer_name' => $customerDetails['name'],
                    'customer_email' => $customerDetails['email'],
                    'customer_phone' => $customerDetails['phone'],
                    'meals' => $processedMeals,
                    'total_amount' => array_sum(array_column($processedMeals, 'total_amount')),
                    'status' => 'pending',
                    'days_preference' => implode(',', $selectedDays),
                    'subscription_days' => $validated['subscription_days'],
                    'payment_transaction_id' => $paymentTransactionId,
                    'payment_service_transaction_id' => $paymentServiceResponse['data']['transaction_id'] ?? null,
                    'payment_urls' => [
                        'process_payment' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}/process",
                        'payment_status' => config('services.payment.url') . "/api/v2/payments/{$paymentServiceResponse['data']['transaction_id']}",
                        'order_status' => config('app.url') . "/api/v2/order-management/pre-order-status/{$orderNo}"
                    ]
                ]
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Pre-order creation failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Pre-order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order details with payment status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getOrderDetails(string $orderNo): JsonResponse
    {
        try {
            // Get order
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Get order details
            $orderDetails = OrderDetail::where('ref_order_no', $orderNo)->get();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => [
                        'order_id' => $order->pk_order_no,
                        'order_no' => $order->order_no,
                        'customer_id' => $order->customer_code,
                        'customer_name' => $order->customer_name,
                        'customer_email' => $order->email_address,
                        'customer_phone' => $order->phone,
                        'customer_address' => $order->ship_address,
                        'product_name' => $order->product_name,
                        'product_type' => $order->product_type,
                        'quantity' => $order->quantity,
                        'amount' => $order->amount,
                        'order_status' => $order->order_status,
                        'payment_mode' => $order->payment_mode,
                        'amount_paid' => $order->amount_paid,
                        'days_preference' => $order->days_preference,
                        'delivery_status' => $order->delivery_status,
                        'order_date' => $order->order_date,
                        'delivery_time' => $order->delivery_time,
                        'delivery_end_time' => $order->delivery_end_time,
                        'recurring_status' => $order->recurring_status,
                    ],
                    'meal_items' => $orderDetails->map(function ($detail) {
                        return [
                            'product_code' => $detail->product_code,
                            'product_name' => $detail->product_name,
                            'quantity' => $detail->quantity,
                            'amount' => $detail->product_amount,
                        ];
                    }),
                    'payment_transaction' => $paymentTransaction ? [
                        'transaction_id' => $paymentTransaction->pk_transaction_id,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id,
                        'status' => $paymentTransaction->status,
                        'gateway' => $paymentTransaction->gateway,
                        'payment_amount' => $paymentTransaction->payment_amount,
                        'created_date' => $paymentTransaction->created_date,
                    ] : null
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get order details', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get order details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer orders categorized by status (upcoming, cancelled, other)
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getCustomerOrders(Request $request, int $customerId): JsonResponse
    {
        try {
            // Get optional filter parameter
            $studentNameFilter = $request->input('student_name_filter');

            // Validate customer exists
            $customer = DB::table('customers')
                ->where('pk_customer_code', $customerId)
                ->where('status', 1)
                ->first(['pk_customer_code', 'customer_name', 'email_address', 'phone']);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Get all orders for the customer with detailed information
            // Note: quickserve orders table structure is different - it has product info directly in orders table
            $query = DB::table('orders as o')
                ->where('o.customer_code', $customerId);

            // Apply student name filter if provided
            if ($studentNameFilter) {
                $query->where('o.ship_address', 'LIKE', $studentNameFilter . '%');
                Log::info('Applied student name filter', [
                    'customer_id' => $customerId,
                    'student_name_filter' => $studentNameFilter
                ]);
            }

            $orders = $query->select([
                    'o.pk_order_no as order_id',
                    'o.order_no',
                    'o.order_date',
                    'o.order_date as delivery_date', // Using order_date as delivery_date for now
                    'o.order_status',
                    'o.delivery_status',
                    'o.payment_mode',
                    'o.amount_paid',
                    'o.amount as total_amount',
                    'o.delivery_time',
                    'o.delivery_end_time',
                    DB::raw('1 as recurring_status'), // Default value
                    DB::raw('"" as days_preference'), // Default value
                    'o.ship_address as customer_address',
                    'o.location_name',
                    'o.city_name',
                    'o.food_type as food_preference',
                    'o.product_code',
                    'o.product_name',
                    'o.quantity',
                    'o.amount as item_amount',
                    'o.product_type'
                ])
                ->orderBy('o.order_date', 'desc')
                ->get();

            Log::info('Retrieved orders for customer', [
                'customer_id' => $customerId,
                'orders_count' => count($orders)
            ]);

            // Group orders by order_id and categorize
            $groupedOrders = $this->groupAndCategorizeOrders($orders);

            // Extract unique student names from each category
            $studentNames = $this->extractStudentNames($groupedOrders);

            Log::info('Customer orders retrieved successfully', [
                'customer_id' => $customerId,
                'total_orders' => count($groupedOrders['all']),
                'upcoming_count' => count($groupedOrders['upcoming']),
                'cancelled_count' => count($groupedOrders['cancelled']),
                'other_count' => count($groupedOrders['other']),
                'student_name_filter' => $studentNameFilter,
                'unique_student_names' => [
                    'upcoming' => count($studentNames['upcoming']),
                    'cancelled' => count($studentNames['cancelled']),
                    'other' => count($studentNames['other'])
                ]
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'customer' => [
                        'customer_id' => $customer->pk_customer_code,
                        'name' => $customer->customer_name,
                        'email' => $customer->email_address,
                        'phone' => $customer->phone
                    ],
                    'summary' => [
                        'total_orders' => count($groupedOrders['all']),
                        'upcoming_orders' => count($groupedOrders['upcoming']),
                        'cancelled_orders' => count($groupedOrders['cancelled']),
                        'other_orders' => count($groupedOrders['other'])
                    ],
                    'student_names' => $studentNames,
                    'filters' => [
                        'student_name_filter' => $studentNameFilter
                    ],
                    'orders' => [
                        'upcoming' => $groupedOrders['upcoming'],
                        'cancelled' => $groupedOrders['cancelled'],
                        'other' => $groupedOrders['other']
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get customer orders', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer orders'
            ], 500);
        }
    }

    /**
     * Create temp_pre_orders records (one for each meal type)
     *
     * @param string $orderNo
     * @param array $validated
     * @param array $selectedDays
     * @param array $customerDetails
     * @param array $processedMeals
     * @return array
     */
    protected function createTempPreOrders(string $orderNo, array $validated, array $selectedDays, array $customerDetails, array $processedMeals): array
    {
        // Get tax settings
        $taxSettings = $this->getTaxSettings();
        $taxRate = $taxSettings['rate'];
        $applyTax = $taxSettings['apply'];

        // Group meals by meal type (breakfast, lunch, etc.)
        $mealsByType = $this->groupMealsByType($processedMeals);

        $tempPreOrderIds = [];
        $mealTypeIndex = 0;

        foreach ($mealsByType as $mealType => $meals) {
            $mealAmount = array_sum(array_column($meals, 'total_amount'));
            $taxAmount = $applyTax ? round($mealAmount * $taxRate / 100, 2) : 0;
            $totalAmount = $mealAmount + $taxAmount;

            // Generate unique order number for each meal type
            $mealOrderNo = $mealTypeIndex === 0 ? $orderNo : $this->generateOrderNumber();

            // Create item_preference JSON like the example
            $itemPreference = $this->createItemPreferenceJson($meals);

            Log::info('Creating temp pre-order for meal type', [
                'meal_type' => $mealType,
                'meal_order_no' => $mealOrderNo,
                'meals' => $meals,
                'meal_amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount
            ]);

            $tempPreOrderId = DB::table('temp_pre_orders')->insertGetId([
                'company_id' => 8163,
                'unit_id' => 8163,
                'fk_kitchen_code' => 1,
                'ref_order' => 0,
                'order_no' => $mealOrderNo,
                'auth_id' => $validated['customer_id'],
                'customer_code' => $validated['customer_id'],
                'customer_name' => $customerDetails['name'],
                'food_preference' => $validated['food_preference'] ?? 'veg',
                'phone' => $customerDetails['phone'],
                'email_address' => $customerDetails['email'],
                'location_code' => $validated['location_code'],
                'location_name' => $validated['location_name'],
                'city' => $validated['city'],
                'city_name' => $validated['city_name'],
                'product_code' => $meals[0]['product_code'], // Use first meal of this type
                'product_name' => $this->getMealTypeDisplayName($mealType),
                'product_description' => $this->getMealTypeDescription($mealType),
                'product_type' => 'Meal',
                'quantity' => array_sum(array_column($meals, 'quantity')),
                'order_type' => 'Day',
                'order_days' => $validated['delivery_dates'][0], // Simple date string instead of JSON array
                'product_price' => $mealAmount,
                'amount' => $mealAmount,
                'total_amt' => $totalAmount,
                'tax' => $taxAmount,
                'total_tax' => $taxAmount,
                'delivery_charges' => 0.00,
                'service_charges' => 0.00,
                'total_delivery_charges' => 0.00,
                'line_delivery_charges' => 0.00,
                'applied_discount' => 0.00,
                'total_applied_discount' => 0.00,
                'order_status' => 'New',
                'order_date' => now()->format('Y-m-d'),
                'due_date' => now()->format('Y-m-d'),
                'ship_address' => $validated['customer_address'],
                'order_menu' => strtolower($mealType),
                'invoice_status' => 'Unbill',
                'amount_paid' => 0,
                'inventory_type' => 'perishable',
                'food_type' => $validated['food_preference'] ?? 'veg',
                'total_third_party_charges' => 0.00,
                'order_for' => 'fixed',
                'PRODUCT_MEAL_CALENDAR' => 0,
                'delivery_type' => 'delivery',
                'delivery_person' => null,
                'payment_mode' => 'online',
                'days_preference' => implode(',', $selectedDays),
                'tp_delivery' => null,
                'tp_delivery_charges' => null,
                'tp_delivery_charges_type' => null,
                'tp_aggregator' => null,
                'tp_aggregator_charges' => null,
                'tp_aggregator_charges_type' => null,
                'tax_method' => 'exclusive',
                'apply_tax' => $applyTax ? 'yes' : 'no',
                'source' => 'api',
                'delivery_time' => $validated['delivery_time'] ?? null,
                'delivery_end_time' => $validated['delivery_end_time'] ?? null,
                'item_preference' => $itemPreference,
                'remark' => null,
                'recurring_status' => 1,
                'delivery_note' => null,
            ]);

            $tempPreOrderIds[] = [
                'id' => $tempPreOrderId,
                'order_no' => $mealOrderNo,
                'meal_type' => $mealType,
                'amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'meals_count' => count($meals)
            ];

            Log::info('Temp pre-order created for meal type', [
                'temp_pre_order_id' => $tempPreOrderId,
                'meal_type' => $mealType,
                'order_no' => $mealOrderNo,
                'customer_id' => $validated['customer_id'],
                'amount' => $mealAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount
            ]);

            $mealTypeIndex++;
        }

        return $tempPreOrderIds;
    }

    /**
     * Create temp_order_payment record
     *
     * @param int $tempPreOrderId
     * @param array $validated
     * @return int
     */
    protected function createTempOrderPayment(int $tempPreOrderId, array $validated): int
    {
        $tempOrderPaymentId = DB::table('temp_order_payment')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'temp_order_id' => 0, // Will be updated when actual orders are created
            'temp_preorder_id' => $tempPreOrderId,
            'amount' => $validated['total_amount'],
            'status' => 'pending',
            'date' => now()->format('Y-m-d'),
            'type' => 'online',
            'istodaysorder' => '0',
            'order_menu' => 'meal', // Default to meal since we support multiple meals
            'recurring_status' => '1',
        ]);

        Log::info('Temp order payment created', [
            'temp_order_payment_id' => $tempOrderPaymentId,
            'temp_pre_order_id' => $tempPreOrderId,
            'amount' => $validated['total_amount']
        ]);

        return $tempOrderPaymentId;
    }

    /**
     * Create initial payment_transaction record with status 'initiated'
     *
     * @param int $tempPreOrderId
     * @param string $orderNo
     * @param array $validated
     * @return int
     */
    protected function createInitialPaymentTransaction(int $tempPreOrderId, string $orderNo, array $validated): int
    {
        $paymentTransactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_details']['email'],
            'customer_phone' => $validated['customer_details']['phone'],
            'customer_name' => $validated['customer_details']['name'],
            'payment_amount' => $validated['total_amount'],
            'transaction_charges' => round($validated['total_amount'] * 0.03, 2), // 3% transaction fee
            'wallet_amount' => 0.00,
            'pre_order_id' => $orderNo,
            'gateway' => 'initiated',
            'status' => 'initiated', // Initial status
            'gateway_transaction_id' => null,
            'description' => "Payment initiated for meal subscription",
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$orderNo}",
            'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$orderNo}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        Log::info('Payment transaction initiated', [
            'payment_transaction_id' => $paymentTransactionId,
            'temp_pre_order_id' => $tempPreOrderId,
            'order_no' => $orderNo,
            'amount' => $validated['total_amount']
        ]);

        return $paymentTransactionId;
    }

    /**
     * Generate order days based on days preference and subscription duration
     *
     * @param string $daysPreference
     * @param int $subscriptionDays
     * @return string
     */
    protected function generateOrderDays(string $daysPreference, int $subscriptionDays): string
    {
        $selectedDays = explode(',', $daysPreference);
        $orderDays = [];

        $startDate = now();
        $endDate = now()->addDays($subscriptionDays);

        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayOfWeek = $date->dayOfWeek; // 0=Sunday, 1=Monday, etc.

            if (in_array((string)$dayOfWeek, $selectedDays)) {
                $orderDays[] = $date->format('Y-m-d');
            }
        }

        return json_encode($orderDays);
    }

    /**
     * Generate unique order number in the format: 4 random chars + YYMMDD
     * Example: 2UO3250702 (matches existing pattern)
     *
     * @return string
     */
    protected function generateOrderNumber(): string
    {
        // Generate 4 random characters (letters and numbers)
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $random = '';
        for ($i = 0; $i < 4; $i++) {
            $random .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Add date in YYMMDD format
        $dateStr = now()->format('ymd');

        return $random . $dateStr;
    }

    /**
     * Create payment transaction record and initiate payment with payment service
     *
     * @param Order $order
     * @param array $validated
     * @return array
     */
    protected function createPaymentTransaction(Order $order, array $validated): array
    {
        // Create local payment transaction record for tracking
        $transactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'customer_name' => $validated['customer_name'],
            'payment_amount' => $validated['amount'],
            'transaction_charges' => round($validated['amount'] * 0.03, 2), // 3% transaction fee
            'wallet_amount' => 0.00,
            'pre_order_id' => $order->order_no,
            'gateway' => 'pending',
            'status' => 'pending',
            'gateway_transaction_id' => null,
            'description' => "Payment for {$validated['product_name']} subscription",
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$order->order_no}",
            'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$order->order_no}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        // Initiate payment with payment service
        $paymentServiceResponse = $this->initiatePaymentWithPaymentService($order, $validated, $transactionId);

        return [
            'local_transaction_id' => $transactionId,
            'payment_service_response' => $paymentServiceResponse
        ];
    }

    /**
     * Initiate payment with payment service v12
     *
     * @param string $orderNo
     * @param array $validated
     * @param int $localTransactionId
     * @return array
     */
    protected function initiatePaymentWithPaymentService(string $orderNo, array $validated, int $localTransactionId): array
    {
        try {
            $paymentServiceUrl = config('services.payment.url', 'http://localhost:8002');

            $paymentData = [
                'customer_id' => $validated['customer_id'],
                'customer_email' => $validated['customer_details']['email'],
                'customer_phone' => $validated['customer_details']['phone'],
                'customer_name' => $validated['customer_details']['name'],
                'amount' => $validated['total_amount'],
                'transaction_charges' => round($validated['total_amount'] * 0.03, 2),
                'wallet_amount' => 0.00,
                'order_id' => $orderNo, // Use order_no as order_id for payment service
                'referer' => 'quickserve_order_api',
                'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$orderNo}",
                'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$orderNo}",
                'context' => 'order_payment',
                'recurring' => true,
                'discount' => 0.00
            ];

            // Call payment service to initiate payment (optimized for speed)
            $response = Http::timeout(15) // Reduced timeout
                ->retry(2, 500) // Reduced retries and delay
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Service' => 'quickserve-v12',
                    'X-Order-Transaction-ID' => $localTransactionId
                ])
                ->post("{$paymentServiceUrl}/api/v2/payments", $paymentData);

            Log::info('Payment service call completed', [
                'order_no' => $orderNo,
                'status_code' => $response->status(),
                'successful' => $response->successful()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Update local transaction with payment service transaction ID
                DB::table('payment_transaction')
                    ->where('pk_transaction_id', $localTransactionId)
                    ->update([
                        'gateway_transaction_id' => $responseData['data']['transaction_id'] ?? null,
                        'modified_date' => now()
                    ]);

                Log::info('Payment initiated with payment service', [
                    'order_no' => $orderNo,
                    'local_transaction_id' => $localTransactionId,
                    'payment_service_transaction_id' => $responseData['data']['transaction_id'] ?? null
                ]);

                return $responseData;
            } else {
                throw new Exception('Payment service initiation failed: ' . $response->body());
            }

        } catch (Exception $e) {
            Log::error('Failed to initiate payment with payment service', [
                'order_no' => $orderNo,
                'local_transaction_id' => $localTransactionId,
                'error' => $e->getMessage()
            ]);

            // Update local transaction status to failed
            DB::table('payment_transaction')
                ->where('pk_transaction_id', $localTransactionId)
                ->update([
                    'status' => 'failed',
                    'description' => 'Payment service initiation failed: ' . $e->getMessage(),
                    'modified_date' => now()
                ]);

            throw $e;
        }
    }

    /**
     * Handle payment success callback from payment service
     * Creates actual orders and order details from temp tables
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentSuccess(Request $request, string $orderNo): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Find the primary temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Find all related temp_pre_orders for the same customer created together
            // This handles multiple meal types (breakfast, lunch) created in the same transaction
            $allRelatedTempPreOrders = DB::table('temp_pre_orders')
                ->where('customer_code', $tempPreOrder->customer_code)
                ->where('order_date', $tempPreOrder->order_date)
                ->whereBetween('last_modified', [
                    date('Y-m-d H:i:s', strtotime($tempPreOrder->last_modified) - 120), // 2 minutes before
                    date('Y-m-d H:i:s', strtotime($tempPreOrder->last_modified) + 120)  // 2 minutes after
                ])
                ->get();

            Log::info('Found related temp pre-orders for payment processing', [
                'primary_order_no' => $orderNo,
                'related_orders_count' => count($allRelatedTempPreOrders),
                'related_order_nos' => array_column($allRelatedTempPreOrders->toArray(), 'order_no')
            ]);

            // Step 1: Update payment_transaction with success details
            $this->updatePaymentTransactionSuccess($orderNo, $request);

            // Step 2: Create payment_transfered record if gateway is Razorpay
            if (strtolower($request->input('gateway', '')) === 'razorpay') {
                $this->createPaymentTransferRecord($orderNo, $request);
            }

            // Step 3: Update temp_order_payment status to success for primary order
            $this->updateTempOrderPaymentSuccess($tempPreOrder->pk_order_no);

            // Step 4: Create actual orders and order details from ALL related temp data
            $allCreatedOrders = [];
            foreach ($allRelatedTempPreOrders as $relatedTempPreOrder) {
                $createdOrders = $this->createActualOrdersFromTemp($relatedTempPreOrder, $request);
                $allCreatedOrders = array_merge($allCreatedOrders, $createdOrders);

                Log::info('Created orders from related temp pre-order', [
                    'temp_order_no' => $relatedTempPreOrder->order_no,
                    'meal_type' => $relatedTempPreOrder->order_menu,
                    'orders_created' => count($createdOrders)
                ]);
            }

            DB::commit();

            Log::info('Payment success processed and orders created', [
                'primary_order_no' => $orderNo,
                'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                'related_temp_orders_count' => count($allRelatedTempPreOrders),
                'total_created_orders' => count($allCreatedOrders),
                'payment_service_transaction_id' => $request->input('payment_service_transaction_id')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment success processed and orders created',
                'data' => [
                    'primary_order_no' => $orderNo,
                    'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                    'related_temp_orders_count' => count($allRelatedTempPreOrders),
                    'created_orders_count' => count($allCreatedOrders),
                    'created_order_details_count' => $this->countOrderDetails($allCreatedOrders),
                    'payment_status' => 'completed',
                    'orders_created' => array_slice($allCreatedOrders, 0, 10) // Show first 10 orders
                ]
            ]);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process payment success', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment success: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment failure callback from payment service
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentFailure(Request $request, string $orderNo): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Find the temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Update payment_transaction with failure details
            DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->update([
                    'status' => 'failed',
                    'gateway' => $request->input('gateway', 'online'),
                    'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                    'description' => 'Payment failed: ' . $request->input('failure_reason', 'Payment gateway failure'),
                    'modified_date' => now()
                ]);

            // Update temp_order_payment status to failed (keep as pending for retry)
            DB::table('temp_order_payment')
                ->where('temp_preorder_id', $tempPreOrder->pk_order_no)
                ->update([
                    'status' => 'pending' // Keep as pending to allow retry
                ]);

            // Update temp_pre_orders status
            DB::table('temp_pre_orders')
                ->where('pk_order_no', $tempPreOrder->pk_order_no)
                ->update([
                    'order_status' => 'Payment Failed'
                ]);

            DB::commit();

            Log::warning('Payment failure processed for pre-order', [
                'order_no' => $orderNo,
                'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                'failure_reason' => $request->input('failure_reason', 'Payment gateway failure')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment failure processed',
                'data' => [
                    'order_no' => $orderNo,
                    'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                    'order_status' => 'Payment Failed',
                    'payment_status' => 'failed',
                    'failure_reason' => $request->input('failure_reason', 'Payment gateway failure'),
                    'retry_available' => true
                ]
            ]);

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process payment failure', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment failure: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pre-order status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getPreOrderStatus(string $orderNo): JsonResponse
    {
        try {
            // Get temp pre-order
            $tempPreOrder = DB::table('temp_pre_orders')->where('order_no', $orderNo)->first();

            if (!$tempPreOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pre-order not found'
                ], 404);
            }

            // Get temp order payment
            $tempOrderPayment = DB::table('temp_order_payment')
                ->where('temp_preorder_id', $tempPreOrder->pk_order_no)
                ->first();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            // Count created orders if payment was successful
            $createdOrdersCount = 0;
            $createdOrderDetailsCount = 0;

            if ($tempOrderPayment && $tempOrderPayment->status === 'success') {
                $createdOrdersCount = DB::table('orders')
                    ->where('ref_order', $tempPreOrder->pk_order_no)
                    ->count();

                $createdOrderDetailsCount = DB::table('order_details')
                    ->whereIn('ref_order_no', function($query) use ($tempPreOrder) {
                        $query->select('order_no')
                              ->from('orders')
                              ->where('ref_order', $tempPreOrder->pk_order_no);
                    })
                    ->count();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'pre_order' => [
                        'temp_pre_order_id' => $tempPreOrder->pk_order_no,
                        'order_no' => $tempPreOrder->order_no,
                        'customer_id' => $tempPreOrder->customer_code,
                        'customer_name' => $tempPreOrder->customer_name,
                        'product_name' => $tempPreOrder->product_name,
                        'amount' => $tempPreOrder->amount,
                        'order_status' => $tempPreOrder->order_status,
                        'days_preference' => $tempPreOrder->days_preference,
                        'order_days' => is_string($tempPreOrder->order_days) &&
                                       json_decode($tempPreOrder->order_days) !== null ?
                                       json_decode($tempPreOrder->order_days) :
                                       [$tempPreOrder->order_days],
                        'created_date' => $tempPreOrder->last_modified
                    ],
                    'payment_status' => [
                        'temp_payment_status' => $tempOrderPayment->status ?? 'unknown',
                        'transaction_status' => $paymentTransaction->status ?? 'unknown',
                        'gateway' => $paymentTransaction->gateway ?? null,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id ?? null
                    ],
                    'orders_created' => [
                        'count' => $createdOrdersCount,
                        'order_details_count' => $createdOrderDetailsCount,
                        'status' => $createdOrdersCount > 0 ? 'completed' : 'pending'
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get pre-order status', [
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get pre-order status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update payment_transaction with success details
     *
     * @param string $orderNo
     * @param Request $request
     * @return void
     */
    protected function updatePaymentTransactionSuccess(string $orderNo, Request $request): void
    {
        // Update the QuickServe transaction (referer = 'order_api') to completed
        $updatedRows = DB::table('payment_transaction')
            ->where('pre_order_id', $orderNo)
            ->where('referer', 'order_api') // Only update QuickServe transaction
            ->update([
                'status' => 'completed',
                'gateway' => $request->input('gateway', 'online'),
                'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                'description' => 'Payment completed successfully',
                'modified_date' => now()
            ]);

        Log::info('Payment transaction updated to completed', [
            'order_no' => $orderNo,
            'updated_rows' => $updatedRows,
            'gateway' => $request->input('gateway'),
            'gateway_transaction_id' => $request->input('payment_service_transaction_id')
        ]);
    }

    /**
     * Create payment_transfered record for Razorpay payments
     *
     * @param string $orderNo
     * @param Request $request
     * @return void
     */
    protected function createPaymentTransferRecord(string $orderNo, Request $request): void
    {
        // Get payment transaction details (QuickServe transaction)
        $paymentTransaction = DB::table('payment_transaction')
            ->where('pre_order_id', $orderNo)
            ->where('referer', 'order_api') // Get QuickServe transaction
            ->first();

        if ($paymentTransaction) {
            $transferId = 'trf_' . uniqid();

            DB::table('payment_transfered')->insert([
                'pk_transfer_id' => $transferId,
                'company_id' => 8163,
                'unit_id' => 8163,
                'fk_transaction_id' => $paymentTransaction->pk_transaction_id,
                'source' => 'razorpay',
                'recipient' => 'merchant_account',
                'amount' => (int)($paymentTransaction->payment_amount * 100), // Amount in paise
                'currency' => 'INR',
                'amount_reversed' => 0,
                'transfered_at' => now()->timestamp,
                'created_date' => now()->timestamp,
                'description' => "Transfer for order {$orderNo}"
            ]);

            Log::info('Payment transfer record created', [
                'transfer_id' => $transferId,
                'order_no' => $orderNo,
                'amount' => $paymentTransaction->payment_amount
            ]);
        }
    }

    /**
     * Update temp_order_payment status to success
     *
     * @param int $tempPreOrderId
     * @return void
     */
    protected function updateTempOrderPaymentSuccess(int $tempPreOrderId): void
    {
        DB::table('temp_order_payment')
            ->where('temp_preorder_id', $tempPreOrderId)
            ->update([
                'status' => 'success'
            ]);

        Log::info('Temp order payment updated to success', [
            'temp_pre_order_id' => $tempPreOrderId
        ]);
    }

    /**
     * Create actual order and order details from temp pre-order
     * Fixed: Create proper orders based on subscription days and meal items
     *
     * @param object $tempPreOrder
     * @param Request $request
     * @return array
     */
    protected function createActualOrdersFromTemp(object $tempPreOrder, Request $request): array
    {
        // Check if orders already exist for this order_no to prevent duplicates
        $existingOrders = DB::table('orders')->where('order_no', $tempPreOrder->order_no)->count();
        if ($existingOrders > 0) {
            Log::warning('Orders already exist for order_no, skipping creation', [
                'order_no' => $tempPreOrder->order_no,
                'existing_count' => $existingOrders
            ]);

            return DB::table('orders')
                ->where('order_no', $tempPreOrder->order_no)
                ->get(['pk_order_no as order_id', 'order_no', 'order_date'])
                ->map(function($order) {
                    return [
                        'order_id' => $order->order_id,
                        'order_no' => $order->order_no,
                        'order_date' => $order->order_date,
                        'status' => 'already_exists'
                    ];
                })->toArray();
        }

        // Parse order days from temp pre-order (handle both JSON array and simple string formats)
        $orderDays = [];

        if (is_string($tempPreOrder->order_days)) {
            // Try to decode as JSON first (legacy format)
            $decoded = json_decode($tempPreOrder->order_days, true);
            if (is_array($decoded)) {
                $orderDays = $decoded;
            } else {
                // Simple string format (new format) - single date
                $orderDays = [$tempPreOrder->order_days];
            }
        } else {
            $orderDays = [$tempPreOrder->order_days];
        }

        if (empty($orderDays)) {
            throw new Exception('Invalid order days data in temp pre-order');
        }

        Log::info('Parsed order days for payment success', [
            'raw_order_days' => $tempPreOrder->order_days,
            'parsed_order_days' => $orderDays,
            'count' => count($orderDays)
        ]);

        $createdOrders = [];

        // Create orders for each subscription day
        foreach ($orderDays as $orderDate) {
            // Check if order already exists for this date to prevent duplicates
            $existingOrder = DB::table('orders')
                ->where('order_no', $tempPreOrder->order_no)
                ->where('order_date', $orderDate)
                ->first(['pk_order_no']);

            if ($existingOrder) {
                Log::warning('Order already exists for this date, skipping', [
                    'order_no' => $tempPreOrder->order_no,
                    'order_date' => $orderDate,
                    'existing_order_id' => $existingOrder->pk_order_no
                ]);

                $createdOrders[] = [
                    'order_id' => $existingOrder->pk_order_no,
                    'order_no' => $tempPreOrder->order_no,
                    'order_date' => $orderDate,
                    'status' => 'already_exists'
                ];
                continue;
            }

            // Get meal items for this specific date from product_planner
            $mealItems = $this->getMealItemsForOrder($tempPreOrder, $orderDate);

            // Debug log for meal items
            Log::info('Meal items retrieved for order creation', [
                'order_date' => $orderDate,
                'meal_items_count' => count($mealItems),
                'meal_items' => $mealItems
            ]);

            // Create order for this specific date
            $orderId = $this->createSingleOrder($tempPreOrder, $tempPreOrder->order_no, $orderDate, $request);

            // Ensure we have at least one meal item (fallback to main product)
            if (empty($mealItems)) {
                Log::warning('No meal items found, creating fallback item', [
                    'order_date' => $orderDate,
                    'product_code' => $tempPreOrder->product_code
                ]);

                $mealItems = [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount
                    ]
                ];
            }

            // Create order details for each meal item for this date
            $this->createOrderDetailsForOrder($orderId, $tempPreOrder->order_no, $tempPreOrder, $mealItems, $orderDate);

            $createdOrders[] = [
                'order_id' => $orderId,
                'order_no' => $tempPreOrder->order_no,
                'order_date' => $orderDate,
                'meal_items_count' => count($mealItems),
                'subscription_days' => count($orderDays),
                'order_days' => $orderDays,
                'status' => 'created'
            ];
        }

        Log::info('Created orders from temp pre-order', [
            'order_no' => $tempPreOrder->order_no,
            'total_orders_created' => count($createdOrders),
            'subscription_days' => count($orderDays),
            'meal_items_per_order' => count($mealItems)
        ]);

        return $createdOrders;
    }

    /**
     * Get meal items for the order from product_planner table (like weekly-planner API)
     * Fetches meal items based on product items and product_planner table
     *
     * @param object $tempPreOrder
     * @param string $orderDate The specific date for this order
     * @return array
     */
    protected function getMealItemsForOrder(object $tempPreOrder, string $orderDate = null): array
    {
        try {
            // Get the main product details to extract items
            $product = DB::table('products')
                ->where('pk_product_code', $tempPreOrder->product_code)
                ->where('status', 1)
                ->first(['pk_product_code', 'name', 'items', 'product_category']);

            Log::info('Product lookup for meal items extraction', [
                'product_code' => $tempPreOrder->product_code,
                'product_found' => $product ? 'yes' : 'no',
                'items_field' => $product->items ?? 'null',
                'items_empty' => empty($product->items ?? ''),
                'items_length' => strlen($product->items ?? ''),
                'product_name' => $product->name ?? 'null'
            ]);

            if (!$product || empty($product->items)) {
                Log::info('No product found or no items defined, using main product', [
                    'product_code' => $tempPreOrder->product_code,
                    'product_found' => $product ? 'yes' : 'no',
                    'items_field' => $product->items ?? 'null'
                ]);

                return [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount / $tempPreOrder->quantity // Calculate per item amount
                    ]
                ];
            }

            // Parse items from product (JSON format like {"395":"1","396":"1","334":"1"})
            $items = [];
            $itemsData = json_decode($product->items, true);

            if (is_array($itemsData)) {
                // Handle JSON object format: {"395":"1","396":"1","334":"1"}
                foreach ($itemsData as $itemCode => $quantity) {
                    $items[] = [
                        'item_code' => (int)$itemCode,
                        'quantity' => (int)$quantity
                    ];
                }
                Log::info('Parsed JSON format items', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items,
                    'parsed_items_count' => count($items),
                    'parsed_items' => $items
                ]);
            } else {
                // Handle old format like "343:1,344:1,345:1"
                $itemPairs = explode(',', $product->items);
                foreach ($itemPairs as $pair) {
                    if (strpos($pair, ':') !== false) {
                        list($itemCode, $quantity) = explode(':', $pair);
                        $items[] = ['item_code' => (int)$itemCode, 'quantity' => (int)$quantity];
                    }
                }
                Log::info('Parsed old format items', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items,
                    'parsed_items_count' => count($items)
                ]);
            }

            if (empty($items)) {
                Log::info('No items found in product, using main product', [
                    'product_code' => $tempPreOrder->product_code,
                    'items_raw' => $product->items
                ]);

                return [
                    [
                        'product_code' => $tempPreOrder->product_code,
                        'product_name' => $tempPreOrder->product_name,
                        'quantity' => 1,
                        'amount' => $tempPreOrder->amount / $tempPreOrder->quantity // Calculate per item amount
                    ]
                ];
            }

            $mealItemsArray = [];
            $useDate = $orderDate ?? now()->format('Y-m-d');

            // For each item, check product_planner table for specific product name
            foreach ($items as $item) {
                $itemCode = $item['item_code'] ?? $item['product_code'] ?? 0;
                $quantity = $item['quantity'] ?? 1;

                if ($itemCode <= 0) continue;

                // Check product_planner table for specific product name (like weekly-planner API)
                $plannerItem = DB::table('product_planner')
                    ->where('date', $useDate)
                    ->where('menu', strtolower($tempPreOrder->order_menu))
                    ->where('fk_kitchen_code', 1) // Default kitchen
                    ->where('generic_product_code', $itemCode)
                    ->first(['specific_product_code', 'specific_product_name']);

                $itemName = '';
                $source = '';

                // Priority logic: planner data first, then fallback to products table
                if ($plannerItem && !empty($plannerItem->specific_product_name)) {
                    $itemName = $plannerItem->specific_product_name;
                    $source = 'product_planner_table';
                } else {
                    // Fallback to products table name
                    $productDetails = DB::table('products')
                        ->where('pk_product_code', $itemCode)
                        ->first(['name', 'unit_price']);

                    if ($productDetails) {
                        $itemName = $productDetails->name;
                        $source = 'products_table_fallback';
                    } else {
                        $itemName = 'Unknown Item';
                        $source = 'not_found';
                    }
                }

                // Calculate proportional amount for this item
                $itemAmount = count($items) > 0 ? ($tempPreOrder->amount / count($items)) : $tempPreOrder->amount;

                $mealItemsArray[] = [
                    'product_code' => $itemCode,
                    'product_name' => $itemName,
                    'quantity' => $quantity,
                    'amount' => $itemAmount,
                    'source' => $source,
                    'date' => $useDate
                ];
            }

            Log::info('Retrieved meal items from product_planner', [
                'product_code' => $tempPreOrder->product_code,
                'menu' => $tempPreOrder->order_menu,
                'date' => $useDate,
                'items_count' => count($mealItemsArray)
            ]);

            return $mealItemsArray;

        } catch (\Exception $e) {
            Log::error('Failed to get meal items from product_planner, using fallback', [
                'product_code' => $tempPreOrder->product_code,
                'error' => $e->getMessage()
            ]);

            // Fallback to single main product
            return [
                [
                    'product_code' => $tempPreOrder->product_code,
                    'product_name' => $tempPreOrder->product_name,
                    'quantity' => 1,
                    'amount' => $tempPreOrder->amount
                ]
            ];
        }
    }

    /**
     * Calculate tax amount based on settings table configuration
     *
     * @param float $amount
     * @return float
     */
    protected function calculateOrderTax(float $amount): float
    {
        try {
            // Get tax settings from settings table
            $applyTax = DB::table('settings')
                ->where('key', 'GLOBAL_APPLY_TAX')
                ->where('company_id', 8163)
                ->value('value');

            if ($applyTax !== 'yes') {
                return 0.00;
            }

            $taxMethod = DB::table('settings')
                ->where('key', 'GLOBAL_TAX_METHOD')
                ->where('company_id', 8163)
                ->value('value') ?? 'exclusive';

            // Get active tax rates
            $taxes = DB::table('tax')
                ->where('status', 1)
                ->where('apply_all_product', 'yes')
                ->get(['tax', 'tax_type', 'base_amount']);

            $totalTax = 0.00;

            foreach ($taxes as $tax) {
                if ($tax->tax_type === 'percent') {
                    $baseAmount = ($tax->base_amount / 100);
                    $taxableAmount = $amount * $baseAmount;

                    if ($taxMethod === 'exclusive') {
                        $totalTax += ($taxableAmount * $tax->tax) / 100;
                    } else { // inclusive
                        $totalTax += $taxableAmount / (1 + ($tax->tax / 100)) * ($tax->tax / 100);
                    }
                } elseif ($tax->tax_type === 'fixed') {
                    $totalTax += $tax->tax;
                }
            }

            return round($totalTax, 2);

        } catch (\Exception $e) {
            Log::error('Tax calculation failed', [
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return 0.00;
        }
    }

    /**
     * Generate daily order number
     *
     * @param string $baseOrderNo
     * @param string $orderDate
     * @return string
     */
    protected function generateDailyOrderNumber(string $baseOrderNo, string $orderDate): string
    {
        $dateStr = str_replace('-', '', $orderDate);
        return $baseOrderNo . '_' . $dateStr;
    }

    /**
     * Create single order record
     *
     * @param object $tempPreOrder
     * @param string $dailyOrderNo
     * @param string $orderDate
     * @param Request $request
     * @return int
     */
    protected function createSingleOrder(object $tempPreOrder, string $dailyOrderNo, string $orderDate, Request $request): int
    {
        // Calculate tax based on settings - ensure amount is float
        $taxAmount = $this->calculateOrderTax((float) $tempPreOrder->amount);

        $orderId = DB::table('orders')->insertGetId([
            'company_id' => $tempPreOrder->company_id,
            'unit_id' => $tempPreOrder->unit_id,
            'fk_kitchen_code' => $tempPreOrder->fk_kitchen_code,
            'ref_order' => $tempPreOrder->pk_order_no, // Reference to temp pre-order
            'order_no' => $dailyOrderNo,
            'auth_id' => $tempPreOrder->auth_id,
            'customer_code' => $tempPreOrder->customer_code,
            'customer_name' => $tempPreOrder->customer_name,
            'food_preference' => $tempPreOrder->food_preference,
            'phone' => $tempPreOrder->phone,
            'email_address' => $tempPreOrder->email_address,
            'location_code' => $tempPreOrder->location_code,
            'location_name' => $tempPreOrder->location_name,
            'city' => $tempPreOrder->city,
            'city_name' => $tempPreOrder->city_name,
            'product_code' => $tempPreOrder->product_code,
            'product_name' => $tempPreOrder->product_name,
            'product_description' => $tempPreOrder->product_description,
            'product_type' => $tempPreOrder->product_type,
            'quantity' => $tempPreOrder->quantity,
            'product_price' => $tempPreOrder->product_price,
            'amount' => $tempPreOrder->amount,
            'applied_discount' => $tempPreOrder->applied_discount,
            'amount_paid' => 1, // Payment completed
            'tax' => $taxAmount, // Use calculated tax amount
            'delivery_charges' => $tempPreOrder->delivery_charges,
            'service_charges' => $tempPreOrder->service_charges,
            'order_status' => 'Confirmed',
            'order_date' => $orderDate,
            'due_date' => null, // Set to NULL as per requirement
            'ship_address' => $tempPreOrder->ship_address,
            'delivery_status' => 'Pending',
            'invoice_status' => 'Unbill',
            'order_menu' => $tempPreOrder->order_menu,
            'inventory_type' => $tempPreOrder->inventory_type,
            'food_type' => $tempPreOrder->food_type,
            'payment_mode' => $request->input('gateway', 'online'),
            'days_preference' => $tempPreOrder->days_preference,
            'delivery_type' => $tempPreOrder->delivery_type,
            'delivery_time' => $tempPreOrder->delivery_time,
            'delivery_end_time' => $tempPreOrder->delivery_end_time,
            'recurring_status' => '1',
            'created_date' => now(),
        ]);

        return $orderId;
    }

    /**
     * Create order details for a single order
     *
     * @param int $orderId
     * @param string $dailyOrderNo
     * @param object $tempPreOrder
     * @param array $mealItems
     * @param string $orderDate
     * @return void
     */
    protected function createOrderDetailsForOrder(int $orderId, string $dailyOrderNo, object $tempPreOrder, array $mealItems, string $orderDate): void
    {
        foreach ($mealItems as $item) {
            // Calculate tax for this specific item - ensure amount is float
            $itemTax = $this->calculateOrderTax((float) $item['amount']);

            DB::table('order_details')->insert([
                'company_id' => $tempPreOrder->company_id,
                'unit_id' => $tempPreOrder->unit_id,
                'ref_order_no' => $dailyOrderNo,
                'meal_code' => $tempPreOrder->product_code,
                'product_code' => $item['product_code'],
                'product_name' => $item['product_name'],
                'quantity' => $item['quantity'],
                'product_type' => 'Meal',
                'order_date' => $orderDate,
                'product_amount' => $item['amount'],
                'product_tax' => $itemTax, // Use calculated tax
                'product_subtype' => 'specific',
                'product_generic_code' => $item['product_code'],
                'product_generic_name' => $item['product_name'],
            ]);
        }

        Log::info('Order details created', [
            'order_id' => $orderId,
            'order_no' => $dailyOrderNo,
            'order_date' => $orderDate,
            'meal_items_count' => count($mealItems)
        ]);
    }

    /**
     * Count total order details created
     *
     * @param array $createdOrders
     * @return int
     */
    protected function countOrderDetails(array $createdOrders): int
    {
        $totalDetails = 0;
        foreach ($createdOrders as $order) {
            // Use meal_items_count if available, otherwise count from order_details table
            if (isset($order['meal_items_count'])) {
                $totalDetails += $order['meal_items_count'];
            } else {
                // Fallback: count from database
                $count = DB::table('order_details')
                    ->where('ref_order_no', $order['order_no'] ?? '')
                    ->count();
                $totalDetails += $count;
            }
        }
        return $totalDetails;
    }

    /**
     * Process selected days from request - handle both new and legacy formats
     *
     * @param array $validated
     * @return array
     */
    protected function processSelectedDays(array $validated): array
    {
        // If new format is provided, use it
        if (isset($validated['selected_days']) && is_array($validated['selected_days'])) {
            return array_map('intval', $validated['selected_days']);
        }

        // Legacy format: convert days_preference string to array
        if (isset($validated['days_preference'])) {
            $daysString = trim($validated['days_preference']);
            if (!empty($daysString)) {
                return array_map('intval', explode(',', $daysString));
            }
        }

        // Default to Monday-Friday if nothing provided
        return [1, 2, 3, 4, 5]; // Monday to Friday
    }

    /**
     * Calculate delivery dates based on start date, selected days, and subscription days
     * Logic similar to admin service working days calculation with cut-off restrictions
     *
     * @param string $startDate Format: Y-m-d
     * @param array $selectedDays Array of day numbers (0=Sunday, 6=Saturday)
     * @param int $subscriptionDays Number of delivery days needed
     * @return array Array of delivery dates in Y-m-d format
     */
    protected function calculateDeliveryDates(string $startDate, array $selectedDays, int $subscriptionDays): array
    {
        $deliveryDates = [];
        $currentDate = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate);
        $daysFound = 0;
        $maxIterations = 365; // Prevent infinite loop
        $today = \Carbon\Carbon::now();

        // Get cut-off settings for kitchen K1 and meal type (breakfast/lunch)
        $cutOffSettings = $this->getCutOffSettings(1, 'breakfast'); // Default to breakfast for K1
        $iterations = 0;

        Log::info('Calculating delivery dates', [
            'start_date' => $startDate,
            'selected_days' => $selectedDays,
            'subscription_days' => $subscriptionDays
        ]);

        while ($daysFound < $subscriptionDays && $iterations < $maxIterations) {
            $dayOfWeek = $currentDate->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday

            // Check if current day is in selected days
            if (in_array($dayOfWeek, $selectedDays)) {
                // Apply cut-off logic - check if this date is available for ordering
                if ($this->isDateAvailableForOrdering($currentDate, $today, $cutOffSettings)) {
                    $deliveryDates[] = $currentDate->format('Y-m-d');
                    $daysFound++;

                    Log::debug('Added delivery date', [
                        'date' => $currentDate->format('Y-m-d'),
                        'day_of_week' => $dayOfWeek,
                        'days_found' => $daysFound
                    ]);
                } else {
                    Log::debug('Date excluded due to cut-off restrictions', [
                        'date' => $currentDate->format('Y-m-d'),
                        'day_of_week' => $dayOfWeek
                    ]);
                }
            }

            $currentDate->addDay();
            $iterations++;
        }

        if ($iterations >= $maxIterations) {
            Log::warning('Reached maximum iterations while calculating delivery dates', [
                'start_date' => $startDate,
                'selected_days' => $selectedDays,
                'subscription_days' => $subscriptionDays,
                'days_found' => $daysFound
            ]);
        }

        Log::info('Delivery dates calculated', [
            'total_dates' => count($deliveryDates),
            'dates' => $deliveryDates
        ]);

        return $deliveryDates;
    }

    /**
     * Get cut-off settings for a specific kitchen and meal type
     * Based on admin-service-v12 logic
     *
     * @param int $kitchenId
     * @param string $mealType (breakfast or lunch)
     * @return array
     */
    protected function getCutOffSettings(int $kitchenId, string $mealType): array
    {
        $mealTypeUpper = strtoupper($mealType);

        // Build the setting keys based on kitchen ID and meal type
        $cutOffDayKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_DAY";
        $cutOffTimeKey = "K{$kitchenId}_{$mealTypeUpper}_ORDER_CUT_OFF_TIME";

        Log::info("Looking for cut-off settings", [
            'cut_off_day_key' => $cutOffDayKey,
            'cut_off_time_key' => $cutOffTimeKey
        ]);

        try {
            // Get the settings from database
            $cutOffDaySetting = DB::table('settings')->where('key', $cutOffDayKey)->first();
            $cutOffTimeSetting = DB::table('settings')->where('key', $cutOffTimeKey)->first();

            $cutOffDay = $cutOffDaySetting ? (int) $cutOffDaySetting->value : 0; // Default to same day
            $cutOffTime = $cutOffTimeSetting ? $cutOffTimeSetting->value : '23:59:59'; // Default to end of day

            Log::info("Cut-off settings retrieved", [
                'cut_off_day' => $cutOffDay,
                'cut_off_time' => $cutOffTime
            ]);

            return [
                'cut_off_day' => $cutOffDay,
                'cut_off_time' => $cutOffTime
            ];
        } catch (\Exception $e) {
            Log::warning("Failed to get cut-off settings, using defaults", [
                'error' => $e->getMessage()
            ]);

            return [
                'cut_off_day' => 0,
                'cut_off_time' => '23:59:59'
            ];
        }
    }

    /**
     * Check if a date is available for ordering based on cut-off settings
     * Based on admin-service-v12 logic
     *
     * @param \Carbon\Carbon $targetDate
     * @param \Carbon\Carbon $currentDateTime
     * @param array $cutOffSettings
     * @return bool
     */
    protected function isDateAvailableForOrdering(\Carbon\Carbon $targetDate, \Carbon\Carbon $currentDateTime, array $cutOffSettings): bool
    {
        $targetDateOnly = $targetDate->copy()->startOfDay();
        $today = $currentDateTime->copy()->startOfDay();

        // For future dates (not today), they are available
        if ($targetDateOnly->gt($today)) {
            Log::debug("Date {$targetDate->format('Y-m-d')} is in the future, including");
            return true;
        }

        // For today's date, check cut-off logic
        $cutOffDay = $cutOffSettings['cut_off_day'];
        $cutOffTime = $cutOffSettings['cut_off_time'];

        // Calculate the actual cut-off datetime
        $cutOffDateTime = $currentDateTime->copy()->startOfDay();

        // Add cut-off days
        if ($cutOffDay > 0) {
            $cutOffDateTime->addDays($cutOffDay);
        }

        // Add cut-off time
        try {
            $timeParts = explode(':', $cutOffTime);
            $cutOffDateTime->setTime((int)$timeParts[0], (int)$timeParts[1], (int)($timeParts[2] ?? 0));
        } catch (\Exception $e) {
            Log::warning("Invalid cut-off time format, using end of day", [
                'cut_off_time' => $cutOffTime,
                'error' => $e->getMessage()
            ]);
            $cutOffDateTime->setTime(23, 59, 59);
        }

        // If cut-off day is 0 (same day), check if current time is before cut-off time
        if ($cutOffDay == 0) {
            $isAvailable = $currentDateTime->lt($cutOffDateTime);
            Log::debug("Same day cut-off check", [
                'target_date' => $targetDate->format('Y-m-d'),
                'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
                'cut_off_time' => $cutOffDateTime->format('Y-m-d H:i:s'),
                'is_available' => $isAvailable
            ]);
            return $isAvailable;
        }

        // For cut-off day > 0, the date is available if we're before the cut-off datetime
        $isAvailable = $currentDateTime->lt($cutOffDateTime);
        Log::debug("Multi-day cut-off check", [
            'target_date' => $targetDate->format('Y-m-d'),
            'current_time' => $currentDateTime->format('Y-m-d H:i:s'),
            'cut_off_datetime' => $cutOffDateTime->format('Y-m-d H:i:s'),
            'cut_off_day' => $cutOffDay,
            'is_available' => $isAvailable
        ]);

        return $isAvailable;
    }

    /**
     * Get customer details from database
     *
     * @param int $customerId
     * @return array|null
     */
    protected function getCustomerDetails(int $customerId): ?array
    {
        try {
            $customer = DB::table('customers')
                ->where('pk_customer_code', $customerId)
                ->where('status', 1) // Only active customers
                ->first(['pk_customer_code', 'customer_name', 'email_address', 'phone']);

            if (!$customer) {
                Log::warning('Customer not found or inactive', ['customer_id' => $customerId]);
                return null;
            }

            return [
                'id' => $customer->pk_customer_code,
                'name' => $customer->customer_name,
                'email' => $customer->email_address,
                'phone' => $customer->phone
            ];
        } catch (\Exception $e) {
            Log::error('Failed to fetch customer details', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Process meals array and fetch product details
     *
     * @param array $meals
     * @return array
     */
    protected function processMeals(array $meals): array
    {
        $processedMeals = [];

        foreach ($meals as $meal) {
            try {
                // Fetch product details from database
                $product = DB::table('products')
                    ->where('pk_product_code', $meal['product_code'])
                    ->where('status', 1)
                    ->first(['pk_product_code', 'name', 'description', 'product_type', 'unit_price']);

                if (!$product) {
                    Log::warning('Product not found or inactive', [
                        'product_code' => $meal['product_code']
                    ]);
                    continue;
                }

                Log::info('Found product for meal', [
                    'product_code' => $meal['product_code'],
                    'product' => $product
                ]);

                $quantity = $meal['quantity'];
                $totalAmount = $product->unit_price * $quantity;

                $processedMeals[] = [
                    'product_code' => $product->pk_product_code ?? $meal['product_code'],
                    'product_name' => $product->name ?? 'Unknown Product',
                    'product_description' => $product->description ?? $product->name ?? 'Unknown Product',
                    'product_type' => $product->product_type ?? 'Meal',
                    'quantity' => $quantity,
                    'unit_price' => $product->unit_price ?? 0,
                    'total_amount' => $totalAmount
                ];

                Log::info('Processed meal', [
                    'product_code' => $product->pk_product_code,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'total_amount' => $totalAmount
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to process meal', [
                    'meal' => $meal,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Processed meals summary', [
            'total_meals' => count($processedMeals),
            'total_amount' => array_sum(array_column($processedMeals, 'total_amount'))
        ]);

        return $processedMeals;
    }

    /**
     * Get tax settings from database
     *
     * @return array
     */
    protected function getTaxSettings(): array
    {
        try {
            $globalApplyTax = DB::table('settings')
                ->where('key', 'GLOBAL_APPLY_TAX')
                ->first(['value']);

            $applyTax = $globalApplyTax && strtolower($globalApplyTax->value) === 'yes';
            $taxRate = 5.0; // Default 5% tax rate

            Log::info('Tax settings retrieved', [
                'apply_tax' => $applyTax,
                'tax_rate' => $taxRate
            ]);

            return [
                'apply' => $applyTax,
                'rate' => $taxRate
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get tax settings, using defaults', [
                'error' => $e->getMessage()
            ]);

            return [
                'apply' => false,
                'rate' => 5.0
            ];
        }
    }

    /**
     * Group meals by meal type (breakfast, lunch, etc.)
     * Uses product name to determine meal type since product_type is generic "Meal"
     *
     * @param array $processedMeals
     * @return array
     */
    protected function groupMealsByType(array $processedMeals): array
    {
        $mealsByType = [];

        foreach ($processedMeals as $meal) {
            $productName = strtolower($meal['product_name'] ?? '');
            $productDescription = strtolower($meal['product_description'] ?? '');

            // Determine meal type from product name or description
            $mealType = 'meal'; // Default

            if (strpos($productName, 'breakfast') !== false || strpos($productDescription, 'breakfast') !== false) {
                $mealType = 'breakfast';
            } elseif (strpos($productName, 'lunch') !== false || strpos($productDescription, 'lunch') !== false) {
                $mealType = 'lunch';
            } elseif (strpos($productName, 'dinner') !== false || strpos($productDescription, 'dinner') !== false) {
                $mealType = 'dinner';
            } elseif (strpos($productName, 'snack') !== false || strpos($productDescription, 'snack') !== false) {
                $mealType = 'snack';
            }

            // If still generic, try to use product_code ranges (common pattern)
            if ($mealType === 'meal') {
                $productCode = $meal['product_code'];
                if ($productCode >= 340 && $productCode <= 349) {
                    $mealType = 'breakfast';
                } elseif ($productCode >= 330 && $productCode <= 339) {
                    $mealType = 'lunch';
                } elseif ($productCode >= 350 && $productCode <= 359) {
                    $mealType = 'dinner';
                }
            }

            if (!isset($mealsByType[$mealType])) {
                $mealsByType[$mealType] = [];
            }

            $mealsByType[$mealType][] = $meal;

            Log::info('Classified meal type', [
                'product_code' => $meal['product_code'],
                'product_name' => $meal['product_name'],
                'classified_as' => $mealType
            ]);
        }

        Log::info('Grouped meals by type', [
            'meal_types' => array_keys($mealsByType),
            'counts' => array_map('count', $mealsByType)
        ]);

        return $mealsByType;
    }

    /**
     * Create item preference JSON like the example
     *
     * @param array $meals
     * @return string
     */
    protected function createItemPreferenceJson(array $meals): string
    {
        $itemPreference = [];

        foreach ($meals as $meal) {
            $itemPreference[$meal['product_code']] = [
                'name' => $meal['product_name'],
                'product_name' => $meal['product_name'],
                'description' => $meal['product_description'] ?? $meal['product_name'],
                'unit_price' => number_format((float) $meal['unit_price'], 2),
                'id' => (string) $meal['product_code'],
                'foodtype' => 'veg', // Default, could be enhanced
                'product_category' => 'General', // Default, could be enhanced
                'image_path' => '',
                'product_subtype' => 'generic',
                'product_code' => (string) $meal['product_code'],
                'swap_with' => 'nocharge',
                'swap_charges' => null,
                'quantity' => (string) $meal['quantity']
            ];
        }

        return json_encode($itemPreference);
    }

    /**
     * Get display name for meal type
     *
     * @param string $mealType
     * @return string
     */
    protected function getMealTypeDisplayName(string $mealType): string
    {
        $displayNames = [
            'breakfast' => 'Breakfast of the Day (Recommended)',
            'lunch' => 'Lunch of the Day (Recommended)',
            'dinner' => 'Dinner of the Day (Recommended)',
            'meal' => 'Meal of the Day (Recommended)'
        ];

        return $displayNames[$mealType] ?? ucfirst($mealType) . ' Subscription';
    }

    /**
     * Get description for meal type
     *
     * @param string $mealType
     * @return string
     */
    protected function getMealTypeDescription(string $mealType): string
    {
        $descriptions = [
            'breakfast' => 'A rotating menu of Indian and International breakfast options to introduce diverse flavours',
            'lunch' => 'A rotating menu of Indian and International lunch options to introduce diverse flavours',
            'dinner' => 'A rotating menu of Indian and International dinner options to introduce diverse flavours',
            'meal' => 'A rotating menu of Indian and International options to introduce diverse flavours'
        ];

        return $descriptions[$mealType] ?? 'A variety of meal options';
    }

    /**
     * Group orders by order_id and categorize them by status
     *
     * @param \Illuminate\Support\Collection $orders
     * @return array
     */
    protected function groupAndCategorizeOrders($orders): array
    {
        $groupedOrders = [];
        $categorizedOrders = [
            'upcoming' => [],
            'cancelled' => [],
            'other' => [],
            'all' => []
        ];

        // Group orders by order_id first
        foreach ($orders as $order) {
            $orderId = $order->order_id;

            if (!isset($groupedOrders[$orderId])) {
                $groupedOrders[$orderId] = [
                    'order_id' => $order->order_id,
                    'order_no' => $order->order_no,
                    'order_date' => $order->order_date,
                    'delivery_date' => $order->delivery_date,
                    'order_status' => $order->order_status,
                    'delivery_status' => $order->delivery_status,
                    'payment_mode' => $order->payment_mode,
                    'amount_paid' => $order->amount_paid,
                    'total_amount' => $order->total_amount,
                    'delivery_time' => $order->delivery_time,
                    'delivery_end_time' => $order->delivery_end_time,
                    'recurring_status' => $order->recurring_status,
                    'days_preference' => $order->days_preference,
                    'customer_address' => $order->customer_address,
                    'location_name' => $order->location_name,
                    'city_name' => $order->city_name,
                    'food_preference' => $order->food_preference,
                    'meal_items' => []
                ];
            }

            // Add meal item if it exists
            if ($order->product_code) {
                $groupedOrders[$orderId]['meal_items'][] = [
                    'product_code' => $order->product_code,
                    'product_name' => $order->product_name,
                    'quantity' => $order->quantity,
                    'amount' => $order->item_amount,
                    'product_type' => $order->product_type
                ];
            }
        }

        // Convert to array and categorize
        $allOrders = array_values($groupedOrders);
        $today = now()->format('Y-m-d');

        foreach ($allOrders as $order) {
            $categorizedOrders['all'][] = $order;

            // Categorization logic
            $orderStatus = strtolower($order['order_status'] ?? '');
            $deliveryStatus = strtolower($order['delivery_status'] ?? '');
            $deliveryDate = $order['delivery_date'];

            // Cancelled orders (check this FIRST, regardless of date)
            if (in_array($orderStatus, ['cancelled', 'canceled', 'cancel', 'refunded']) ||
                in_array($deliveryStatus, ['cancelled', 'canceled', 'cancel'])) {
                $categorizedOrders['cancelled'][] = $order;
            }
            // Upcoming orders (future delivery date and not cancelled/completed)
            elseif ($deliveryDate && $deliveryDate > $today &&
                    !in_array($orderStatus, ['completed', 'delivered', 'complete']) &&
                    !in_array($deliveryStatus, ['delivered', 'completed'])) {
                $categorizedOrders['upcoming'][] = $order;
            }
            // Other orders (past orders, completed, etc.)
            else {
                $categorizedOrders['other'][] = $order;
            }
        }

        // Sort each category by delivery_date (upcoming: ascending, others: descending)
        usort($categorizedOrders['upcoming'], function($a, $b) {
            return strcmp($a['delivery_date'], $b['delivery_date']);
        });

        usort($categorizedOrders['cancelled'], function($a, $b) {
            return strcmp($b['delivery_date'], $a['delivery_date']);
        });

        usort($categorizedOrders['other'], function($a, $b) {
            return strcmp($b['delivery_date'], $a['delivery_date']);
        });

        Log::info('Orders categorized successfully', [
            'total_orders' => count($allOrders),
            'upcoming_count' => count($categorizedOrders['upcoming']),
            'cancelled_count' => count($categorizedOrders['cancelled']),
            'other_count' => count($categorizedOrders['other'])
        ]);

        return $categorizedOrders;
    }

    /**
     * Extract unique student names from customer addresses for each order category
     *
     * @param array $categorizedOrders
     * @return array
     */
    protected function extractStudentNames(array $categorizedOrders): array
    {
        $studentNames = [
            'upcoming' => [],
            'cancelled' => [],
            'other' => []
        ];

        foreach ($categorizedOrders as $category => $orders) {
            if ($category === 'all') {
                continue; // Skip the 'all' category
            }

            $uniqueNames = [];

            foreach ($orders as $order) {
                $customerAddress = $order['customer_address'] ?? '';

                // Extract student name (0th index after explode by comma)
                $addressParts = explode(',', $customerAddress);
                $studentName = trim($addressParts[0] ?? '');

                // Only add non-empty, unique student names
                if (!empty($studentName) && !in_array($studentName, $uniqueNames)) {
                    $uniqueNames[] = $studentName;
                }
            }

            // Sort student names alphabetically
            sort($uniqueNames);
            $studentNames[$category] = $uniqueNames;
        }

        Log::info('Student names extracted successfully', [
            'upcoming_students' => count($studentNames['upcoming']),
            'cancelled_students' => count($studentNames['cancelled']),
            'other_students' => count($studentNames['other']),
            'sample_upcoming' => array_slice($studentNames['upcoming'], 0, 3),
            'sample_cancelled' => array_slice($studentNames['cancelled'], 0, 3),
            'sample_other' => array_slice($studentNames['other'], 0, 3)
        ]);

        return $studentNames;
    }
}
