import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletTransactions } from '@/components/customer-service-v12/[id]/wallet/transactions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletTransactions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletTransactions />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletTransactions')).toBeInTheDocument();
  });
});