
<?php //echo'<pre>';print_r($emailtemplate); die;?>
<div id="content">
      	<div class="large-12 columns">
      	<?php echo $this->form()->openTag($form);?>
      		 <div class="portlet box light-grey">        
        	<div class="portlet-title">
            <h4 class="white"><i class="fa fa-table"></i><?php $template_key =  $form->get('template_key')->getValue();
									  echo str_replace('_', " ", $template_key);?><?php  //echo $this->formElement($form->get('pk_template_id')); //echo $emailtemplate['pk_template_id']?></h4>  
            <ul class="toolOption">            	
     
                <li>
                    <div class="tools">
                        <a href="javascript:;" class="collapse"></a>
                    </div>
                </li>
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customerInfo" class="display">                   
 				 <tbody>
                        <tr>
                            <td style="width:20%">Title</td>
                            <td style="width:1%">:</td>
                            <td style="width:79%">
                            	<?php echo $this->formElement($form->get('pk_set_id')); 
                            		$setid =  $form->get('pk_set_id')->getValue();
                            		
                            		
                            		echo $this->formElement($form->get('pk_template_id'));
                            	?> 
                            	<?php $template_key =  $form->get('template_key')->getValue();
									  echo str_replace('_', " ", $template_key);
								?> 
							</td>
                        </tr>
                        <tr>
                            <td>Purpose</td>
                            <td>:</td>
                            <td><?php  $purpose =  $form->get('purpose')->getValue();
									   echo $purpose;
								 ?>
							</td>
                        </tr>
                        <tr>
                            <td>Message Subject</td>
                            <td>:</td>
                            <td><?php echo $this->formElement($form->get('subject'));?></td>
                        </tr>
                        <tr>
                            <td>Type</td>
                            <td>:</td>
                            <td><?php echo $this->formElement($form->get('type'));?></td>
                        </tr>
                        <tr>
                            <td>Message Body</td>
                            <td>:</td>
                             <td>
                             	<?php echo $this->formElement($form->get('body'));?>
                             	<!-- <textarea class="jqte-test"> </textarea> -->
                             </td>
                          <!-- <td><?php //echo $this->formElement($form->get('body'));?></td> -->  
                        </tr>   
                        
                        <tr>
                        	<td>&nbsp;</td>
                        	<td>&nbsp;</td>
                        	<td><a href="#" data-reveal-id="myModal">Add Variables</a></td>
                        </tr> 
                        <tr>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>
								
								<button type="submit" id="submitbutton" class="button left tiny left5 dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
								<button type="button" class="button left tiny left5 redBg"  onclick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'emaildetails','id'=>$setid));?>'" >Cancel &nbsp;<i class="fa fa-ban"></i></button>
							</td>
						</tr>
                        
                    </tbody>
                </table>          
          	</div>
        </div>   
         <?php echo $this->formElement($form->get('csrf')); echo $this->formElement($form->get('backurl')); echo $this->form()->closeTag($form);?>
        </div>
        </div>
        
      <!-- popup starts -->
<div id="myModal" class="reveal-modal medium" data-reveal>
  <h2 style="font-size:24px">Variables</h2>
  <p >Please note that non-based variables depend on the context of use.</p>
  
  <table class="large-12 columns">
  	<tr>
  		<td style="vertical-align:top;">
  			<table class="large-12 columns">
  				<tr>
  					<th>Basic Variables</th>
  					<th>Content</th>
  				</tr>
  				<?php 
  				 foreach ($basicvariable as $key => $val) { 
  				?>
  				<tr>
  					<td><?php echo $val['variable'];?></td>
  					<td><?php echo ucwords($val['content']);?></td>
  				</tr>
  				<?php 
  				}
  				?>
  			</table>
  		</td>
  		<td style="vertical-align:top;">
  		<table class="large-12 columns">
  				<tr>
  					<th>Other Variables</th>
  					<th>Content</th>
  				</tr>
  				<?php 
  				 foreach ($othervariable as $key => $val) { 
  				?>
  				<tr>
  					<td><?php echo $val['variable'];?></td>
  					<td><?php echo ucwords($val['content']);?></td>
  				</tr>
  				<?php 
  				}
  				?>
  			</table>
  		</td>
  	</tr>
  </table>
  
  <a class="close-reveal-modal">&#215;</a>
</div>

<!-- popup ends -->
      
      
 <script>
	function switchEditorView(view){
			
				var obj = document.getElementById("cke_32");
				CKEDITOR.tools.callFunction(52,obj);
	}
 
	$(document).ready(function(){	
		$('.jqte-test').jqte();

		$(document).on('change',"#type",function(){
			var type = $( "#type option:selected").val();

			if(type == "text")
			{
				$(".jqte_source").removeClass("jqte_hiddenField");
				$(".jqte_editor").addClass("jqte_hiddenField");
				$(".jqte_tool").addClass("jqte_hiddenField");
				$(".jqte_tool_21").removeClass("jqte_hiddenField");
			}
			else
			{
				$(".jqte_source").addClass("jqte_hiddenField");
				$(".jqte_editor").removeClass("jqte_hiddenField");
				$(".jqte_tool").removeClass("jqte_hiddenField");
				//$(".jqte_tool_21").addClass("jqte_hiddenField");
				
				
			}
			
				//$(".jqte_tool_21 a").trigger("click");
			
		});

	
		
	    /* CKEDITOR.replace(
                'body', {
                    removeButtons: 'About',
//                    removePlugins: 'sourcearea',
                }
              );
        	 
		var val = $( "#type option:selected").val();

		if(val == "text")
		{	
			setTimeout(function(){
				$("#cke_32_label").trigger("click");
			},2000,val);
		}
		

		$(document).on('click','#cke_32_label',function(){
			
			if($("#cke_1_contents").find('iframe').length){
				$("#type").val("html");
			}else{
				$("#type").val("text");
			}
		}); */
		
		/* 	$("#type").on( "change", function() {
			
				var selectedOption =  $(this).find(":selected").val();
				
				if(selectedOption == "text")
				{	
					$("#cke_32_label").trigger("click");
				}
				else if(selectedOption == "html")
				{		
					$("#cke_32_label").trigger("click");
				}
				
			}); */
	});
</script>  
        