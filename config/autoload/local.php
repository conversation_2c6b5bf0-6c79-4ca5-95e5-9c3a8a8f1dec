<?php
/**
 * Local Configuration Override
 *
 * This configuration override file is for overriding environment-specific and
 * security-sensitive configuration information. Copy this file without the
 * .dist extension at the end and populate values as needed.
 *
 * @NOTE: This file is ignored from Git by default with the .gitignore included
 * in ZendSkeletonApplication. This is a good practice, as it prevents sensitive
 * credentials from accidentally being committed into version control.
 */

return array(
    'db' => array(
        'driver' => 'Pdo_Sqlite',
        'database' => __DIR__ . '/../../data/db/mock.sqlite',
    ),
    'dbr' => array(
        'driver' => 'Pdo_Sqlite',
        'database' => __DIR__ . '/../../data/db/mock.sqlite',
    ),
    'service_manager' => array(
        'factories' => array(
            'Zend\Db\Adapter\Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
            'Write_Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
            'Read_Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
        ),
    ),
    'use_mock_db' => true,
    'development_mode' => true,

    // Global application settings
    'settings' => array(
        // Client settings
        'CLIENT_WEB_URL' => 'http://localhost:8888',

        // Merchant settings
        'MERCHANT_SUPPORT_EMAIL' => '<EMAIL>',
        'MERCHANT_WORKING_HOURS' => '9 AM - 5 PM',
        'MERCHANT_COMPANY_NAME' => 'Demo Company',
        'MERCHANT_SENDER_ID' => 'DEMO',

        // Global settings
        'GLOBAL_WEBSITE_PHONE' => '555-1234',
        'GLOBAL_AUTH_METHOD' => 'legacy', // Options: legacy, onesso

        // Signature settings
        'SIGNATURE_COMPANY_NAME' => 'Demo Company',

        // CMS settings
        'url_name' => 'default',
    ),
);
