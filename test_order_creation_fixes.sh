#!/bin/bash

# Test script to verify temp_order_payment fixes in actual API
echo "🧪 Testing Order Creation API - temp_order_payment Fixes"
echo "========================================================"
echo ""

BASE_URL="http://*************:8003/api/v2/order-management"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎯 Testing Fixes:${NC}"
echo "1. order_menu should show actual meal type (breakfast/lunch/dinner) instead of 'meal'"
echo "2. amount should be base price excluding tax (matching older records)"
echo ""

# Test Case 1: Breakfast Order
echo -e "${YELLOW}📋 Test 1: Breakfast Order${NC}"
echo "Creating order with breakfast meal (product_code: 342)..."
echo ""

breakfast_response=$(curl -s -X POST "${BASE_URL}/create" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "customer_id": 1,
    "customer_address": "Test Breakfast Order, Test Area, Test City - 400001",
    "location_code": 18,
    "location_name": "ORCHIDS The International School, Vikhroli",
    "city": 9,
    "city_name": "Mumbai",
    "meals": [
      {
        "product_code": 342,
        "quantity": 1
      }
    ],
    "start_date": "2025-08-01",
    "selected_days": [1],
    "delivery_time": "08:00:00",
    "delivery_end_time": "09:00:00",
    "food_preference": "veg",
    "subscription_days": 1
  }' 2>/dev/null)

echo "Response:"
echo "$breakfast_response" | jq . 2>/dev/null || echo "$breakfast_response"
echo ""

# Test Case 2: Lunch Order
echo -e "${YELLOW}📋 Test 2: Lunch Order${NC}"
echo "Creating order with lunch meal (product_code: 335)..."
echo ""

lunch_response=$(curl -s -X POST "${BASE_URL}/create" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "customer_id": 1,
    "customer_address": "Test Lunch Order, Test Area, Test City - 400001",
    "location_code": 18,
    "location_name": "ORCHIDS The International School, Vikhroli",
    "city": 9,
    "city_name": "Mumbai",
    "meals": [
      {
        "product_code": 335,
        "quantity": 1
      }
    ],
    "start_date": "2025-08-01",
    "selected_days": [1],
    "delivery_time": "12:00:00",
    "delivery_end_time": "13:00:00",
    "food_preference": "veg",
    "subscription_days": 1
  }' 2>/dev/null)

echo "Response:"
echo "$lunch_response" | jq . 2>/dev/null || echo "$lunch_response"
echo ""

# Test Case 3: Mixed Meals Order
echo -e "${YELLOW}📋 Test 3: Mixed Meals Order (Breakfast + Lunch)${NC}"
echo "Creating order with both breakfast and lunch meals..."
echo ""

mixed_response=$(curl -s -X POST "${BASE_URL}/create" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "customer_id": 1,
    "customer_address": "Test Mixed Order, Test Area, Test City - 400001",
    "location_code": 18,
    "location_name": "ORCHIDS The International School, Vikhroli",
    "city": 9,
    "city_name": "Mumbai",
    "meals": [
      {
        "product_code": 342,
        "quantity": 1
      },
      {
        "product_code": 335,
        "quantity": 1
      }
    ],
    "start_date": "2025-08-01",
    "selected_days": [1],
    "delivery_time": "08:00:00",
    "delivery_end_time": "13:00:00",
    "food_preference": "veg",
    "subscription_days": 1
  }' 2>/dev/null)

echo "Response:"
echo "$mixed_response" | jq . 2>/dev/null || echo "$mixed_response"
echo ""

echo -e "${GREEN}🔍 Verification Instructions:${NC}"
echo "To verify the fixes, check the database records:"
echo ""
echo "1. Check temp_order_payment table:"
echo "   SELECT temp_preorder_id, amount, order_menu, status, date"
echo "   FROM temp_order_payment"
echo "   ORDER BY temp_preorder_id DESC LIMIT 5;"
echo ""
echo "2. Expected results:"
echo "   - order_menu should show 'breakfast', 'lunch', etc. (not 'meal')"
echo "   - amount should be base price without tax"
echo ""
echo "3. Check temp_pre_orders for comparison:"
echo "   SELECT order_no, meal_type, amount, tax_amount, total_amount"
echo "   FROM temp_pre_orders"
echo "   ORDER BY pk_temp_pre_order_id DESC LIMIT 5;"
echo ""
echo -e "${BLUE}📊 Expected Behavior:${NC}"
echo "- Single meal order: order_menu = specific meal type (breakfast/lunch/dinner)"
echo "- Mixed meal order: order_menu = primary meal type (breakfast has highest priority)"
echo "- Amount = sum of base amounts from all meals (excluding tax)"
echo ""
echo -e "${GREEN}✅ Fixes Applied:${NC}"
echo "1. ✅ order_menu now shows actual meal type instead of generic 'meal'"
echo "2. ✅ amount now shows base price excluding tax (matching older records)"
echo "3. ✅ Primary meal type determination for mixed orders (breakfast > lunch > dinner > snack)"
echo ""
echo "🎉 Test completed! Check the database to verify the actual records."
