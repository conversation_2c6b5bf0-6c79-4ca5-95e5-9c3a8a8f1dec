<?php
/**
 * This File manages the products on fooddialer system
 * It is used to add ,update delete product
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
 
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Validator\File\Size;
use Zend\Filter\File\RenameUpload;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;
use Zend\Validator\Db;
use Zend\Db\Adapter\Adapter;
use RecursiveIteratorIterator as RecursiveIteratorIterator;
use RecursiveArrayIterator  as RecursiveArrayIterator;

use PHPExcel;
use PHPExcel_IOFactory;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\S3;
use Lib\Utility;
use Lib\QuickServe\CommonConfig as QScommon;
use Lib\QuickServe\Catalogue as QSCatalogue;

use QuickServe\Model\Product;
use QuickServe\Model\Meal;
use QuickServe\Model\ProductCalendar;
use QuickServe\Model\MealCalendar;
use QuickServe\Model\ImportProductValidator;
use QuickServe\Model\ImportCustomerValidator;
use QuickServe\Model\LocationValidator;
use QuickServe\Model\ProductTable;
use Admin\Form\MealForm;
use Admin\Form\ImportProductForm;
use Admin\Form\ProductForm;


class ProductController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\ProductTable model
	 *
	 * @var QuickServe\Model\ProductTable $productTable
	 */
    protected $productTable;
    protected $planTable;
    protected $productCalendarTable;
    
    /**
     * It has an instance of QuickServe\Model\MultilingualCodeSupportTable model
     * 
     * @var QuickServe\Model\MultilingualCodeSupportTable $multilingualCodeSupportTable
     */
    protected $multilingualCodeSupportTable;
    /**
     * It has an instance of QuickServe\Model\LocationMappingTable model
     *
     * @var QuickServe\Model\LocationMappingTable $locationMappingTable
     */
    protected $locationMappingTable;
    protected $mealTable;
    protected $mealCalendarTable;
    /**
     * It has an instance of AuthService model
     *
     * @var AuthService $authservice
     */
    protected $authservice;
	/**
	 * To display the list of products
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
    public function indexAction()
     {
     	if (! $this->authservice)
     	{
            $this->authservice = $this->getServiceLocator()
            ->get('AuthService');
     	}
     	
     	$iden = $this->authservice->getIdentity();
     	
     	$session_setting = new Container('setting');
     	
     	$layoutviewModel = $this->layout();
     	$acl =$layoutviewModel->acl;
     	$loguser = $layoutviewModel->loggedUser;
     	
     	$this->layout()->setVariables(array('page_title'=>"Products",'description'=>"Menu Product List",'breadcrumb'=>"Products"));
     	
     	return new ViewModel(array(
            'acl' => $acl,
            'loggedUser' => $loguser,
            'flashMessages'=> $this->flashMessenger()->getMessages()
     	));
     
     }

   /**
    * 
    * @return JsonModel
    */  

    public function ajxProductAction()
    {
       if (! $this->authservice)
       {
            $this->authservice = $this->getServiceLocator()->get('AuthService');
       }

        $iden = $this->authservice->getIdentity();

        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
        $calendar_setting = $setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
        $utility = Utility::getInstance();

        $layout = $this->layout();
        $acl = $layout->acl;
     	
     	$viewModel = new ViewModel();
     	
     	$loggedUser = $layout->loggedUser;
     	
     	$select = new QSelect();
     	
     	$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
     	$arrColumns = array('0'=>'name','1'=>'kitchen_code','2'=>'description','3'=>'unit_price','4'=>'threshold','5'=>"screen","6"=>"products.status");
     	$order_by = $arrColumns[$arrOrder[0]['column']];
     	$order = $arrOrder[0]['dir'];
     	 
     	$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
     	 
     	$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
     	$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
     	$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
     	$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
     	$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
     	 
     	$status = $this->params()->fromQuery('status');
     	 
     	$columns = $this->params()->fromQuery('columns');

     	$select->join(array('km' => "kitchen_master"), 'km.pk_kitchen_code=products.screen', array('pk_kitchen_code','kitchen_name','kitchen_alias','location_id','location','base_kitchen','kitchen_address'), QSelect::JOIN_LEFT);
     	
     	$select->where("product_type IN ('Main','Extra')");
     	
     	$select->where(
     	
        new \Zend\Db\Sql\Predicate\PredicateSet(
            array(
                //new \Zend\Db\Sql\Predicate\Operator('pk_product_code', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('name', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('kitchen_code', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('description', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('unit_price', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('threshold', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('screen', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('product_subtype', 'LIKE', '%'.$search.'%'),

            ),
            // optional; OP_AND is default
            \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )
     	
     	);
     	
     	$select->order($order_by . ' ' . $order);
     	
     	$products = $this->getProductTable()->fetchAll($select,$page,null,null,null,null,$_SESSION['adminkitchen'],null,'yes', array_column($iden->kitchens, 'fk_kitchen_code'));
     	$products->setCurrentPageNumber($page);
     	$products->setItemCountPerPage($itemsPerPage);
     	$products->setPageRange(7);
     	
     	$returnVar = array();
     	$returnVar['draw'] = $draw;
     	$returnVar['recordsTotal'] = $products->getTotalItemCount();
     	$returnVar['recordsFiltered'] = $products->getTotalItemCount();
     	$returnVar['data'] = array();
     	
     	
     	foreach($products as $product){
            $arrTmp = array();

            array_push($arrTmp,$product['name']);
            array_push($arrTmp,$product['kitchen_code']);
            //array_push($arrTmp,$product['description']);
            array_push($arrTmp,$utility->getLocalCurrency($product['unit_price']));
            array_push($arrTmp,$product['threshold']);
            $ktcode = ($product['screen'] !=0) ? $product['kitchen_name'].' ('.$product['kitchen_alias'].')' : "All Kitchen";
            array_push($arrTmp,$ktcode);
            array_push($arrTmp,$product['product_subtype']);
            $status =  ($product['status']=="1" )? '<span class="active">Active</span>':'<span class="inactive">Inactive</span>';
            array_push($arrTmp,$status);
            $str = "";
            $textadd = ($product['status']) == "0"? 'Activate' :'Deactivate';
            if($acl->isAllowed($loggedUser->rolename,'product','edit')){  

                    $str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('product', array('action'=>'edit', 'id' => $product['pk_product_code'])).'\'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
            }
            if($acl->isAllowed($loggedUser->rolename,'product','delete')){
                $str .=	' <a href="'.$this->url()->fromRoute('product',array('action'=>'delete', 'id' => $product['pk_product_code'])).'" onclick="return confirm(\'Are you sure you want to '.$textadd.' this product ?\')">';
                if($textadd=='Deactivate'){
                        $str .= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"  data-text-swap="Wait.."><i class="fa fa-ban"></i></button>';
                }else{
                        $str.='<button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
                }
                $str.= '</a>';
            }
            if( $acl->isAllowed($loggedUser->rolename,'product','edit') && $product['product_subtype'] == 'generic' && $setting['GLOBAL_ALLOW_MENU_PLANNER'] == 'yes' ){  
                    $str.='<button class="smBtn greenBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('product', array('action'=>'plan', 'id' => $product['pk_product_code'])).'\'" data-tooltip  title="Plan Product"><i class="fa fa-cutlery"></i></button>';
            }
     		
            /*if($acl->isAllowed($loggedUser->rolename,'product','edit')){
                if($calendar_setting == 1){
                  $str .= ' <a href="'.$this->url()->fromRoute('product', array('action'=>'product-calendar', 'id' => $product['pk_product_code'])).'"/>';
                  $textadd = ($product['status'])== "0"? 'Activate' :'Deactivate';
                  $str.= '<button class="smBtn greenBg has-tip tip-top"   data-tooltip title="Calendar"><i class="fa fa-calendar"></i></button>';
                }  
            }*/        		

        array_push($arrTmp,$str);

        array_push($returnVar['data'],$arrTmp);

    }
    return new JsonModel($returnVar);
 }
    /**
     * To add new product
     *
     * @return \Admin\Form\ProductForm
     */
    public function addAction()
    {
//        error_reporting(E_ALL);
//        ini_set('dispaly_errors','On');
    	$session_setting = new Container('setting');
    	$setting = $session_setting->setting;
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	
    	$libCommon = QScommon::getInstance($sm);
    	
    	$config_variables = $sm->get('config');
		$multilingual_code_model = $this->getMultilingualCodeSupportTable();//$adapt
		$location_mapping_model = $this->getLocationMappingTable();
    	// Get upload path from global config file
    	//$path = $this->getServiceLocator()->get('config')['source_path'];
//        $image = new ImageMagick();
    	$form = new ProductForm($sm);
    	
        //$form = new ContentForm();
        $form->get('submit')->setAttribute('value', 'Add');

        $location_mapped_array = array();

        $request = $this->getRequest();
        
        if ($request->isPost()) {

            // collecting data for location mapping through post and not from database
            $location_mapped_array = (isset($_POST['location']) && !empty($_POST['location'])) ? $_POST['location'] : array();
            if( empty($location_mapped_array) ) { $location_mapped_array = array(); }

        //	echo "<pre>";print_r($request->getPost());die;
            $product = new Product();
            $product->getInputFilter()->get('name')
            ->getValidatorChain()                  // Filters are run second w/ FileInput
            ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table'     => 'products',
                    'field'     => 'name',
                    'adapter'   => $adapt,
                    'message'   => 'Product already exists',
                    'exclude'	=> 'product_type !="Meal"'
            	)
            ));

            if($_FILES['image_path']['type']=='') {
                $form->get('image_path')->setMessages(array('Please select file to upload.'));
            }

            $data = array_merge(
                $this->getRequest()->getPost()->toArray(),
                $this->getRequest()->getFiles()->toArray()
            );

			$product->getInputFilter()->get('swap_with')->setRequired(false);
			$product->getInputFilter()->get('swap_charges')->setRequired(false);
			$product->getInputFilter()->get('is_swappable')->setRequired(false);
			
			if($setting['GLOBAL_ALLOW_MENU_PLANNER']=='yes'){

				$product->getInputFilter()->get('is_swappable')->setRequired(true);
				
				if($data['is_swappable'] == 1){

					$product->getInputFilter()->get('swap_with')->setRequired(true);

					if($data['swap_with']=='swapcharge'){

						$product->getInputFilter()->get('swap_charges')->setRequired(true);

					}else{
						$product->getInputFilter()->remove('swap_charges');
					}
					
				}else{
					$product->getInputFilter()->remove('swap_charges');
				}
				
			}

            $form->setInputFilter($product->getInputFilter());
            $form->setData($data);
           
            if ($_FILES['image_path']['error'] == 0) {
                $product->addImageFilter();
            }
            if ($form->isValid() && $_FILES['image_path']['type']!="")
            { 
                            	
            	$data=($form->getData());
            	
            	$product->exchangeArray($data);
            	
                $product->image_path = $data['image_path']['tmp_name'];//source

                if($data['product_type']=="Meal" || $data['product_type']=="Main"){
                    $image_height_width   = $this->getServiceLocator()->get('Config')['product_image_resize'];

                }elseif($data['product_type']=="Extra"){
                    $image_height_width   = $this->getServiceLocator()->get('Config')['extra_product_image_resize'];
                }
                
                $product->image_path = $libCommon->uploadImage($product->image_path);
                
                $data_product = $this->getProductTable()->saveProduct($product);
                
                if($data_product){
                    
                    $full_name=$loguser->first_name." ".$loguser->last_name;
                    $product_name=$data_product['name'];
                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                    $activity_log_data['context_name']= $full_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'product';
                    $activity_log_data['action']= 'add';
                    $activity_log_data['description']= "Product : New product '$product_name' created.";

                    $libCommon->saveActivityLog($activity_log_data);
                }

                $id = $this->getProductTable()->getLastInsertValue();
                $multilingual = $multilingual_code_model->saveMultilingualCodeSupport($_POST['other_language'], $id);
                ($data_product)?$this->flashMessenger()->addSuccessMessage("Product added successfully"):$this->flashMessenger()->addErrorMessage("Error adding product.");

                return $this->redirect()->toRoute('product');
            }else{
               
                //dd($form->getMessages());
            }

          
        }
        
        //$form->get('number_of_pages')->setOptions();
        $this->layout()->setVariables(array('page_title'=>"Add Product",'breadcrumb'=>" Add Product"));
        return array('form' => $form,
            'language_array' => $config_variables['supported_nonenglish_languages'],
            'location_mapped_array' => $location_mapped_array,
            'settings'=>$setting
        );
    }
	/**
	 * To update product of given product id
	 *
	 * @param int id
	 * @return \Admin\Form\ProductForm
	 */
    public function editAction()
    {
        
    	$session_setting = new Container('setting');
    	$setting = $session_setting->setting;
        
        $id = (int) $this->params('id');
        if (!$id)
        {
            return $this->redirect()->toRoute('product', array('action' => 'add'));
        }
        
        
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
         
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $config = $sm->get('Config');
        
        $s3 = $sm->get('S3');
        
        $libCommon = QScommon::getInstance($sm);
        
    	$config_variables = $sm->get('config');
    	//$bucketName = $config_variables['aws_details']['bucket'];
    	$bucketName = $s3::$bucketInfo['bucket'];
    	//$bucketUrl = $config_variables['aws_bucket_url'];
    	$bucketFolder = $setting['S3_BUCKET_URL'];
    	
		$multilingual_code_model = $this->getMultilingualCodeSupportTable();
		$location_mapping_model = $this->getLocationMappingTable();
        $form = new ProductForm($sm);
        
        $product = $this->getProductTable()->getProduct($id);
        $old_image = $product['image_path'];
        
        $product['category'] = explode(',',$product['category']);

        $local_language_array = $location_mapped_array = array();
        $records = $multilingual_code_model->getMultilingualCodeSupport(null, array('context_ref_id' => $id, 'context' => 'product_language', 'status' => 1) );
        $local_language_array = $multilingual_code_model->getArray($records);

        $records = $location_mapping_model->getLocationMapping(null, array('context_ref_id' => $id, 'context' => 'product', 'status' => 1) );
        $location_mapped_array = $location_mapping_model->getArray($records);
        $location_mapped_array = $location_mapping_model->getLocationIds($location_mapped_array);
        if( empty($local_language_array) ) { $local_language_array = array(); }
    	if( empty($location_mapped_array) ) { $location_mapped_array = array(); }
      
        $form->bind($product);
        
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $product->name;

        $request = $this->getRequest();
        
        if ($request->isPost()) {
        	
        	$product = new Product();
            $product->image_path = $old_image;
        	//$product->getInputFilter()->get('image_path')->setRequired(false);
        	$product->getInputFilter()->get('name')
        	->getValidatorChain()                  // Filters are run second w/ FileInput
        	->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table'     => 'products',
                    'field'     => 'name',
                    'adapter'   => $adapt,
                    'message'   => 'Product Already exists',
                    'exclude' => array(
                        'field' => 'name',
                        'value' => $val,
                    )
        	)
        	));

			$data = array_merge(
            		$this->getRequest()->getPost()->toArray(),
            		$this->getRequest()->getFiles()->toArray()
            );			

			$product->getInputFilter()->get('swap_with')->setRequired(false);
			$product->getInputFilter()->get('swap_charges')->setRequired(false);
			$product->getInputFilter()->get('is_swappable')->setRequired(false);
			
			if($setting['GLOBAL_ALLOW_MENU_PLANNER']=='yes'){

				$product->getInputFilter()->get('is_swappable')->setRequired(true);
				
				if($data['is_swappable'] == 1){

					$product->getInputFilter()->get('swap_with')->setRequired(true);

					if($data['swap_with']=='swapcharge'){

						$product->getInputFilter()->get('swap_charges')->setRequired(true);

					}else{
						$product->getInputFilter()->remove('swap_charges');
					}
					
				}else{
					$product->getInputFilter()->remove('swap_charges');
				}
				
			}

            //$product->setAdapter($adapt);
            $form->setInputFilter($product->getInputFilter());
            $form->setData($data);
            
        	if ($_FILES['image_path']['error'] == 0) {
				$product->addImageFilter();
			}
			
            if ($form->isValid()) {
            	$data=($form->getData());
            	
            	//print_r($data);exit;
            	$product->exchangeArray($data);

                //if no image is uploaded then do not set the image path

                if($data['image_path']['tmp_name']!=''){

                    $product->image_path=$data['image_path']['tmp_name'];

                    if($data['product_type']=="Meal" || $data['product_type']=="Main"){
                       
                        $image_height_width   = $this->getServiceLocator()->get('Config')['product_image_resize'];

                    }elseif($data['product_type']=="Extra"){
                        
                        $image_height_width   = $this->getServiceLocator()->get('Config')['extra_product_image_resize'];
                    }
                   
                    $product->image_path = $libCommon->uploadImage($product->image_path, $old_image);
                }else{
                    
                    if(isset($data['pk_product_code']) && $data['pk_product_code']!=""){
                        
                        $product->image_path = $product->image_path;
                    }
                }
                
                
                $data_product = $this->getProductTable()->saveProduct($product);

                $full_name=$loguser->first_name." ".$loguser->last_name;
                $product_name=$product->name;
                $activity_log_data=array();
                $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                $activity_log_data['context_name']= $full_name;
                $activity_log_data['context_type']= 'user';
                $activity_log_data['controller']= 'product';
                $activity_log_data['action']= 'edit';
                $activity_log_data['description']= "Product : product '$product_name' updated.";
                //$activity_log_data['description']= "'$product_name' product updated by $full_name";
                $libCommon->saveActivityLog($activity_log_data);

                $multilingual = $multilingual_code_model->saveMultilingualCodeSupport($_POST['other_language'], $id);//$this->getProductTable()->getLastInsertValue()
            	
                $this->flashMessenger()->addSuccessMessage("Product updated successfully",'success');

                // Redirect to list of albums
                return $this->redirect()->toRoute('product');

            }
           
        }
        
        $hostname = $s3->getHostname();
        
        $fileonAws = false;
                
        if ($s3->getObject($bucketName, "$bucketFolder/product/$product->image_path")) {
            $fileonAws = true;
        }
        else {
            $fileonAws = false;
        }
        
        $this->layout()->setVariables(array('page_title'=>"Edit Product",'breadcrumb'=>"Edit Product"));
        return array(
            'id' => $id,
            'form' => $form,
            'product' => $product,
            'language_array' => $config_variables['supported_nonenglish_languages'],
            'local_language_array' => $local_language_array,
            'location_mapped_array' => $location_mapped_array,
            'aws_folder'=>$bucketFolder,
            'image_on_aws'=>$fileonAws,
            'aws_bucket_url' => $hostname,
            'settings'=>$setting
        );
    }
	/**
	 * To delete product of given product id
	 *
	 * @param int id
	 * @return route product
	 */
    public function deleteAction() {
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	

    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	
    	$libCommon = QScommon::getInstance($sm);
    	
        $id = (int) $this->params('id');
        
        if(!$id){
            return $this->redirect()->toRoute('product');
        }
        
        $select = new QSelect();
        $select->where(array('pk_product_code'=>$id));
        $products = $this->getProductTable()->fetchAll($select);
        $arrproducts=$products->toArray();
        $product_name=$arrproducts[0]['name'];
        $product_status=($arrproducts[0]['status'])=='1'?'deactivated':'activated';

        $data_product = $this->getProductTable()->deleteProduct($id);
        
        if($data_product)
        {
            $full_name=$loguser->first_name." ".$loguser->last_name;
            $activity_log_data=array();
            $activity_log_data['context_ref_id']=$loguser->pk_user_code;
            $activity_log_data['context_name']= $full_name;
            $activity_log_data['context_type']= 'user';
            $activity_log_data['controller']= 'product';
            $activity_log_data['action']= 'delete';
            $activity_log_data['description']= "Product : product '$product_name' $product_status.";

            //$activity_log_data['description']= "'$product_name' product $product_status  by $full_name";
            $libCommon->saveActivityLog($activity_log_data);
        }
        
            //print_r($data_product);exit;
            ($data_product)?$this->flashMessenger()->addSuccessMessage("Product Updated Successfully"):$this->flashMessenger()->addErrorMessage("Error deleting product.");

            return $this->redirect()->toRoute('product');
    }
    
    /**
     * This action provide utility to add and edit product plan.
     */
    public function planAction() {
    	
    	//error_reporting(E_ALL);
        //ini_set('display_errors', 'On');
    	
        $id = (int) $this->params('id');
   		
    	//$kitchen = (int) $this->params('kitchen');
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$kitchen = $_SESSION['adminkitchen'];
    	
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;
    	
    	$menus = $setting['MENU_TYPE'];
        
    	$request = $this->getRequest();
    	$year = $request->getPost('year');
    	$selectedmenu = $request->getPost('selectedmenu');
 
    	if($year==''){
            $year = date('Y');
    	}
    	$month = $request->getPost('month');
    	if($month==''){
            $month = date('m');
    	}
     	if($selectedmenu==''){
            $selectedmenu ='lunch';
    	}
    	
    	//$select = new QSelect();
    	//$select->where(array('pk_product_code'=>$id));
    	$product = $this->getProductTable()->getProduct($id);
    	$productname = $product['name'];
    	$breadcrum = "Product > ".ucfirst($productname);
    	//dd($product);
    	//dd($planned_products_from_db);
     	$this->layout()->setVariables(array('page_title'=>"Product Planner",'description'=>"Plan products weekwise",'breadcrumb'=> $breadcrum));
    	$view = new ViewModel();
    	//echo $selectedmenu;exit;
    	$view->setVariables(
            array(
	    	'acl'=>$acl,
	    	'product' => $product,
	    	'id' => $id,
	    	'kitchen' => $kitchen,
	    	'year'	=> $year,
	    	'month' =>  $month,
	    	'menus' => $menus,
	    	'selected_menu'=>$selectedmenu
            )
    	);
    	//echo "<pre>fff";print_r($view);exit;
    	return $view;
        
    }
    /**
     * Get instance of QuickServe\Model\LocationMappingTable.
     * 
     * @method getLocationMappingTable
     * @access public
     * @return QuickServe\Model\LocationMappingTable
     */
    public function getLocationMappingTable() {
    	if (!$this->locationMappingTable) {
            $sm = $this->getServiceLocator();
            $this->locationMappingTable = $sm->get('QuickServe\Model\LocationMappingTable');
    	}
    	return $this->locationMappingTable;
    }
    /**
     * Get instance of QuickServe\Model\MultilingualCodeSupportTable.
     * 
     * @method getMultilingualCodeSupportTable
     * @access public
     * @return QuickServe\Model\MultilingualCodeSupportTable
     */
    public function getMultilingualCodeSupportTable() {
    	if (!$this->multilingualCodeSupportTable) {
            $sm = $this->getServiceLocator();
            $this->multilingualCodeSupportTable = $sm->get('QuickServe\Model\MultilingualCodeSupportTable');
    	}
    	return $this->multilingualCodeSupportTable;
    }
	/**
	 * Get instance of QuickServe\Model\ProductTable
	 *
	 * @return QuickServe\Model\ProductTable
	 */
    public function getProductTable() {
    	if (!$this->productTable) {
            $sm = $this->getServiceLocator();
            $this->productTable = $sm->get('QuickServe\Model\ProductTable');
    	}
    	return $this->productTable;
    }
    
	/**
	 * Get instance of QuickServe\Model\PlanMasterTable
	 *
	 * @return QuickServe\Model\PlanMasterTable
	 */
    public function getPlanTable() {
    	if (!$this->planTable) {
            $sm = $this->getServiceLocator();
            $this->planTable = $sm->get('QuickServe\Model\PlanMasterTable');
    	}
    	return $this->planTable;
    }
    
    /**
     * Get instance of QuickServe\Model\MealTable
     *
     * @return QuickServe\Model\MealTable
     */
    public function getMealTable() {
       
    	if (!$this->mealTable) {
            $sm = $this->getServiceLocator();
            //echo "<pre>";print_r(get_class($sm->get('QuickServe\Model\MealTable')));echo "</pre>";die;
            //$sm->get('QuickServe\Model\MealTable');
            $this->mealTable = $sm->get('QuickServe\Model\MealTable');
    	}
        
    	return $this->mealTable;
    }
    
    /**
     * Display/ list of meals 
     * @return \Zend\View\Model\ViewModel
     */
    public function mealAction()
    {
    	if (! $this->authservice)
    	{
            $this->authservice = $this->getServiceLocator()
            ->get('AuthService');
    	}
    	
    	$iden = $this->authservice->getIdentity();
    	
    	$session_setting = new Container('setting');
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$this->layout()->setVariables(array('page_title'=>"Meals and Combos",'description'=>"Meal and Combo List",'breadcrumb'=>"Meals and Combos"));
    	
    	return new ViewModel(array(
            'acl' => $acl,
            'loggedUser' => $loguser,
            'flashMessages'=> $this->flashMessenger()->getMessages()
    	));
    	
    	
    }
    

    public function ajxMealAction() {
    
    	if (! $this->authservice) {
    		$this->authservice = $this->getServiceLocator()
    		->get('AuthService');
    	}
    	
    	$iden = $this->authservice->getIdentity();
    	
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;
    	$calendar_setting = $setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
    	$utility = Utility::getInstance();
    	
    	$layout = $this->layout();
    	$acl = $layout->acl;
    	
    	$viewModel = new ViewModel();
    	
    	$loggedUser = $layout->loggedUser;
    	
    	$select = new QSelect();
    	
    	$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
    	$arrColumns = array('0'=>'pk_product_code','1'=>'name','2'=>'unit_price','3'=>'threshold','4'=>"status");
    	
    	$order_by = $arrColumns[$arrOrder[0]['column']];
    	$order = $arrOrder[0]['dir'];
    	 
    	$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
    	 
    	$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
    	$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
    	$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
    	$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
    	$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
    	 
    	$status = $this->params()->fromQuery('status');
    	 
    	$columns = $this->params()->fromQuery('columns');
    	 
    	$select->where("product_type='Meal'");
    	
    	$select->where(
    	
            new \Zend\Db\Sql\Predicate\PredicateSet(
                array(
                    //new \Zend\Db\Sql\Predicate\Operator('pk_product_code', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('name', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('unit_price', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('threshold', 'LIKE', '%'.$search.'%'),
                    new \Zend\Db\Sql\Predicate\Operator('status', 'LIKE', '%'.$search.'%'),
                ),
                // optional; OP_AND is default
                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )
    	);
    	
    	$select->order($order_by . ' ' . $order);
    	
    	$meals = $this->getProductTable()->fetchAll($select,$page,null,null,null,null,$_SESSION['adminkitchen'], null, 'yes', array_column($iden->kitchens, 'fk_kitchen_code'));
//    	dd($meals);
    	$meals->setCurrentPageNumber($page)
    	->setItemCountPerPage($itemsPerPage)
    	->setPageRange(7);
    	
    	$returnVar = array();
    	$returnVar['draw'] = $draw;
    	$returnVar['recordsTotal'] = $meals->getTotalItemCount();
    	$returnVar['recordsFiltered'] = $meals->getTotalItemCount();
    	$returnVar['data'] = array();
        
    	foreach($meals as $meal) {
            
            $arrTmp = array();
            if($meal->is_swappable==1) {
                array_push($arrTmp,'<span title="Swappable" class="orange-theme"><i class="fa fa-exchange" aria-hidden="true"></i></span>');    
            }else{
                array_push($arrTmp,'');    
            }
            
            array_push($arrTmp,$meal['name']);
            if($calendar_setting == 0){
                    $mealObj = $this->getMealTable()->getMeal($meal['pk_product_code']);
                    $mealContent = $mealObj->getItemsToString();
                    //array_push($arrTmp,$mealContent);
            }
            
            array_push($arrTmp,$utility->getLocalCurrency($meal['unit_price']));
            array_push($arrTmp,$meal['threshold']);
            $status =  ($meal['status']=="1" )? '<span class="active">Active</span>':'<span class="inactive">Inactive</span>';
            array_push($arrTmp,$status);
            $str = "";
            $textadd = ($meal['status']) == "0"? 'Activate' :'Deactivate';
            if($acl->isAllowed($loggedUser->rolename,'meal','edit')){

                    $str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('meal', array('action'=>'add-meal', 'id' => $meal['pk_product_code'])).'\'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
            }
            if($acl->isAllowed($loggedUser->rolename,'meal','delete')){
                $str .=	' <a href="'.$this->url()->fromRoute('meal',array('action'=>'delete-meal', 'id' => $meal['pk_product_code'])).'" onclick="return confirm(\'Are you sure you want to '.$textadd.' this meal ?\')">';
                if($textadd=='Deactivate'){
                        $str .= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"  data-text-swap="Wait.."><i class="fa fa-ban"></i></button>';
                }else{
                        $str.='<button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
                }
                $str.= '</a>';
            }
    		 
            if($acl->isAllowed($loggedUser->rolename,'meal','edit')){
                if($calendar_setting == 1){
                        $str .= '<a href="'.$this->url()->fromRoute('meal', array('action'=>'add-meal-calendarwise', 'id' => $meal['pk_product_code'] , 'kitchen' => $meal['screen'])).'"/>';
                        $str.= '<button class="smBtn greenBg has-tip tip-top"   data-tooltip title="Menus"><i class="fa fa-cutlery"></i></button>';
                } 
            }
            $str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('meal', array('action'=>'mealplan', 'id' => $meal['pk_product_code'])).'\'" data-tooltip title="View Planned Menu " data-text-swap="Wait.."><i class="fa fa-eye"></i></button>';

            array_push($arrTmp,$str);

            array_push($returnVar['data'],$arrTmp);
    		
    	}
    	return new JsonModel($returnVar);
    }
    
    /**
     * To add new Meal
     *
     * @return \Admin\Form\MealForm
     */
    public function addMealAction()
    {
    	$session_setting = new Container('setting');
    	$setting = $session_setting->setting;
    	$calendar_setting = $setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
    	$flag=0;
    	$err_message='';
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    
    	//echo "Enter"; exit;
    	$sm = $this->getServiceLocator();
    	//$adapt = $sm->get('Write_Adapter');
    	$s3 = $sm->get('S3');
    	
    	$libCommon = QScommon::getInstance($sm);
    	
    	$config_variables = $sm->get('config');
    	
    	//$bucketName = $config_variables['aws_details']['bucket'];
    	$bucketName = $s3::$bucketInfo['bucket'];
        
    	//$bucketUrl = $config_variables['aws_bucket_url'];
    	$bucketFolder = $setting['S3_BUCKET_URL'];
    	
    	$multilingual_code_model = $this->getMultilingualCodeSupportTable();//$adapt
        
    	$location_mapping_model = $this->getLocationMappingTable();
                                
    	// Get upload path from global config file
    	//$path = $this->getServiceLocator()->get('config')['source_path'];
       
    	$form = new MealForm($sm); 
        
    	$form->get('submit')->setAttribute('value', 'Add');
        
    	$request = $this->getRequest();
        
    	$id = $this->params()->fromRoute('id',null);
    	
    	$mealRow = null;$local_language_array = $location_mapped_array = array();
    	 
    	if($id){
    	 	
            $mealRow = $this->getMealTable()->getMeal($id);
            $arrMeal = (array) $mealRow;

            $form->setData($arrMeal);
            
            $records = $multilingual_code_model->getMultilingualCodeSupport(null, array('context_ref_id' => $id, 'context' => 'meal_language', 'status' => 1) );
            $local_language_array = $multilingual_code_model->getArray($records);
            $records = $location_mapping_model->getLocationMapping(null, array('context_ref_id' => $id, 'context' => 'meal', 'status' => 1) );
            $location_mapped_array = $location_mapping_model->getArray($records);
            $location_mapped_array = $location_mapping_model->getLocationIds($location_mapped_array);
            
            if( empty($local_language_array) ) { $local_language_array = array(); }
            if( empty($location_mapped_array) ) { $location_mapped_array = array(); }
    	}
    	//echo "files<pre>"; print_r($_FILES); exit();
    	
    	$old_image = $mealRow->image_path;
    	
    	$messages = array();
    	
    	if ($request->isPost()) {
    	    
            $data = $request->getPost();
            //echo "<pre>"; print_r($data); exit();
            $arrFilter = array(
                    'table'     => 'products',
                    'field'     => 'name',
                    'adapter'   => $sm->get('Read_Adapter'),  
                    'message'   => 'Meal already exists',
                    //'exclude' => " product_type IN ('Main','Extra') "
                    'exclude'	=> 'product_type !="Extra"'
            );

        if(isset($data['pk_product_code']) && $data['pk_product_code']!=""){
            
            $mealRow = $this->getMealTable()->getMeal($data['pk_product_code']);
            $arrFilter['exclude'] = " ( pk_product_code = ".$data['pk_product_code']." AND product_type IN ('Main','Extra') )";
        }
    		  
        $meal = new Meal();
       
        $meal->getInputFilter()->get('name')
        ->getValidatorChain()                  // Filters are run second w/ FileInput
        ->attach(new \Zend\Validator\Db\NoRecordExists($arrFilter));
              
        //echo "<pre>"; print_r($_FILES['image_path']); exit();
    	        
        
        if(!$id)
        {
          
            if($_FILES['image_path']['type']=='')
            {
                $flag=1;
                $form->get('image_path')->setMessages(array('Please select file to upload.'));
                //echo "inside";
            }
            else 
            {
                //echo "else";
                $filetype = array(
                    'image/gif','image/jpg','image/jpeg','image/png','image/x-png',

                );
                if (!in_array($_FILES['image_path']['type'],$filetype) ) {
                    $flag=1;
                    $form->get('image_path')->setMessages(array( 'Please Upload only image files'));
                }
            }
        }
        $meal->getInputFilter()->get('swap_with')->setRequired(false);
        $meal->getInputFilter()->get('swap_charges')->setRequired(false);
        
        if($data['is_swappable'] == 1){

            $meal->getInputFilter()->get('swap_with')->setRequired(true);

            if($data['swap_with']=='swapcharge'){

                $meal->getInputFilter()->get('swap_charges')->setRequired(true);

            }else{
                $meal->getInputFilter()->remove('swap_charges');
            }
    		
            }else{
                $meal->getInputFilter()->remove('swap_charges');
            }
    	
            //$meal->setAdapter($adapt);

            $form->setInputFilter($meal->getInputFilter());

            $data = array_merge(
                $this->getRequest()->getPost()->toArray(),
                $this->getRequest()->getFiles()->toArray()
            );

            $form->setData($data);
    		
    		
            //image validation
            if ($_FILES['image_path']['error'] == 0) {
                    $meal->addImageFilter();
            }
    		
            if ($form->isValid() && $flag==0)
            {

                $fdata = $form->getData();
                
                $meal->exchangeArray($data);

                if ($_FILES['image_path']['error'] == 0) {

                    $imageName =  substr($fdata['image_path']['tmp_name'],strrpos($fdata['image_path']['tmp_name'], "/")+1);

                    $meal->image_path = $imageName;

                    $imagePath   = $fdata['image_path']['tmp_name'];
                    
                    $meal->image_path = $libCommon->uploadImage($imagePath);

                    //S3 Implementation Ends

                }else{

                    if(isset($data['pk_product_code']) && $data['pk_product_code']!=""){

                        $meal->image_path = $mealRow->image_path;
                    }
                }

                /***************************************************/    			

                //echo "<pre>";print_r($meal);echo "</pre>";die;

                $data_meal = $this->getMealTable()->saveMeal($meal);

                if($data_meal)
                {
                    $start = "";
                    if(empty($id)){
                            $action =  "created";
                            $start = "New";
                    }else {
                            $action = "updated";
                            $start = "";
                    };


                    $meal_name=$meal->name;
                    $full_name=$loguser->first_name." ".$loguser->last_name;
                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                    $activity_log_data['context_name']= $full_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'product';
                    $activity_log_data['action']= 'addmeal';
                    $activity_log_data['description']= "Meal : $start meal '$meal_name' $action.";
                    //$activity_log_data['description']= "'$meal_name' meal $action by $full_name";
                    $libCommon->saveActivityLog($activity_log_data);
                }


                $id = empty($id) ? $this->getMealTable()->getLastInsertValue() : $id;
                $multilingual = $multilingual_code_model->saveMultilingualCodeSupport($_POST['other_language'], $id);//$this->getMealTable()->getLastInsertValue()

                ($data_meal) ? $this->flashMessenger()->addSuccessMessage("Meal added successfully") : $this->flashMessenger()->addErrorMessage("Error adding meals.");
                // Redirect to list of product
                return $this->redirect()->toRoute('meal');

            }else{

                    //$messages = $form->getMessages();
                    //echo "<pre>";print_r($messages);echo "</pre>";die;
       		}
    
    
    	}
    	
    	SHOWDETAIL:
    		
    	if($mealRow != null){
    		$this->layout()->setVariables(array('page_title'=>"Edit Meal and Combo",'breadcrumb'=>" Edit Meal and Combo"));
    	}else{
    		$this->layout()->setVariables(array('page_title'=>"Add Meal and Combo",'breadcrumb'=>"Add Meal and Combo"));
    	}
    	
    	$hostname = $s3->getHostname();
    	
    	$fileonAws = false;
    	
    	if ($mealRow->image_path !="" && $s3->getObject($bucketName, "$bucketFolder/product/$mealRow->image_path")) {
    		$fileonAws = true;
    	}
    	else {
    		$fileonAws = false;
    	}
    	
    	return array(
    		'form' => $form,
    		'mealRow' => $mealRow,
    		'messages' => $messages,
    		'err_message'=>$err_message,
    		'language_array' => $config_variables['supported_nonenglish_languages'],
    		'local_language_array' => $local_language_array,
        	'location_mapped_array' => $location_mapped_array,
    		'calendar_setting' => $calendar_setting,
    		'aws_folder'=>$bucketFolder,
    		'image_on_aws'=>$fileonAws,
    		'aws_bucket_url' => $hostname,
    	    'settings' => $setting,
    	);
    	
    }
    
    /**
     * To delete product of given product id
     *
     * @param int id
     * @return route product
     */
    public function deleteMealAction() {
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;

    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	 
    	$libCommon = QScommon::getInstance($sm);
    	
    	$id = (int) $this->params('id');
        
    	if(!$id){
    		return $this->redirect()->toRoute('meal');
    	}
    	
    	
    	$select = new QSelect();
    	$select->where(array('pk_product_code'=>$id));
    	
    	$products = $this->getProductTable()->fetchAll($select);
    	$arrproducts = $products->toArray();
    	$product_name=$arrproducts[0]['name'];
    	$product_status=($arrproducts[0]['status'])=='1'?'deactivated':'activated';
    	
    
    	$data_product = $this->getProductTable()->deleteProduct($id);
    	
    	
    	if($data_product)
    	{
    		$full_name=$loguser->first_name." ".$loguser->last_name;
    		$activity_log_data=array();
    		$activity_log_data['context_ref_id']=$loguser->pk_user_code;
    		$activity_log_data['context_name']= $full_name;
    		$activity_log_data['context_type']= 'user';
    		$activity_log_data['controller']= 'product';
    		$activity_log_data['action']= 'delete';
    		$activity_log_data['description']= "Meal : Meal '$product_name' $product_status.";
    		//$activity_log_data['description']= "'$product_name' meal $product_status  by $full_name";
    		$libCommon->saveActivityLog($activity_log_data);
    	}
    	
    
    	//print_r($data_product);exit;
    	($data_product)?$this->flashMessenger()->addSuccessMessage("Meal Updated Successfully"):$this->flashMessenger()->addErrorMessage("Error deleting product.");
    
    	return $this->redirect()->toRoute('meal');
    }    
    
    
    
    public function importAction(){

    	/* error_reporting(E_ALL);
    	ini_set('display_errors','On'); */

        ini_set('memory_limit','800M');

    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$request = $this->getRequest();
    	
    	$form = new ImportProductForm($sm);
    	$importProductValidator = new ImportProductValidator();
    	$columns = array();

    	$columns = $importProductValidator->getImportColumns();
    	$colLocMapping = array('context','context_ref_id','location_id','location');
    	
    	$errors = array();
    	$importedData = array();
    	
    	if ($request->isPost())
    	{
            $data = $request->getPost();
            //echo "<pre>Post";print_r($request->getPost());die;
            if($request->getPost('submit')!=''){


            $form->setValidationGroup('import_file');

            $post = array_merge(
                            $this->getRequest()->getPost()->toArray(),
                            $this->getRequest()->getFiles()->toArray()
            );

            //$importProductValidator->setAdapter($adapt);
            $form->setInputFilter($importProductValidator->getInputFilter());
            $form->setData($post);
    	
            if ($form->isValid())
            {
                $data = $form->getData();

                $importProductValidator->exchangeArray($data);

                $extension = substr($data['import_file']['name'],strpos($data['import_file']['name'],".")+1);

                $filename = substr($data['import_file']['tmp_name'],strpos($data['import_file']['tmp_name'],"/data")+1);

                $filename = $_SERVER['DOCUMENT_ROOT']."/".$filename;

                $objPHPExcel = new PHPExcel();

                $objExcelReader = PHPExcel_IOFactory::load($filename);

                $worksheet = $objExcelReader->getActiveSheet();

                foreach ($worksheet->getRowIterator() as $row) {

                    $rowIndex = $row->getRowIndex();

                    //$importedData[$rowIndex] =

                    $cellIterator = $row->getCellIterator();
                    $cellIterator->setIterateOnlyExistingCells(false); // Loop all cells, even if it is not set.

                    foreach ($cellIterator as $cell) {
                            $importedData[$rowIndex][] = $cell->getValue();
                    }

                }

                unlink($filename);
            }
    	 }
    	 elseif($request->getPost('import')!=''){

            $setting_session = new Container('setting');
            $setting = $setting_session->setting;
            //echo "<pre>PostImport";print_r($request->getPost());die;

            //	$selectedColumnsStr = $request->getPost('selectedColumnsList');
            //$selectedColumns = explode(',',$selectedColumnsStr);

            foreach($columns['madatoryColumnList'] as $pkey=>$pcol){

                if(!in_array($pkey,$data['importColumns'])){

                    $errors["column"][] = $pcol;
                    $str = str_replace("_", " ", $pcol)." is mandatory <br />";
                    $errors["msg"] = (isset($errors["msg"])) ? $errors["msg"].$str : $str ;
                }
            }
    	 	 
            if(!empty($errors)){
                    goto SHOWDETAILS;
            }
    	 	 
            $isEmpty = new \Zend\Validator\NotEmpty();
            $validLength = new \Zend\Validator\StringLength(array('max' => 9));
            $importProductValidator = new ImportProductValidator();
            //$importProductValidator->setAdapter($adapt);

            $importCustomerValidator = new ImportCustomerValidator();
            //$importCustomerValidator->setAdapter($adapt);

            $objLocation = $sm->get('QuickServe\Model\LocationTable');
            $objLocationValidator = new LocationValidator();
            //$objLocationValidator->setAdapter($adapt);

            $selectedColumns = array();

            foreach($data['importColumns'] as $key=>$col){

                if($col !=""){

                    $selectedColumns[] = $col;
                }

            }
            // add default columns to be inserted
            $selectedColumns[] = "image_path";
            $selectedColumns[] = "created_date";
            $selectedColumns[] = "company_id";
            $selectedColumns[] = "unit_id";
            $selectedColumns[] = "is_custom";

            $selectedColumns = array_diff($selectedColumns, array('available_location'));
            $placeholder = array_fill(0, count($selectedColumns), '?');
            $placeholder = "(" . implode(',', $placeholder) . ")";
            $rowCountSuccess = 0;
            $placeholderValues = array();

            $totalRows = count($data['importData']);
            $importErrors = array();

            $columnsStr = "(" .implode(",",$selectedColumns). ")";

            $validProductType = array('Extra','Main');
            $validProductSubType = array('generic','specific');
            $validStatus = array('Active','Inactive');
            $validUnit = array('gram','kg','litre','mililitre','piece');
            $validFoodType =  array_keys(ProductTable::$food_types);
            $validProductCategory  = $this->getProductTable()->getProductCategories();
            $validLocations = $objLocation->fetchAll();
            $validLocations = $validLocations->toArray();

            $rowLocations = array();
    	 	
            //echo "data<pre>"; print_r($data['importData']); exit();

            foreach($data['importData'] as $rowkey=>$row){

                $flgError = 0;
                $tmpValues = array();
                
                foreach($data['importColumns'] as $key=>$col){

                    if($col !=""){

                        $value = trim($row[$key]);

                        switch($col){

                            case "name":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify the product name";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                $validator = new \Zend\Validator\Db\NoRecordExists(
                                    array(
                                        'table'   => 'products',
                                        'field'   => 'name',
                                        'adapter' => $adapt
                                    )
                                );

                                if (!$validator->isValid($value)) {
                                    $flgError = 1;
                                    foreach ($validator->getMessages() as $message) {
                                            $str = "Product already exists";
                                    }
                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{
                                            $importErrors[$rowkey] = $str;
                                    }
                                } 

                            break;
    	 	
                            case "kitchen_code":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify the kitchen code";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                if (!$validLength->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Kitchen Code can't be more than 9 characters";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;
    	 						 
                            case "category":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify the menu";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                $menuCategory = $setting['MENU_TYPE'];
                                $menuCategory = array_map('strtolower', $menuCategory);

                                $categoryVal = explode(',',trim(strtolower($value)));
                                $categoryVal = array_map('trim', $categoryVal);
                                $unique = array_unique($categoryVal);

                                // The order of the arrays matters!
                                $isSubset = (count(array_intersect($unique, $menuCategory)) == count($unique))?1:0;

                                if($isSubset){
                                    $value = strtolower($value);
                                }else{
                                    $flgError = 1;
                                    $str = "Please specify valid menu category";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;

                            case "description":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify description";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                            break;


                            case "unit_price":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify Unit Price";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                if ($value ==0 || $value<0) {
                                    $flgError = 1;
                                    $str = "Unit price can not be zero";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;

                            case "product_type":
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify product type";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                if(!in_array($value, $validProductType)){
                                    $flgError = 1;
                                    $str = "Please specify valid product category. Accepted value are ".implode(",",$validProductType)." ";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;
                            
                            case "product_subtype":
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify product sub type";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                if(!in_array($value, $validProductSubType)){
                                    $flgError = 1;
                                    $str = "Please specify valid product sub type. Accepted value are ".implode(",",$validProductSubType)." ";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;


                            case "screen":
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify screen";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                            break;

                            case "threshold":
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify threshold";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                            break;
    	 							
                            case "quantity":
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify qunantiy";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                if ($value ==0 || $value<0) {
                                    $flgError = 1;
                                    $str = "Quantity can not be zero";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;
    	 						
                            case "unit":
                                $value = strtolower($value);

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify unit";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                if(!in_array($value, $validUnit)){
                                    $flgError = 1;
                                    $str = "Please specify valid Unit. Accepted value are ".implode(",",$validUnit)." ";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                            break;

                            case "status":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify status";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                if(!in_array($value, $validStatus)){
                                    $flgError = 1;
                                    $str = "Please specify valid status. Accepted value are ".implode(",",$validStatus)." ";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                $value = ($value=='Active') ? 1 : 0;

                            break;
    	 								
                            case "food_type":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify the food type";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                $footTypes = array_map('strtolower', $validFoodType);

                                if(!in_array(strtolower($value), $footTypes)){
                                    $flgError = 1;
                                    $str = "Please specify valid Food type. Accepted value are ".implode(",",$footTypes)." ";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;

                            case "product_category":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify the product category";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }

                                $productCategoryTypes = array_map('strtolower', $validProductCategory);

                                if(!in_array(strtolower($value), $productCategoryTypes)){
                                    $flgError = 1;
                                    $str = "Please specify valid Product Category. Accepted value are ".implode(",",$productCategoryTypes)." ";

                                    if(isset($importErrors[$rowkey])){
                                            $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                            $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;

                            case "max_quantity_per_meal":

                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $str = "Please specify max quantity per meal";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                if ($value ==0 || $value<0) {
                                    $flgError = 1;
                                    $str = "The maximum quantity should be either 1 or more";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }
                            break;

                            case "available_location":
                                $tmpFlg = 0;
                                if (!$isEmpty->isValid($value)) {
                                    $flgError = 1;
                                    $tmpFlg = 1;
                                    $str = "Please specify available locations";

                                    if(isset($importErrors[$rowkey])){
                                        $importErrors[$rowkey] .= "<br/>".$str;
                                    }else{

                                        $importErrors[$rowkey] = $str;
                                    }
                                }

                                //	$locationArr = array_map('strtolower', $validLocations);

                                $locationVal = explode(',',trim(strtolower($value)));
                                $locationVal = array_map('trim', $locationVal);

                                if(!$tmpFlg){
                                    $temLoc = array();
                                    foreach ($locationVal as $val){

                                        $validLocation = $importCustomerValidator->checkValidLocation($val,$validLocations);
                                        if(!$validLocation){
                                            $tmpFlg = 1;
                                            $flgError = 1;

                                            $str = "$val is not valid a location";
                                            if(isset($importErrors[$rowkey])){
                                                    $importErrors[$rowkey] .= "<br/>".$str;
                                            }else{

                                                    $importErrors[$rowkey] = $str;
                                            }

                                            break;

                                        }

                                        $temLoc[$validLocation['pk_location_code']] = $validLocation['location'];
                                    }


                                }

                                if(!$tmpFlg){
                                        $rowLocations[$rowkey] = $temLoc;
                                }
                            break;
                        }
    	 				
                        if($col!='available_location'){
                                $tmpValues[] = $value;
                        }
                    }
    	 			 
                } // end of columns
    	 		
                if(!$flgError){
                    if(!empty($tmpValues)){

                        array_push($tmpValues,"noimage.png");// image name
                        array_push($tmpValues,date("Y-m-d"));// createdDate
                        array_push($tmpValues,$GLOBALS['company_id']);// company_id
                        array_push($tmpValues,$GLOBALS['unit_id']);// unit_id
                        array_push($tmpValues,0);// unit_id
                        $rowCountSuccess++;
                        $placeholderValues = array_merge($placeholderValues,$tmpValues);
                    }
                } 


            }
    	 
            $lastProductId = 0;
            if(!empty($placeholderValues)){

                $platform = $adapt->getPlatform();
                $placeholder = implode(',', array_fill(0, $rowCountSuccess, $placeholder));
                $table = $platform->quoteIdentifier("products");
                $q = "INSERT INTO $table $columnsStr VALUES $placeholder";
                $adapt->query($q)->execute($placeholderValues);
                $q1 = "SELECT LAST_INSERT_ID()";
                $id = $adapt->query($q1,Adapter::QUERY_MODE_EXECUTE);
                //$id = $sql->execQuery();
                $id = $id->toArray();
                $lastProductId = $id[0]['LAST_INSERT_ID()'];
            }
    	 	
            /*
            $mappingInsert = array();
            $insertTill = ($lastProductId-1)+$rowCountSuccess;


            if($lastProductId!=0)

            $cntr =1;
            for($row=$lastProductId ; $row<=$insertTill;$row++){

                    for($i= $cntr; $i<=count($rowLocations[$cntr]); $i++){

                            foreach ($rowLocations[$cntr] as $locid=>$loc){
                                    $arrTemp = array();
                                    $arrTemp['context'] = 'product';
                                    $arrTemp['context_ref_id'] = $row;
                                    $arrTemp['location_id'] = 	$locid;
                                    $arrTemp['location'] = $loc;
                                    $mappingInsert [] = $arrTemp;
                            }

                            $cntr++;
                            break;
                    }
            }

            //echo "mapping<pre>"; print_r($mappingInsert); exit();

            $recur_flat_arr_obj_mapping =  new RecursiveIteratorIterator(new RecursiveArrayIterator($mappingInsert));
            $arrPlaceholderValMapping= iterator_to_array($recur_flat_arr_obj_mapping, false);

            $placeholderMapping = array_fill(0, count($colLocMapping), '?');
            //echo "<pre>"; print_r($placeholderMapping); exit();
            $placeholderMapping = "(" . implode(',', $placeholderMapping) . ")";
            //echo "<pre>"; print_r($placeholderMapping); exit();
            $placeholderMapping = implode(',', array_fill(0, count($mappingInsert), $placeholderMapping));
     //	echo "<pre>"; print_r($placeholderMapping); exit();
            $columnsMappingStr = "(" .implode(",",$colLocMapping). ")";


            $platform = $adapt->getPlatform();
            $table = $platform->quoteIdentifier("location_mapping");
            echo $table;
            echo "aa<pre>"; print_r($columnsMappingStr);
            echo "bb<pre>"; print_r($placeholderMapping);
            exit();


            $q1 = "INSERT INTO $table $columnsMappingStr VALUES $placeholderMapping";

            echo $q1; exit();
            $adapt->query($q1)->execute($arrPlaceholderValMapping);
            */
            // Forward request to result page...

            return $this->forward()->dispatch('Admin\Controller\Product', array(
                    'action' => 'import-result',
                    'rowCountSuccess' => $rowCountSuccess,
                    'totalRows' => $totalRows,
                    'importErrors' => $importErrors,
            ));
    	 }
    	}
    	SHOWDETAILS:
    	
    	$this->layout()->setVariables(array('page_title'=>"Import Products",'description'=>"Import Products",'breadcrumb'=>"Import Products"));
    	
    	$view = new ViewModel();
    	
    	$view->setVariables(
            array(
                'columns' =>$columns,
                'importedData' =>$importedData,
                'form' =>$form,
                'errors' =>$errors
            )
    	);
    	
    	return $view;
    
    }
    
     public function importResultAction(){
    
    	$rowCountSuccess = $this->params()->fromRoute('rowCountSuccess');
    	$totalRows = $this->params()->fromRoute('totalRows');
    	$importErrors = $this->params()->fromRoute('importErrors');
    
    
    	$this->layout()->setVariables(array('page_title'=>"Import Products Result",'description'=>"Import Product Result",'breadcrumb'=>"Import Products"));
    
    	$view = new ViewModel();
    
    	$view->setVariables(
            array(
                'rowCountSuccess' =>$rowCountSuccess,
                'totalRows' =>$totalRows,
                'importErrors' =>$importErrors
            )
    	);
    
    	return $view;
    
    }

	/**
	 * 
	 * The controller action `getKitchenLocationsAction` is used for fetching appropriate locations for a selected kitchen through ajax.
	 * 
	 * @method getKitchenLocationsAction
	 * @access public
	 * @return JSON|boolean
	 */
	public function getKitchenLocationsAction() {
            $location_array = array(0=>array('value' => '0', 'text' => 'All locations'));
            #if($this->getRequest()->isAjax()) {
            $posted_values = $this->getRequest()->getPost();
            $kitchen_id = $posted_values->kitchen_id;
            if( !empty($kitchen_id) ) {
                $sm = $this->getServiceLocator();
                $adapt = $sm->get('Write_Adapter');
                $sql = new \Zend\Db\Sql\Sql($adapt);

                $select = $sql->select();
                $select->from('kitchen_master');
                $select->where('pk_kitchen_code='.$kitchen_id);
                $results = $sql->execQuery($select);
                $city_id = null;
                // Iterate through all records.
                foreach ($results as $res) {
                        // value is the product category name
                        $city_id = $res['city_id'];break;
                }// end of foreach

                $select = $sql->select();
                $select->from('delivery_locations');
                if( !empty($city_id) ) {
                        $select->where('city ="'.$city_id.'"');
                }
                $select->where('status=1');
                $select->order('location ASC');
                $statement = $sql->prepareStatementForSqlObject($select);//echo $sql->getSqlStringForSqlObject($select);exit();
                $locations = $statement->execute();
                $locations = $sql->execQuery($select);
                // Iterate through all records.
                foreach ($locations as $res) {
                        // value is the product category name
                        array_push($location_array, array('value' => $res['pk_location_code'], 'text' => $res['location']));
                }// end of foreach
            }// end of if
            echo json_encode($location_array);
            exit();
            #}
	}
    /**
     * To assign product with calendar dates
     *
     */
    public function productCalendar_oldAction()
    {
    	$id = (int) $this->params('id');
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	$request = $this->getRequest();
    	$product = $this->getProductCalendarTable()->getProductCalendar($id);
    	$product_category = $this->getProductTable()->getProduct($id)['product_category'];
    	//echo "<pre>";print_r($product_category['product_category']);exit;
    	if ($request->isPost())
    	{
            $product = new ProductCalendar();
            $data = $request->getPost();
            $product->exchangeArray($data);
            //$this->getProductCalendarTable()->delete('milestone')->where("goal_id = $edit_id");
            //echo "<pre>";print_r($product);exit;
            $this->getProductCalendarTable()->saveProductCalendar($product,$loguser,$product_category);
            $this->flashMessenger()->addSuccessMessage("Product updated successfully",'success');

            // Redirect to list of albums
            return $this->redirect()->toRoute('product');
    	}//$product->count()
    	$this->layout()->setVariables(array('page_title'=>"Import Products Result",'description'=>"Import Product Result",'breadcrumb'=>"Import Products"));
    	$view = new ViewModel();
    	$view->setVariables(
            array(
                'acl'=>$acl,
                'product' => $product,
                'id' => $id,
            )
    	);
    	//echo "<pre>fff";print_r($product['calendar_date']);exit;
    	return $view;
    }
    
    public function productcalendarajxAction(){
        $sm = $this->getServiceLocator();
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        $request = $this->getRequest();
        $selectedmenu = $request->getPost('selected_menu');
        $selectedkitchen = $_SESSION['adminkitchen'];
        $selectedcategory = $request->getPost('selected_products_category');
        $all_product = $this->getProductCalendarTable()->getAllProducts($selectedmenu,$selectedkitchen,$selectedcategory);
        //echo "<pre>";print_r($all_product);die;
        return new JsonModel($all_product);
    }

    public function productCalendarAction(){
    	$sm = $this->getServiceLocator();
        $libCommon = QSCommon::getInstance($sm);
    	$kitchens = $libCommon->getKitchenScreen();
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	$request = $this->getRequest();
    	$selectedmenu = $request->getPost('selectedmenu');
    	
        if (! $this->authservice){
     		$this->authservice = $this->getServiceLocator()->get('AuthService');
     	}
     	
     	$iden = $this->authservice->getIdentity();
     	
        $selectedkitchen = ($_SESSION['adminkitchen'] == 'all') ? reset(array_column($iden->kitchens, 'fk_kitchen_code')) : $_SESSION['adminkitchen']; // overriding logic for product calendar and add-meal-calendarwise
        
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;
    	$menus = $setting['MENU_TYPE'];
    	
    	if(empty($selectedmenu)){
    		$selectedmenu = $menus[0];
    	}
    	//echo '111 '.$selectedmenu;exit;
    	$all_product_categories = $this->getProductCalendarTable()->getDistinctProductCategory($selectedmenu,$selectedkitchen);
    	//echo "<pre>";print_r($all_product_categories);exit;
        $selectedcategory = $request->getPost('selected_products_category');
    	if(empty($selectedcategory)){
    		$selectedcategory = $all_product_categories[0]['product_category_name'];
    	}
    	
    	$all_product = $this->getProductCalendarTable()->getAllProducts($selectedmenu,$selectedkitchen,$selectedcategory);
    	
    	$this->layout()->setVariables(array('page_title'=>"Product Calendar",'description'=>"Product Calendar",'breadcrumb'=>"Product Calendar","back"=>"product-calendar"));
    	$view = new ViewModel();
    	
    	//echo "<pre>";print_r($all_product);die;
        $getHolidays = $libCommon->fetchHolidaysList('holiday');
        $all_previous_products = $this->getProductCalendarTable()->getAllProductsOnPrevDate($selectedmenu,$selectedkitchen);
        $weekOff = $libCommon->fetchHolidaysList('weekoff');
        $holidays_list = "";
        foreach ($getHolidays as $key=>$vals){
            $holidays_list .= "'".Date('Y/m/d',strtotime($vals['holiday_date']))."',";
        }
        $holidays=rtrim($holidays_list,",");
        $holiday_duplicate = $holidays;
        /*foreach ($all_previous_products as $key=>$vals){
            $holidays_list .= "'".Date('Y/m/d',strtotime($vals))."',";
        }
        $holidays=rtrim($holidays_list,",");*/
        $prev_booked_date = "";
        foreach ($all_previous_products as $key=>$vals){
            $prev_booked_date .= "'".Date('Y/m/d',strtotime($vals))."',";
        }
        $prev_booked_date=rtrim($prev_booked_date,",");
        $prev_booked_date = "";
        foreach ($all_previous_products as $key=>$vals){
            $prev_booked_date .= "'".Date('Y/m/d',strtotime($vals))."',";
        }
        $prev_booked_date=rtrim($prev_booked_date,",");
        //echo "<pre>";print_r($holidays);exit;
        //echo "<pre>";print_r($getHolidays);print_r($weekOff);exit;
    	$view->setVariables(
            array(
                'acl'=>$acl,
                'all_products' => $all_product,
                'all_product_categories' => $all_product_categories,
                //'id' => $id,
                'menus'=>$menus,
                'selectedmenu'=>$selectedmenu,
                'selectedkitchen'=>$selectedkitchen,
                'holiday_duplicate' =>$holiday_duplicate,
                'kitchens'=>$kitchens,
                'selected_products_category'=>$selectedcategory,
                'weekOff'=>$weekOff[0]['holiday_description'],
                'holidays'=>$holidays,
                'all_previous_products'=>$prev_booked_date
            )
    	);
    	return $view;

    }

    public function fetchProductOnDateAction(){
        
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        
        $request = $this->getRequest();
        //echo "<pre>";print_r($request->getPost());exit;
        $selected_date = $request->getPost('selected_date');
        $selectedmenu = $request->getPost('selected_menu');
        $selectedkitchen = $_SESSION['adminkitchen'];
        
        if(empty($selectedmenu)){
            $selectedmenu = 'breakfast';
        }
        
        $selectedcategory = $request->getPost('selected_products_category',null);
        if(empty($selectedcategory)){
            //$selectedcategory = $all_product_categories[0]['distinct_product_category'];
        }
       //echo $selectedmenu.' '.$selectedkitchen.' '.$selectedcategory.' '.$selected_date;exit;
       $all_selected_products = $this->getProductCalendarTable()->getAllProductsOnDate($selectedmenu,$selectedkitchen,$selectedcategory,$selected_date);
        $returnVar['all_selected_products'] = $all_selected_products;
        return new JsonModel($returnVar);
    }
    
    /**
     * Allow to create product plan, maps generic to specific.
     */
    public function addProductPlanAction() {
    	
    	$id = (int) $this->params('id');
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$kitchen = $_SESSION['adminkitchen'];
    	
    	
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;
    	$menus = $setting['MENU_TYPE'];
    	
    	$this->layout()->setVariables(array('page_title'=>"Product Planner",'description'=>"Product Planner",'breadcrumb'=>"Product Planner","back"=>"meal-calendar"));
    	
    	$view = new ViewModel();
    	
    	$view->setVariables(
    		array(
	    		'acl'=>$acl,
	    		'id' => $id,
	    		'kitchen' => $kitchen
    		)
    	);
    	//dd($view);
    	return $view;    	
    }
    
    /**
     * To assign meal with calendar dates
     *
     */
    public function addMealCalendarwiseAction()
    {
    	$id = (int) $this->params('id');
    	//$kitchen = (int) $this->params('kitchen');
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
        if (! $this->authservice){
     		$this->authservice = $this->getServiceLocator()->get('AuthService');
     	}
     	
     	$iden = $this->authservice->getIdentity();
     	
        $kitchen = ($_SESSION['adminkitchen'] == 'all') ? reset(array_column($iden->kitchens, 'fk_kitchen_code')) : $_SESSION['adminkitchen']; // overriding logic for product calendar and add-meal-calendarwise
        
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;
    	$menus = $setting['MENU_TYPE'];
    	$request = $this->getRequest();
    	$year = $request->getPost('year');
    	$selectedmenu = $request->getPost('selectedmenu');
 
    	if($year==''){
    		$year = date('Y');
    	}
    	$month = $request->getPost('month');
    	if($month==''){
    		$month = date('m');
    	}
     	if($selectedmenu==''){
    		$selectedmenu ='lunch';
    	}
    	$mealName = $this->getProductTable()->getMealName($id);
    	if(count($mealName)>0){
    		$mealName = $mealName[0]['meal_name'];
    	}else{
    		$mealName = '';
    	}
    	$product = $this->getProductCalendarTable()->getProductCalendar($id);
  		
    	$products_from_db = $this->getMealCalendarTable()->getProductOnDate(0,$id,$selectedmenu,$_SESSION['adminkitchen']);
    	
    	$this->layout()->setVariables(array('page_title'=>"Meal Calendar",'description'=>"Meal Calendar",'breadcrumb'=>"Meal Calendar","back"=>"meal-calendar"));
    	$view = new ViewModel();
    	//echo $selectedmenu;exit;
    	$view->setVariables(
    	array(
    	'acl'=>$acl,
    	'product' => $product,
    	'id' => $id,
    	'kitchen' => $kitchen,
    	'products_from_db'	 =>	$products_from_db,
    	'year'	=>	$year,
    	'month' =>  $month,
    	'meal_name' => $mealName,
    	'menus'=>$menus,
    	'selected_menu'=>$selectedmenu
    			)
    	);
    	//echo "<pre>fff";print_r($view);exit;
    	return $view;
    }
    
    public function addMealOnDateAction(){
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$request = $this->getRequest();
    	$product_code = $request->getPost('product_code');
    	$product_name = $request->getPost('product_name');
    	$product_qty = $request->getPost('product_qty');
    	$product_category = $request->getPost('product_category');
    	$fk_product_code = $request->getPost('fk_product_code');
    	$selected_menu = $request->getPost('selected_menu');
    	$fk_kitchen = $_SESSION['adminkitchen'];
    	
    	$date = explode('-',$request->getPost('date'));
    	$day = $date[0];
    	$month = $date[1];
    	$year = $date[2];
    	if(strlen($day)==1){
    		$day = '0'.$day;
    	}
    	if(strlen($month)==1){
    		$month = '0'.$month;
    	}
    	$date = $year.'-'.$month.'-'.$day;
    	//echo $day;exit;
    	$mealcalendar = new MealCalendar();
    	
    	$data_meal = $this->getMealCalendarTable()->saveMealCalendarWise($mealcalendar,$product_code,$product_qty,$product_name,$product_category,$fk_product_code,$date,$loguser,$selected_menu,$fk_kitchen);
    	
    	$returnVar['newly_added_rows'] = $data_meal;
    	$returnVar['data'] = $product_code;
    	$returnVar['newly_added_date'] = $day.'-'.$month.'-'.$year;
    	return new JsonModel($returnVar);
    }
    
    public function addProductOnDateAction(){

    	//error_reporting(E_ALL);
		//ini_set('display_errors', 'On');
		
    	$session_setting = new Container("setting");
    	$setting = $session_setting->setting;		
		
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$request = $this->getRequest();
    	$date = $request->getPost('date');
    	$date = date('Y-m-d',strtotime($date));
    	
    	$products = $request->getPost('products',null);
    	$menu = $request->getPost('menu');
    	$gid = $request->getPost('gpid');
    	$sid = $request->getPost('spid',null);
    	$mode = $request->getPost('mode',null);
    	
    	$fk_kitchen = $_SESSION['adminkitchen'];

        //dd($products);
    	
        $arrKitchen = array();

        if($fk_kitchen == 'all') {

            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser;  

            array_push($arrKitchen, '0');
            
            foreach ($loguser->kitchens as $key => $value) {
                array_push($arrKitchen, $value['fk_kitchen_code']);
            }
            //$kitchen = implode(',', $arrKitchen);
        }
        else {
            array_push($arrKitchen, $fk_kitchen);
            //$kitchen = implode(',', $arrKitchen);
        }

        //dd($arrKitchen);

        /*
        $kitchen = '';

        if($fk_kitchen == 'all') {
            $fk_kitchen = 0;
        }
        */

/*      else {
            $kitchen = $fk_kitchen;
        }*/

        /*
        $arrKitchen = array('0');        

        if($kitchen == 'all') {
            
            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser; 

            foreach ($loguser->kitchens as $key => $value) {
                array_push($arrKitchen, $value['fk_kitchen_code']);
            }
            $kitchen = implode(',', $arrKitchen);
        }
        else {
            array_push($arrKitchen, $kitchen);
            $kitchen = implode(',', $arrKitchen);
        } 
        */

    	$product_plan = $this->getProductTable()->saveProductPlan($date,$products,$menu,$arrKitchen,$gid,$sid,$mode);
    	
    	$returnVar['newly_added_rows'] = $product_plan;
    	$returnVar['data'] = $products;
    	$returnVar['newly_added_date'] = $date;

    	return new JsonModel($returnVar);
    	    	
    } 
    
    public function removeProductFromMealAction(){
    	$mealcalendar = new MealCalendar();
    	$request = $this->getRequest();
    	$delete_product_id = $request->getPost('idtodelete');
    	$deleted_id = $this->getMealCalendarTable()->deleteProductFromMeal($mealcalendar,$delete_product_id);
    	$returnVar['deleted_id'] = $deleted_id;
    	return new JsonModel($returnVar);
    }
    /**
     * Get instance of QuickServe\Model\ProductTable
     *
     * @return QuickServe\Model\ProductTable
     */
    public function getProductCalendarTable() {
    	if (!$this->productCalendarTable) {
    		$sm = $this->getServiceLocator();
    		$this->productCalendarTable = $sm->get('QuickServe\Model\ProductCalendarTable');
    	}
    	return $this->productCalendarTable;
    }
    
    /**
     * Get instance of QuickServe\Model\ProductTable
     *
     * @return QuickServe\Model\ProductTable
     */
    public function getMealCalendarTable() {
    	if (!$this->mealCalendarTable) {
    		$sm = $this->getServiceLocator();
    		$this->mealCalendarTable = $sm->get('QuickServe\Model\MealCalendarTable');
    	}
    	return $this->mealCalendarTable;
    }
    
    public function getWeeksProductAction() {
    	
    	$utility = Utility::getInstance();
        $sm = $this->getServiceLocator();
        $libCatalogue = QSCatalogue::getInstance($sm);
    	$request = $this->getRequest();
        $id = (int)$request->getPost('id');
        $name = $request->getPost('name');
        $product_category = $request->getPost('product_category');
        $menu = $request->getPost('menu');
        $month = $request->getPost('month');
        $year = $request->getPost('year');
        $kitchen = $_SESSION['adminkitchen'];
        $weeknum = (int)$request->getPost('weeknum',null);

        $currentYear = date("Y");
        $currentMonth = date("m");

        if($kitchen == 'all') {
            $kitchen = '0';
        }

        //dd($weeknum);
        /*
        $arrKitchen = array();        

        if($kitchen == 'all') {

            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser; 

            foreach ($loguser->kitchens as $key => $value) {
                array_push($arrKitchen, $value['fk_kitchen_code']);
            }

            $kitchen = implode(',', $arrKitchen);
        }

        else {
            array_push($arrKitchen, $kitchen);
            $kitchen = implode(',', $arrKitchen);
        }*/

        //dd($kitchen);
   		
        if(empty($weeknum)){

            if($year==$currentYear  && $month==$currentMonth){
                $weekStartDate = date("Y-m-d", strtotime('monday this week'));
                $weeknum = (int)date("W");

            }else{
                $weekStartDate = date("Y-m-d", strtotime('monday this week', (mktime(0,0,0,$month,1,$year))));
                $weeknum = (int)date("W", strtotime('monday this week', (mktime(0,0,0,$month,1,$year))));

            }

        }else{
            $dto = new \DateTime();
            $dto->setISODate($year, $weeknum);
            $weekStartDate = $dto->format('Y-m-d');

        }

        $weeknumNext = $weeknum + 1;
        $weeknumPrev = $weeknum - 1;

        $monthArray = array("1"=>"Janaury","2"=>"February","3"=>"March","4"=>"April","5"=>"May","6"=>"June","7"=>"July","8"=>"August","9"=>"September","10"=>"October","11"=>"November","12"=>"December");

        $strmonth = '';
        foreach( $monthArray as $k => $v ) {
            if( $k == $month ) {
                $strmonth = $v;
            }
        }			
        
        $weekDetails = $utility->getWeekStartDate($year,$month,$weekStartDate,$weeknum);
        $planned_products = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($id,null,$menu,$kitchen,null,$year,$month,'daywise','display',$weeknum);
        
        $viewModel = new ViewModel();
        $viewModel->setVariables(array(
            'id'=>$id,
            'name'=>$name,
            'product_category'=>$product_category,
            'menu'=>$menu,
            'weekDetails'=>$weekDetails,
            'weeknumNext'=>$weeknumNext,
            'weeknumPrev'=>$weeknumPrev,
            'month'=>$month,
            'year'=>$year,
            'currentMonth'=>$currentMonth,
            'currentYear'=>$currentYear,
            'currentWeekNo'=>$weeknum,
            'kitchen'=>$kitchen,
            'planned_products'=>$planned_products,
            'weekStartDate'=>$weekStartDate
            )
        )->setTerminal(true);
                        
    	return $viewModel;   			
   		
    }
    
    public function getProductOnDateAction()
    {
    	
    	$request = $this->getRequest();
        $day = $request->getPost('day');
        $month = $request->getPost('month');
        $year = $request->getPost('year');
        $id = (int)$request->getPost('id');
        $kitchen = $request->getPost('kitchen');
        $selectedMenu = $request->getPost('selected_menu');

        if(strlen($day)==1){
                $day = '0'.$day;
        }
        if(strlen($month)==1){
                $month = '0'.$month;
        }
        $date = $year.'-'.$month.'-'.$day;
        $product_category = $this->getProductCalendarTable()->getProductCategoryOnDate($date,$selectedMenu,null,$kitchen);
        $product_category_from_db = $this->getMealCalendarTable()->getProductOnDate($date,$id,$selectedMenu,$kitchen);
        $count_product_category = $this->getProductCalendarTable()->getDistinctProductCategoryOnDate($date);
        $returnVar['db_data'] = $product_category_from_db;
        $returnVar['data'] = $product_category;
        $returnVar['count'] = $count_product_category;
    	return new JsonModel($returnVar);
    }
    
    public function getSpecificProductAction() {
    	
        $sm = $this->getServiceLocator();
        $libCatalogue = QSCatalogue::getInstance($sm);
    	$request = $this->getRequest();
        $id = (int)$request->getPost('id');
        $menu = $request->getPost('menu');
        $product_cat = $request->getPost('product_cat'); 
        $date = $request->getPost('date');
        $kitchen = $request->getPost('kitchen');  
        $fetch = $request->getPost('fetch'); 

        //dd($kitchen);
        if($kitchen != '0') {
            $arrKitchen = array('0');
            array_push($arrKitchen, $kitchen);
            $kitchen = implode(',', $arrKitchen);
        }

        $specficProducts=$this->getProductTable()->getSpecificProduct($id,$product_cat,null,$kitchen);

        $plannedProductsfromDb=$sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($id,date('Y-m-d',strtotime($date)),$menu,$kitchen,'','','',$fetch);
   		//dd($plannedProductsfromDb);
        $returnVar['data']=$specficProducts;
        $returnVar['planned_products']=$plannedProductsfromDb;
   		
        return new JsonModel($returnVar);
    	
    }
    
    public function addProductCalendarWiseAction(){
    	
    	$layoutviewModel = $this->layout();
    	$acl =$layoutviewModel->acl;
    	$loguser = $layoutviewModel->loggedUser;
    	
    	$product = new ProductCalendar();
    	$request = $this->getRequest();
    	$selected_products = $request->getPost('selected_products');
    	
    	//echo "<pre>";print_r($selected_products);echo "<pre>";die;
    	
    	$adpt = $this->getServiceLocator()->get("Write_Adapter");
    	
    	$selected_date = $request->getPost('selected_date');
    	$selected_menu = $request->getPost('selected_menu');
        $repeat_dates = $request->getPost('repeat_dates');
        $copymenuvalue = $request->getPost('copymenuvalue');
        $selected_kitchen = $_SESSION['adminkitchen'];
		
        // fetch old products for this date...
        
        $dtdb = date("Y-m-d",strtotime($selected_date));
        
        $sql = new QSql($sm);
        
        $select = $sql->select();
			
        $select->from("product_calendar");
        //$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code',array('total_order','date'),$select::JOIN_LEFT);
        $select->where(array('menu' => $selected_menu ,'fk_kitchen_code' => $selected_kitchen,'calendar_date' => $dtdb));

        //echo "<pre>";print_r($select->getSqlString());die;
        $statement = $sql->prepareStatementForSqlObject($select);
        $resultSet = $statement->execute();
        $resultSet->buffer();

        //echo "Old count ==".$resultSet->count();
		
        if($resultSet->count() > 0){

            $deletedProducts = array();

            foreach ($resultSet as $row){

                $found = 0;

                foreach ($selected_products as $cat=>$catProducts){

                    foreach ($catProducts as $cProdId){

                        if($row['fk_product_code'] == $cProdId){

                            $found = 1;
                            break 2;
                        }
                    }
                }

                if(!$found){

                        array_push($deletedProducts,$row['fk_product_code']);
                }

            }
			
            if(count($deletedProducts) > 0){

                //echo "<pre> deleted ";print_r($selected_date);echo "</pre>";

                $strDeletedProduct = implode(",",$deletedProducts);

                $select = $sql->select();

                $selected_date_db = date("Y-m-d",strtotime($selected_date));

                $select->from("meal_calendar");
                $select->columns(array("product_names"=> new \Zend\Db\Sql\Expression("GROUP_CONCAT(product_name)"),"dates"=> new \Zend\Db\Sql\Expression("GROUP_CONCAT(calendar_date)")));
                $select->where("product_code IN ($strDeletedProduct) and calendar_date = '$selected_date_db' AND fk_kitchen_code = '$selected_kitchen' AND menu = '$selected_menu' ");
                $select->join(array("p"=>"products"),"p.pk_product_code = fk_product_code",array("meal_name"=>"name"));
                $select->group(array("fk_product_code"));

                $statement = $sql->prepareStatementForSqlObject($select);
                $resultSet = $statement->execute();
                $resultSet->buffer();

                //echo $resultSet->count();

                if($resultSet->count() > 0){

                    $msg = "Can not save products for this date. \nDeleted products found in meal calendar, please find meals details \n\n";

                    foreach ($resultSet as $meal){
                            $msg .= "Meal : ".$meal['meal_name']." == Products : ".$meal['product_names']."\n\n";
                    }
                    return new JsonModel(array("status"=>"error","msg"=>$msg));
                }
            }

        }
        
    	$this->getProductCalendarTable()->saveProductCalendarWise($product,$loguser,$selected_products,$selected_date,$selected_menu,$repeat_dates,$copymenuvalue,$selected_kitchen);
    	$this->flashMessenger()->addSuccessMessage("Menu saved");
        // Redirect to list of product
        //return $this->redirect()->toRoute('product/product-calendar');
        $returnVar =[];
    	//return new JsonModel($returnVar);
    	return new JsonModel(array("status"=>"success","msg"=>""));
    }
    
    public function getProductCalendarWiseAction(){
    	$request = $this->getRequest();
    	$selected_date = $request->getPost('selected_date');
    	$selected_menu = $request->getPost('selected_menu');
    //	$searchval = $request->getPost('searchval');
    	$product_category = $this->getProductCalendarTable()->getProductCategoryOnDate(date('Y-m-d',strtotime($selected_date)),$selected_menu);
    	$returnVar['product_category'] = $product_category;
    	return new JsonModel($returnVar);
    }
    
    public function ajaxGetProductsAction() {
    	$request = $this->getRequest();
    	$kitchen_screen=$request->getPost('screen');
    	$prod_subtype=$request->getPost('prod_subtype');
    	$products=$this->getProductTable()->getProductScreenwise($kitchen_screen,$prod_subtype);
    	return new JsonModel($products);
    }
    
    public function ajaxGetMealPlanAction() {
		$request = $this->getRequest();
    	$kitchen_screen=$request->getPost('screen');
    	$plans=$this->getPlanTable()->getMealPlanKitchenwise($kitchen_screen);
    	return new JsonModel($plans);
    }
    
    /**
     * 
     * display plans of meals weekwise
     * @method getProductById($id) returns product based on $id
     * @method getItems($items) returns product $ids for items

     * @method getPlannedProductOnDate() returns planned product for $date,$ids,$menu,$kitchen,$month,$year
     * @return ViewModel
     */

    public function mealplanAction() {  
        
        $request = $this->getRequest();
        $utility = Utility::getInstance();
        $sm = $this->getServiceLocator();
        $libCatalogue = QSCatalogue::getInstance($sm);
    	$session_setting = new Container('setting');
    	$setting = $session_setting->setting;    	
        
        $id = (int) $this->params('id');
        
        if (!$id) {
            return $this->redirect()->toRoute('meal', array('action' => 'meal'));
        }
        
        if (! $this->authservice){
     		$this->authservice = $this->getServiceLocator()->get('AuthService');
     	}
     	
     	$iden = $this->authservice->getIdentity();
     	
        $kitchen = ($_SESSION['adminkitchen'] == 'all') ? reset(array_column($iden->kitchens, 'fk_kitchen_code')) : $_SESSION['adminkitchen']; 

        
        $weeknum = $request->getPost('weeknum',date("W"));      
        $main = $this->getProductTable()->getProduct($id);
        $menus = $setting['MENU_TYPE']; 
        $menu = $request->getPost('menu'); 
        $today = date('Y-m-d');
        $date = date('Y-m-d', strtotime('first day of this month'));             
        $month = $request->getPost('month');
        
        $monthArray = array("1"=>"Janaury","2"=>"February","3"=>"March","4"=>"April","5"=>"May","6"=>"June","7"=>"July","8"=>"August","9"=>"September","10"=>"October","11"=>"November","12"=>"December");
       
        $strmonth = '';
        foreach( $monthArray as $k => $v ) {
            if( $k == $month ) {
                $strmonth = $v;
            }
        }
       
        if($request->isPost()){
             
            $product = $libCatalogue->getProductById($id);
            if($product){
               
                $product_items = $libCatalogue->getItems($product->items);
           
                if($product_items){
                
                    foreach($product_items as $key => $value){
                    $product_details[$key] = $value['name'];
                    }
                    $ids = array_keys(json_decode($product->items, true));

                    $formData = $request->getPost();
                    $year=$this->params()->fromPost('year');
                    $month=$this->params()->fromPost('month');
                    $menu=$this->params()->fromPost('menu');
                    $kitchen = $this ->params() -> fromPost('kitchen');            
                    $meal_name = $request->getPost('meal_name'); 
                    
                    $data = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($ids,null,$menu,$kitchen,null,$year,$month,null,null,null);
                     //echo "<pre>";print_r($data);echo "</pre>";die;
                    $arrData = array();                    

                    $startTime = mktime(12, 0, 0, $month, $d, $year);
                   
                    $dateInFourWeeks = strtotime("+4 weeks", $startTime);
                    
                    while ($startTime <= $dateInFourWeeks) {  
                        $weekdetails = $utility->getWeekStartDate($year,$month,null,(int)date('W', $startTime));
                        $result[ date('Y', $startTime).'_'. (int)date('W', $startTime) ] =  $weekdetails['week_dates'];
                        $startTime += strtotime('+1 week', 0);
                    }  
                    
                    for($d=1; $d<=31; $d++) {
                        $time=mktime(12, 0, 0, $month, $d, $year);
                        if (date('m', $time)==$month){
                            $monthdetails[]=date('d-m-Y', $time);
                        }                        
                    }    
                    
                    foreach ($result as $weekdetails) {
                        foreach ($weekdetails as $wdate){
                            foreach ($product_items as $gItem){
                                $tmp = array();
                                $tmp['product_code'] = $gItem['id'];
                                $tmp['product_name'] = $gItem['name'];
                                $tmp['product_subtype'] = 'generic';
                                $tmp['specific'] = array();  
                                
                                $arrData[$wdate][$gItem['id']] = $tmp;
                               
                                foreach ($data as $key => $sproduct){ 
                                    $weekday = date("Y-m-d",strtotime($sproduct['date']));
                                    $wday = date("Y-m-d", strtotime($wdate));
                                    if($gItem['id'] == $sproduct['generic_product_code'] && $weekday==$wday){
                                        $arrData[$wdate][$gItem['id']]['specific'][$sproduct['id']] = $sproduct;  
                                    }                  
                                }     
                            }
                        }
                    }
                    switch($formData['export_type']){
                
                        case "xls":
                            $selected_columns = array("Date","Generic Product","Specific Product");

                            $objPHPExcel = new PHPExcel();

                            $objPHPExcel->getProperties()->setCreator("Fooddialer")
                            ->setLastModifiedBy("Fooddialer")
                            ->setTitle("PHPExcel Document")
                            ->setSubject("Fooddialer MealPlan")
                            ->setDescription("MealPlan")
                            ->setKeywords("Fooddialer")
                            ->setCategory("Fooddialer");

                            $headerSheet =array();

                            foreach ($selected_columns as $key=>$column){
                                $colname = str_replace("_", " ", $column);
                                $colname = ($colname == 'generic_product_code')?'generic_product_code':$colname;
                                $columnName =  ucwords($colname);
                                $headerSheet[] = $columnName;
                            }
                            $activeSheet = $objPHPExcel->getActiveSheet();

                            $activeSheet->setTitle('Meal Plan');
                            $rowIndex = 1;
                            $rowHeight = 20;

                            $activeSheet->fromArray($headerSheet, '', "A{$rowIndex}");
                            
                            $highestColumn = $activeSheet->getHighestColumn();
                            $activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

                            for ($col = ord('a'); $col <= ord($highestColumn); $col++){
                                $activeSheet->getColumnDimension(chr($col))->setAutoSize(true);
                            }

                            $header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
                            $activeSheet->getStyle($header_range)->getFont()->setBold(true);

                            $rowIndex++;

                            //to export data in excel sheets
                            
                            $currentDate = "";   
                            $previousDate ="";
                            foreach($monthdetails as $mdate){   
                                $day = date('D', strtotime($mdate));
                                foreach($arrData as $Gkey=>$Gval){
                                    if($mdate == $Gkey){                                        
                                        foreach($Gval as $newkey => $newval){    
                                            
                                            $strSpecific = "";
                                            if(!empty($newval['specific'])){
                                                $specificval = array();
                                                
                                                foreach($newval['specific'] as $spkey=>$spvalue){
                                                    
                                                    $specificval[] = $spvalue['specific_product_name'];
                                                }
                                                
                                                $strSpecific = implode(',', $specificval);
                                                
                                            }                                            
                                            else{
                                                
                                                $strSpecific = '';                                                 
                                            }
                                            
                                            $tempArray = array();                                                    
                                            if(empty($currentDate)) {                                                        
                                                $tempArray[] = $mdate.'('.$day.')';
                                                $currentDate = $mdate;
                                            }
                                            elseif($mdate == $currentDate) {
                                                $tempArray[] = '';
                                            }
                                            elseif($mdate != $currentDate) {
                                                $tempArray[] = $mdate.'('.$day.')';
                                                $currentDate = $mdate;
                                            }
                                            $tempArray[] = $newval['product_name'];
                                            $tempArray[] = $strSpecific;
                                            
//                                            echo "<pre>";print_r($tempArray[0]);
                                            $activeSheet->fromArray($tempArray, ' ', "A{$rowIndex}");
                                            $activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
                                            $rowIndex++;
                                        }
                                    }                                   
                                }                                
                            }
//                           die;
                            $objPHPExcel->setActiveSheetIndex(0);                          
                            $filename = "{$formData['menu']}_{$meal_name}_report.xls";
                            $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
                            $filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$strmonth.'-'.$year;
                            $objWriter->save($filenamePath);

                            header('Content-type: application/vnd.ms-excel');

                            header('Content-Disposition: attachment; filename="'.$filename.'"');

                            $objWriter->save('php://output');

                            $objPHPExcel->disconnectWorksheets();
                            unset($objPHPExcel);
                            unlink($filenamePath);
                        break;    
                    }            
                }
            }
        } 
        $mealName = $this->getProductTable()->getMealName($id);
        $productname = $mealName[0]['meal_name'];
    	$breadcrum = "Meals and Combos > ".ucfirst($productname);
     	$this->layout()->setVariables(array('page_title'=>"View Planned Menu",'description'=>"View planned products weekwise",'breadcrumb'=> $breadcrum));
        return new ViewModel(array(
                'id'  =>$id,
                'main'=>$main,
                'menus'=>$menus,
                'weeknum' => $weeknum,
                'adminkitchen' =>$kitchen,
                'startDate' => $date,
                'meal_name' => $productname,
            ));                       
    }
    
    /**
     * 
     * @method getWeekStartDate() return $weekDetails for $year,$month,$weekStartDate,$weeknum
     * @method getProductById($id) returns product based on $id
     * @method getItems($items) returns product $ids for items 
     * @method getPlannedProductOnDate() returns planned product for $date,$ids,$menu,$kitchen,$month,$year
     * @return ViewModel
     * 
     */
    public function ajxMealPlanItemsAction() {
       
        $utility = Utility::getInstance();
        $sm = $this->getServiceLocator();
        $libCatalogue = QSCatalogue::getInstance($sm);
        
        $request = $this->getRequest();
    	$id=$request->getPost('id');
        $kitchen=$request->getPost('kitchen');
        $month = $request->getPost('month');
        $year = $request->getPost('year');
        $mealName = $this->getProductTable()->getMealName($id);
        $weeknum = $request->getPost('weeknum',null);
        $menu = $request->getPost('menu');         
        
        $currentYear = date("Y");
        $currentMonth = date("m");
   		
        if(empty($weeknum)){

            if($year==$currentYear  && $month==$currentMonth){               
                $weeknum = (int)date("W");
            }else{                
                $weeknum = (int)date("W", strtotime('monday this week', (mktime(0,0,0,$month,1,$year))));
            }
        }
        
        $weeknumNext = $weeknum + 1;
        $weeknumPrev = $weeknum - 1;

        $weekDetails = $utility->getWeekStartDate($year,$month,null,$weeknum);   
        
        //echo "<pre>";dd($weekDetails);

        $product = $libCatalogue->getProductById($id);
    	 
        if($product){
          
             $product_items = $libCatalogue->getItems($product->items);
            
             if($product_items){
                
                foreach($product_items as $key => $value){
                    $product_details[$key] = $value['name'];
                }
                try{   
                    $ids = array_keys(json_decode($product->items, true));
                    
                    $data = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($ids,null,$menu,$kitchen,null,$year,$month,null,'display',$weeknum);
                    //echo '<pre>specific items'; print_r($data); '</pre>'; die;
                    $arrData = array();
                    
                    $weekDays = [];                                        
                    
                    foreach ($weekDetails['week_dates'] as $wkey => $wdate){
                        
                        $wday = date("l",strtotime($wdate));
                        $wmonth = date("m",strtotime($wdate));
                        $wyear = date("Y",strtotime($wdate));
                        
                        if($wmonth==$month && $wyear==$year){
                            
                            array_push($weekDays,$wdate);
                            
                            foreach ($product_items as $gItem){
                                $tmp = array();
                                $tmp['product_code'] = $gItem['id'];
                                $tmp['product_name'] = $gItem['name'];
                                $tmp['product_subtype'] = 'generic';
                                $tmp['specific'] = array();  

                                $arrData[$wday][$gItem['id']] = $tmp;

                                foreach ($data as $key => $sproduct){
                                    
                                    $weekday = date("l",strtotime($sproduct['date']));

                                    if($gItem['id'] == $sproduct['generic_product_code'] && $weekday==$wday){
                                        $arrData[$wday][$gItem['id']]['specific'][$sproduct['id']] = $sproduct;
                                    }                  
                                }                                  
                            }
                        }
                    }                    
                } catch (\Exception $ex) {
                    echo $ex->getMessage();         
                }                
                $view  = new ViewModel(array(
                    'data'=> $arrData,
                    'year' => $year,
                    'weeknum'=>$weeknum,
                    'meal_name' => $mealName[0]['meal_name'],
                    'weekdays'=>$weekDays,
                    'weekDetails'=>$weekDetails,
                    'weeknumNext'=>$weeknumNext,
                    'weeknumPrev'=>$weeknumPrev,
                    'menu'  => $menu,
                ));
                $view->setTerminal(true);
                return $view;
            }
        }
    }
    
    
    
    
}
