<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // Register the API routes directly
            Route::prefix('api/v2/quickserve/orders')->group(function () {
                Route::get('/', [App\Http\Controllers\Api\V2\OrderController::class, 'index']);
                Route::post('/', [App\Http\Controllers\Api\V2\OrderController::class, 'store']);
                Route::get('/{id}', [App\Http\Controllers\Api\V2\OrderController::class, 'show']);
                Route::put('/{id}', [App\Http\Controllers\Api\V2\OrderController::class, 'update']);
                Route::delete('/{id}', [App\Http\Controllers\Api\V2\OrderController::class, 'destroy']);
                Route::get('/customer/{customerId}', [App\Http\Controllers\Api\V2\OrderController::class, 'getByCustomer']);
                Route::patch('/{id}/status', [App\Http\Controllers\Api\V2\OrderController::class, 'updateStatus']);
                Route::patch('/{id}/delivery-status', [App\Http\Controllers\Api\V2\OrderController::class, 'updateDeliveryStatus']);
            });
        });
    }
}
