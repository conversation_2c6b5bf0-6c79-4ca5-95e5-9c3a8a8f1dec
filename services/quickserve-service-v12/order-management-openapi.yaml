openapi: 3.0.3
info:
  title: Order Management API
  description: |
    Comprehensive order management system for QuickServe food delivery platform.

    ## Complete Order Journey (Updated with Temporary Tables)
    1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
    2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
    3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
    4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
    5. **Order Fulfillment** - All orders ready for delivery with status "Confirmed"

    ## Mobile App Integration
    **Step 1:** POST /order-management/create → Get payment_service_transaction_id
    **Step 2:** Use Payment Service v12 APIs with transaction ID
    **Step 3:** Orders automatically created when payment succeeds

    ## Service Separation
    - **Order Management**: QuickServe Service (temp tables → actual orders)
    - **Payment Processing**: Payment Service v12 (unchanged)
    - **Database**: Proper transaction handling with temp tables
    
  version: 1.0.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>
servers:
  - url: http://*************:8003/api/v2
    description: Development server
  - url: https://api.quickserve.com/v2
    description: Production server

paths:
  /order-management/create:
    post:
      tags:
        - Order Management
      summary: Create a new pre-order with subscription
      description: |
        Creates a pre-order in temporary tables and initiates payment with Payment Service v12.

        **What happens:**
        1. Creates record in `temp_pre_orders`
        2. Creates record in `temp_order_payment` (status: pending)
        3. Creates record in `payment_transaction` (status: initiated)
        4. Calls Payment Service v12 to initiate payment
        5. Returns payment URLs for mobile app

        **After payment success:**
        - Creates multiple orders (one per delivery day based on start_date and selected_days)
        - Creates order_details (fetches meal items from product_planner table)
        - Updates all payment tables

        **New Features:**
        - Uses start_date (YYYY-MM-DD) instead of current date
        - Selected_days array (0=Sunday, 6=Saturday) for specific day selection
        - Fetches meal items from product_planner table automatically
        - Prevents duplicate orders for same day/meal
        - Implements cut-off restrictions based on K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME settings
        - Supports multiple meals in cart (breakfast, lunch, etc.)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
            example:
              customer_id: 3800
              customer_name: "Customer User"
              customer_email: "<EMAIL>"
              customer_phone: "919998887779"
              customer_address: "123 Test Street, Test Area, Test City - 400001"
              location_code: 1001
              location_name: "Test Location"
              city: 1
              city_name: "Mumbai"
              product_code: 342
              product_name: "International Breakfast Subscription"
              product_type: "Meal"
              quantity: 1
              amount: 200.00
              start_date: "2025-01-27"
              selected_days: [1, 2, 3, 4, 5]
              delivery_time: "08:00:00"
              delivery_end_time: "09:00:00"
              food_preference: "veg"
              subscription_days: 15
      responses:
        '201':
          description: Pre-order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePreOrderResponse'
              example:
                success: true
                message: "Pre-order created successfully"
                data:
                  temp_pre_order_id: 46154
                  order_no: "ORD202507221240574531"
                  customer_id: 3800
                  amount: 200
                  status: "pending"
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  subscription_days: 15
                  calculated_delivery_dates: ["2025-01-27", "2025-01-28", "2025-01-29", "2025-01-30", "2025-01-31"]
                  payment_transaction_id: 30609
                  payment_service_transaction_id: "TXN30610"
                  payment_urls:
                    process_payment: "http://*************:8002/api/v2/payments/TXN30610/process"
                    payment_status: "http://*************:8002/api/v2/payments/TXN30610"
                    order_status: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/details/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get order details
      description: Retrieves complete order information including meal items and payment status
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Order details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailsResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/customer/{customerId}:
    get:
      tags:
        - Order Management
      summary: Get customer orders
      description: Retrieves paginated list of orders for a customer
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
          example: 3800
        - name: per_page
          in: query
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: status
          in: query
          schema:
            type: string
            enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
      responses:
        '200':
          description: Customer orders retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrdersResponse'

  /order-management/pre-order-status/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get pre-order status and progress
      description: |
        Retrieves complete pre-order status including:
        - Temp pre-order details
        - Payment status (temp_order_payment + payment_transaction)
        - Created orders count (after payment success)
        - Order details count (meal items created)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507221240574531"
      responses:
        '200':
          description: Pre-order status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreOrderStatusResponse'
              example:
                success: true
                data:
                  pre_order:
                    temp_pre_order_id: 46154
                    order_no: "ORD202507221240574531"
                    customer_id: 3800
                    customer_name: "Customer User"
                    product_name: "International Breakfast Subscription"
                    amount: "200.00"
                    order_status: "New"
                    days_preference: "1,2,3,4,5"
                    order_days: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                    created_date: "2025-07-22 18:10:57"
                  payment_status:
                    temp_payment_status: "success"
                    transaction_status: "completed"
                    gateway: "razorpay"
                    gateway_transaction_id: "TXN30610"
                  orders_created:
                    count: 12
                    order_details_count: 36
                    status: "completed"
        '404':
          description: Pre-order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/status/{orderNo}:
    get:
      tags:
        - Payment Management
      summary: Get payment status
      description: Retrieves current payment status for an order
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentStatusResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/webhook:
    post:
      tags:
        - Payment Processing
      summary: Payment gateway webhook
      description: |
        Handles payment confirmation from payment gateways.
        This endpoint automatically updates order status when payment is confirmed.
        **No additional API calls needed after payment confirmation.**
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentWebhookRequest'
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'
        '401':
          description: Invalid signature
        '500':
          description: Webhook processing failed

  /payment/callback:
    post:
      tags:
        - Payment Processing
      summary: Payment callback handler
      description: Handles payment gateway callbacks (redirects)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentCallbackRequest'
      responses:
        '200':
          description: Callback processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'

  /payment/simulate/success:
    post:
      tags:
        - Testing
      summary: Simulate payment success
      description: Testing endpoint to simulate successful payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /payment/simulate/failure:
    post:
      tags:
        - Testing
      summary: Simulate payment failure
      description: Testing endpoint to simulate payment failure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment failure simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /order-management/payment-success/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment success callback
      description: |
        Called by Payment Service v12 when payment is successful.
        Automatically updates order status to "Confirmed" and creates recurring orders.
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                amount:
                  type: number
                  example: 125.00
      responses:
        '200':
          description: Payment success processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

  /order-management/payment-failure/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment failure callback
      description: |
        Called by Payment Service v12 when payment fails.
        Updates order status to "Payment Failed".
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                failure_reason:
                  type: string
                  example: "Insufficient funds"
      responses:
        '200':
          description: Payment failure processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

components:
  schemas:
    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - customer_name
        - customer_email
        - customer_phone
        - customer_address
        - location_code
        - location_name
        - city
        - city_name
        - product_code
        - product_name
        - product_type
        - quantity
        - amount
        - start_date
        - selected_days
        - subscription_days
      properties:
        customer_id:
          type: integer
          description: Customer ID
        customer_name:
          type: string
          maxLength: 45
        customer_email:
          type: string
          format: email
          maxLength: 45
        customer_phone:
          type: string
          maxLength: 15
        customer_address:
          type: string
          maxLength: 250
        location_code:
          type: integer
        location_name:
          type: string
          maxLength: 45
        city:
          type: integer
        city_name:
          type: string
          maxLength: 45
        product_code:
          type: integer
        product_name:
          type: string
          maxLength: 255
        product_type:
          type: string
          maxLength: 60
        quantity:
          type: integer
          minimum: 1
        amount:
          type: number
          format: float
          minimum: 0
        start_date:
          type: string
          format: date
          description: "Start date for subscription in YYYY-MM-DD format"
          example: "2025-01-27"
        selected_days:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          description: "Array of selected day numbers (0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday)"
          example: [1, 2, 3, 4, 5]
        delivery_time:
          type: string
          format: time
          example: "12:30:00"
        delivery_end_time:
          type: string
          format: time
          example: "13:30:00"
        food_preference:
          type: string
          maxLength: 255
          example: "veg"
        subscription_days:
          type: integer
          minimum: 1
          maximum: 30
          description: "Number of days for subscription (e.g., 15 days)"
          example: 15



    CreatePreOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Pre-order created successfully"
        data:
          type: object
          properties:
            temp_pre_order_id:
              type: integer
              example: 46154
              description: "Temporary pre-order ID for tracking"
            order_no:
              type: string
              example: "ORD202507221240574531"
              description: "Unique order number"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 200
            status:
              type: string
              example: "pending"
              description: "Pre-order status (pending until payment)"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            subscription_days:
              type: integer
              example: 15
              description: "Total subscription duration in days"
            payment_transaction_id:
              type: integer
              example: 30609
              description: "Local payment transaction ID"
            payment_service_transaction_id:
              type: string
              example: "TXN30610"
              description: "Payment Service v12 transaction ID"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
                  description: "URL to check pre-order status"

    CreateOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order created successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              example: 127616
            order_no:
              type: string
              example: "ORD202507220800028674"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 125
            status:
              type: string
              example: "New"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            local_transaction_id:
              type: integer
              example: 30604
              description: "Local transaction ID in QuickServe service"
            payment_service_transaction_id:
              type: integer
              example: 15234
              description: "Transaction ID from Payment Service v12"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://localhost:8003/api/v2/order-management/details/ORD202507220800028674"
                  description: "URL to check order status in QuickServe"

    OrderDetailsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order:
              $ref: '#/components/schemas/OrderDetails'
            meal_items:
              type: array
              items:
                $ref: '#/components/schemas/MealItemDetails'
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    OrderDetails:
      type: object
      properties:
        order_id:
          type: integer
        order_no:
          type: string
        customer_id:
          type: integer
        customer_name:
          type: string
        customer_email:
          type: string
        customer_phone:
          type: string
        customer_address:
          type: string
        product_name:
          type: string
        product_type:
          type: string
        quantity:
          type: integer
        amount:
          type: string
        order_status:
          type: string
          enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        delivery_status:
          type: string
        order_date:
          type: string
          format: date-time
        delivery_time:
          type: string
          format: time
        delivery_end_time:
          type: string
          format: time
        recurring_status:
          type: string

    MealItemDetails:
      type: object
      properties:
        product_code:
          type: integer
        product_name:
          type: string
        quantity:
          type: integer
        amount:
          type: string

    PaymentTransactionDetails:
      type: object
      properties:
        transaction_id:
          type: integer
        gateway_transaction_id:
          type: string
          nullable: true
        status:
          type: string
          enum: [pending, completed, failed]
        gateway:
          type: string
        payment_amount:
          type: string
        created_date:
          type: string
          format: date-time

    CustomerOrdersResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderSummary'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    OrderSummary:
      type: object
      properties:
        pk_order_no:
          type: integer
        order_no:
          type: string
        customer_code:
          type: integer
        customer_name:
          type: string
        product_name:
          type: string
        amount:
          type: string
        order_status:
          type: string
        order_date:
          type: string
          format: date-time
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        recurring_status:
          type: string

    PaginationInfo:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        last_page:
          type: integer

    PaymentStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order_no:
              type: string
            order_status:
              type: string
            amount:
              type: string
            amount_paid:
              type: string
            payment_mode:
              type: string
              nullable: true
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    PaymentWebhookRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
          enum: [completed, success, failed, failure, pending]
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string
        gateway_transaction_id:
          type: string

    PaymentCallbackRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string

    WebhookResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string

    PaymentSimulationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string
            amount_paid:
              type: number
            reason:
              type: string
              description: "Present only for failure scenarios"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error description"

    PaymentCallbackResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Payment success processed"
        data:
          type: object
          properties:
            order_no:
              type: string
              example: "ORD202507220800028674"
            order_status:
              type: string
              example: "Confirmed"
            payment_status:
              type: string
              example: "completed"
            failure_reason:
              type: string
              description: "Present only for failure callbacks"
              example: "Insufficient funds"

    PreOrderStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            pre_order:
              type: object
              properties:
                temp_pre_order_id:
                  type: integer
                  example: 46154
                order_no:
                  type: string
                  example: "ORD202507221240574531"
                customer_id:
                  type: integer
                  example: 3800
                customer_name:
                  type: string
                  example: "Customer User"
                product_name:
                  type: string
                  example: "International Breakfast Subscription"
                amount:
                  type: string
                  example: "200.00"
                order_status:
                  type: string
                  example: "New"
                days_preference:
                  type: string
                  example: "1,2,3,4,5"
                order_days:
                  type: array
                  items:
                    type: string
                    format: date
                  example: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                  description: "Calculated delivery dates based on days_preference and subscription_days"
                created_date:
                  type: string
                  format: date-time
                  example: "2025-07-22 18:10:57"
            payment_status:
              type: object
              properties:
                temp_payment_status:
                  type: string
                  enum: [pending, success, failed]
                  example: "success"
                  description: "Status from temp_order_payment table"
                transaction_status:
                  type: string
                  enum: [initiated, completed, failed]
                  example: "completed"
                  description: "Status from payment_transaction table"
                gateway:
                  type: string
                  example: "razorpay"
                gateway_transaction_id:
                  type: string
                  example: "TXN30610"
            orders_created:
              type: object
              properties:
                count:
                  type: integer
                  example: 12
                  description: "Number of actual orders created (one per delivery day)"
                order_details_count:
                  type: integer
                  example: 36
                  description: "Number of order_details records created (orders × meal_items)"
                status:
                  type: string
                  enum: [pending, completed]
                  example: "completed"
                  description: "Whether actual orders have been created"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authenticated endpoints"

tags:
  - name: Order Management
    description: "Core order management operations"
  - name: Payment Management
    description: "Payment status and tracking"
  - name: Payment Processing
    description: "Payment gateway integration"
  - name: Payment Callbacks
    description: "Payment service callback handlers"
  - name: Testing
    description: "Testing and simulation endpoints"
