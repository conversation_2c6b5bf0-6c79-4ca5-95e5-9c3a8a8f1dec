
   <?php
$form = $this->form;
$form->setAttribute('action', $this->url('custgroup', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>   
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
          <form  method="post">
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('group_name')); ?></label>
              </div>
              <div class="large-8 columns">
               <?php  
               		echo $this->formHidden($form->get('group_code'));
					echo $this->formElement($form->get('group_name'));
					echo $this->formElementErrors($form->get('group_name'),array('class' => 'red'));
			  ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('fk_location_code')); ?></label>
              </div>
              <div class="large-8 columns">
                <?php 
	                echo $this->formElement($form->get('fk_location_code'));
					echo $this->formElementErrors($form->get('fk_location_code'),array('class' => 'red'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 columns">
                 <?php 
	                echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'),array('class' => 'red'));
				 ?>
              </div>
            </div>
             <?php echo $this->formElement($form->get('backurl'));
				 ?>
            <div class="row">
              <div class="large-4 columns">&nbsp;</div>
              <div class="large-8 columns">
                
                <button	type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg" onClick="location.href='group.html'">Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
            
          </form>
        </div>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
