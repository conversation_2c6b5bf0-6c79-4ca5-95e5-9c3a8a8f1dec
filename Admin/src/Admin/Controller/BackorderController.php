<?php
/**
 * This File mainly used for Order process at through BackEnd
 * <PERSON><PERSON> can place customer's order by logging into his account
 * He can also recharge into customer's account through Admin Panel
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: BackorderController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Db\Sql\Select;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Admin\Form\BackorderForm;
use Admin\Form\FrontForm;
//use Front\Form\PayForm;
//use Front\Form\NewCustomerForm;
//use Front\Form\orderForm;

use QuickServe\Model\FrontValidator;
use QuickServe\Model\FrontTable;
//use Front\Model\PayValidator;
//use Front\Model\NewCustomerValidator;
//use Front\Model\OrderFormValidator;
//use Front\Controller\FrontController;
use QuickServe\Model\CustomerTable;
use Lib\QuickServe\CommonConfig as QScommon;
use Lib\QuickServe\Customer as QSCustomer;
use Zend\Session\Container;
use Zend\View\Model\JsonModel;
use Lib\Utility;

class BackorderController extends AbstractActionController {
	
	protected $frontTable;
	/**
	 * It has an instance of Front\Model\FrontTable model
	 *
	 * @var Front\Model\FrontTable $BackorderTable
	 */
    protected $BackorderTable;
    protected $MealTable;
    /**
     * This variable used for the storage of data
     *
     * @var ArrayObject $_storage
     */
    protected $_storage=null;
    /**
     * To check valid email
     *
     * @param string $email
     * @return boolean
     */
    
    /**
     * Customer Login Page
     * Admin login into customers account through this action
     *
     * @return \Zend\View\Model\ViewModel
     */
  	public function indexAction()
    {
        $sm = $this->getServiceLocator();        
        $libCommon = Qscommon::getInstance($sm);
        $libCustomer = QSCustomer::getInstance($sm);
    	$cust_session = new Container('customer');
    	unset($cust_session->customer);
    	$cart = new Container('cart');
    	unset($cart->cart);
    	
    	
    	//$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$cust_session = new Container('customer');
    	$customer = $cust_session->customer;

		$sessionsetting = new Container('setting');
		$setting = $sessionsetting->setting;

        $form = new BackorderForm($sm);
        
    	$request = $this->getRequest();

    	if ($request->isPost()) {
    		$phone  = $request->getPost('phone');
    		
    		$oldcustomer = new FrontValidator();
    		$action_order = "";
    		if (strpos($request->getPost('phone'),'@') !== false) {
    			$action_order = "Email";
    			 
    			//entered value is an email
    			$oldcustomer->getInputFilter()->get('phone')
    			->getValidatorChain()                  // Filters are run second w/ FileInput
    			->attach(new \Zend\Validator\Db\RecordExists(array(
    					'table'     => 'customers',
    					'field'     => 'email_address',
    					'adapter'   => $adapt,
    					'message'   => 'Email-ID does not exists',
    					'break_chain_on_failure' => true,
    			)
    		))->attach(new \Zend\Validator\EmailAddress(array('break_chain_on_failure' => true)));
    		
			} else {
    			$action_order = "Mobile";
    			//$this->getFrontTable()->setActionOfInput('Mobile');
    			//assumed... entered value is an mobile
    			$oldcustomer->getInputFilter()->get('phone')
    			->getValidatorChain()                  // Filters are run second w/ FileInput
    			->attach(new \Zend\Validator\Db\RecordExists(array(
    					'table'     => 'customers',
    					'field'     => 'phone',
    					'adapter'   => $adapt,
    					'message'   => 'Mobile Number does not exists',
    					'break_chain_on_failure' => true,
    					)
    				));
    			}
    
    		
    			$sms_common = $libCommon->getSmsConfig($setting);

    			$chkvalidcustomer = $libCustomer->validcustomer($phone,$action_order);
    			
				if($chkvalidcustomer['status'] == '0'){
    				return new JsonModel(array(
    					'msg' => "error",
    					'errormsg' => "Your account is inactive, please contact administrator at - ".$setting['GLOBAL_WEBSITE_PHONE']."",
    							
    				));
    	
    			}
    		
    			$form->setValidationGroup('phone');
    			$form->setInputFilter($oldcustomer->getInputFilter());
    
    			$form->setData($request->getPost());
    			 
    			if ($form->isValid()) {
    				 
    				$oldcustomer->exchangeArray($form->getData());
    				
//    				//$data_product = $this->getFrontTable()->getOldCustomer($oldcustomer,$action_order);
//    				 
//    				$cust_result = array();
//    				foreach ($data_product as $key=>$value){
//    					$cust_result[$key] = $value;
//    				}
    				// Redirect to list of albums
    				
    				$phone =  $request->getPost('phone');
    				$rolename = $_SESSION['Zend_Auth']['storage']->rolename;
    				$name = $_SESSION['Zend_Auth']['storage']->first_name." ".$_SESSION['Zend_Auth']['storage']->last_name;
    				
    				return new JsonModel(array(
						'msg' => "success",
						'phone' => $phone,
						'role' => $rolename,
						'name' => $name,
    				));
    			}else{
    				return new JsonModel(array(
    				 	'msg' => "error",
    				 	'errormsg' => $form->getMessages(),
    				));
    			 }
    
    	}
    
    	$this->layout()->setVariables(array('page_title'=>"Order For Existing Customer",'description'=>"Registered Customer Order",'breadcrumb'=>"Existing Customer"));

    	return array(
    		'form'      => $form,
    	);
    }
 	
	public function getFrontTable()
	{
		if (!$this->frontTable) {
			$sm = $this->getServiceLocator();
			$this->frontTable = $sm->get('QuickServe\Model\FrontTable');
		}
	
		return $this->frontTable;
	}
}
