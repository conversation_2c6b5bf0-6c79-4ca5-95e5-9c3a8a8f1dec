apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ item.name }}
  namespace: {{ item.namespace }}
  labels:
    app: {{ item.name }}
spec:
  replicas: {{ item.replicas }}
  selector:
    matchLabels:
      app: {{ item.name }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{ item.name }}
    spec:
      containers:
      - name: {{ item.name }}
        image: {{ item.image }}
        imagePullPolicy: Always
        ports:
        - containerPort: {{ item.port }}
        env:
        - name: APP_ENV
          value: production
        - name: APP_DEBUG
          value: "false"
        - name: APP_URL
          value: https://api.cubeonebiz.com
        - name: DB_CONNECTION
          value: mysql
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: db_host
        - name: DB_PORT
          value: "3306"
        - name: DB_DATABASE
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: db_database
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: db_username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: db_password
        - name: RABBITMQ_HOST
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: rabbitmq_host
        - name: RABBITMQ_PORT
          value: "5672"
        - name: RABBITMQ_USER
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: rabbitmq_user
        - name: RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: rabbitmq_password
        - name: RABBITMQ_VHOST
          value: "/"
        - name: QUEUE_CONNECTION
          value: rabbitmq
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: redis_host
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: redis_password
        - name: APP_KEY
          valueFrom:
            secretKeyRef:
              name: {{ item.name }}-secrets
              key: app_key
        resources:
          limits:
            cpu: {{ item.resources.limits.cpu }}
            memory: {{ item.resources.limits.memory }}
          requests:
            cpu: {{ item.resources.requests.cpu }}
            memory: {{ item.resources.requests.memory }}
        livenessProbe:
          httpGet:
            path: {{ item.health_check_path }}
            port: {{ item.port }}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{ item.health_check_path }}
            port: {{ item.port }}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          failureThreshold: 3
      imagePullSecrets:
      - name: dockerhub-secret
