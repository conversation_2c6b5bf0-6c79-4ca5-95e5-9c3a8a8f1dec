describe('Token Persistence E2E Tests', () => {
  beforeEach(() => {
    // Clear all storage before each test
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Visit the application
    cy.visit('/');
  });

  describe('Authentication Persistence', () => {
    it('should persist authentication state across page refresh', () => {
      // Mock successful authentication
      cy.window().then((win) => {
        // Set up mock authentication data
        const authData = {
          user: {
            id: 'test-user-123',
            email: '<EMAIL>',
            fullName: 'Test User',
            username: 'testuser',
            roles: ['user', 'admin']
          },
          authenticated: true,
          expiresAt: Date.now() + 3600000 // 1 hour from now
        };

        // Store development auth
        win.localStorage.setItem('dev_auth', JSON.stringify(authData));

        // Store tokens using TokenManager format
        win.localStorage.setItem('onefood_access_token', 'mock-access-token-123');
        win.localStorage.setItem('onefood_refresh_token', 'mock-refresh-token-456');
        win.localStorage.setItem('onefood_token_expiry', (Date.now() + 3600000).toString());
        win.localStorage.setItem('onefood_user_info', JSON.stringify(authData.user));
      });

      // Navigate to a protected route
      cy.visit('/dashboard/overview');

      // Verify user is authenticated
      cy.get('[data-testid="user-menu"]').should('exist');
      cy.get('[data-testid="user-name"]').should('contain', 'Test User');

      // Refresh the page
      cy.reload();

      // Verify authentication persists after refresh
      cy.get('[data-testid="user-menu"]').should('exist');
      cy.get('[data-testid="user-name"]').should('contain', 'Test User');

      // Verify user is still on protected route
      cy.url().should('include', '/dashboard/overview');
    });

    it('should redirect to login when no authentication data exists', () => {
      // Visit a protected route without authentication
      cy.visit('/dashboard/overview');

      // Should be redirected to login
      cy.url().should('include', '/auth/sign-in');
      cy.get('[data-testid="login-form"]').should('exist');
    });

    it('should clear expired tokens and redirect to login', () => {
      cy.window().then((win) => {
        // Set up expired authentication data
        const expiredAuthData = {
          user: {
            id: 'expired-user',
            email: '<EMAIL>',
            fullName: 'Expired User'
          },
          authenticated: true,
          expiresAt: Date.now() - 3600000 // 1 hour ago
        };

        // Store expired tokens
        win.localStorage.setItem('dev_auth', JSON.stringify(expiredAuthData));
        win.localStorage.setItem('onefood_access_token', 'expired-token');
        win.localStorage.setItem('onefood_refresh_token', 'expired-refresh');
        win.localStorage.setItem('onefood_token_expiry', (Date.now() - 3600000).toString());
      });

      // Visit a protected route
      cy.visit('/dashboard/overview');

      // Should be redirected to login due to expired tokens
      cy.url().should('include', '/auth/sign-in');

      // Verify expired tokens are cleared
      cy.window().then((win) => {
        expect(win.localStorage.getItem('onefood_access_token')).to.be.null;
        expect(win.localStorage.getItem('onefood_refresh_token')).to.be.null;
        expect(win.localStorage.getItem('onefood_token_expiry')).to.be.null;
      });
    });
  });

  describe('Token Refresh Functionality', () => {
    it('should automatically refresh tokens when near expiry', () => {
      // Intercept token refresh API call
      cy.intercept('POST', '/v2/auth/refresh-token', {
        statusCode: 200,
        body: {
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
          expires_in: 3600
        }
      }).as('refreshToken');

      cy.window().then((win) => {
        // Set up tokens that are about to expire (within 5 minutes)
        const nearExpiryTime = Date.now() + 240000; // 4 minutes from now

        win.localStorage.setItem('onefood_access_token', 'expiring-token');
        win.localStorage.setItem('onefood_refresh_token', 'valid-refresh-token');
        win.localStorage.setItem('onefood_token_expiry', nearExpiryTime.toString());
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'user-123',
          email: '<EMAIL>',
          fullName: 'Test User'
        }));
      });

      // Visit the application
      cy.visit('/dashboard/overview');

      // Make an API call that would trigger token validation
      cy.intercept('GET', '/api/user/profile', {
        statusCode: 200,
        body: { message: 'Profile data' }
      }).as('getProfile');

      cy.get('[data-testid="profile-button"]').click();

      // Verify token refresh was called
      cy.wait('@refreshToken');

      // Verify new tokens are stored
      cy.window().then((win) => {
        expect(win.localStorage.getItem('onefood_access_token')).to.equal('new-access-token');
        expect(win.localStorage.getItem('onefood_refresh_token')).to.equal('new-refresh-token');
      });
    });

    it('should handle token refresh failure and redirect to login', () => {
      // Intercept token refresh API call with failure
      cy.intercept('POST', '/v2/auth/refresh-token', {
        statusCode: 401,
        body: { error: 'Invalid refresh token' }
      }).as('refreshTokenFail');

      cy.window().then((win) => {
        // Set up tokens that are about to expire
        const nearExpiryTime = Date.now() + 240000; // 4 minutes from now

        win.localStorage.setItem('onefood_access_token', 'expiring-token');
        win.localStorage.setItem('onefood_refresh_token', 'invalid-refresh-token');
        win.localStorage.setItem('onefood_token_expiry', nearExpiryTime.toString());
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'user-123',
          email: '<EMAIL>'
        }));
      });

      // Visit the application
      cy.visit('/dashboard/overview');

      // Make an API call that would trigger token validation
      cy.intercept('GET', '/api/user/profile', {
        statusCode: 401,
        body: { error: 'Unauthorized' }
      }).as('getProfileUnauth');

      cy.get('[data-testid="profile-button"]').click();

      // Verify token refresh was attempted
      cy.wait('@refreshTokenFail');

      // Should be redirected to login
      cy.url().should('include', '/auth/sign-in');

      // Verify tokens are cleared
      cy.window().then((win) => {
        expect(win.localStorage.getItem('onefood_access_token')).to.be.null;
        expect(win.localStorage.getItem('onefood_refresh_token')).to.be.null;
      });
    });
  });

  describe('Multiple Tab Synchronization', () => {
    it('should maintain consistent auth state across multiple tabs', () => {
      // Set up authentication in first tab
      cy.window().then((win) => {
        const authData = {
          user: {
            id: 'multi-tab-user',
            email: '<EMAIL>',
            fullName: 'Multi Tab User'
          },
          authenticated: true
        };

        win.localStorage.setItem('onefood_access_token', 'multi-tab-token');
        win.localStorage.setItem('onefood_refresh_token', 'multi-tab-refresh');
        win.localStorage.setItem('onefood_token_expiry', (Date.now() + 3600000).toString());
        win.localStorage.setItem('onefood_user_info', JSON.stringify(authData.user));
      });

      // Verify authentication in first tab
      cy.visit('/dashboard/overview');
      cy.get('[data-testid="user-name"]').should('contain', 'Multi Tab User');

      // Open second tab (simulate by visiting same URL)
      cy.visit('/dashboard/analytics');
      cy.get('[data-testid="user-name"]').should('contain', 'Multi Tab User');

      // Logout from second tab
      cy.get('[data-testid="logout-button"]').click();

      // Verify logout redirects to login
      cy.url().should('include', '/auth/sign-in');

      // Verify tokens are cleared (simulating cross-tab effect)
      cy.window().then((win) => {
        expect(win.localStorage.getItem('onefood_access_token')).to.be.null;
      });
    });
  });

  describe('Development Mode Authentication', () => {
    it('should handle development mode authentication', () => {
      // Set environment to development mode
      cy.window().then((win) => {
        // Override environment variable for test
        (win as any).process = { env: { NODE_ENV: 'development' } };
      });

      // Visit test auth page
      cy.visit('/test-auth');

      // Click development login button
      cy.get('[data-testid="dev-login-button"]').click();

      // Verify development authentication is set
      cy.window().then((win) => {
        const devAuth = win.localStorage.getItem('dev_auth');
        expect(devAuth).to.not.be.null;
        
        const authData = JSON.parse(devAuth!);
        expect(authData.authenticated).to.be.true;
        expect(authData.user).to.exist;
      });

      // Refresh page and verify persistence
      cy.reload();
      cy.get('[data-testid="auth-status"]').should('contain', 'Authenticated');
    });

    it('should handle development mode logout', () => {
      // Set up development authentication
      cy.window().then((win) => {
        const devAuth = {
          user: {
            id: 'dev-user',
            email: '<EMAIL>',
            fullName: 'Dev User'
          },
          authenticated: true,
          token: 'dev-token-123',
          expiresAt: Date.now() + 3600000
        };

        win.localStorage.setItem('dev_auth', JSON.stringify(devAuth));
      });

      cy.visit('/test-auth');
      cy.get('[data-testid="auth-status"]').should('contain', 'Authenticated');

      // Logout
      cy.get('[data-testid="dev-logout-button"]').click();

      // Verify logout
      cy.get('[data-testid="auth-status"]').should('contain', 'Not Authenticated');

      // Verify dev auth is cleared
      cy.window().then((win) => {
        expect(win.localStorage.getItem('dev_auth')).to.be.null;
      });
    });
  });

  describe('API Integration with Token Management', () => {
    it('should automatically add authorization headers to API requests', () => {
      // Set up authentication
      cy.window().then((win) => {
        win.localStorage.setItem('onefood_access_token', 'api-test-token');
        win.localStorage.setItem('onefood_token_expiry', (Date.now() + 3600000).toString());
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'api-user',
          email: '<EMAIL>'
        }));
      });

      // Intercept API calls to verify authorization header
      cy.intercept('GET', '/api/**', (req) => {
        expect(req.headers).to.have.property('authorization');
        expect(req.headers.authorization).to.equal('Bearer api-test-token');
        req.reply({ statusCode: 200, body: { success: true } });
      }).as('apiCall');

      cy.visit('/dashboard/overview');

      // Trigger an API call
      cy.get('[data-testid="refresh-data-button"]').click();

      // Verify API call was made with correct headers
      cy.wait('@apiCall');
    });

    it('should handle 401 responses by attempting token refresh', () => {
      // Set up tokens that need refresh
      cy.window().then((win) => {
        win.localStorage.setItem('onefood_access_token', 'expired-api-token');
        win.localStorage.setItem('onefood_refresh_token', 'valid-refresh-token');
        win.localStorage.setItem('onefood_token_expiry', (Date.now() + 3600000).toString());
      });

      // Intercept initial API call that returns 401
      cy.intercept('GET', '/api/user/profile', {
        statusCode: 401,
        body: { error: 'Token expired' }
      }).as('initialApiCall');

      // Intercept token refresh call
      cy.intercept('POST', '/v2/auth/refresh-token', {
        statusCode: 200,
        body: {
          access_token: 'new-api-token',
          refresh_token: 'new-refresh-token',
          expires_in: 3600
        }
      }).as('tokenRefresh');

      // Intercept retry API call with new token
      cy.intercept('GET', '/api/user/profile', (req) => {
        if (req.headers.authorization === 'Bearer new-api-token') {
          req.reply({ statusCode: 200, body: { user: 'profile data' } });
        } else {
          req.reply({ statusCode: 401, body: { error: 'Invalid token' } });
        }
      }).as('retryApiCall');

      cy.visit('/dashboard/profile');

      // Verify the sequence: initial call -> refresh -> retry
      cy.wait('@initialApiCall');
      cy.wait('@tokenRefresh');
      cy.wait('@retryApiCall');

      // Verify new token is stored
      cy.window().then((win) => {
        expect(win.localStorage.getItem('onefood_access_token')).to.equal('new-api-token');
      });
    });
  });

  describe('Browser Storage Events', () => {
    it('should respond to storage events from other tabs', () => {
      // Set up initial authentication
      cy.window().then((win) => {
        win.localStorage.setItem('onefood_access_token', 'initial-token');
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'storage-user',
          email: '<EMAIL>',
          fullName: 'Storage User'
        }));
      });

      cy.visit('/dashboard/overview');
      cy.get('[data-testid="user-name"]').should('contain', 'Storage User');

      // Simulate storage event from another tab (logout)
      cy.window().then((win) => {
        // Clear tokens to simulate logout from another tab
        win.localStorage.clear();
        
        // Dispatch storage event
        const storageEvent = new StorageEvent('storage', {
          key: 'onefood_access_token',
          oldValue: 'initial-token',
          newValue: null,
          storageArea: win.localStorage
        });
        
        win.dispatchEvent(storageEvent);
      });

      // Should redirect to login due to storage event
      cy.url().should('include', '/auth/sign-in');
    });
  });

  describe('Token Security', () => {
    it('should not expose tokens in console logs', () => {
      cy.window().then((win) => {
        // Set up authentication
        win.localStorage.setItem('onefood_access_token', 'secret-token-123');
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'security-user'
        }));

        // Monitor console for token exposure
        const originalConsoleLog = win.console.log;
        const originalConsoleError = win.console.error;
        const originalConsoleWarn = win.console.warn;

        const loggedMessages: string[] = [];

        win.console.log = (...args) => {
          loggedMessages.push(args.join(' '));
          originalConsoleLog.apply(win.console, args);
        };

        win.console.error = (...args) => {
          loggedMessages.push(args.join(' '));
          originalConsoleError.apply(win.console, args);
        };

        win.console.warn = (...args) => {
          loggedMessages.push(args.join(' '));
          originalConsoleWarn.apply(win.console, args);
        };

        // Use the application
        cy.visit('/dashboard/overview');

        // Check that token is not exposed in console
        cy.then(() => {
          const tokenInLogs = loggedMessages.some(msg => 
            msg.includes('secret-token-123') && 
            !msg.includes('stored successfully') // Allow success messages
          );
          expect(tokenInLogs).to.be.false;
        });

        // Restore console methods
        win.console.log = originalConsoleLog;
        win.console.error = originalConsoleError;
        win.console.warn = originalConsoleWarn;
      });
    });

    it('should clear sensitive data on window unload', () => {
      cy.window().then((win) => {
        // Set up authentication
        win.localStorage.setItem('onefood_access_token', 'unload-token');
        win.localStorage.setItem('onefood_user_info', JSON.stringify({
          id: 'unload-user'
        }));

        // Add beforeunload event listener to verify cleanup
        win.addEventListener('beforeunload', () => {
          // In a real application, sensitive data might be cleared here
          // This test verifies the event is properly handled
        });
      });

      cy.visit('/dashboard/overview');

      // Navigate away (simulating tab close)
      cy.visit('/auth/sign-in');

      // Note: In practice, the beforeunload event would handle cleanup
      // This test structure validates the event handling exists
    });
  });
}); 