<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 Checking Product Types in Database\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // Check 1: All distinct product_type values in orders table
    echo "📋 Check 1: Distinct Product Types in Orders Table\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $productTypes = DB::table('orders')
        ->select('product_type')
        ->distinct()
        ->whereNotNull('product_type')
        ->where('product_type', '!=', '')
        ->orderBy('product_type')
        ->pluck('product_type');
    
    if ($productTypes->isEmpty()) {
        echo "❌ No product types found in orders table\n";
    } else {
        echo "✅ Found product types in orders table:\n";
        foreach ($productTypes as $type) {
            $count = DB::table('orders')->where('product_type', $type)->count();
            echo "  - '{$type}' ({$count} orders)\n";
        }
    }
    echo "\n";
    
    // Check 2: Product types with sample product names
    echo "📋 Check 2: Product Types with Sample Product Names\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    foreach ($productTypes as $type) {
        $sampleProducts = DB::table('orders')
            ->where('product_type', $type)
            ->select('product_name')
            ->distinct()
            ->limit(5)
            ->pluck('product_name');
        
        echo "Product Type: '{$type}'\n";
        echo "  Sample Products:\n";
        foreach ($sampleProducts as $product) {
            echo "    - '{$product}'\n";
        }
        echo "\n";
    }
    
    // Check 3: Check products table for more specific types
    echo "📋 Check 3: Product Types in Products Table\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    try {
        $productTableTypes = DB::table('products')
            ->select('product_type')
            ->distinct()
            ->whereNotNull('product_type')
            ->where('product_type', '!=', '')
            ->orderBy('product_type')
            ->pluck('product_type');
        
        if ($productTableTypes->isEmpty()) {
            echo "❌ No product types found in products table\n";
        } else {
            echo "✅ Found product types in products table:\n";
            foreach ($productTableTypes as $type) {
                $count = DB::table('products')->where('product_type', $type)->count();
                echo "  - '{$type}' ({$count} products)\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ Error accessing products table: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // Check 4: Check for meal_type or category columns
    echo "📋 Check 4: Check for Meal Type or Category Columns\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    // Check orders table structure
    $ordersColumns = DB::select("SHOW COLUMNS FROM orders");
    $mealTypeColumns = [];
    
    foreach ($ordersColumns as $column) {
        if (stripos($column->Field, 'meal') !== false || 
            stripos($column->Field, 'category') !== false || 
            stripos($column->Field, 'type') !== false) {
            $mealTypeColumns[] = $column->Field;
        }
    }
    
    echo "Relevant columns in orders table:\n";
    foreach ($mealTypeColumns as $column) {
        echo "  - {$column}\n";
    }
    echo "\n";
    
    // Check 5: Sample orders with all type-related fields
    echo "📋 Check 5: Sample Orders with Type-Related Fields\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $sampleOrders = DB::table('orders')
        ->select([
            'pk_order_no',
            'order_no',
            'product_name',
            'product_type',
            'product_code'
        ])
        ->limit(10)
        ->get();
    
    foreach ($sampleOrders as $order) {
        echo "Order {$order->order_no}:\n";
        echo "  Product: '{$order->product_name}'\n";
        echo "  Type: '{$order->product_type}'\n";
        echo "  Code: {$order->product_code}\n";
        echo "  ---\n";
    }
    echo "\n";
    
    // Check 6: Check if there's a mapping table or lookup
    echo "📋 Check 6: Check Product Code to Type Mapping\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $productCodeMapping = DB::table('orders')
        ->select('product_code', 'product_name', 'product_type')
        ->distinct()
        ->orderBy('product_code')
        ->limit(20)
        ->get();
    
    echo "Product Code to Type Mapping:\n";
    foreach ($productCodeMapping as $mapping) {
        echo "  Code {$mapping->product_code}: '{$mapping->product_name}' → '{$mapping->product_type}'\n";
    }
    echo "\n";
    
    // Check 7: Analyze product names for meal type patterns
    echo "📋 Check 7: Analyze Product Names for Meal Type Patterns\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $allProductNames = DB::table('orders')
        ->select('product_name', 'product_type')
        ->distinct()
        ->get();
    
    $mealTypeAnalysis = [
        'breakfast' => [],
        'lunch' => [],
        'dinner' => [],
        'meal' => [],
        'other' => []
    ];
    
    foreach ($allProductNames as $product) {
        $name = strtolower($product->product_name);
        
        if (strpos($name, 'breakfast') !== false) {
            $mealTypeAnalysis['breakfast'][] = $product;
        } elseif (strpos($name, 'lunch') !== false) {
            $mealTypeAnalysis['lunch'][] = $product;
        } elseif (strpos($name, 'dinner') !== false) {
            $mealTypeAnalysis['dinner'][] = $product;
        } elseif (strpos($name, 'meal') !== false) {
            $mealTypeAnalysis['meal'][] = $product;
        } else {
            $mealTypeAnalysis['other'][] = $product;
        }
    }
    
    echo "Meal Type Analysis from Product Names:\n";
    foreach ($mealTypeAnalysis as $type => $products) {
        echo "  {$type}: " . count($products) . " products\n";
        if (!empty($products)) {
            echo "    Sample: '{$products[0]->product_name}' (type: '{$products[0]->product_type}')\n";
        }
    }
    echo "\n";
    
    // Check 8: Recommendations
    echo "📋 Check 8: Recommendations\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    echo "Based on analysis:\n";
    
    if (count($productTypes) > 1) {
        echo "✅ Multiple product types found - can use existing product_type field\n";
    } else {
        echo "❌ Only generic product types found - need to derive from product_name\n";
    }
    
    $breakfastCount = count($mealTypeAnalysis['breakfast']);
    $lunchCount = count($mealTypeAnalysis['lunch']);
    $dinnerCount = count($mealTypeAnalysis['dinner']);
    
    if ($breakfastCount > 0 || $lunchCount > 0 || $dinnerCount > 0) {
        echo "✅ Meal types can be derived from product names:\n";
        echo "    - Breakfast products: {$breakfastCount}\n";
        echo "    - Lunch products: {$lunchCount}\n";
        echo "    - Dinner products: {$dinnerCount}\n";
    }
    
    echo "\nRecommended approach:\n";
    if (in_array('Breakfast', $productTypes->toArray()) || in_array('Lunch', $productTypes->toArray())) {
        echo "✅ Use existing product_type field (already has specific meal types)\n";
    } else {
        echo "✅ Derive meal type from product_name field (current implementation is correct)\n";
        echo "✅ Consider updating product_type field in database for better performance\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Summary:\n";
echo "- Check current product_type values in orders table\n";
echo "- Analyze product names for meal type patterns\n";
echo "- Determine best approach for meal type detection\n";
echo "- Provide recommendations for improvement\n";
