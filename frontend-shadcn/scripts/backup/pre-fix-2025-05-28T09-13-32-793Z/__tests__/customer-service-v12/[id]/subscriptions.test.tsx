import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Subscriptions } from '@/components/customer-service-v12/[id]/subscriptions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Subscriptions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Subscriptions />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Subscriptions')).toBeInTheDocument();
  });
});