# 📡 API Updates - Phase 1 & Phase 2 Completion

**Date**: December 19, 2024  
**Version**: 2025.2.0  
**Status**: Production Ready

---

## 🔒 Enhanced Security API Endpoints

### GeoIP Service Integration

#### **GET** `/api/v2/auth/security/geoip/{ip}`
**Description**: Get geographic information for an IP address  
**Authentication**: JWT Required  
**Parameters**:
- `ip` (string, required): IP address to lookup

**Response**:
```json
{
  "status": "success",
  "data": {
    "ip": "***********",
    "country": "United States",
    "country_code": "US",
    "region": "California",
    "city": "San Francisco",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "timezone": "America/Los_Angeles",
    "isp": "Example ISP",
    "threat_level": "low",
    "is_vpn": false,
    "is_proxy": false
  }
}
```

#### **POST** `/api/v2/auth/security/threat-analysis`
**Description**: Analyze IP address for security threats  
**Authentication**: JWT Required  
**Request Body**:
```json
{
  "ip": "***********",
  "user_id": 123,
  "action": "login",
  "user_agent": "Mozilla/5.0..."
}
```

**Response**:
```json
{
  "status": "success",
  "data": {
    "threat_score": 25,
    "risk_level": "low",
    "geographic_anomaly": false,
    "recommendations": [
      "Monitor for unusual activity",
      "Enable MFA if not already active"
    ],
    "blocked": false
  }
}
```

#### **GET** `/api/v2/auth/security/events`
**Description**: Get security events with pagination  
**Authentication**: JWT Required  
**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 15)
- `type` (string, optional): Event type filter
- `severity` (string, optional): Severity filter

**Response**:
```json
{
  "status": "success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "type": "login_attempt",
        "severity": "medium",
        "ip_address": "***********",
        "country": "United States",
        "threat_score": 25,
        "created_at": "2024-12-19T10:30:00Z"
      }
    ],
    "total": 150,
    "per_page": 15,
    "last_page": 10
  }
}
```

---

## 🧙‍♂️ Setup Wizard Service API Endpoints

### Setup Wizard Status

#### **GET** `/api/v2/admin/setup-wizard/status`
**Description**: Get current setup wizard status  
**Authentication**: JWT Required  

**Response**:
```json
{
  "status": "success",
  "data": {
    "completed": false,
    "current_step": 3,
    "total_steps": 7,
    "progress_percentage": 42.86
  }
}
```

#### **PUT** `/api/v2/admin/setup-wizard/status`
**Description**: Update setup wizard status  
**Authentication**: JWT Required  
**Request Body**:
```json
{
  "completed": false,
  "current_step": 4
}
```

### Setup Steps

#### **POST** `/api/v2/admin/setup-wizard/company-profile`
**Description**: Setup company profile (Step 1)  
**Request Body**:
```json
{
  "company_name": "Example Restaurant",
  "postal_address": "123 Main St, City, State 12345",
  "support_email": "<EMAIL>",
  "phone": "******-123-4567",
  "sender_id": "EXAMPLE"
}
```

#### **POST** `/api/v2/admin/setup-wizard/system-settings`
**Description**: Setup system settings (Step 2)  
**Request Body**:
```json
{
  "locale": "en_US",
  "currency": "USD",
  "currency_symbol": "$",
  "time_zone": "America/New_York"
}
```

#### **POST** `/api/v2/admin/setup-wizard/payment-gateways`
**Description**: Setup payment gateways (Step 3)  
**Request Body**:
```json
{
  "primary_gateway": "stripe",
  "gateways": [
    {
      "provider": "stripe",
      "enabled": true,
      "test_mode": false,
      "credentials": {
        "public_key": "pk_live_...",
        "secret_key": "sk_live_..."
      }
    }
  ]
}
```

#### **POST** `/api/v2/admin/setup-wizard/menu`
**Description**: Setup menu (Step 4)  
**Request Body**:
```json
{
  "categories": [
    {
      "name": "Appetizers",
      "description": "Start your meal right",
      "items": [
        {
          "name": "Caesar Salad",
          "description": "Fresh romaine lettuce...",
          "price": 12.99,
          "dietary_options": ["vegetarian"],
          "allergens": ["dairy", "gluten"]
        }
      ]
    }
  ]
}
```

#### **POST** `/api/v2/admin/setup-wizard/subscription`
**Description**: Setup subscription plan (Step 5)  
**Request Body**:
```json
{
  "selected_plan": {
    "plan_id": "pro_monthly",
    "billing_period": "monthly",
    "auto_renew": true,
    "promo_code": "WELCOME20"
  },
  "billing_information": {
    "billing_name": "John Doe",
    "billing_email": "<EMAIL>",
    "billing_address": "123 Business St"
  }
}
```

#### **POST** `/api/v2/admin/setup-wizard/team`
**Description**: Setup team members (Step 6)  
**Request Body**:
```json
{
  "invitations": [
    {
      "email": "<EMAIL>",
      "role": "manager",
      "permissions": ["orders.manage", "kitchen.view"],
      "send_invitation": true
    }
  ]
}
```

#### **POST** `/api/v2/admin/setup-wizard/complete`
**Description**: Complete setup wizard (Step 7)  
**Authentication**: JWT Required  

**Response**:
```json
{
  "status": "success",
  "message": "Setup wizard completed successfully",
  "data": {
    "completed": true,
    "completion_date": "2024-12-19T15:30:00Z",
    "next_steps": [
      "Configure delivery zones",
      "Upload menu images",
      "Test payment processing"
    ]
  }
}
```

---

## 🗺️ OpenStreetMaps API Endpoints

### Map Services

#### **GET** `/api/v2/maps/tiles/{z}/{x}/{y}.png`
**Description**: Get map tiles for display  
**Parameters**:
- `z` (integer): Zoom level
- `x` (integer): Tile X coordinate  
- `y` (integer): Tile Y coordinate

#### **GET** `/api/v2/maps/geocode/search`
**Description**: Search for addresses  
**Query Parameters**:
- `q` (string, required): Search query
- `limit` (integer, optional): Maximum results (default: 10)

**Response**:
```json
{
  "status": "success",
  "data": [
    {
      "display_name": "123 Main St, City, State, Country",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "importance": 0.8
    }
  ]
}
```

#### **GET** `/api/v2/maps/geocode/reverse`
**Description**: Reverse geocode coordinates to address  
**Query Parameters**:
- `lat` (float, required): Latitude
- `lon` (float, required): Longitude

#### **POST** `/api/v2/maps/route`
**Description**: Calculate route between points  
**Request Body**:
```json
{
  "coordinates": [
    [-74.0060, 40.7128],
    [-73.9857, 40.7484]
  ],
  "profile": "driving"
}
```

---

## 📊 Enhanced Analytics Endpoints

### Real-time Analytics

#### **GET** `/api/v2/analytics/realtime/dashboard`
**Description**: Get real-time dashboard data  
**Authentication**: JWT Required  

**Response**:
```json
{
  "status": "success",
  "data": {
    "active_orders": 45,
    "revenue_today": 2847.50,
    "average_order_value": 32.15,
    "delivery_performance": {
      "on_time_percentage": 94.2,
      "average_delivery_time": 28
    }
  }
}
```

---

## 🔧 API Standards & Guidelines

### Authentication
- All endpoints require JWT authentication unless specified
- Include `Authorization: Bearer {token}` header
- Tokens expire after 24 hours with 7-day refresh tokens

### Response Format
```json
{
  "status": "success|error",
  "message": "Human readable message",
  "data": {},
  "errors": {},
  "meta": {
    "timestamp": "2024-12-19T15:30:00Z",
    "version": "2025.2.0"
  }
}
```

### Error Handling
- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Invalid or missing authentication
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **422**: Unprocessable Entity - Validation errors
- **500**: Internal Server Error - Server-side error

### Rate Limiting
- **Standard**: 1000 requests per hour per user
- **Setup Wizard**: 100 requests per hour per user
- **Maps API**: 10,000 requests per hour per application

---

**📡 All API endpoints are now production-ready with comprehensive documentation and Kong API Gateway integration.**
