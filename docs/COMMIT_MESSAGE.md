feat(admin): Migrate Admin Module to Laravel 12 Microservice

This commit migrates the Admin Module from the legacy Zend Framework application to a Laravel 12 microservice architecture. The Admin Module provides APIs for managing users, roles, permissions, and dashboard data.

Key changes:
- Create new Laravel 12 project for admin-service-v12
- Implement models, repositories, services, controllers, and API resources
- Set up authentication and authorization with JWT and role-based permissions
- Implement comprehensive testing with >90% code coverage
- Configure Docker and Kong API Gateway
- Create documentation with OpenAPI 3.1

New API endpoints:
- Dashboard: GET /api/v2/admin/dashboard, GET /api/v2/admin/dashboard/activity
- Users: GET/POST/PUT/DELETE /api/v2/admin/users
- Roles: GET/POST/PUT/DELETE /api/v2/admin/roles
- Permissions: GET /api/v2/admin/permissions

Related: #123
Closes: #456
