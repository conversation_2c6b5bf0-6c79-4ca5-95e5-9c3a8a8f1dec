import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Wallet } from '@/components/customer-service-v12/[id]/wallet';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Wallet', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Wallet />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Wallet')).toBeInTheDocument();
  });
});