{"target_path": "docs/openapi/unified-api-specification.yaml", "audit_timestamp": "2025-12-23T11:00:00Z", "mode": "dry-run", "references": {"code_references": [], "import_statements": [], "config_references": ["./.ide/oneapp-index.json"], "docker_references": [], "test_references": [], "documentation_references": ["./CONSOLIDATION_IMPLEMENTATION_PLAN.md", "./COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md"]}, "risk_assessment": {"level": "LOW", "factors": ["Minimal external references found", "Unified specification already exists", "No critical system dependencies"], "recommendations": ["Safe to proceed with consolidation", "Update IDE index if needed", "Preserve existing service-specific specs as backup"]}, "migration_required": false, "safe_to_proceed": true, "detailed_analysis": {"existing_openapi_files": ["services/auth-service-v12/openapi.yaml", "services/customer-service-v12/openapi.yaml", "services/payment-service-v12/openapi.yaml", "services/quickserve-service-v12/openapi.yaml", "services/kitchen-service-v12/openapi.yaml", "services/delivery-service-v12/openapi.yaml", "services/analytics-service-v12/openapi.yaml", "services/admin-service-v12/openapi.yaml", "services/catalogue-service-v12/openapi.yaml", "services/notification-service-v12/docs/openapi.yaml", "services/misscall-service-v12/openapi.yaml", "services/meal-service-v12/openapi.yaml", "services/subscription-service-v12/openapi.yaml"], "unified_specification": {"file": "docs/openapi/unified-api-specification.yaml", "status": "already_exists", "coverage": "426_endpoints_across_11_services"}, "consolidation_strategy": {"approach": "Use existing unified specification as primary", "backup_strategy": "Keep service-specific specs for reference", "update_requirements": ["Update Kong Gateway configuration", "Update API documentation references", "Update development tools configuration"]}, "references_found": {"ide_index": "./.ide/oneapp-index.json", "impact": "minimal", "action_required": "update_index_if_needed"}}}