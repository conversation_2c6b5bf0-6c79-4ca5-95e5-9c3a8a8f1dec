import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]PasswordChange } from '@/components/customer-service-v12/[id]/password/change';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]PasswordChange', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]PasswordChange />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]PasswordChange')).toBeInTheDocument();
  });
});