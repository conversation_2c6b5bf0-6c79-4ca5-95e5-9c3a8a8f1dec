<?php

declare(strict_types=1);

namespace App\Services\Resilience;

use Illuminate\Support\Facades\Log;

/**
 * Retry Pattern Implementation
 * 
 * Provides configurable retry logic with exponential backoff
 * for handling transient failures
 */
class Retry
{
    private int $maxAttempts;
    private int $delay; // milliseconds
    private float $backoffMultiplier;
    private array $retryableExceptions;

    public function __construct(array $config = [])
    {
        $this->maxAttempts = $config['max_attempts'] ?? 3;
        $this->delay = $config['delay'] ?? 1000; // 1 second
        $this->backoffMultiplier = $config['backoff_multiplier'] ?? 2.0;
        $this->retryableExceptions = $config['retryable_exceptions'] ?? [];
    }

    /**
     * Execute callable with retry logic
     */
    public function execute(callable $callback)
    {
        $attempt = 1;
        $lastException = null;

        while ($attempt <= $this->maxAttempts) {
            try {
                return $callback();
            } catch (\Throwable $e) {
                $lastException = $e;

                if (!$this->shouldRetry($e, $attempt)) {
                    throw $e;
                }

                if ($attempt < $this->maxAttempts) {
                    $delayMs = $this->calculateDelay($attempt);
                    
                    Log::warning("Retry attempt failed, retrying", [
                        'attempt' => $attempt,
                        'max_attempts' => $this->maxAttempts,
                        'delay_ms' => $delayMs,
                        'exception' => $e->getMessage(),
                    ]);

                    $this->sleep($delayMs);
                }

                $attempt++;
            }
        }

        Log::error("All retry attempts exhausted", [
            'max_attempts' => $this->maxAttempts,
            'final_exception' => $lastException?->getMessage(),
        ]);

        throw $lastException;
    }

    /**
     * Check if exception should trigger retry
     */
    private function shouldRetry(\Throwable $exception, int $attempt): bool
    {
        if ($attempt >= $this->maxAttempts) {
            return false;
        }

        // If no specific exceptions configured, retry all
        if (empty($this->retryableExceptions)) {
            return true;
        }

        // Check if exception is in retryable list
        foreach ($this->retryableExceptions as $retryableClass) {
            if ($exception instanceof $retryableClass) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate delay for current attempt with exponential backoff
     */
    private function calculateDelay(int $attempt): int
    {
        return (int) ($this->delay * pow($this->backoffMultiplier, $attempt - 1));
    }

    /**
     * Sleep for specified milliseconds
     */
    private function sleep(int $milliseconds): void
    {
        usleep($milliseconds * 1000);
    }

    /**
     * Execute with custom retry configuration
     */
    public static function with(array $config): self
    {
        return new self($config);
    }

    /**
     * Execute with linear backoff (no exponential increase)
     */
    public static function linear(int $maxAttempts = 3, int $delay = 1000): self
    {
        return new self([
            'max_attempts' => $maxAttempts,
            'delay' => $delay,
            'backoff_multiplier' => 1.0,
        ]);
    }

    /**
     * Execute with exponential backoff
     */
    public static function exponential(int $maxAttempts = 3, int $initialDelay = 1000, float $multiplier = 2.0): self
    {
        return new self([
            'max_attempts' => $maxAttempts,
            'delay' => $initialDelay,
            'backoff_multiplier' => $multiplier,
        ]);
    }

    /**
     * Get retry configuration
     */
    public function getConfig(): array
    {
        return [
            'max_attempts' => $this->maxAttempts,
            'delay' => $this->delay,
            'backoff_multiplier' => $this->backoffMultiplier,
            'retryable_exceptions' => $this->retryableExceptions,
        ];
    }
}
