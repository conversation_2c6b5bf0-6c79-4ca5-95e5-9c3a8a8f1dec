<?php

/**
 * Test script to verify meal items from order_details table
 * Formatted as CSV string like "Indian Breakfast Main, Indian Breakfast Side, Seasonal Fruits"
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// Set up database connection
$capsule = new DB;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '*************',
    'database' => 'onefooddialer_2025',
    'username' => 'root',
    'password' => 'root',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "🧪 Testing Meal Items from order_details Table\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test 1: Check order_details table structure
echo "📋 Test 1: order_details Table Structure\n";
echo "-" . str_repeat("-", 50) . "\n";

try {
    $columns = DB::select("DESCRIBE order_details");
    echo "Table columns found:\n";
    foreach ($columns as $column) {
        echo "  - {$column->Field} ({$column->Type})\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "❌ Error checking table structure: " . $e->getMessage() . "\n\n";
}

// Test 2: Sample order_details data
echo "📊 Test 2: Sample order_details Data\n";
echo "-" . str_repeat("-", 50) . "\n";

try {
    $sampleData = DB::table('order_details')
        ->select('ref_order_no', 'order_date', 'product_name', 'quantity')
        ->orderBy('ref_order_no', 'desc')
        ->limit(10)
        ->get();

    if ($sampleData->count() > 0) {
        echo "Sample order_details records:\n";
        foreach ($sampleData as $record) {
            echo "  Order: {$record->ref_order_no}, Date: {$record->order_date}, Product: {$record->product_name}, Qty: {$record->quantity}\n";
        }
    } else {
        echo "No order_details records found.\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "❌ Error fetching sample data: " . $e->getMessage() . "\n\n";
}

// Test 3: Simulate getMealItemsFromOrderDetails function
echo "🍽️ Test 3: Meal Items CSV Generation\n";
echo "-" . str_repeat("-", 50) . "\n";

function getMealItemsFromOrderDetails($orderNo, $orderDate) {
    try {
        // Fetch meal items from order_details table
        $mealItems = DB::table('order_details')
            ->where('ref_order_no', $orderNo)
            ->where('order_date', $orderDate)
            ->orderBy('product_name')
            ->pluck('product_name')
            ->toArray();

        // Remove duplicates and create CSV string
        $uniqueMealItems = array_unique($mealItems);
        $csvMealItems = implode(', ', $uniqueMealItems);

        return [
            'csv' => $csvMealItems,
            'count' => count($uniqueMealItems),
            'items' => $uniqueMealItems
        ];

    } catch (Exception $e) {
        return [
            'csv' => 'Meal items not available',
            'count' => 0,
            'items' => [],
            'error' => $e->getMessage()
        ];
    }
}

// Test with sample orders
$testOrders = [
    ['order_no' => '8SPS250725', 'order_date' => '2025-07-25'],
    ['order_no' => 'XAJE250724', 'order_date' => '2025-07-24'],
    ['order_no' => 'TEST123', 'order_date' => '2025-07-25'] // Non-existent for testing
];

foreach ($testOrders as $testOrder) {
    echo "Testing Order: {$testOrder['order_no']}, Date: {$testOrder['order_date']}\n";
    
    $result = getMealItemsFromOrderDetails($testOrder['order_no'], $testOrder['order_date']);
    
    echo "  Meal Items CSV: \"{$result['csv']}\"\n";
    echo "  Items Count: {$result['count']}\n";
    
    if (!empty($result['items'])) {
        echo "  Individual Items:\n";
        foreach ($result['items'] as $item) {
            echo "    - {$item}\n";
        }
    }
    
    if (isset($result['error'])) {
        echo "  Error: {$result['error']}\n";
    }
    
    echo "\n";
}

// Test 4: Expected API Response Structure
echo "📡 Test 4: Expected API Response Structure\n";
echo "-" . str_repeat("-", 50) . "\n";

$expectedResponse = [
    'success' => true,
    'data' => [
        'orders' => [
            'upcoming' => [
                [
                    'order_id' => 12345,
                    'order_no' => '8SPS250725',
                    'order_date' => '2025-07-25',
                    'order_status' => 'Confirmed',
                    'total_amount' => '160.00',
                    'plan_type' => '10 day',
                    'meal_items' => 'Indian Breakfast Main, Indian Breakfast Side, Seasonal Fruits', // ✅ CSV format
                    'delivery_time' => '08:00:00',
                    'customer_address' => 'Student Address',
                    'location_name' => 'School Location'
                ]
            ]
        ]
    ]
];

echo "Expected Response Structure:\n";
echo json_encode($expectedResponse, JSON_PRETTY_PRINT) . "\n\n";

// Test 5: Database Query Verification
echo "🔍 Test 5: Database Query Verification\n";
echo "-" . str_repeat("-", 50) . "\n";

echo "SQL Query used:\n";
echo "SELECT product_name FROM order_details\n";
echo "WHERE ref_order_no = ? AND order_date = ?\n";
echo "ORDER BY product_name;\n\n";

echo "Expected meal item examples:\n";
echo "- Indian Breakfast Main\n";
echo "- Indian Breakfast Side\n";
echo "- Seasonal Fruits\n";
echo "- Premium Lunch Main\n";
echo "- Premium Lunch Side\n";
echo "- Fresh Juice\n\n";

echo "✅ Enhancement Summary:\n";
echo "-" . str_repeat("-", 50) . "\n";
echo "✅ meal_items: Now fetched from order_details table\n";
echo "✅ Format: CSV string (comma-separated)\n";
echo "✅ Deduplication: Removes duplicate product names\n";
echo "✅ Sorting: Alphabetical order by product_name\n";
echo "✅ Error handling: Fallback message for missing data\n";
echo "✅ Logging: Detailed audit trail\n\n";

echo "🎉 Meal Items CSV Enhancement Test Completed!\n";
