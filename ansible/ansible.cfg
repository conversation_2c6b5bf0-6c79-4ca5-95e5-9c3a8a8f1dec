[defaults]
inventory = inventory.yml
host_key_checking = False
roles_path = roles
remote_user = ec2-user
private_key_file = ~/.ssh/cubeonebiz.pem
retry_files_enabled = False
interpreter_python = auto_silent
callback_whitelist = profile_tasks
stdout_callback = yaml
bin_ansible_callbacks = True
timeout = 30

[ssh_connection]
pipelining = True
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null
control_path = /tmp/ansible-ssh-%%h-%%p-%%r
