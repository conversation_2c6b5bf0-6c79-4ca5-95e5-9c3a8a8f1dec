openapi: 3.0.3
info:
  title: Order Management API
  description: |
    Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies.

    ## Complete Order Journey (Updated with Temporary Tables & Wallet Integration)
    1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
    2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
    3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
    4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
    5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
    6. **Order Fulfillment** - All orders ready for delivery with status "New" (not "Confirmed")

    ## Mobile App Integration
    **Step 1:** POST /order-management/create → Get payment_service_transaction_id
    **Step 2:** Use Payment Service v12 APIs with transaction ID
    **Step 3:** Orders automatically created when payment succeeds with wallet locking

    ## Advanced Cancellation System
    ### Time-Based Refund Policies:
    1. **Before Cutoff Time** → 100% refund + unlock wallet amount
    2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
    3. **After 08:00:00** → No cancellation allowed

    ### Settings-Based Configuration:
    - All cutoff times and refund percentages configurable via database settings
    - Kitchen-specific and meal-specific policies
    - Cutoff day calculation (0=same day, 1=day before, etc.)

    ## Service Separation
    - **Order Management**: QuickServe Service (temp tables → actual orders)
    - **Payment Processing**: Payment Service v12 (unchanged)
    - **Wallet Management**: Integrated locking/unlocking system
    - **Database**: Proper transaction handling with temp tables and wallet tracking

  version: 2.0.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>
servers:
  - url: http://*************:8003/api/v2
    description: Development server
  - url: https://api.quickserve.com/v2
    description: Production server

# Configuration Settings for Cancellation Policies
x-cancellation-settings:
  description: |
    Database settings that control the time-based cancellation policies.
    All settings are stored in the `settings` table and can be modified without code changes.

  cutoff-times:
    K1_BREAKFAST_ORDER_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CUT_OFF_TIME: "00:01:00"
    K1_LUNCH_ORDER_CUT_OFF_DAY: "0"

  cancellation-cutoffs:
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_DAY: "0"

  refund-percentages:
    BREAKFAST_PARTIAL_REFUND_PERCENTAGE: "0"    # 0% refund for breakfast in partial window
    LUNCH_PARTIAL_REFUND_PERCENTAGE: "50"       # 50% refund for lunch in partial window

  time-windows:
    CANCELLATION_PARTIAL_REFUND_START_TIME: "00:01:00"
    CANCELLATION_PARTIAL_REFUND_END_TIME: "08:00:00"
    CANCELLATION_NO_REFUND_START_TIME: "08:00:01"

  feature-flags:
    ENABLE_TIME_BASED_CANCELLATION: "yes"
    ENABLE_WALLET_LOCKING: "yes"
    ENABLE_AUTOMATIC_REFUND: "yes"

paths:
  /order-management/create:
    post:
      tags:
        - Order Management
      summary: Create a new pre-order with subscription
      description: |
        Creates a pre-order in temporary tables and initiates payment with Payment Service v12.

        **What happens:**
        1. Creates record in `temp_pre_orders`
        2. Creates record in `temp_order_payment` (status: pending)
        3. Creates record in `payment_transaction` (status: initiated)
        4. Calls Payment Service v12 to initiate payment
        5. Returns payment URLs for mobile app

        **After payment success:**
        - Creates multiple orders (one per delivery day based on start_date and selected_days)
        - Creates order_details (fetches meal items from product_planner table)
        - Updates all payment tables
        - Locks wallet amounts for cancellation tracking

        **Order Placement Fixes (v2.0):**
        - ✅ order_status = "New" (not "Confirmed")
        - ✅ payment_mode = "online" (not gateway name like "razorpay")
        - ✅ temp_pre_order.due_date = NULL (not current date)
        - ✅ temp_pre_order.city_name = actual city name from database (not city ID)
        - ✅ temp_pre_order.food_preference = "[]" (not "veg")
        - ✅ temp_pre_order.delivery_time = NULL, delivery_end_time = NULL
        - ✅ temp_pre_order.order_days = CSV format (not JSON array)
        - ✅ temp_order_payment = separate records for each meal (not clubbed)
        - ✅ Wallet locking during order creation for cancellation tracking

        **New Features:**
        - Uses start_date (YYYY-MM-DD) instead of current date
        - Selected_days array (0=Sunday, 6=Saturday) for specific day selection
        - Fetches meal items from product_planner table automatically
        - Prevents duplicate orders for same day/meal
        - Implements cut-off restrictions based on K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME settings
        - Supports multiple meals in cart via meals array
        - Fetches customer details (name, email, phone) from customer table
        - Fetches product details (name, description) from products table
        - City name database lookup with fallbacks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
            example:
              customer_id: 3800
              customer_address: "123 Test Street, Test Area, Test City - 400001"
              location_code: 1001
              location_name: "Test Location"
              city: 1
              city_name: "Mumbai"
              meals:
                - product_code: 342
                  quantity: 1
                - product_code: 343
                  quantity: 1
              start_date: "2025-01-27"
              selected_days: [1, 2, 3, 4, 5]
              delivery_time: "08:00:00"
              delivery_end_time: "09:00:00"
              food_preference: "veg"
              subscription_days: 15
      responses:
        '201':
          description: Pre-order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePreOrderResponse'
              example:
                success: true
                message: "Pre-order created successfully"
                data:
                  temp_pre_order_id: 46154
                  order_no: "ORD202507221240574531"
                  customer_id: 3800
                  amount: 200
                  status: "pending"
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  subscription_days: 15
                  calculated_delivery_dates: ["2025-01-27", "2025-01-28", "2025-01-29", "2025-01-30", "2025-01-31"]
                  payment_transaction_id: 30609
                  payment_service_transaction_id: "TXN30610"
                  payment_urls:
                    process_payment: "http://*************:8002/api/v2/payments/TXN30610/process"
                    payment_status: "http://*************:8002/api/v2/payments/TXN30610"
                    order_status: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/details/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get order details
      description: Retrieves complete order information including meal items and payment status
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Order details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailsResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/customer/{customerId}:
    get:
      tags:
        - Order Management
      summary: Get customer orders with cancellation status
      description: |
        Retrieves paginated list of orders for a customer with real-time cancellation eligibility.

        **New Features:**
        - ✅ `is_cancellable` flag for each order based on time-based policies
        - ✅ `cancellation_policy` object with refund percentage and policy details
        - ✅ Real-time calculation based on current time and settings
        - ✅ Meal-specific cancellation policies (breakfast vs lunch)

        **Cancellation Policies:**
        - **Before Cutoff**: 100% refund allowed
        - **Partial Window (00:01-08:00)**: Breakfast 0%, Lunch 50%
        - **After 08:00**: No cancellation allowed
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
          example: 3800
        - name: per_page
          in: query
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: status
          in: query
          schema:
            type: string
            enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
      responses:
        '200':
          description: Customer orders retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrdersResponse'

  /order-management/cancellation-settings:
    get:
      tags:
        - Order Management
      summary: Get cancellation policy settings
      description: |
        Retrieves current cancellation policy settings from database.

        **Returns:**
        - Cutoff times for each meal type
        - Refund percentages for partial refund window
        - Time windows for different policies
        - Feature flags for cancellation system
      responses:
        '200':
          description: Cancellation settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancellationSettingsResponse'

  /order-management/pre-order-status/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get pre-order status and progress
      description: |
        Retrieves complete pre-order status including:
        - Temp pre-order details
        - Payment status (temp_order_payment + payment_transaction)
        - Created orders count (after payment success)
        - Order details count (meal items created)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507221240574531"
      responses:
        '200':
          description: Pre-order status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreOrderStatusResponse'
              example:
                success: true
                data:
                  pre_order:
                    temp_pre_order_id: 46154
                    order_no: "ORD202507221240574531"
                    customer_id: 3800
                    customer_name: "Customer User"
                    product_name: "International Breakfast Subscription"
                    amount: "200.00"
                    order_status: "New"
                    days_preference: "1,2,3,4,5"
                    order_days: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                    created_date: "2025-07-22 18:10:57"
                  payment_status:
                    temp_payment_status: "success"
                    transaction_status: "completed"
                    gateway: "razorpay"
                    gateway_transaction_id: "TXN30610"
                  orders_created:
                    count: 12
                    order_details_count: 36
                    status: "completed"
        '404':
          description: Pre-order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/status/{orderNo}:
    get:
      tags:
        - Payment Management
      summary: Get payment status
      description: Retrieves current payment status for an order
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentStatusResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/webhook:
    post:
      tags:
        - Payment Processing
      summary: Payment gateway webhook
      description: |
        Handles payment confirmation from payment gateways.
        This endpoint automatically updates order status when payment is confirmed.
        **No additional API calls needed after payment confirmation.**
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentWebhookRequest'
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'
        '401':
          description: Invalid signature
        '500':
          description: Webhook processing failed

  /payment/callback:
    post:
      tags:
        - Payment Processing
      summary: Payment callback handler
      description: Handles payment gateway callbacks (redirects)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentCallbackRequest'
      responses:
        '200':
          description: Callback processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'

  /payment/simulate/success:
    post:
      tags:
        - Testing
      summary: Simulate payment success
      description: Testing endpoint to simulate successful payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /payment/simulate/failure:
    post:
      tags:
        - Testing
      summary: Simulate payment failure
      description: Testing endpoint to simulate payment failure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment failure simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /order-management/payment-success/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment success callback
      description: |
        Called by Payment Service v12 when payment is successful.
        Automatically updates order status to "Confirmed" and creates recurring orders.
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                amount:
                  type: number
                  example: 125.00
      responses:
        '200':
          description: Payment success processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

  /order-management/payment-failure/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment failure callback
      description: |
        Called by Payment Service v12 when payment fails.
        Updates order status to "Payment Failed".
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                failure_reason:
                  type: string
                  example: "Insufficient funds"
      responses:
        '200':
          description: Payment failure processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

  /order-management/cancel/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Cancel order with time-based refund processing
      description: |
        Advanced order cancellation with time-based refund policies and wallet management.

        **Time-Based Refund Policies:**
        1. **Before Cutoff Time** → 100% refund + unlock wallet amount
        2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
        3. **After 08:00:00** → No cancellation allowed

        **Enhanced Features:**
        - ✅ Time-based refund calculation based on meal type
        - ✅ Automatic wallet unlocking for locked amounts
        - ✅ Settings-driven cancellation policies
        - ✅ Meal-specific filtering (breakfast, lunch, dinner)
        - ✅ Detailed refund breakdown in response
        - ✅ Only cancels current or future date orders
        - ✅ Prevents cancellation of prepared/dispatched orders

        **Wallet Integration:**
        - Unlocks previously locked wallet amounts
        - Credits refund amount to customer wallet
        - Maintains detailed transaction history
        - ✅ Credits refund to customer wallet automatically
        - ✅ Updates order status to 'Cancelled'
        - ✅ Updates order_details and kitchen_data
        - ✅ Supports partial cancellation (specific dates)

        **Validation Rules:**
        - Orders must be in 'New', 'Confirmed', or 'Processing' status
        - Order date must be today or future date
        - Cannot cancel 'Prepared', 'Dispatched', or 'Delivered' orders

        **Refund Process:**
        1. Calculates total refund: `amount + tax + delivery_charges + service_charges - applied_discount`
        2. Credits amount to customer_wallet with `amount_type = 'cr'`
        3. Creates wallet transaction record with refund details
        4. Updates all related tables (orders, order_details, kitchen_data)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to cancel
          example: "XAJE250724"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderRequest'
      responses:
        '200':
          description: Order cancelled successfully with refund processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderResponse'
        '400':
          description: Invalid request - orders cannot be cancelled (already prepared/delivered)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Cannot cancel orders that are already prepared, dispatched, or delivered."
        '404':
          description: No eligible orders found for cancellation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "No eligible orders found for cancellation. Orders can only be cancelled for current or future dates."
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Cancellation processing failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - customer_address
        - location_code
        - location_name
        - city
        - city_name
        - meals
        - start_date
        - selected_days
        - subscription_days
      properties:
        customer_id:
          type: integer
          description: Customer ID (details fetched from customer table)
        customer_address:
          type: string
          maxLength: 250
          description: Delivery address for this order
        location_code:
          type: integer
        location_name:
          type: string
          maxLength: 45
        city:
          type: integer
        city_name:
          type: string
          maxLength: 45
        meals:
          type: array
          items:
            $ref: '#/components/schemas/MealCartItem'
          description: Array of meals in cart
        start_date:
          type: string
          format: date
          description: "Start date for subscription in YYYY-MM-DD format"
          example: "2025-01-27"
        selected_days:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          description: "Array of selected day numbers (0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday)"
          example: [1, 2, 3, 4, 5]
        delivery_time:
          type: string
          format: time
          example: "12:30:00"
        delivery_end_time:
          type: string
          format: time
          example: "13:30:00"
        food_preference:
          type: string
          maxLength: 255
          example: "veg"
        subscription_days:
          type: integer
          minimum: 1
          maximum: 30
          description: "Number of days for subscription (e.g., 15 days)"
          example: 15

    MealCartItem:
      type: object
      required:
        - product_code
        - quantity
      properties:
        product_code:
          type: integer
          description: Product code (details fetched from products table)
        quantity:
          type: integer
          minimum: 1
          description: Quantity of this meal

    CreatePreOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Pre-order created successfully"
        data:
          type: object
          properties:
            temp_pre_order_id:
              type: integer
              example: 46154
              description: "Temporary pre-order ID for tracking"
            order_no:
              type: string
              example: "ORD202507221240574531"
              description: "Unique order number"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 200
            status:
              type: string
              example: "pending"
              description: "Pre-order status (pending until payment)"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            subscription_days:
              type: integer
              example: 15
              description: "Total subscription duration in days"
            payment_transaction_id:
              type: integer
              example: 30609
              description: "Local payment transaction ID"
            payment_service_transaction_id:
              type: string
              example: "TXN30610"
              description: "Payment Service v12 transaction ID"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
                  description: "URL to check pre-order status"

    CreateOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order created successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              example: 127616
            order_no:
              type: string
              example: "ORD202507220800028674"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 125
            status:
              type: string
              example: "New"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            local_transaction_id:
              type: integer
              example: 30604
              description: "Local transaction ID in QuickServe service"
            payment_service_transaction_id:
              type: integer
              example: 15234
              description: "Transaction ID from Payment Service v12"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://localhost:8003/api/v2/order-management/details/ORD202507220800028674"
                  description: "URL to check order status in QuickServe"

    OrderDetailsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order:
              $ref: '#/components/schemas/OrderDetails'
            meal_items:
              type: array
              items:
                $ref: '#/components/schemas/MealItemDetails'
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    OrderDetails:
      type: object
      properties:
        order_id:
          type: integer
        order_no:
          type: string
        customer_id:
          type: integer
        customer_name:
          type: string
        customer_email:
          type: string
        customer_phone:
          type: string
        customer_address:
          type: string
        product_name:
          type: string
        product_type:
          type: string
        product_code:
          type: integer
          description: "Product code from orders table"
          example: 336
        quantity:
          type: integer
          description: "Quantity of the product ordered"
          example: 1
        item_amount:
          type: string
          description: "Amount for this specific item"
          example: "75.00"
        quantity:
          type: integer
        amount:
          type: string
        order_status:
          type: string
          enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        delivery_status:
          type: string
        order_date:
          type: string
          format: date-time
        delivery_time:
          type: string
          format: time
        delivery_end_time:
          type: string
          format: time
        recurring_status:
          type: string

    MealItemDetails:
      type: object
      properties:
        product_code:
          type: integer
        product_name:
          type: string
        quantity:
          type: integer
        amount:
          type: string

    PaymentTransactionDetails:
      type: object
      properties:
        transaction_id:
          type: integer
        gateway_transaction_id:
          type: string
          nullable: true
        status:
          type: string
          enum: [pending, completed, failed]
        gateway:
          type: string
        payment_amount:
          type: string
        created_date:
          type: string
          format: date-time

    CustomerOrdersResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderSummary'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    OrderSummary:
      type: object
      properties:
        pk_order_no:
          type: integer
        order_no:
          type: string
        customer_code:
          type: integer
        customer_name:
          type: string
        product_name:
          type: string
        amount:
          type: string
        order_status:
          type: string
        order_date:
          type: string
          format: date-time
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        recurring_status:
          type: string
        is_cancellable:
          type: boolean
          description: "Whether this order can be cancelled based on current time and policies"
          example: true
        cancellation_policy:
          type: object
          description: "Cancellation policy details for this order"
          properties:
            refund_percentage:
              type: integer
              description: "Percentage of refund if cancelled now"
              example: 50
            policy_type:
              type: string
              enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
              description: "Type of cancellation policy currently applicable"
              example: "partial_refund_window"
            cutoff_time:
              type: string
              format: time
              description: "Cutoff time for this meal type"
              example: "00:01:00"
            cutoff_day:
              type: integer
              description: "Days before delivery when cutoff applies (0=same day, 1=day before)"
              example: 0

    PaginationInfo:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        last_page:
          type: integer

    PaymentStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order_no:
              type: string
            order_status:
              type: string
            amount:
              type: string
            amount_paid:
              type: string
            payment_mode:
              type: string
              nullable: true
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    PaymentWebhookRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
          enum: [completed, success, failed, failure, pending]
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string
        gateway_transaction_id:
          type: string

    PaymentCallbackRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string

    WebhookResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string

    PaymentSimulationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string
            amount_paid:
              type: number
            reason:
              type: string
              description: "Present only for failure scenarios"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error description"

    PaymentCallbackResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Payment success processed"
        data:
          type: object
          properties:
            order_no:
              type: string
              example: "ORD202507220800028674"
            order_status:
              type: string
              example: "Confirmed"
            payment_status:
              type: string
              example: "completed"
            failure_reason:
              type: string
              description: "Present only for failure callbacks"
              example: "Insufficient funds"

    PreOrderStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            pre_order:
              type: object
              properties:
                temp_pre_order_id:
                  type: integer
                  example: 46154
                order_no:
                  type: string
                  example: "ORD202507221240574531"
                customer_id:
                  type: integer
                  example: 3800
                customer_name:
                  type: string
                  example: "Customer User"
                product_name:
                  type: string
                  example: "International Breakfast Subscription"
                amount:
                  type: string
                  example: "200.00"
                order_status:
                  type: string
                  example: "New"
                days_preference:
                  type: string
                  example: "1,2,3,4,5"
                order_days:
                  type: array
                  items:
                    type: string
                    format: date
                  example: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                  description: "Calculated delivery dates based on days_preference and subscription_days"
                created_date:
                  type: string
                  format: date-time
                  example: "2025-07-22 18:10:57"
            payment_status:
              type: object
              properties:
                temp_payment_status:
                  type: string
                  enum: [pending, success, failed]
                  example: "success"
                  description: "Status from temp_order_payment table"
                transaction_status:
                  type: string
                  enum: [initiated, completed, failed]
                  example: "completed"
                  description: "Status from payment_transaction table"
                gateway:
                  type: string
                  example: "razorpay"
                gateway_transaction_id:
                  type: string
                  example: "TXN30610"
            orders_created:
              type: object
              properties:
                count:
                  type: integer
                  example: 12
                  description: "Number of actual orders created (one per delivery day)"
                order_details_count:
                  type: integer
                  example: 36
                  description: "Number of order_details records created (orders × meal_items)"
                status:
                  type: string
                  enum: [pending, completed]
                  example: "completed"
                  description: "Whether actual orders have been created"

    CancelOrderRequest:
      type: object
      required:
        - reason
      properties:
        reason:
          type: string
          maxLength: 500
          description: "Reason for order cancellation"
          example: "Customer requested cancellation due to change in plans"
        cancel_dates:
          type: array
          items:
            type: string
            format: date
          description: "Optional: Specific dates to cancel. If not provided, all future orders will be cancelled"
          example: ["2025-08-18", "2025-08-20"]
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional: Filter cancellation by specific meal type"
          example: "lunch"

    CancelOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Orders cancelled successfully with time-based refund policy"
        data:
          type: object
          properties:
            cancelled_orders:
              type: integer
              description: "Number of orders cancelled"
              example: 3
            cancelled_order_ids:
              type: array
              items:
                type: integer
              description: "List of cancelled order IDs"
              example: [127733, 127734, 127735]
            total_refund_amount:
              type: number
              format: decimal
              description: "Total refund amount credited to wallet (after applying time-based policies)"
              example: 125.50
            wallet_credited:
              type: boolean
              description: "Whether refund was successfully credited to wallet"
              example: true
            wallet_unlocked:
              type: number
              format: decimal
              description: "Total wallet amount unlocked from locked state"
              example: 200.00
            customer_code:
              type: integer
              description: "Customer code for the cancelled orders"
              example: 1
            order_no:
              type: string
              description: "Order number that was cancelled"
              example: "XAJE250724"
            refund_breakdown:
              type: array
              description: "Detailed breakdown of refund calculation for each cancelled order"
              items:
                type: object
                properties:
                  order_id:
                    type: integer
                    example: 127733
                  order_date:
                    type: string
                    format: date
                    example: "2025-08-06"
                  meal_type:
                    type: string
                    example: "lunch"
                  base_amount:
                    type: number
                    format: decimal
                    example: 125.00
                  refund_percentage:
                    type: integer
                    example: 50
                  refund_amount:
                    type: number
                    format: decimal
                    example: 62.50
                  wallet_unlocked:
                    type: number
                    format: decimal
                    example: 125.00
                  policy_type:
                    type: string
                    enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
                    example: "partial_refund_window"
                  cutoff_time:
                    type: string
                    format: time
                    example: "00:01:00"

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            reason: ["The reason field is required."]
            cancel_dates.0: ["The cancel_dates.0 must be a date after or equal to today."]

    CancellationSettingsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            cutoff_times:
              type: object
              properties:
                breakfast:
                  type: object
                  properties:
                    cutoff_time:
                      type: string
                      format: time
                      example: "00:01:00"
                    cutoff_day:
                      type: integer
                      example: 0
                lunch:
                  type: object
                  properties:
                    cutoff_time:
                      type: string
                      format: time
                      example: "00:01:00"
                    cutoff_day:
                      type: integer
                      example: 0
            refund_percentages:
              type: object
              properties:
                breakfast_partial_refund:
                  type: integer
                  example: 0
                lunch_partial_refund:
                  type: integer
                  example: 50
            time_windows:
              type: object
              properties:
                partial_refund_start:
                  type: string
                  format: time
                  example: "00:01:00"
                partial_refund_end:
                  type: string
                  format: time
                  example: "08:00:00"
                no_refund_start:
                  type: string
                  format: time
                  example: "08:00:01"
            feature_flags:
              type: object
              properties:
                time_based_cancellation_enabled:
                  type: boolean
                  example: true
                wallet_locking_enabled:
                  type: boolean
                  example: true
                automatic_refund_enabled:
                  type: boolean
                  example: true

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authenticated endpoints"

tags:
  - name: Order Management
    description: "Core order management operations"
  - name: Payment Management
    description: "Payment status and tracking"
  - name: Payment Processing
    description: "Payment gateway integration"
  - name: Payment Callbacks
    description: "Payment service callback handlers"
  - name: Testing
    description: "Testing and simulation endpoints"
