<?php

namespace App\Events\Customer;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Customer Deleted Event
 * 
 * This event is fired when a customer is deleted.
 */
class CustomerDeleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var int
     */
    public $customerId;

    /**
     * Create a new event instance.
     *
     * @param int $customerId
     * @return void
     */
    public function __construct(int $customerId)
    {
        $this->customerId = $customerId;
    }
}
