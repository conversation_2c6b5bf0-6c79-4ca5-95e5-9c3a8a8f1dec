import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueKitchen[id] } from '@/components/catalogue-service-v12/kitchen/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueKitchen[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueKitchen[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueKitchen[id]')).toBeInTheDocument();
  });
});