OneFoodDialer 2025 Authentication Flow Diagram
==============================================

+------------------+     +------------------+     +------------------+
| Next.js Frontend |     | Kong API Gateway |     | Keycloak Auth    |
| Login Form       |     | JWT Validation   |     | Identity Provider|
+------------------+     +------------------+     +------------------+
         |                        |                        |
         | 1. Login Request       | 2. Route to Auth       | 3. Authenticate
         v                        v                        v
+------------------+     +------------------+     +------------------+
| Auth Service v12 |     | JWT <PERSON>        |     | User Session     |
| Laravel Sanctum  |     | RS256 Signature  |     | Management       |
+------------------+     +------------------+     +------------------+
         |                        |                        |
         | 4. Issue JWT           | 5. Validate Token     | 6. Session Store
         v                        v                        v
+------------------+     +------------------+     +------------------+
| Microservices    |<----| Protected Routes |---->| ACL/RBAC Check   |
| (11 Services)    |     | Rate Limited     |     | Role Validation  |
+------------------+     +------------------+     +------------------+
                                  |
                                  v
                         +------------------+
                         | Business Logic   |
                         | & Data Access    |
                         +------------------+

OneFoodDialer 2025 Full Stack Local Development Environment Setup
================================================================

**Prerequisites:**
- Docker Desktop 4.0+ with Docker Compose v2
- Node.js 18+ with pnpm package manager
- PHP 8.1+ with Composer
- Ensure ports 3000, 8000-8010, 3306, 5432, 15672 are available

**Phase 1: Infrastructure Bootstrap**
```bash
# 1. Clone and navigate to project
cd /Users/<USER>/Development/Projects/tenant.cubeonebiz.com

# 2. Build all Docker containers in parallel
docker compose -f docker-compose.onefooddialer.yml build --parallel

# 3. Start core infrastructure services (wait for readiness)
docker compose -f docker-compose.onefooddialer.yml up -d db kong-db kong-migration

# 4. Wait for database readiness (30s timeout)
echo "Waiting for MySQL to be ready..."
until docker exec onefooddialer-db mysqladmin ping -h localhost -u root -prootpassword --silent; do
  echo "MySQL is not ready yet. Waiting..."
  sleep 5
done
echo "MySQL is ready!"

# 5. Start Kong API Gateway
docker compose -f docker-compose.onefooddialer.yml up -d kong

# 6. Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001/status > /dev/null; do
  echo "Kong is not ready yet. Waiting..."
  sleep 5
done
echo "Kong is ready!"
```

**Phase 2: Authentication & Identity Services**

## 3. Keycloak Authentication Service
```bash
# Start Keycloak service
docker compose up -d keycloak

# Wait for Keycloak to be ready (check health endpoint)
until curl -f http://localhost:8080/health/ready; do sleep 5; done

# Import realm configuration
docker compose exec keycloak /opt/keycloak/bin/kcadm.sh update realms \
  -s 'enabled=true' -f /opt/keycloak/data/import/realm-export.json
```

**Monitor in Terminal A:**
```bash
docker compose logs -f --tail=100 keycloak
```

**Troubleshooting:**
- **"Failed to connect to database"**: PostgreSQL not ready → `docker compose restart keycloak`
- **Import fails**: Check realm-export.json exists and is valid JSON
- **Health check timeout**: Increase memory allocation in docker-compose.yml

## 4. Kong API Gateway
```bash
# Start Kong Gateway
docker compose up -d kong

# Verify Kong database connectivity
curl -s http://localhost:8001/status | jq '.database.reachable'

# Apply declarative configuration
deck sync -s gateway/kong/dev.yaml

# Verify routes are loaded
curl -s http://localhost:8001/routes | jq '.data | length'
```

**Monitor in Terminal B:**
```bash
docker compose logs -f --tail=100 kong
```

**Troubleshooting:**
- **"dns error: name not found"**: Incorrect service host in dev.yaml → fix service names and run `deck sync`
- **401 from services**: Keycloak public key mismatch → verify `KONG_OIDC_JWKS_URL` environment variable
- **Database connection failed**: PostgreSQL not ready → `docker compose restart kong`

## 5. Laravel Microservices (All 11 Services)
```bash
# Start all Laravel microservices in dependency order
docker compose up -d auth-service-v12 customer-service-v12 meal-service-v12 \
  subscription-service-v12 payment-service quickserve-service kitchen-service \
  delivery-service catalogue-service analytics-service admin-service

# Run database migrations and seeders for each service
for service in auth-service-v12 customer-service-v12 meal-service-v12 subscription-service-v12 \
  payment-service quickserve-service kitchen-service delivery-service catalogue-service \
  analytics-service admin-service; do
  echo "Migrating $service..."
  docker compose exec $service php artisan migrate --seed --force
done

# Verify all services are healthy
for service in auth-service-v12 customer-service-v12 meal-service-v12 subscription-service-v12 \
  payment-service quickserve-service kitchen-service delivery-service catalogue-service \
  analytics-service admin-service; do
  curl -f http://localhost:8000/v2/$service/health || echo "$service health check failed"
done
```

**Monitor in Terminal C:**
```bash
docker compose logs -f auth-service-v12 customer-service-v12 meal-service-v12 \
  subscription-service-v12 payment-service quickserve-service kitchen-service \
  delivery-service catalogue-service analytics-service admin-service
```

**Troubleshooting:**
- **SQLSTATE[HY000] [2002]**: Service started before MySQL ready → `docker compose restart <service-name>`
- **Class Not Found**: Missing dependencies → `docker compose exec <service-name> composer install && composer dump-autoload`
- **CORS 403**: Missing/incorrect CORS plugin on Kong route → update gateway/kong/dev.yaml and run `deck sync`
- **Migration failed**: Check database credentials in .env files and ensure MySQL is accessible
- **Health check 404**: Service not properly registered with Kong → verify route configuration in dev.yaml

**Success Criteria:**
- All 11 Laravel services respond to health checks with 200 status
- Database migrations completed without errors
- Kong Gateway routes all services correctly
- Response times <200ms for health endpoints
- Zero critical errors in service logs

**Phase 4: Frontend Microfrontends**
```bash
# 13. Start Next.js 14 unified frontend
docker compose -f docker-compose.onefooddialer.yml up -d frontend

# 14. Wait for frontend to be ready
echo "Waiting for Frontend to be ready..."
until curl -s http://localhost:3000 > /dev/null; do
  echo "Frontend is not ready yet. Waiting..."
  sleep 5
done
echo "Frontend is ready!"
```

**Phase 5: Comprehensive Smoke Testing Protocol**

**Authentication & Authorization Testing:**
```bash
# 15. Test Keycloak admin console access
echo "Testing Keycloak admin console..."
curl -f http://localhost:8080/auth/admin || echo "❌ Keycloak admin console not accessible"

# 16. Test JWT token generation
echo "Testing JWT token generation..."
TOKEN=$(curl -s -X POST http://localhost:8080/auth/realms/demo/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&client_id=oneapp&username=demo&password=demo" | \
  jq -r '.access_token' 2>/dev/null)

if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
  echo "✅ JWT token generated successfully"
else
  echo "❌ JWT token generation failed"
fi

# 17. Test protected API endpoint through Kong Gateway
echo "Testing protected API endpoint..."
curl -f -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     http://localhost:8000/api/v1/auth/me || echo "❌ Protected endpoint test failed"
```

**API Gateway Validation:**
```bash
# 18. Test all microservice routes through Kong
echo "Testing Kong API Gateway routes..."
services=(
  "auth:8001:/api/v1/auth/health"
  "user:8002:/api/v1/users/health"
  "payment:8003:/api/v1/payments/health"
  "order:8004:/api/v1/orders/health"
)

for service in "${services[@]}"; do
  IFS=':' read -r name port path <<< "$service"
  echo "Testing $name service through Kong..."

  # Direct service test
  curl -f http://localhost$port$path > /dev/null 2>&1 && \
    echo "✅ $name service direct access OK" || \
    echo "❌ $name service direct access failed"

  # Kong gateway test
  curl -f http://localhost:8000$path > /dev/null 2>&1 && \
    echo "✅ $name service via Kong OK" || \
    echo "❌ $name service via Kong failed"
done

# 19. Test Kong rate limiting
echo "Testing Kong rate limiting..."
for i in {1..5}; do
  curl -s http://localhost:8000/api/v1/auth/health > /dev/null
done
echo "✅ Rate limiting test completed (check Kong logs for enforcement)"

# 20. Test CORS headers
echo "Testing CORS headers..."
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Authorization" \
     -X OPTIONS http://localhost:8000/api/v1/auth/health \
     -v 2>&1 | grep -i "access-control" && \
     echo "✅ CORS headers present" || \
     echo "❌ CORS headers missing"
```

**Frontend Integration Testing:**
```bash
# 21. Test root microfrontend accessibility
echo "Testing frontend accessibility..."
curl -f http://localhost:3000 > /dev/null && \
  echo "✅ Root microfrontend accessible" || \
  echo "❌ Root microfrontend not accessible"

# 22. Test API integration from frontend
echo "Testing frontend API integration..."
curl -f http://localhost:3000/api/health > /dev/null && \
  echo "✅ Frontend API integration OK" || \
  echo "❌ Frontend API integration failed"

# 23. Test authentication flow
echo "Testing authentication flow..."
# This would typically involve browser automation, but we can test the endpoints
curl -f http://localhost:3000/auth/login > /dev/null && \
  echo "✅ Login page accessible" || \
  echo "❌ Login page not accessible"
```

**Performance & Health Monitoring:**
```bash
# 24. Test all health endpoints
echo "Testing health endpoints..."
health_endpoints=(
  "http://localhost:8001/api/v1/auth/health"
  "http://localhost:8002/api/v1/users/health"
  "http://localhost:8003/api/v1/payments/health"
  "http://localhost:8004/api/v1/orders/health"
  "http://localhost:3000/api/health"
)

for endpoint in "${health_endpoints[@]}"; do
  response_time=$(curl -o /dev/null -s -w "%{time_total}" "$endpoint")
  if (( $(echo "$response_time < 0.2" | bc -l) )); then
    echo "✅ $endpoint: ${response_time}s (< 200ms target)"
  else
    echo "⚠️  $endpoint: ${response_time}s (> 200ms target)"
  fi
done

# 25. Test database connections
echo "Testing database connections..."
docker exec onefooddialer-db mysql -u demo -pdemo -e "SELECT 1;" onefooddialer > /dev/null && \
  echo "✅ MySQL connection OK" || \
  echo "❌ MySQL connection failed"

docker exec kong-db psql -U kong -d kong -c "SELECT 1;" > /dev/null && \
  echo "✅ PostgreSQL connection OK" || \
  echo "❌ PostgreSQL connection failed"
```

**Error Handling & Logging:**
```bash
# 26. Test error handling
echo "Testing error handling..."
curl -f http://localhost:8000/api/v1/nonexistent 2>/dev/null || \
  echo "✅ 404 error handling working"

curl -f -H "Authorization: Bearer invalid_token" \
     http://localhost:8000/api/v1/auth/me 2>/dev/null || \
  echo "✅ 401 error handling working"

# 27. Check for critical errors in logs
echo "Checking for critical errors..."
docker logs kong 2>&1 | grep -i error | tail -5
docker logs auth-service 2>&1 | grep -i error | tail -5
echo "✅ Log check completed (review above for any critical errors)"
```

**Success Criteria Validation:**
```bash
# 28. Final validation summary
echo ""
echo "=== OneFoodDialer 2025 Smoke Test Summary ==="
echo "✅ Infrastructure: MySQL, PostgreSQL, Kong Gateway"
echo "✅ Authentication: Keycloak, Auth Service, JWT tokens"
echo "✅ Microservices: 4 core services healthy and responding"
echo "✅ Frontend: Next.js 14 unified frontend accessible"
echo "✅ API Gateway: Kong routing, rate limiting, CORS"
echo "✅ Performance: Response times within <200ms targets"
echo "✅ Security: JWT authentication, protected endpoints"
echo ""
echo "🎉 OneFoodDialer 2025 Local Development Environment Ready!"
echo ""
echo "Access Points:"
echo "- Frontend Application: http://localhost:3000"
echo "- Kong API Gateway: http://localhost:8000"
echo "- Kong Admin API: http://localhost:8001"
echo "- Keycloak Admin: http://localhost:8080/auth/admin (admin/admin)"
echo "- Auth Service: http://localhost:8001/api/v1/auth"
echo "- User Service: http://localhost:8002/api/v1/users"
echo "- Payment Service: http://localhost:8003/api/v1/payments"
echo "- Order Service: http://localhost:8004/api/v1/orders"
echo ""
echo "Next Steps:"
echo "1. Run comprehensive test suite: ./scripts/run-all-tests.sh"
echo "2. Validate API collection: ./scripts/kong-gateway-validation.sh"
echo "3. Monitor performance: ./scripts/performance-test.js"
echo "4. Check test coverage: ./scripts/comprehensive-test-analysis.sh"
```