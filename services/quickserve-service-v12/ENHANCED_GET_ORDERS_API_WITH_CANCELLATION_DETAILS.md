# 📋 ENHANCED GET ORDERS API WITH CANCELLATION DETAILS

## 🎯 **Overview**

The Get Orders API has been enhanced to include comprehensive cancellation details for cancelled orders, providing information about who cancelled the order, when it was cancelled, and refund details.

---

## ✅ **What's Been Enhanced**

### **1. Database Query Enhancement**
- Added `cancelled_by` field to track who initiated the cancellation
- Added `cancelled_on` field to track when the order was cancelled
- Added `cancellation_reason` from the remark field
- Added `order_created_on` timestamp
- Added `meal_type` information

### **2. Cancellation Details Structure**
For cancelled orders, the API now returns:
```json
{
  "cancellation_details": {
    "cancelled_by": "customer",
    "cancelled_on": "2025-07-28T10:30:00Z",
    "cancellation_reason": "Customer requested cancellation due to change in plans",
    "refund_amount": 75.00,
    "refund_percentage": 50,
    "refund_policy_applied": "partial_refund_window"
  }
}
```

### **3. Order Status Updates**
Both cancellation methods now properly track:
- `cancelled_by`: Who initiated the cancellation (customer/admin/system)
- `cancelled_on`: Timestamp when cancellation occurred
- Enhanced `remark` field with detailed cancellation information

---

## 🚀 **API Endpoint**

```
GET /api/v2/order-management/customer/{customerCode}
```

### **Query Parameters**
- `include_cancelled` (boolean, default: true) - Include cancelled orders
- `order_status` (string) - Filter by specific order status
- `student_name_filter` (string) - Filter by student name

---

## 📋 **Enhanced Response Format**

### **Sample Response with Cancellation Details**

```json
{
  "success": true,
  "data": {
    "customer": {
      "customer_id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "9876543210"
    },
    "summary": {
      "total_orders": 25,
      "upcoming_orders": 15,
      "cancelled_orders": 5,
      "other_orders": 5
    },
    "orders": {
      "upcoming": [
        {
          "order_id": 127810,
          "order_no": "QA93250725",
          "order_date": "2025-09-03",
          "order_status": "New",
          "delivery_status": "Pending",
          "meal_type": "breakfast",
          "product_name": "Poha",
          "total_amount": 150.00,
          "order_created_on": "2025-07-25T10:30:00Z",
          "cancellation_details": null
        }
      ],
      "cancelled": [
        {
          "order_id": 127811,
          "order_no": "QA93250726",
          "order_date": "2025-09-02",
          "order_status": "Cancelled",
          "delivery_status": "Cancelled",
          "meal_type": "lunch",
          "product_name": "Biryani",
          "total_amount": 200.00,
          "order_created_on": "2025-07-25T09:15:00Z",
          "cancellation_details": {
            "cancelled_by": "customer",
            "cancelled_on": "2025-07-28T10:30:00Z",
            "cancellation_reason": "Customer requested cancellation due to change in plans | Refund: 50% | Policy: partial_refund_window",
            "refund_amount": 100.00,
            "refund_percentage": 50,
            "refund_policy_applied": "partial_refund_window"
          }
        }
      ],
      "other": [
        {
          "order_id": 127812,
          "order_no": "QA93250727",
          "order_date": "2025-07-20",
          "order_status": "Delivered",
          "delivery_status": "Delivered",
          "meal_type": "breakfast",
          "product_name": "Upma",
          "total_amount": 120.00,
          "order_created_on": "2025-07-18T08:45:00Z",
          "cancellation_details": null
        }
      ]
    }
  }
}
```

---

## 🧪 **Test Commands**

### **1. Get All Customer Orders (Including Cancelled)**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/customer/1' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

### **2. Get Only Cancelled Orders**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/customer/1?order_status=Cancelled' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

### **3. Get Orders Excluding Cancelled**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/customer/1?include_cancelled=false' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

### **4. Get Orders with Student Name Filter**

```bash
curl -X GET 'http://************:8000/api/v2/order-management/customer/1?student_name_filter=John' \
  -H 'Accept: application/json' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

---

## 📊 **Cancellation Details Breakdown**

### **Fields Explained**

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `cancelled_by` | string | Who initiated the cancellation | "customer", "admin", "system" |
| `cancelled_on` | datetime | When the order was cancelled | "2025-07-28T10:30:00Z" |
| `cancellation_reason` | string | Detailed reason for cancellation | "Customer requested cancellation..." |
| `refund_amount` | decimal | Amount refunded to customer | 75.00 |
| `refund_percentage` | integer | Percentage of refund based on timing | 50 |
| `refund_policy_applied` | string | Which refund policy was applied | "partial_refund_window" |

### **Refund Policy Types**

| Policy Type | Description | Typical Refund % |
|-------------|-------------|------------------|
| `full_refund_before_cutoff` | Cancelled before cutoff time | 100% |
| `partial_refund_window` | Cancelled in partial refund window | 0-50% |
| `no_refund_after_cutoff` | Cancelled after cutoff time | 0% |
| `no_refund` | No refund applicable | 0% |
| `unknown` | Policy could not be determined | Varies |
| `error` | Error in determining policy | 0% |

---

## 🔍 **Database Schema Updates**

### **Orders Table Enhancement**
The following fields are now properly populated during cancellation:

```sql
ALTER TABLE orders 
ADD COLUMN cancelled_by VARCHAR(50) NULL COMMENT 'Who cancelled the order (customer/admin/system)',
ADD COLUMN cancelled_on TIMESTAMP NULL COMMENT 'When the order was cancelled';
```

### **Sample Database Records**

**Active Order:**
```sql
SELECT order_no, order_status, cancelled_by, cancelled_on, remark 
FROM orders 
WHERE order_no = 'QA93250725';

-- Result:
-- order_no: QA93250725
-- order_status: New
-- cancelled_by: NULL
-- cancelled_on: NULL
-- remark: NULL
```

**Cancelled Order:**
```sql
SELECT order_no, order_status, cancelled_by, cancelled_on, remark 
FROM orders 
WHERE order_no = 'QA93250726';

-- Result:
-- order_no: QA93250726
-- order_status: Cancelled
-- cancelled_by: customer
-- cancelled_on: 2025-07-28 10:30:00
-- remark: Customer requested cancellation | Refund: 50% | Policy: partial_refund_window
```

---

## 🎯 **Use Cases**

### **1. Customer Support**
- Quickly identify who cancelled an order and when
- Understand the reason for cancellation
- Check refund status and amount

### **2. Analytics**
- Track cancellation patterns by time and reason
- Analyze refund policy effectiveness
- Monitor customer satisfaction

### **3. Mobile App Integration**
- Display cancellation history to customers
- Show refund details and status
- Provide transparency in cancellation process

---

## 📋 **Testing Scenarios**

### **Scenario 1: Recently Cancelled Order**
1. Cancel an order using the cancel API
2. Immediately call the get orders API
3. Verify cancellation details are present and accurate

### **Scenario 2: Historical Cancelled Orders**
1. Retrieve orders for a customer with historical cancellations
2. Verify all cancelled orders have proper cancellation details
3. Check refund amounts match wallet transactions

### **Scenario 3: Mixed Order Status**
1. Get orders for a customer with various order statuses
2. Verify only cancelled orders have cancellation_details
3. Confirm non-cancelled orders have cancellation_details as null

---

## 🎉 **Benefits**

### **✅ Enhanced Transparency**
- Customers can see exactly when and why orders were cancelled
- Clear refund information and policy application

### **✅ Better Customer Support**
- Support agents have complete cancellation history
- Easy to resolve disputes and questions

### **✅ Improved Analytics**
- Track cancellation patterns and reasons
- Analyze refund policy effectiveness
- Monitor customer behavior

### **✅ Audit Trail**
- Complete record of who cancelled what and when
- Compliance with business requirements
- Historical data for analysis

---

## 🚀 **Status: READY FOR TESTING**

The enhanced Get Orders API with cancellation details is now fully implemented and ready for testing. All cancelled orders will now include comprehensive cancellation information, providing complete transparency and audit trail for order management.

**Key Files Updated:**
- ✅ `OrderManagementController.php` - Enhanced with cancellation tracking
- ✅ `order-management-openapi-clean.yaml` - Updated OpenAPI specification
- ✅ Database queries enhanced to include cancellation fields

**Test the API now to see the enhanced cancellation details in action!** 🎯
