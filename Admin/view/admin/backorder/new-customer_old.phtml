   <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Order For New Customers</span></h2>
          </div>
          <!--contenttitle-->

          <br />
         <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'new-customer')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		      echo $this->form()->openTag($form);
     	 ?>

          	<p>
              <label><?php echo $this->formLabel($form->get('customer_name')); ?></label>
              <span class="field">
              	 <?php
              echo $this->formElement($form->get('check_mobile_verification'));
              	echo $this->formElement($form->get('customer_name'));
				echo $this->formElementErrors($form->get('customer_name')); ?>
              </span>
            </p>

            <p>
              <label><?php echo $this->formLabel($form->get('phone')); ?> </label>
              <span class="field">
                <?php echo $this->formElement($form->get('phone'));
				echo $this->formElementErrors($form->get('phone')); ?>
              </span> </p>
             <p>
              <label><?php echo $this->formLabel($form->get('email_address')); ?> </label>
              <span class="field">
                <?php echo $this->formElement($form->get('email_address'));
				echo $this->formElementErrors($form->get('email_address')); ?>
              </span> </p>


            <p>
              <label><?php echo $this->formLabel($form->get('location_code')); ?></label>
              <span class="field">
               <?php echo $this->formElement($form->get('location_code'));
				echo $this->formElementErrors($form->get('location_code')); ?>
              </span>
            </p>

             <p>
              <label><?php echo $this->formLabel($form->get('company_name')); ?></label>
              <span class="field">
              	 <?php echo $this->formElement($form->get('company_name'));
				echo $this->formElementErrors($form->get('company_name')); ?>
              </span>
           </p>

            <p>
              <label><?php echo $this->formLabel($form->get('customer_Address')); ?></label>
              <span class="field">
              	 <?php echo $this->formElement($form->get('customer_Address'));
				echo $this->formElementErrors($form->get('customer_Address')); ?>
              </span>
            </p>




            <p class="stdformbutton">
               <?php echo $this->formElement($form->get('csrf')); ?>
            	 <?php echo $this->formElement($form->get('submit_button')); ?>
             <!--  <input type="reset" class="reset radius2" value="Reset Button" />-->
            </p>
          <?php echo $this->form()->closeTag($form);?>
          <br />
          <br />
        </div>
        <!--content-->
