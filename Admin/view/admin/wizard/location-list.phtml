
    <div class="bulkadd table-responsive mt-10 mb-10">
    <table id="customer" class="basictable norowcolor display displayTable">
       <thead>
          <tr>
             <th class="blockfour">Locations</th>
             <th class="blockfour">Status</th>
             <th class="blockfour">How much do you charge for delivery?</th>
             <th class="blockfour">Delivery time (in minutes)</th>
             <th class="blockfour">Action</th>
          </tr>
       </thead>
       <tbody><?php if(isset($this->paginator) && sizeof($this->paginator) > 0) {
           foreach ($this->paginator as $location) :  ?>
          <tr class="tableform" data-location-id = "<?php echo $location->pk_location_code;?>">
             <td>
                <div class="form-group float-label-control">
                  <input type="text" class="form-control empty" name="location[<?php echo $location->pk_location_code;?>]['name']" value="<?php echo $location->location;?>">
                  <label for=""></label>
                </div>
                <span class="location-error mt-5  text-error" ></span>
             </td>
             <td>
                <div class="div-flex">
                   <div class="input-control radio default-style mb-10" data-role="input-control">
                      <label class="pull-left mr10">
                         <input type="radio" id="" name="location[<?php echo $location->pk_location_code;?>]['status']" value="1" <?php echo ($location->status == '1') ? 'checked' : '';?>> <span class="check"></span> Enable
                      </label>
                   </div>
                   <div class="input-control radio default-style mb-10" data-role="input-control">
                      <label class="pull-left mr10">
                         <input type="radio" id="" name="location[<?php echo $location->pk_location_code;?>]['status']" value="0" <?php echo ($location->status == '0') ? 'checked' : '';?> > <span class="check"></span> Disable
                      </label>
                   </div>
                 </div>
                 <span class="status-error mt-10  text-error"></span>

             </td>
             <td>
                <div class="form-group float-label-control">
                  <input type="text" class="form-control empty" name="location[<?php echo $location->pk_location_code;?>]['delivery_charges']"  value="<?php echo $location->delivery_charges;?>">
                  <label for=""></label>
                </div>
                 <span class="charges-error mt-10 text-error"></span>

             </td>
             <td>
                <div class="form-group float-label-control">
                  <input type="text" class="form-control empty" name="location[<?php echo $location->pk_location_code;?>]['delivery_time']"  value="<?php echo $location->delivery_time;?>">
                  <label for=""></label>
                </div>
                 <span class="time-error mt-10  text-error"></span>

             </td>
             <td>
                <div class="form-group float-label-control">
                    <i class="fa fa-floppy-o fa-2x update-location" data-tooltip title="save" aria-hidden="true" style="cursor:pointer"></i>
                </div>
             </td>
          </tr>
          <?php endforeach; } else {?>
            <tr>
              <td colspan="5" class="norecord"><span class="charges-error mt-10 text-error">No Record Found</span></td>
            </tr>
          <?php }?>
       </tbody>
    </table><br>
 <?php
 // add at the end of the file after the table
 echo $this->paginationControl(
     // the paginator object
     $this->paginator,
     // the scrolling style
     'sliding',
     // the partial to use to render the control
     'partial/wizard_paginator.phtml',
     // the route to link to when a user clicks a control link
     array(
         'route' => 'wizard', 'action' => 'location'
     )
 );
 ?>
    </div>
<script>
$(document).ready(function() {
    $(".update-location").on('click', function(){
        
        var tr = $(this).closest('tr');
        
        var location_id = tr.attr('data-location-id');
        
        var name = $('input[name="location['+location_id+'][\'name\']"]').val();
        var status = $('input[name="location['+location_id+'][\'status\']"]:checked').val();
        var charges = $('input[name="location['+location_id+'][\'delivery_charges\']"]').val();
        var time = $('input[name="location['+location_id+'][\'delivery_time\']"]').val();
        
        $(tr).find('.location-error, .status-error, .charges-error, .time-error').html('');
        
        $.ajax({
            url:"<?php echo $this->url('wizard',array('action' => 'edit-location')); ?>",
            type: "POST",
            data: { pk_location_code : location_id, name : name, status : status, charges : charges, time : time },
            success:function(response)
            {                
                if(response.status == 'error'){
                    
                    $.each(response.data, function(key, msg){
                        
                        $(tr).find('.'+key+'-error').html(msg);                        
                    });
                    
                }else{
                    $("#common-content1").html('').append(response.data);
                    $('#common-modal1').modal('show');   
                }
            }
        });
    });
});
</script>