import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]WalletDeposit } from '@/components/customer-service-v12/[id]/wallet/deposit';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]WalletDeposit', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]WalletDeposit />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]WalletDeposit')).toBeInTheDocument();
  });
});