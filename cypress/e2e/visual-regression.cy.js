describe('Visual Regression Tests', () => {
  beforeEach(() => {
    // Mock the authentication
    cy.intercept('POST', '/v2/auth/login', {
      statusCode: 200,
      body: {
        token: 'fake-jwt-token',
        user: {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
        },
      },
    }).as('loginRequest');

    // Mock the dashboard data
    cy.intercept('GET', '/v2/dashboard/stats', {
      statusCode: 200,
      body: {
        data: {
          customers: {
            total: 1248,
            new: 42,
            growth: 12.5,
          },
          orders: {
            total: 356,
            new: 24,
            growth: 8.2,
          },
          revenue: {
            total: 28456.78,
            new: 1245.89,
            growth: 15.3,
          },
          kitchen: {
            pending: 12,
            inProgress: 8,
            ready: 5,
          },
          delivery: {
            pending: 7,
            inTransit: 15,
            delivered: 342,
          },
        },
      },
    }).as('dashboardStatsRequest');

    // Login and visit the dashboard
    cy.login();
  });

  it('should match dashboard snapshot', () => {
    cy.visit('/dashboard');
    cy.wait('@dashboardStatsRequest');
    cy.get('[data-testid="customer-stats"]').should('be.visible');
    cy.get('[data-testid="order-stats"]').should('be.visible');
    cy.get('[data-testid="revenue-stats"]').should('be.visible');
    cy.percySnapshot('Dashboard');
  });

  it('should match customer list snapshot', () => {
    // Mock the customer list data
    cy.intercept('GET', '/v2/customer/customers*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+1234567890',
            address: '123 Main St, Anytown, USA',
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T00:00:00.000Z',
          },
          {
            id: 2,
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '+1987654321',
            address: '456 Oak St, Anytown, USA',
            created_at: '2023-01-02T00:00:00.000Z',
            updated_at: '2023-01-02T00:00:00.000Z',
          },
        ],
        meta: {
          total: 2,
          current_page: 1,
          per_page: 10,
          last_page: 1,
        },
      },
    }).as('customerListRequest');

    cy.visit('/customer');
    cy.wait('@customerListRequest');
    cy.get('table').should('be.visible');
    cy.percySnapshot('Customer List');
  });

  it('should match order list snapshot', () => {
    // Mock the order list data
    cy.intercept('GET', '/v2/order/orders*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            customer_id: 1,
            status: 'completed',
            total: 25.99,
            items: [
              {
                id: 1,
                name: 'Burger',
                price: 12.99,
                quantity: 1,
              },
              {
                id: 2,
                name: 'Fries',
                price: 4.99,
                quantity: 1,
              },
              {
                id: 3,
                name: 'Drink',
                price: 2.99,
                quantity: 1,
              },
            ],
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T00:00:00.000Z',
          },
          {
            id: 2,
            customer_id: 2,
            status: 'in_progress',
            total: 35.99,
            items: [
              {
                id: 1,
                name: 'Pizza',
                price: 18.99,
                quantity: 1,
              },
              {
                id: 2,
                name: 'Salad',
                price: 8.99,
                quantity: 1,
              },
              {
                id: 3,
                name: 'Drink',
                price: 2.99,
                quantity: 1,
              },
            ],
            created_at: '2023-01-02T00:00:00.000Z',
            updated_at: '2023-01-02T00:00:00.000Z',
          },
        ],
        meta: {
          total: 2,
          current_page: 1,
          per_page: 10,
          last_page: 1,
        },
      },
    }).as('orderListRequest');

    cy.visit('/order');
    cy.wait('@orderListRequest');
    cy.get('table').should('be.visible');
    cy.percySnapshot('Order List');
  });

  it('should match payment list snapshot', () => {
    // Mock the payment list data
    cy.intercept('GET', '/v2/payment/payments*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            order_id: 1,
            amount: 25.99,
            status: 'completed',
            payment_method: 'credit_card',
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T00:00:00.000Z',
          },
          {
            id: 2,
            order_id: 2,
            amount: 35.99,
            status: 'pending',
            payment_method: 'paypal',
            created_at: '2023-01-02T00:00:00.000Z',
            updated_at: '2023-01-02T00:00:00.000Z',
          },
        ],
        meta: {
          total: 2,
          current_page: 1,
          per_page: 10,
          last_page: 1,
        },
      },
    }).as('paymentListRequest');

    cy.visit('/payment');
    cy.wait('@paymentListRequest');
    cy.get('table').should('be.visible');
    cy.percySnapshot('Payment List');
  });

  it('should match kitchen orders snapshot', () => {
    // Mock the kitchen orders data
    cy.intercept('GET', '/v2/kitchen/orders*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            order_id: 1,
            status: 'completed',
            items: [
              {
                id: 1,
                name: 'Burger',
                quantity: 1,
                notes: 'No onions',
              },
              {
                id: 2,
                name: 'Fries',
                quantity: 1,
                notes: '',
              },
            ],
            chef_id: 1,
            chef_name: 'Chef John',
            start_time: '2023-01-01T00:10:00.000Z',
            end_time: '2023-01-01T00:25:00.000Z',
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T00:25:00.000Z',
          },
          {
            id: 2,
            order_id: 2,
            status: 'in_progress',
            items: [
              {
                id: 1,
                name: 'Pizza',
                quantity: 1,
                notes: 'Extra cheese',
              },
              {
                id: 2,
                name: 'Salad',
                quantity: 1,
                notes: 'Dressing on the side',
              },
            ],
            chef_id: 2,
            chef_name: 'Chef Jane',
            start_time: '2023-01-02T00:10:00.000Z',
            end_time: null,
            created_at: '2023-01-02T00:00:00.000Z',
            updated_at: '2023-01-02T00:10:00.000Z',
          },
        ],
        meta: {
          total: 2,
          current_page: 1,
          per_page: 10,
          last_page: 1,
        },
      },
    }).as('kitchenOrdersRequest');

    cy.visit('/kitchen/orders');
    cy.wait('@kitchenOrdersRequest');
    cy.get('table').should('be.visible');
    cy.percySnapshot('Kitchen Orders');
  });

  it('should match delivery orders snapshot', () => {
    // Mock the delivery orders data
    cy.intercept('GET', '/v2/delivery/orders*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: 1,
            order_id: 1,
            status: 'delivered',
            customer_id: 1,
            customer_name: 'John Doe',
            customer_address: '123 Main St, Anytown, USA',
            customer_phone: '+1234567890',
            agent_id: 1,
            agent_name: 'Delivery Agent 1',
            pickup_time: '2023-01-01T00:30:00.000Z',
            delivery_time: '2023-01-01T01:00:00.000Z',
            created_at: '2023-01-01T00:00:00.000Z',
            updated_at: '2023-01-01T01:00:00.000Z',
          },
          {
            id: 2,
            order_id: 2,
            status: 'in_transit',
            customer_id: 2,
            customer_name: 'Jane Smith',
            customer_address: '456 Oak St, Anytown, USA',
            customer_phone: '+1987654321',
            agent_id: 2,
            agent_name: 'Delivery Agent 2',
            pickup_time: '2023-01-02T00:30:00.000Z',
            delivery_time: null,
            created_at: '2023-01-02T00:00:00.000Z',
            updated_at: '2023-01-02T00:30:00.000Z',
          },
        ],
        meta: {
          total: 2,
          current_page: 1,
          per_page: 10,
          last_page: 1,
        },
      },
    }).as('deliveryOrdersRequest');

    cy.visit('/delivery/orders');
    cy.wait('@deliveryOrdersRequest');
    cy.get('table').should('be.visible');
    cy.percySnapshot('Delivery Orders');
  });
});
