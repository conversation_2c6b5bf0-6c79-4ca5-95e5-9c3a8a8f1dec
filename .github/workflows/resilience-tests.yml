name: Resilience Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * *'  # Run every day at midnight

jobs:
  resilience-package-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, intl, pdo_sqlite, sqlite3
        coverage: pcov
    
    - name: Install dependencies
      run: |
        cd packages/resilience
        composer install --prefer-dist --no-progress
    
    - name: Run tests
      run: |
        cd packages/resilience
        ./vendor/bin/phpunit --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./packages/resilience/coverage.xml
        flags: resilience-package
        fail_ci_if_error: false
  
  kitchen-service-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: kitchen_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, intl, pdo_mysql, zip
        coverage: pcov
    
    - name: Copy .env
      run: |
        cd services/kitchen-service-v12
        cp .env.example .env
        sed -i 's/DB_DATABASE=.*/DB_DATABASE=kitchen_test/' .env
        sed -i 's/DB_USERNAME=.*/DB_USERNAME=root/' .env
        sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=password/' .env
    
    - name: Install dependencies
      run: |
        cd services/kitchen-service-v12
        composer install --prefer-dist --no-progress
    
    - name: Generate key
      run: |
        cd services/kitchen-service-v12
        php artisan key:generate
    
    - name: Run migrations
      run: |
        cd services/kitchen-service-v12
        php artisan migrate --seed
    
    - name: Run tests
      run: |
        cd services/kitchen-service-v12
        php artisan test --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./services/kitchen-service-v12/coverage.xml
        flags: kitchen-service
        fail_ci_if_error: false
  
  auth-service-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: auth_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, intl, pdo_mysql, zip
        coverage: pcov
    
    - name: Copy .env
      run: |
        cd services/auth-service-v12
        cp .env.example .env
        sed -i 's/DB_DATABASE=.*/DB_DATABASE=auth_test/' .env
        sed -i 's/DB_USERNAME=.*/DB_USERNAME=root/' .env
        sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=password/' .env
    
    - name: Install dependencies
      run: |
        cd services/auth-service-v12
        composer install --prefer-dist --no-progress
    
    - name: Generate key
      run: |
        cd services/auth-service-v12
        php artisan key:generate
    
    - name: Run migrations
      run: |
        cd services/auth-service-v12
        php artisan migrate --seed
    
    - name: Run tests
      run: |
        cd services/auth-service-v12
        php artisan test --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./services/auth-service-v12/coverage.xml
        flags: auth-service
        fail_ci_if_error: false
  
  payment-service-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: payment_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, intl, pdo_mysql, zip
        coverage: pcov
    
    - name: Copy .env
      run: |
        cd services/payment-service-v12
        cp .env.example .env
        sed -i 's/DB_DATABASE=.*/DB_DATABASE=payment_test/' .env
        sed -i 's/DB_USERNAME=.*/DB_USERNAME=root/' .env
        sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=password/' .env
    
    - name: Install dependencies
      run: |
        cd services/payment-service-v12
        composer install --prefer-dist --no-progress
    
    - name: Generate key
      run: |
        cd services/payment-service-v12
        php artisan key:generate
    
    - name: Run migrations
      run: |
        cd services/payment-service-v12
        php artisan migrate --seed
    
    - name: Run tests
      run: |
        cd services/payment-service-v12
        php artisan test --coverage-clover=coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./services/payment-service-v12/coverage.xml
        flags: payment-service
        fail_ci_if_error: false
