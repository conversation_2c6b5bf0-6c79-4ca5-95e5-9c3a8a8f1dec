# CubeOneBiz Troubleshooting Guide

This document provides troubleshooting steps for common issues that may arise during the deployment and operation of the CubeOneBiz microservices architecture.

## Table of Contents

1. [Infrastructure Issues](#infrastructure-issues)
   - [Terraform Deployment Issues](#terraform-deployment-issues)
   - [EKS Cluster Issues](#eks-cluster-issues)
   - [RDS Issues](#rds-issues)
   - [ElastiCache Issues](#elasticache-issues)
   - [Amazon MQ Issues](#amazon-mq-issues)
2. [Microservice Issues](#microservice-issues)
   - [Deployment Issues](#deployment-issues)
   - [Runtime Issues](#runtime-issues)
   - [Scaling Issues](#scaling-issues)
3. [Networking Issues](#networking-issues)
   - [Kong API Gateway Issues](#kong-api-gateway-issues)
   - [VPC and Subnet Issues](#vpc-and-subnet-issues)
   - [Security Group Issues](#security-group-issues)
4. [CI/CD Issues](#cicd-issues)
   - [GitHub Actions Issues](#github-actions-issues)
   - [Docker Build Issues](#docker-build-issues)
5. [Monitoring and Logging Issues](#monitoring-and-logging-issues)
   - [CloudWatch Issues](#cloudwatch-issues)
   - [Prometheus and Grafana Issues](#prometheus-and-grafana-issues)

## Infrastructure Issues

### Terraform Deployment Issues

#### Issue: Terraform apply fails with permission errors

**Symptoms:**
- Error message: "Error: User: ... is not authorized to perform: ..."

**Solution:**
1. Verify that the AWS credentials have the necessary permissions
2. Check IAM policies and roles
3. Use the AWS Policy Simulator to test permissions

```bash
# Check AWS credentials
aws sts get-caller-identity

# Update AWS credentials
aws configure
```

#### Issue: Terraform state lock

**Symptoms:**
- Error message: "Error acquiring the state lock"

**Solution:**
1. Check if another Terraform process is running
2. If no other process is running, force unlock the state

```bash
terraform force-unlock LOCK_ID
```

### EKS Cluster Issues

#### Issue: EKS cluster creation fails

**Symptoms:**
- Terraform apply hangs or fails during EKS cluster creation
- Error in CloudTrail logs

**Solution:**
1. Check VPC and subnet configuration
2. Verify IAM roles and policies
3. Check CloudTrail logs for detailed error messages

```bash
# Check EKS cluster status
aws eks describe-cluster --name cubeonebiz-cluster

# Check CloudTrail logs
aws cloudtrail lookup-events --lookup-attributes AttributeKey=EventName,AttributeValue=CreateCluster
```

#### Issue: Unable to connect to EKS cluster

**Symptoms:**
- `kubectl` commands fail with "Unable to connect to the server"

**Solution:**
1. Update kubeconfig
2. Check security groups
3. Verify IAM role mappings

```bash
# Update kubeconfig
aws eks update-kubeconfig --name cubeonebiz-cluster --region us-east-1

# Check AWS IAM Authenticator configuration
kubectl describe configmap -n kube-system aws-auth
```

### RDS Issues

#### Issue: RDS instance is not accessible from EKS

**Symptoms:**
- Microservices cannot connect to the database
- Connection timeout errors

**Solution:**
1. Check security group rules
2. Verify subnet configuration
3. Test connectivity from a bastion host

```bash
# Check RDS status
aws rds describe-db-instances --db-instance-identifier cubeonebiz-mysql

# Test connectivity from bastion host
nc -zv <rds-endpoint> 3306
```

#### Issue: RDS performance issues

**Symptoms:**
- Slow query responses
- High CPU utilization

**Solution:**
1. Check CloudWatch metrics
2. Analyze slow query logs
3. Consider scaling up the instance

```bash
# Enable slow query logs
aws rds modify-db-parameter-group --db-parameter-group-name cubeonebiz-mysql-parameter-group --parameters "ParameterName=slow_query_log,ParameterValue=1,ApplyMethod=immediate" "ParameterName=long_query_time,ParameterValue=1,ApplyMethod=immediate"

# Get slow query logs
aws rds download-db-log-file-portion --db-instance-identifier cubeonebiz-mysql --log-file-name slowquery/mysql-slowquery.log
```

### ElastiCache Issues

#### Issue: ElastiCache cluster is not accessible

**Symptoms:**
- Microservices cannot connect to Redis
- Connection timeout errors

**Solution:**
1. Check security group rules
2. Verify subnet configuration
3. Test connectivity from a bastion host

```bash
# Check ElastiCache status
aws elasticache describe-replication-groups --replication-group-id cubeonebiz-redis

# Test connectivity from bastion host
nc -zv <elasticache-endpoint> 6379
```

#### Issue: ElastiCache memory issues

**Symptoms:**
- Out of memory errors
- Evictions

**Solution:**
1. Check CloudWatch metrics
2. Analyze memory usage
3. Consider scaling up the instance or enabling cluster mode

```bash
# Check memory usage
aws cloudwatch get-metric-statistics --namespace AWS/ElastiCache --metric-name DatabaseMemoryUsagePercentage --dimensions Name=CacheClusterId,Value=cubeonebiz-redis --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ) --end-time $(date -u +%Y-%m-%dT%H:%M:%SZ) --period 300 --statistics Average
```

### Amazon MQ Issues

#### Issue: Amazon MQ broker is not accessible

**Symptoms:**
- Microservices cannot connect to RabbitMQ
- Connection timeout errors

**Solution:**
1. Check security group rules
2. Verify subnet configuration
3. Test connectivity from a bastion host

```bash
# Check Amazon MQ status
aws mq describe-broker --broker-id cubeonebiz-rabbitmq

# Test connectivity from bastion host
nc -zv <mq-endpoint> 5672
```

#### Issue: Amazon MQ performance issues

**Symptoms:**
- Slow message processing
- High CPU utilization

**Solution:**
1. Check CloudWatch metrics
2. Analyze queue sizes
3. Consider scaling up the instance

```bash
# Check CPU usage
aws cloudwatch get-metric-statistics --namespace AWS/AmazonMQ --metric-name CpuUtilization --dimensions Name=Broker,Value=cubeonebiz-rabbitmq --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ) --end-time $(date -u +%Y-%m-%dT%H:%M:%SZ) --period 300 --statistics Average
```

## Microservice Issues

### Deployment Issues

#### Issue: Pod creation fails

**Symptoms:**
- Pods are stuck in "Pending" or "ImagePullBackOff" state

**Solution:**
1. Check pod events
2. Verify image name and tag
3. Check Docker registry credentials

```bash
# Check pod status
kubectl get pods -n cubeonebiz

# Check pod events
kubectl describe pod <pod-name> -n cubeonebiz

# Check Docker registry secret
kubectl get secret dockerhub-secret -n cubeonebiz -o yaml
```

#### Issue: Pod crashes on startup

**Symptoms:**
- Pods are in "CrashLoopBackOff" state

**Solution:**
1. Check pod logs
2. Verify environment variables
3. Check for missing secrets or config maps

```bash
# Check pod logs
kubectl logs <pod-name> -n cubeonebiz

# Check environment variables
kubectl exec <pod-name> -n cubeonebiz -- env

# Check secrets
kubectl get secrets -n cubeonebiz
```

### Runtime Issues

#### Issue: Microservice returns 500 errors

**Symptoms:**
- HTTP 500 responses
- Error logs in the pod

**Solution:**
1. Check pod logs
2. Verify database connection
3. Check for exceptions in the application code

```bash
# Check pod logs
kubectl logs <pod-name> -n cubeonebiz

# Check database connection
kubectl exec <pod-name> -n cubeonebiz -- php artisan db:monitor

# Check application health
kubectl exec <pod-name> -n cubeonebiz -- curl http://localhost:8000/api/health
```

#### Issue: Microservice is slow

**Symptoms:**
- High response times
- Timeouts

**Solution:**
1. Check pod resource usage
2. Analyze database queries
3. Check external service dependencies

```bash
# Check pod resource usage
kubectl top pod <pod-name> -n cubeonebiz

# Check database query logs
kubectl exec <pod-name> -n cubeonebiz -- php artisan db:monitor --queries

# Check external service dependencies
kubectl exec <pod-name> -n cubeonebiz -- curl -v http://external-service
```

### Scaling Issues

#### Issue: Horizontal Pod Autoscaler not working

**Symptoms:**
- Pods are not scaling up under load

**Solution:**
1. Check HPA status
2. Verify metrics server is running
3. Check resource requests and limits

```bash
# Check HPA status
kubectl describe hpa <hpa-name> -n cubeonebiz

# Check metrics server
kubectl get pods -n kube-system | grep metrics-server

# Check pod resource requests
kubectl describe pod <pod-name> -n cubeonebiz | grep -A 3 Requests
```

## Networking Issues

### Kong API Gateway Issues

#### Issue: Kong API Gateway not routing requests

**Symptoms:**
- 404 errors when accessing microservices
- No traffic reaching the microservices

**Solution:**
1. Check Kong configuration
2. Verify Ingress resources
3. Check Kong logs

```bash
# Check Kong configuration
kubectl get kongplugin -n kong
kubectl get kongconsumer -n kong

# Check Ingress resources
kubectl get ingress -A

# Check Kong logs
kubectl logs -n kong <kong-pod-name>
```

### VPC and Subnet Issues

#### Issue: Pods cannot communicate with each other

**Symptoms:**
- Network timeouts between pods
- Services cannot resolve each other

**Solution:**
1. Check VPC and subnet configuration
2. Verify security groups
3. Check CoreDNS configuration

```bash
# Check CoreDNS configuration
kubectl get configmap coredns -n kube-system -o yaml

# Test network connectivity
kubectl exec <pod-name> -n cubeonebiz -- ping <service-name>.<namespace>.svc.cluster.local

# Check network policies
kubectl get networkpolicy -A
```

## CI/CD Issues

### GitHub Actions Issues

#### Issue: GitHub Actions workflow fails

**Symptoms:**
- Workflow fails with error message
- Jobs do not complete

**Solution:**
1. Check workflow logs
2. Verify secrets are configured
3. Check repository permissions

```bash
# Check workflow logs in GitHub Actions UI

# Verify secrets
# Go to GitHub repository -> Settings -> Secrets and variables -> Actions
```

### Docker Build Issues

#### Issue: Docker build fails

**Symptoms:**
- Error during docker build step
- Image not pushed to registry

**Solution:**
1. Check Dockerfile
2. Verify build context
3. Check DockerHub credentials

```bash
# Test Docker build locally
docker build -t test-image ./services/<service-name>

# Check DockerHub login
docker login
```

## Monitoring and Logging Issues

### CloudWatch Issues

#### Issue: Logs not appearing in CloudWatch

**Symptoms:**
- Missing logs in CloudWatch console
- No metrics for resources

**Solution:**
1. Check IAM permissions
2. Verify CloudWatch agent configuration
3. Check log group and stream names

```bash
# Check CloudWatch agent status
sudo systemctl status amazon-cloudwatch-agent

# Check CloudWatch agent configuration
cat /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json

# Check CloudWatch logs
aws logs describe-log-groups
```

### Prometheus and Grafana Issues

#### Issue: Metrics not appearing in Grafana

**Symptoms:**
- Empty dashboards
- No data in Grafana

**Solution:**
1. Check Prometheus configuration
2. Verify ServiceMonitor resources
3. Check Prometheus targets

```bash
# Check Prometheus configuration
kubectl get configmap -n monitoring prometheus-server -o yaml

# Check ServiceMonitor resources
kubectl get servicemonitor -A

# Check Prometheus targets
# Access Prometheus UI -> Status -> Targets
```
