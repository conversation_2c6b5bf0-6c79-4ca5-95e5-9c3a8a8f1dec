<?php

namespace Database\Factories;

use App\Models\CustomerWallet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Customer Wallet Factory
 * 
 * This factory creates test customer wallets.
 */
class CustomerWalletFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerWallet::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'customer_code' => function () {
                return \App\Models\Customer::factory()->create()->pk_customer_code;
            },
            'balance' => $this->faker->randomFloat(2, 0, 1000),
            'status' => 1,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the wallet is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
            ];
        });
    }

    /**
     * Indicate that the wallet has zero balance.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function zeroBalance()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance' => 0,
            ];
        });
    }
}
