<?php 
$utility = \Lib\Utility::getInstance ();
$setting_session = new Zend\Session\Container ( "setting" );
$setting = $setting_session->setting;

?>

<script type="text/javascript"
	src="/admin/js/plugins/jquery.flot.min.js"></script>
   <!-- <script src="jquery.harmonize-text.min.js"></script>-->
   
<script type="text/javascript"
	src="/admin/js/plugins/jquery.flot.resize.min.js"></script>
<!--<script type="text/javascript" src="/admin/js/custom/dashboard.js"></script>  -->
<script type="text/javascript" src="https://www.google.com/jsapi"></script>
<script type="text/javascript">
    jQuery(document).ready(function(){
        getKitchenOverview('<?php echo $_SESSION['adminkitchen'];?>');
        getTodayDeliverChart('<?php echo $_SESSION['adminkitchen'];?>');
        getTodayOrdersrChart('<?php echo $_SESSION['adminkitchen'];?>');
	});
</script>
<!-- BEGIN PAGE -->


<!-- END PAGE HEADER-->
<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide" title="Help"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="todaysDash" class="common-orange-btn-on-hover"> Todays Dashboard</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="graphs" class="common-orange-btn-on-hover">Graphs & activity</a>
                </li>
                <li class="devider"></li>
                <!--li>
                    <a id="editCust" class="common-orange-btn-on-hover"> Edit Customer</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="walletAD" class="common-orange-btn-on-hover">Wallet Add/Debit</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="placeOrder" class="common-orange-btn-on-hover"> Place order </a>
                </li-->
            </ul>
        </div>
    </div>
</div> 

<div id="dashboard">
	<div class="row">
		<div class="large-20 columns">
			<div class="dashboard-div  greenBg has-tip tip-top todaysorders" data-tooltip
				aria-haspopup="true"
				title="New Orders till date" >
				<div class="visual">
					<i class="fa fa-cutlery"></i>
				</div>
				<div class="details">
					<div class="number"><?php echo (int)$orders[0]['TotalWithoutCancel']; //(int) $orders['new']; ?></div>
					<div class="desc">Total Today's Orders</div>
				</div>
				<a href="/order/view/today" class="more">View more<i
					class="fa fa-hand-o-up"></i></a>
			</div>
		</div>
		<div class="large-20 columns">
			<div class="dashboard-div orangeBg has-tip tip-top processOrders" data-tooltip
				aria-haspopup="true"
				title="Orders in process till date" >
				<div class="visual">
					<i class="fa fa-circle-o-notch fa-spin"></i><i
						class="fa fa-cutlery inside"></i>
				</div>
				<div class="details">
					<div class="number"><?php echo (int)$orders[0]['InProcess']; //(int)$orders['in_process']; ?></div>
					<div class="desc">Order In Process</div>
				</div>
				<a href="/order/view/today?id=1" class="more">View more<i class="fa fa-truck"></i></a>
			</div>
		</div>
		<div class="large-20 columns">
			<div class="dashboard-div blueBg has-tip tip-top deliveredOrders" data-tooltip
				aria-haspopup="true"
				title="Orders delivered till date" >
				<div class="visual">
					<i class="fa fa-truck"></i>
				</div>
				<div class="details">
					<div class="number"><?php echo (int)$orders[0]['Delivered']; //(int)$orders['delivered']; ?></div>
					<div class="desc">Delivered Orders</div>
				</div>
				<a href="/order/view/today?dst=delivered" class="more">View more<i class="fa fa-hand-o-up"></i></a>
			</div>
		</div>
		<div class="large-20 columns">
			<div class="dashboard-div redBg has-tip tip-top cancelledOrders" data-tooltip
				aria-haspopup="true"
				title="Orders cancelled till date">
				<div class="visual">
					<i class="fa fa-ban"></i>
				</div>
				<div class="details">
					<div class="number"><?php echo (int)$orders[0]['Cancelled']; //(int)$orders['delivered']; ?></div>
					<div class="desc">Cancelled Order</div>
				</div>
				<a href="/order/view/cancel" class="more">View more<i class="fa fa-hand-o-up"></i></a>
			</div>
		</div>
		<div class="large-20 columns">
			<div class="dashboard-div vavendarRBg has-tip tip-top dueAmt" data-tooltip
				aria-haspopup="true"
				title="Outstanding Amount till date">
				<div class="visual">
					<i class="fa fa-money"></i>
				</div>
				<div class="details">
					<div class="number">
						<?php echo $utility->getLocalCurrency($orders[0]['AmountDue']); //echo $this->currencyFormat(number_format($orders[0]['AmountDue'],2)); //echo $formatter(number_format($orders[0]['AmountDue'],2), 'EUR'); ?>
						<!-- <i class="fa fa-rupee"></i> --> <?php //echo number_format($orders[0]['AmountDue'],2); //@number_format($orders['unpaid'],2);(int)$orders[0]['AmountDue'] ?></div>
					<div class="desc">Receivable Due</div>
				</div>
				<!-- dataForGraph = data; -->
				<!-- <a href="/invoice/view/unpaid" class="more">View more <i class="fa fa-hand-o-up"></i></a> -->

				<a href="/collection" class="more">View more <i class="fa fa-hand-o-up"></i></a>
			</div>
		</div> 
		<div class="clearBoth20"></div>
		<div class="large-6 columns kitchenOverview">
			<div class="portlet box grey" >
				<div class="portlet-title">
					<h4 class="white">
						<i class="fa fa-cutlery"></i>Kitchen Overview
					</h4>
					<ul class="toolOption">
						<li>
							<div class="tools">
								<a href="javascript:;" class="collapse"></a>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body">
					<div id="yearly_chart" style="height: 400px; margin: 0 auto"></div>
				</div>
			</div>
		</div>
		<div class="large-6 columns delorderOverview">
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white">
						<i class="fa fa-bar-chart-o"></i>Order Overview
					</h4>
					<ul class="toolOption">
						<li>
							<div class="tools">
								<a href="javascript:;" class="collapse"></a>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body">
					<div id="chart_div2" style="height: 400px; margin: 0 auto"></div>
				</div>
			</div>
		</div>
		<div class="clearBoth20"></div>
		<div class="large-6 columns plcorderOverview">
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white">
						<i class="fa fa-bar-chart-o"></i>Order Overview
					</h4>
					<ul class="toolOption">
						<li>
							<div class="tools">
								<a href="javascript:;" class="collapse"></a>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body">
					<div id="chart_div3" style="height: 400px; margin: 0 auto"></div>
				</div>
			</div>
		</div>

		<div class="large-6 columns latestActivity">
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white">
						<i class="fa fa-tasks"></i>Latest Activities
					</h4>
					<ul class="toolOption">
						<li>
							<div class="tools">
								<a href="javascript:;" class="collapse"></a>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body">
					<table id="example" class="display todo">

						<tbody>
							<tr>
								<th>Modules</th>
								<th>Activity</th>
								<th>User</th>
								<th>Date Time</th>
							</tr>
							<?php foreach($activity as $key=>$val){ ?>
								<tr>
									<td><?php echo $val['controller']?></td>
									<td><?php echo $val['description']?></td>
									<td><?php echo $val['context_name']?></td>
									<!-- <td><?php //echo $val['modified_date']?></td> -->
									<td><?php $date = $val['modified_date']; echo $utility->displayDate($date,$setting['DATE_FORMAT'])." ".date('h:i:s',strtotime($date));?></td>
								</tr>
						<?php }?>
						</tbody>

					</table>
					<div class="clearBoth20"></div>
					<div class="right">
						<a class="btn button btnHover" id="showactivitylog" href="<?php echo $this->url('dashboard',array('action'=>'showactivity'))?>"><i class="fa fa-arrow-circle-o-right"></i> Read More</a>
					</div>
					<div class="clearBoth5"></div>
				</div>
				<!-- Chart Kitchen End-->
			</div>

		</div>
	</div>
	<div class="clearBoth20"></div>
	<script type="text/javascript">
        $(document).on('click',"#todaysDash",function(e){
	        e.preventDefault();
	        $('.todaysorders').attr("data-step", "1");
	        $('.todaysorders').attr("data-intro", "Shows Order to be processed today.");
	        $('.processOrders').attr("data-step", "2");
	        $('.processOrders').attr("data-intro", "Todays orders for preparation");
	        $('.deliveredOrders').attr("data-step", "3");
	        $('.deliveredOrders').attr("data-intro", "Todays Delivered orders");
	        $('.cancelledOrders').attr("data-step", "4");
	        $('.cancelledOrders').attr("data-intro", "Todays Cancelled orders");
	        $('.dueAmt').attr("data-step", "5");
	        $('.dueAmt').attr("data-intro", "Pending collections");
	        introJs().start();
	        $('.todaysorders').removeAttr("data-step");
	        $('.todaysorders').removeAttr("data-intro");
	        $('.processOrders').removeAttr("data-step");
	        $('.processOrders').removeAttr("data-intro");
	        $('.deliveredOrders').removeAttr("data-step");
	        $('.deliveredOrders').removeAttr("data-intro");
	        $('.cancelledOrders').removeAttr("data-step");
	        $('.cancelledOrders').removeAttr("data-intro");
	        $('.dueAmt').removeAttr("data-step");
	        $('.dueAmt').removeAttr("data-intro");
	    });
	    $(document).on('click',"#graphs",function(e){
	        e.preventDefault();
	        $('.kitchenOverview').attr("data-step", "1");
	        $('.kitchenOverview').attr("data-intro", "Kitchen wise popular / Most sellable products.");
	        $('.kitchenOverview').attr("data-position", "right");
	        $('.delorderOverview').attr("data-step", "2");
	        $('.delorderOverview').attr("data-intro", "Todays orders for delivery");
	        $('.delorderOverview').attr("data-position", "left");
	        $('.plcorderOverview').attr("data-step", "3");
	        $('.plcorderOverview').attr("data-intro", "Todays orders placed");
	        $('.plcorderOverview').attr("data-position", "right");
	        $('.latestActivity').attr("data-step", "4");
	        $('.latestActivity').attr("data-intro", "Activity logs of system (customer portal and admin)");
	        $('.latestActivity').attr("data-position", "left");
	        introJs().start();
	        $('.kitchenOverview').removeAttr("data-step");
	        $('.kitchenOverview').removeAttr("data-intro");
	        $('.delorderOverview').removeAttr("data-step");
	        $('.delorderOverview').removeAttr("data-intro");
	        $('.plcorderOverview').removeAttr("data-step");
	        $('.plcorderOverview').removeAttr("data-intro");
	        $('.latestActivity').removeAttr("data-step");
	        $('.latestActivity').removeAttr("data-intro");
	    });
	</script>    
</div>
</div>
</div>
<!-- END PAGE CONTAINER-->
    
    
        
<!-- END PAGE -->
