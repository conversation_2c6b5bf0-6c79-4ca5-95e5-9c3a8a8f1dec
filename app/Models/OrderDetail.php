<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderDetail extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ref_order_no',
        'product_code',
        'product_name',
        'quantity',
        'amount',
        'tax',
        'company_id',
        'unit_id',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'float',
        'tax' => 'float',
        'quantity' => 'integer',
        'company_id' => 'integer',
        'unit_id' => 'integer',
    ];

    /**
     * Get the order that owns the order detail.
     *
     * @return BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'ref_order_no', 'order_no');
    }

    /**
     * Get the product that owns the order detail.
     *
     * @return BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_code', 'product_code');
    }

    /**
     * Get the total amount including tax.
     *
     * @return float
     */
    public function getTotalAmount(): float
    {
        return $this->amount + $this->tax;
    }

    /**
     * Get the unit price.
     *
     * @return float
     */
    public function getUnitPrice(): float
    {
        return $this->quantity > 0 ? $this->amount / $this->quantity : 0;
    }

    /**
     * Get the unit tax.
     *
     * @return float
     */
    public function getUnitTax(): float
    {
        return $this->quantity > 0 ? $this->tax / $this->quantity : 0;
    }
}
