<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class ProductResource
 * 
 * This resource transforms a product model into a JSON response.
 */
class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'formatted_price' => '$' . number_format($this->price, 2),
            'category_id' => $this->category_id,
            'category' => $this->whenLoaded('category', function () {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                ];
            }),
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
            'image_url' => $this->image_url,
            'thumbnail_url' => $this->thumbnail_url,
            'is_available' => (bool) $this->is_available,
            'sku' => $this->sku,
            'stock' => $this->stock,
            'weight' => $this->weight,
            'dimensions' => $this->dimensions,
            'tags' => $this->whenLoaded('tags', function () {
                return $this->tags->pluck('name');
            }),
            'attributes' => $this->whenLoaded('attributes', function () {
                return $this->attributes->map(function ($attribute) {
                    return [
                        'name' => $attribute->name,
                        'value' => $attribute->pivot->value,
                    ];
                });
            }),
            'average_rating' => $this->when($this->reviews_count > 0, function () {
                return round($this->reviews_sum_rating / $this->reviews_count, 1);
            }),
            'reviews_count' => $this->when($this->reviews_count > 0, $this->reviews_count),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
            
            // Include links to related resources
            'links' => [
                'self' => route('api.v2.products.show', $this->id),
                'reviews' => route('api.v2.products.reviews.index', $this->id),
                'related' => route('api.v2.products.related', $this->id),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'status' => 'success',
            'meta' => [
                'api_version' => '2.0',
            ],
        ];
    }
}
