<?php

echo "🔄 ORDER SWAP API - QUICK USAGE EXAMPLE\n";
echo "=" . str_repeat("=", 50) . "\n\n";

echo "📋 SCENARIO: Customer wants to swap breakfast item\n";
echo "Current Order: Poha (₹150) → New Order: Upma (₹200)\n";
echo "Swap Charges: ₹25\n";
echo "Total Change: ₹75 (₹50 price diff + ₹25 swap charges)\n\n";

echo "🚀 API CALL:\n";
echo "-" . str_repeat("-", 50) . "\n";

$apiCall = [
    'method' => 'POST',
    'url' => 'http://************:8000/api/v2/order-management/swap/QA93250725',
    'headers' => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
    'body' => [
        'order_date' => '2025-09-03',
        'new_product_code' => 342,
        'reason' => 'Customer prefers Up<PERSON> over Poha',
        'meal_type' => 'breakfast'
    ]
];

echo "Method: {$apiCall['method']}\n";
echo "URL: {$apiCall['url']}\n";
echo "Headers:\n";
foreach ($apiCall['headers'] as $header) {
    echo "  {$header}\n";
}
echo "\nRequest Body:\n";
echo json_encode($apiCall['body'], JSON_PRETTY_PRINT) . "\n\n";

echo "📋 EXPECTED RESPONSE:\n";
echo "-" . str_repeat("-", 50) . "\n";

$expectedResponse = [
    'success' => true,
    'message' => 'Order swapped successfully',
    'data' => [
        'order_id' => 127810,
        'order_no' => 'QA93250725',
        'order_date' => '2025-09-03',
        'swap_details' => [
            'old_product' => [
                'code' => 341,
                'name' => 'Poha',
                'price' => 150.00
            ],
            'new_product' => [
                'code' => 342,
                'name' => 'Upma',
                'price' => 200.00
            ],
            'price_difference' => 50.00,
            'swap_charges' => 25.00,
            'total_amount_change' => 75.00,
            'new_order_amount' => 225.00
        ],
        'reason' => 'Customer prefers Upma over Poha'
    ]
];

echo json_encode($expectedResponse, JSON_PRETTY_PRINT) . "\n\n";

echo "📊 DATABASE CHANGES:\n";
echo "-" . str_repeat("-", 50) . "\n";

echo "ORDERS table updates:\n";
$orderUpdates = [
    'product_code' => '341 → 342',
    'product_name' => 'Poha → Upma',
    'amount' => '150.00 → 225.00',
    'tax' => 'Recalculated based on new amount',
    'remark' => 'Appended: Swapped to Upma. Reason: Customer prefers Upma over Poha'
];

foreach ($orderUpdates as $field => $change) {
    echo "  {$field}: {$change}\n";
}

echo "\nORDER_DETAILS table updates:\n";
$detailUpdates = [
    'product_code' => '341 → 342',
    'product_name' => 'Poha → Upma',
    'product_amount' => 'Proportionally adjusted',
    'product_tax' => 'Recalculated'
];

foreach ($detailUpdates as $field => $change) {
    echo "  {$field}: {$change}\n";
}

echo "\n🔍 VALIDATION CHECKS:\n";
echo "-" . str_repeat("-", 50) . "\n";

$validationChecks = [
    '✅ Order exists for date 2025-09-03',
    '✅ Order status is swappable (not cancelled/delivered)',
    '✅ Products 341 and 342 are in same category (Breakfast)',
    '✅ Both products are active and available',
    '✅ New product (342) is swappable',
    '✅ Same food type (both veg or both non-veg)',
    '✅ Order date is in future (not past date)'
];

foreach ($validationChecks as $check) {
    echo "  {$check}\n";
}

echo "\n💡 CURL COMMAND:\n";
echo "-" . str_repeat("-", 50) . "\n";

$curlCommand = "curl -X POST 'http://************:8000/api/v2/order-management/swap/QA93250725' \\\n";
$curlCommand .= "  -H 'Content-Type: application/json' \\\n";
$curlCommand .= "  -H 'Accept: application/json' \\\n";
$curlCommand .= "  -d '" . json_encode($apiCall['body']) . "'";

echo $curlCommand . "\n\n";

echo "🎯 COMMON ERROR SCENARIOS:\n";
echo "-" . str_repeat("-", 50) . "\n";

$errorScenarios = [
    'Different Categories' => 'Trying to swap breakfast item with lunch item',
    'Past Date' => 'Attempting to swap order from yesterday',
    'Cancelled Order' => 'Order already cancelled by customer',
    'Delivered Order' => 'Order already delivered to customer',
    'Inactive Product' => 'New product is not active/available',
    'Non-swappable Product' => 'New product has is_swappable = false'
];

foreach ($errorScenarios as $error => $description) {
    echo "  ❌ {$error}: {$description}\n";
}

echo "\n✅ SUCCESS INDICATORS:\n";
echo "-" . str_repeat("-", 50) . "\n";

$successIndicators = [
    'HTTP 200 status code',
    'success: true in response',
    'swap_details with old/new product info',
    'Updated amounts and charges',
    'Database records updated',
    'Audit log entries created'
];

foreach ($successIndicators as $indicator) {
    echo "  ✅ {$indicator}\n";
}

echo "\n🎉 READY TO TEST!\n";
echo "Copy the curl command above to test the API\n";

// Function to actually test the API (commented out for safety)
/*
function testSwapAPI() {
    $url = 'http://************:8000/api/v2/order-management/swap/QA93250725';
    $data = [
        'order_date' => '2025-09-03',
        'new_product_code' => 342,
        'reason' => 'Customer prefers Upma over Poha',
        'meal_type' => 'breakfast'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "\nAPI Response (HTTP {$httpCode}):\n";
    echo $response . "\n";
}

// Uncomment to test:
// testSwapAPI();
*/
