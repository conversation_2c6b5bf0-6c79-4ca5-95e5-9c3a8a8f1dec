<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The activity includes add,update and delete delivery locations
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\JsonModel;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;

use Admin\Form\LocationForm;
use QuickServe\Model\LocationValidator;

class LocationController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\LocationTable model
	 *
	 * @var QuickServe\Model\LocationTable $locationTable
	 */
	protected $locationTable;
    /**
     * It has an instance of QuickServe\Model\MultilingualCodeSupportTable model
     * 
     * @var QuickServe\Model\MultilingualCodeSupportTable $multilingualCodeSupportTable
     */
    protected $multilingualCodeSupportTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	
	//protected $activitylogTable;
	protected $kitchenmatserTable;
	
	protected $kitchenmasterTable;
	/**
	 * This function used to display the list of delivery locations
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container('setting');

		$city_data=$this->getCityTable()->fetchAll();

		//dd($city_data);
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Location",'description'=>"Delivery Area",'breadcrumb'=>"Location"));
		
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
				'cities' => $city_data,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
		
	
	}
	/**
	 * To add new delivery location
	 *
	 * @return \Admin\Form\LocationForm
	 */
	public function addAction()
	{
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$sm = $this->getServiceLocator();
		
		
		$adapt = $sm->get('Write_Adapter');
		$libCommon = Qscommon::getInstance($sm);
		$config_variables = $sm->get('config');
		$multilingual_code_model = $this->getMultilingualCodeSupportTable();
		$form = new LocationForm($sm);

		$form->get('submit')->setAttribute('value', 'Add');

		$request = $this->getRequest();
		if ($request->isPost()) {
			
			$location = new LocationValidator();
			$location->getInputFilter()->get('location')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'delivery_locations',
					'field'     => 'location',
					'adapter'   => $adapt,
					'message'   => 'Location Already Exists',
			)
			));

			/* $location->getInputFilter()->get('unique_location_code')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'delivery_locations',
					'field'     => 'unique_location_code',
					'adapter'   => $adapt,
					'message'   => 'Location Code Already Exists',
			)
			)); */
			
			//$location->setAdapter($adapt);
			$form->setInputFilter($location->getInputFilter());
			$form->setData($request->getPost());
		//	echo "<pre>"; print_r($request->getPost()); exit;

			if ($form->isValid()) {
				 
				$location->exchangeArray($form->getData());
				
				$data_location = $this->getLocationTable()->saveLocation($location);
              
				$multilingual = $multilingual_code_model->saveMultilingualCodeSupport($_POST['other_language'], $this->getLocationTable()->getLastInsertValue());

				($data_location && $multilingual) ?$this->flashMessenger()->addSuccessMessage("Location added successfully"):$this->flashMessenger()->addErrorMessage("Error adding location.");
				if($data_location)
				{
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$location_name=$location->location;
					/* $activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'location';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "Location : New location $location_name created.";
					$this->getActivityLogTable()->saveActivityLog($activity_log_data); */
					
						$full_name=$loguser->first_name." ".$loguser->last_name;
						$location_name=$location->location;
						$activity_log_data=array();
						$activity_log_data['context_ref_id']=$loguser->pk_user_code;
						$activity_log_data['context_name']= $full_name;
						$activity_log_data['context_type']= 'user';
						$activity_log_data['controller']= 'location';
						$activity_log_data['action']= 'add';
						$activity_log_data['description']= "Location : New location $location_name created.";
						//$activity_log_data['description']= "'$location_name' location added by $full_name";
						$libCommon->saveActivityLog($activity_log_data);
					
				}
				// Redirect to list of albums
				return $this->redirect()->toRoute('location');
			}

		}
		//$form->get('number_of_pages')->setOptions();

		$this->layout()->setVariables(array('page_title'=>"Add Location",'breadcrumb'=>"Add Location"));
		return array('form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages']
		);
	}
	
	public function ajxLocationAction()
	{
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;

		$city = $this->params()->fromQuery('city');
	    $status = $this->params()->fromQuery('status');

	    $filter = array(
	        'city' => $city,
	        'status' => $status
    	);
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'location','1'=>'cityname','2'=>'pin','3'=>'sub_city_area','4'=>'fk_kitchen_code','5'=>"delivery_charges",'6'=>"delivery_time",'7'=>"status");
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		 
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		 
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		 
		$status = $this->params()->fromQuery('status');
		 
		$select->where(
		
				new \Zend\Db\Sql\Predicate\PredicateSet(
						array(
								new \Zend\Db\Sql\Predicate\Operator('location', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('city.city', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('pin', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('sub_city_area', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('is_default', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('delivery_charges', 'LIKE', '%'.$search.'%'),
									
						),
						\Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
		
		);
		
		$select->order($order_by . ' ' . $order);
        
		$locations = $this->getLocationTable()->fetchAll($select,$page,$filter);
		$kitchens = $this->getKitchenMasterTable()->fetchAll()->toArray();
		
		$kitchen_arr=array();
		foreach($kitchens as $key=>$val){
			$kitchen_arr[$val['pk_kitchen_code']]=$val['kitchen_name'];
		}
		
		$locations->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $locations->getTotalItemCount();
		$returnVar['recordsFiltered'] = $locations->getTotalItemCount();
		$returnVar['data'] = array();
		
		foreach($locations as $location){
			//dd($location);
			$arrTmp = array();
			array_push($arrTmp,$location['location']);
			array_push($arrTmp,$location['cityname']);
			array_push($arrTmp,$location['pin']);
			array_push($arrTmp,$location['sub_city_area']);
			$fk_kitchen_code =  $kitchen_arr[$location['fk_kitchen_code']];
			array_push($arrTmp,$fk_kitchen_code);
            
			array_push($arrTmp,$utility->getLocalCurrency($location['delivery_charges']));
			array_push($arrTmp,$location['delivery_time']);
			
			$status =  ($location['status']=="1" )? '<span class="active">Active</span>':'<span class="inactive">Inactive</span>';
			array_push($arrTmp,$status);
			$str = "";
			
			$textadd = ($location['status']==1)?'Deactive' :'Activate';
				
			if($acl->isAllowed($loggedUser->rolename,'location','edit')){
			
				$str.='<a href="'.$this->url()->fromRoute('location', array('action' => 'edit', 'id' => $location['pk_location_code'])).'" class="btn btn5 btn_pencil5">';
					
				$str.= '<button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button></a>';
			}
			if($acl->isAllowed($loggedUser->rolename,'location','delete')){
				$str.='<a onclick="return confirm("Are you sure you want to '.$textadd.' this location ?")" href="'.$this->url()->fromRoute('location', array('action' => 'delete', 'id' => $location['pk_location_code'])).'" class="btn btn5 btn_trash5">';
			
				if($textadd == 'Deactive') {
					$str.= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Delete"><i class="fa fa-ban"></i></button>';
				}else if($textadd == 'Activate'){
					$str.=' <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
				}
                
			}
            
			array_push($arrTmp,$str);
			
			array_push($returnVar['data'],$arrTmp);
		}
		return new JsonModel($returnVar);
	}
	/**
	 * To update the delivery location of given location id
	 *
	 * @return \Admin\Form\LocationForm
	 */
	public function editAction()
	{
//		    	error_reporting(E_ALL);
//        ini_set("display_errors","On");
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('location', array('action' => 'add'));
		}
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$location = $this->getLocationTable()->getLocation($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$config_variables = $sm->get('config');
		$multilingual_code_model = $this->getMultilingualCodeSupportTable();
		$form = new LocationForm($sm);
		//echo '<pre>';print_r($form);exit();

		$local_language_array = array();
		$records = $multilingual_code_model->getMultilingualCodeSupport(null, array('context_ref_id' => $id, 'context' => 'location_language', 'status' => 1) );
		$local_language_array = $multilingual_code_model->getArray($records);
		if( empty($local_language_array) ) { $local_language_array = array(); }

		$form->bind($location);
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $location->location;
		//$val1 = $location->unique_location_code;
		$request = $this->getRequest();

		if ($request->isPost())
		 {
		 	//echo "<pre>"; print_r($request->getPost()); exit;
			$location = new LocationValidator();
		//	echo "<pre>"; print_r($location); exit;
			$location->getInputFilter()->get('location')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'delivery_locations',
					'field'     => 'location',
					'adapter'   => $adapt,
					'message'   => 'Location Already exists',
					'exclude' => array(
							'field' => 'location',
							'value' => $val,
					)
			)
			));
			/* $location->getInputFilter()->get('unique_location_code')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'delivery_locations',
					'field'     => 'unique_location_code',
					'adapter'   => $adapt,
					'message'   => 'Location Already exists',
					'exclude' => array(
							'field' => 'unique_location_code',
							'value' => $val1,
					)
			)
			)); */
			//echo '<pre>';print_r($request->getPost());die;
			//$location->setAdapter($adapt);
			$form->setInputFilter($location->getInputFilter());
			$form->setData($request->getPost());
			if ($form->isValid()) {
			
				$location->exchangeArray($form->getData());
				$data_location=$this->getLocationTable()->saveLocation($location);
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$location_name=$location->location;
				
				/* $activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'location';
				$activity_log_data['action']= 'edit';
				$activity_log_data['description']= "Location : Location $location_name updated.";
				$this->getActivityLogTable()->saveActivityLog($activity_log_data); */

				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'location';
				$activity_log_data['action']= 'edit';
				$activity_log_data['description']= "Location : Location $location_name updated.";
				
				//$activity_log_data['description']= "'$location_name' location updated by $full_name";
				
				$libCommon->saveActivityLog($activity_log_data);

				$multilingual = $multilingual_code_model->saveMultilingualCodeSupport($_POST['other_language'], $id);

				$this->flashMessenger()->addSuccessMessage("Location updated successfully");
				// Redirect to list of albums
				return $this->redirect()->toRoute('location');
			}
			else
			{
				/* $messages = $form->getMessages();
				echo "<pre>";print_r($messages);echo "</pre>";die; */
			}
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Location",'breadcrumb'=>"Edit Location"));
		
		return array(
			'id' => $id,
			'form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages'],
    		'local_language_array' => $local_language_array
		);
	}
	/**
	 * To delete the delivery location of given location id
	 *
	 * @param int id
	 * @return route location
	 */
	public function deleteAction() {
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('location');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$select = new QSelect();
		$select->where(array('pk_location_code'=>$id));
		$locations = $this->getLocationTable()->fetchAll($select);
		$arrlocation = $locations->toArray();
		$location_name = $arrlocation[0]['location'];
		$location_status = ($arrlocation[0]['status'])=='1'?'deactivated':'activated';
		
		$data_location=$this->getLocationTable()->deleteLocation($id);
		($data_location) ?$this->flashMessenger()->addSuccessMessage("Location updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating location.");
		
		if($data_location)
		{

			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'location';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "Location : Location $location_name $location_status.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
		return $this->redirect()->toRoute('location');
	}

	public function updatedefaultAction() {
		$id = $_POST['id'];
		$result = $this->getLocationTable()->updateisdefault($id);
		echo $result;exit();
	}
    /**
     * Get instance of QuickServe\Model\MultilingualCodeSupportTable.
     * 
     * @method getMultilingualCodeSupportTable
     * @access public
     * @return QuickServe\Model\MultilingualCodeSupportTable
     */
    public function getMultilingualCodeSupportTable() {
    	if (!$this->multilingualCodeSupportTable) {
    		$sm = $this->getServiceLocator();
    		$this->multilingualCodeSupportTable = $sm->get('QuickServe\Model\MultilingualCodeSupportTable');
    	}
    	return $this->multilingualCodeSupportTable;
    }
	/**
	 * Get instance of QuickServe\Model\LocationTable
	 *
	 * @return QuickServe\Model\LocationTable
	 */
	public function getLocationTable()
	{
		if (!$this->locationTable) {
			$sm = $this->getServiceLocator();
			$this->locationTable = $sm->get('QuickServe\Model\LocationTable');
		}
		return $this->locationTable;
	}
	
	/* public function getActivityLogTable()
	{
		if (!$this->activitylogTable)
		{
			$sm = $this->getServiceLocator();
			$this->activitylogTable = $sm->get('QuickServe\Model\ActivityLogTable');
		}
		return $this->activitylogTable;
	} */
	
	public function getKitchenMasterTable() {
		if (!$this->kitchenmasterTable) {
			$sm = $this->getServiceLocator();
			$this->kitchenmasterTable = $sm->get('QuickServe\Model\KitchenMasterTable');
		}
		return $this->kitchenmasterTable;
	}

	public function getCityTable()
	{
	        if (!$this->citytable)
	        {
	                $sm = $this->getServiceLocator();
	                $this->citytable = $sm->get('QuickServe\Model\CityTable');
	        }
	        return $this->citytable;
	}
}
