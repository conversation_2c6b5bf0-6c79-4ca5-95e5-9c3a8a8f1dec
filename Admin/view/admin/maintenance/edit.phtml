<script src=/admin/js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>

      <style>
.changestylestatus label { width:auto; }
</style>
   
        <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Maintenance Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
          <?php 
          	 $form->setAttribute('action', $this->url('maintenance_crud', array('action' => 'edit','id'     => $this->id,)));
		 	 $form->setAttribute('class', 'stdform');
			 $form->prepare();
         	 echo $this->form()->openTag($form);
         ?>
        
            <p>
              <label><?php echo $this->formLabel($form->get('maintenance_name')); ?></label>
              <span class="field">
				 <?php 
              echo $this->formHidden($form->get('pk_maintenances_id'));
              echo $this->formElement($form->get('maintenance_name'));
              echo $this->formElementErrors($form->get('maintenance_name'));
              ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('description')); ?></label>
              <span class="field">
             <?php 
            echo $this->formElement($form->get('description'));
			echo $this->formElementErrors($form->get('description'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('price_plan')); ?></label>
              <span class="field">
               <?php echo $this->formElement($form->get('price_plan'));
					echo $this->formElementErrors($form->get('price_plan'));
			?>
              </span> </p>
			 <p>
              <label><?php echo $this->formLabel($form->get('number_of_pages')); ?></label>
              <span class="field">
            <?php 
            echo $this->formElement($form->get('number_of_pages'));
			echo $this->formElementErrors($form->get('number_of_pages'));
			?>
              </span> </p>
             <p>
              <label><?php echo $this->formLabel($form->get('time_period')); ?></label>
              <span class="field"> 
             <?php echo $this->formElement($form->get('time_period'));echo "  Days"; echo $this->formElement($form->get('csrf'));
					
			 ?>
              </span>
              <?php echo '<span class="red">'.$this->formElementErrors($form->get('time_period')).'</span>'; ?> </p>
              <label><?php echo $this->formLabel($form->get('maintenance_status')); ?></label>
              <span class="formwrapper changestylestatus">
                <?php echo $this->formElement($form->get('maintenance_status'));
					echo $this->formElementErrors($form->get('maintenance_status'));
			?>&nbsp;&nbsp;
             </span> </p>
            <p class="stdformbutton">
              <?php echo $this->formSubmit($form->get('submit')); ?>
              
            </p>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag(); ?>
          <br clear="all" />
          <br />
        </div>
      
      
     



