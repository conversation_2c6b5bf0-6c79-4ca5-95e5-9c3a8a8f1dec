import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Admin[id]UpdateStatus } from '@/components/admin-service-v12/[id]/update-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Admin[id]UpdateStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Admin[id]UpdateStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('Admin[id]UpdateStatus')).toBeInTheDocument();
  });
});