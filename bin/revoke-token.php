<?php
/**
 * Token Revocation Utility
 * 
 * This script revokes a JWT token by adding it to the blacklist.
 * 
 * Usage:
 * php bin/revoke-token.php <token>
 */

// Define application path
define('APPLICATION_PATH', realpath(__DIR__ . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/.env')) {
    $dotenv = new \Dotenv\Dotenv(APPLICATION_PATH);
    $dotenv->load();
}

// Import required classes
use Lib\QuickServe\Auth\JwtTokenUtil;

// Check if token is provided
if ($argc < 2) {
    echo "Usage: php bin/revoke-token.php <token>\n";
    exit(1);
}

// Get token from command line
$token = $argv[1];

// Output header
echo "===========================================\n";
echo "Token Revocation Utility\n";
echo "===========================================\n\n";

// Create JWT token utility
$jwtTokenUtil = new JwtTokenUtil();

// Decode token
$payload = $jwtTokenUtil->decodeToken($token);

if (!$payload) {
    echo "Error: Invalid token or token already expired.\n";
    exit(1);
}

// Check if token has JWT ID
if (!isset($payload['jti'])) {
    echo "Error: Token does not have a JWT ID (jti) claim.\n";
    exit(1);
}

// Get token ID and expiration
$tokenId = $payload['jti'];
$expiration = isset($payload['exp']) ? $payload['exp'] : (time() + 3600);

// Get token subject
$subject = isset($payload['sub']) ? $payload['sub'] : 'unknown';

// Blacklist token
$result = $jwtTokenUtil->blacklistToken($tokenId, $expiration);

if ($result) {
    echo "Token successfully revoked.\n";
    echo "Token ID: {$tokenId}\n";
    echo "Subject: {$subject}\n";
    echo "Expiration: " . date('Y-m-d H:i:s', $expiration) . "\n";
} else {
    echo "Error: Failed to revoke token.\n";
    exit(1);
}

echo "\n===========================================\n";
echo "Token revocation completed successfully!\n";
echo "===========================================\n";

// Make the script executable
chmod(__FILE__, 0755);
