openapi: 3.1.0
info:
  title: Catalogue Service API
  description: API for managing products, menus, carts, plan meals, and themes
  version: 1.0.0
servers:
  - url: /api/v2
    description: API v2 base URL
paths:
  /catalogue/products:
    get:
      summary: Get all products
      description: Returns a list of products with optional filtering
      parameters:
        - name: food_type
          in: query
          description: Filter by food type (veg, non-veg)
          schema:
            type: string
            enum: [veg, non-veg]
        - name: kitchen_id
          in: query
          description: Filter by kitchen ID
          schema:
            type: integer
        - name: status
          in: query
          description: Filter by status
          schema:
            type: boolean
        - name: product_category_id
          in: query
          description: Filter by product category ID
          schema:
            type: integer
        - name: per_page
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 15
      responses:
        '200':
          description: A list of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    post:
      summary: Create a new product
      description: Creates a new product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductInput'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product created successfully
                  data:
                    $ref: '#/components/schemas/Product'
  /catalogue/products/{id}:
    get:
      summary: Get a specific product
      description: Returns a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
    put:
      summary: Update a product
      description: Updates a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductInput'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product updated successfully
                  data:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
    delete:
      summary: Delete a product
      description: Deletes a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product deleted successfully
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
components:
  schemas:
    Product:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Vegetable Biryani
        description:
          type: string
          example: Delicious vegetable biryani with fresh vegetables and aromatic spices
        unit_price:
          type: number
          format: float
          example: 250.00
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        product_category_id:
          type: integer
          example: 2
        image_path:
          type: string
          example: products/veg-biryani.jpg
        product_subtype:
          type: string
          example: rice
        swap_with:
          type: integer
          nullable: true
          example: 5
        swap_charges:
          type: number
          format: float
          example: 50.00
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        kitchen_id:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        category:
          $ref: '#/components/schemas/ProductCategory'
        kitchen:
          $ref: '#/components/schemas/Kitchen'
    ProductInput:
      type: object
      required:
        - name
        - unit_price
        - food_type
        - product_category_id
        - kitchen_id
      properties:
        name:
          type: string
          example: Vegetable Biryani
        description:
          type: string
          example: Delicious vegetable biryani with fresh vegetables and aromatic spices
        unit_price:
          type: number
          format: float
          example: 250.00
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        product_category_id:
          type: integer
          example: 2
        image_path:
          type: string
          example: products/veg-biryani.jpg
        product_subtype:
          type: string
          example: rice
        swap_with:
          type: integer
          nullable: true
          example: 5
        swap_charges:
          type: number
          format: float
          example: 50.00
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        kitchen_id:
          type: integer
          example: 1
    ProductCategory:
      type: object
      properties:
        id:
          type: integer
          example: 2
        product_category_name:
          type: string
          example: Rice
        description:
          type: string
          example: All rice dishes
        image_path:
          type: string
          example: categories/rice.jpg
        type:
          type: string
          enum: [meal, product, extra]
          example: product
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Kitchen:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Main Kitchen
        description:
          type: string
          example: Main kitchen for all operations
        address:
          type: string
          example: 123 Main St
        city:
          type: string
          example: Mumbai
        state:
          type: string
          example: Maharashtra
        country:
          type: string
          example: India
        pincode:
          type: string
          example: 400001
        phone:
          type: string
          example: +91 9876543210
        email:
          type: string
          example: <EMAIL>
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        per_page:
          type: integer
          example: 15
        total:
          type: integer
          example: 75
