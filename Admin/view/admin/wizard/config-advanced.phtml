 <section class="wizard-info">
    <div class="row">
        <div class="col-sm-12">
            <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
            </ul>
        </div>
    </div>
    <div class="step-2-1">
        <div class="row">
            <div class="col-sm-12">
                <h4 class="info-about-table">Below are some additional features. You may choose to configure now or continue.</h4>
            </div>
        </div>
       <div class="mt-10 form-box">
            <form  id="form-container" name="form-container">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="clearfix form-head">
                            <div class="pull-left">
                               <h4 class="txt-color title">Additional features</h4>
                            </div>
                            <div class="pull-right">
                               <p class="txt-color title">Step 2 of 3</p>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <p class="qus"><i class="fa fa-check-circle circle-active"></i> Do you want to allow meal swapping after placing an order?</p>
                                <div class="div-flex">
                                    <div class="input-control radio default-style mb-10" data-role="input-control">
                                       <label class="pull-left mr10">
                                          <input type="radio" id="" name="GLOBAL_ALLOW_MEAL_SWAP" value="yes" <?php echo ($settings['GLOBAL_ALLOW_MEAL_SWAP'] == 'yes' ) ? 'checked' : '' ?> > <span class="check"></span> Yes
                                       </label>
                                        <span class='error' id="GLOBAL_ALLOW_MEAL_SWAP_err" style="color:#FC6E51"></span>
                                    </div>
                                    <div class="input-control radio default-style mb-10" data-role="input-control">
                                       <label class="pull-left mr10">
                                          <input type="radio" id="" name="GLOBAL_ALLOW_MEAL_SWAP" value="no" <?php echo ($settings['GLOBAL_ALLOW_MEAL_SWAP'] == 'no' ) ? 'checked' : '' ?> > <span class="check"></span> No
                                       </label>
                                    </div>
                                 </div>
                            </div>
                           <hr>
                           <div class="form-group">
                              <p class="qus"><i class="fa fa-check-circle circle-active"></i> Would you like to allow customers to set item preference for their subscription plans?</p>
                              <div class="div-flex">
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_ALLOW_MEAL_ITEM_SWAP" value="yes" <?php echo ($settings['GLOBAL_ALLOW_MEAL_ITEM_SWAP'] == 'yes' ) ? 'checked' : '' ?> > <span class="check"></span> Yes
                                    </label>
                                 </div>
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_ALLOW_MEAL_ITEM_SWAP" value="no" <?php echo ($settings['GLOBAL_ALLOW_MEAL_ITEM_SWAP'] == 'no' ) ? 'checked' : '' ?> > <span class="check"></span> No
                                    </label>
                                 </div>
                               </div>
                           </div>
                           <hr>
                           <div class="form-group">
                               <p class="qus"><i class="fa fa-check-circle circle-active"></i> Would you like to add a page for sides and extra items?</p>
                               <div class="div-flex">
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION" value="yes" <?php echo ($settings['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'] == 'yes' ) ? 'checked' : '' ?> > <span class="check"></span> Yes
                                    </label>
                                 </div>
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION" value="no" <?php echo ($settings['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'] == 'no' ) ? 'checked' : '' ?> > <span class="check"></span> No
                                    </label>
                                 </div>
                               </div>
                           </div>
                           <hr>
                           <div class="form-group">
                              <p class="qus"><i class="fa fa-check-circle circle-active"></i> Would you like to enable the kitchen screen (KOT) ?</p>
                              <div class="div-flex">
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                        <input type="radio" id="" name="GLOBAL_SKIP_KITCHEN" value="no" <?php echo ($settings['GLOBAL_SKIP_KITCHEN'] == 'yes' ) ? 'checked' : '' ?>> <span class="check"></span> Yes
                                    </label>
                                 </div>
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                        <input type="radio" id="" name="GLOBAL_SKIP_KITCHEN" value="yes"  <?php echo ($settings['GLOBAL_SKIP_KITCHEN'] == 'no' ) ? 'checked' : '' ?>> <span class="check"></span> No
                                    </label>
                                 </div>
                               </div>
                           </div>
                        </div>
                        <div class="col-sm-6">
                           <div class="form-group">
                              <p class="qus"><i class="fa fa-check-circle circle-active"></i> Choose one of the themes for you portal.</p>
                              <div class="form-group float-label-control" id="select-field">
                                    <input type="text" class="form-control" name="GLOBAL_THEME" value="<?php echo $settings['GLOBAL_THEME']?>">
                                    <label for="city">Themes</label>
<!--                                 <select class="form-control" id="city" name="theme">                                     
                                     <option value="<?php //echo $settings['GLOBAL_THEME']?>" <?php //echo ($settings['GLOBAL_THEME'] == 'sandwich') ? 'selected': ''; ?>><?php //echo $settings['GLOBAL_THEME'];?></option>
                                 </select>-->
                              </div>
                           </div>
                           <div class="form-group">
                               <p class="qus"><i class="fa fa-check-circle circle-active"></i> Would you like to enable instant ordering? </p>
                               <div class="div-flex">
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_ALLOW_INSTANT_ORDER" value="yes" <?php echo ( $settings['GLOBAL_ALLOW_INSTANT_ORDER'] == 'yes' ) ? 'checked' : '' ?>> <span class="check"></span> Yes
                                    </label>
                                 </div>
                                 <div class="input-control radio default-style mb-10" data-role="input-control">
                                    <label class="pull-left mr10">
                                       <input type="radio" id="" name="GLOBAL_ALLOW_INSTANT_ORDER" value="no" <?php echo ( $settings['GLOBAL_ALLOW_INSTANT_ORDER'] == 'no' ) ? 'checked' : '' ?>> <span class="check"></span> No
                                    </label>
                                 </div>
                               </div>
                           </div>
                            <hr>
                            <div class="form-group">
                                <p class="qus"><i class="fa fa-check-circle circle-active"></i> Would you like to enable parcel pickup from kitchen? <br>(Customers will give option to choose pickup / delivery while placing an order)</p>
                                <div class="div-flex">
                                  <div class="input-control radio default-style mb-10" data-role="input-control">
                                     <label class="pull-left mr10">
                                        <input type="radio" id="" name="GLOBAL_DELIVERY_TYPE" value="pickup" <?php echo (strpos($settings['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ? 'checked' : '' ?>> <span class="check"></span> Yes
                                     </label>
                                  </div>
                                  <div class="input-control radio default-style mb-10" data-role="input-control">
                                     <label class="pull-left mr10">
                                        <input type="radio" id="" name="GLOBAL_DELIVERY_TYPE" value="delivery" <?php echo (strpos($settings['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false ) ? '' : 'checked' ?>> <span class="check"></span> No
                                     </label>
                                  </div>
                                </div>
                            </div>
                            <hr>
                            <div class="form-group">
                                <p class="qus"><i class="fa fa-check-circle circle-active"></i> How many labels/stickers would you like to print per page?</p>
                                <div class="form-group float-label-control" id="" >
                                    <label for="">Labels/stickers per page</label>
                                    <select class="form-control" id="PRINT_LABEL" name="PRINT_LABEL_TEMPLATE">
                                        <?php foreach($label_templates as $template){?>
                                        <option value="<?php echo $template['pk_template_id'];?>" <?php echo ($template['pk_template_id'] == $settings['PRINT_LABEL_TEMPLATE']) ? 'selected' : ''; ?> ><?php echo $template['name']?></option>
                                        <?php }?>
                                    </select>
                               </div>
                            </div>
                         </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>
