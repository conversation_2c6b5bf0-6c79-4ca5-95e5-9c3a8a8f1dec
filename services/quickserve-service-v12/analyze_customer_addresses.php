<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 Analyzing Customer Address Format for Student Name Extraction\n";
echo "=" . str_repeat("=", 70) . "\n\n";

try {
    // Check 1: Sample customer addresses from orders
    echo "📋 Check 1: Sample Customer Addresses from Orders\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    $sampleAddresses = DB::table('orders')
        ->select('ship_address as customer_address', 'order_no', 'customer_code')
        ->whereNotNull('ship_address')
        ->where('ship_address', '!=', '')
        ->distinct()
        ->limit(20)
        ->get();
    
    if ($sampleAddresses->isEmpty()) {
        echo "❌ No customer addresses found in orders table\n";
    } else {
        echo "✅ Found customer addresses in orders table:\n\n";
        foreach ($sampleAddresses as $index => $address) {
            echo "Address " . ($index + 1) . ":\n";
            echo "  Full Address: '{$address->customer_address}'\n";
            
            // Try different delimiters
            $delimiters = [',', ';', '|', '-', ' '];
            
            foreach ($delimiters as $delimiter) {
                $parts = explode($delimiter, $address->customer_address);
                if (count($parts) > 1) {
                    $firstPart = trim($parts[0]);
                    echo "  Split by '{$delimiter}': '{$firstPart}' (+ " . (count($parts) - 1) . " more parts)\n";
                }
            }
            
            echo "  Customer Code: {$address->customer_code}\n";
            echo "  Order No: {$address->order_no}\n";
            echo "  ---\n";
        }
    }
    echo "\n";
    
    // Check 2: Address pattern analysis
    echo "📋 Check 2: Address Pattern Analysis\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    $allAddresses = DB::table('orders')
        ->select('ship_address as customer_address')
        ->whereNotNull('ship_address')
        ->where('ship_address', '!=', '')
        ->distinct()
        ->limit(100)
        ->pluck('customer_address');
    
    $patterns = [
        'comma_separated' => 0,
        'semicolon_separated' => 0,
        'pipe_separated' => 0,
        'dash_separated' => 0,
        'space_only' => 0,
        'no_delimiter' => 0
    ];
    
    $studentNameExamples = [];
    
    foreach ($allAddresses as $address) {
        if (strpos($address, ',') !== false) {
            $patterns['comma_separated']++;
            $parts = explode(',', $address);
            $studentNameExamples['comma'][] = trim($parts[0]);
        } elseif (strpos($address, ';') !== false) {
            $patterns['semicolon_separated']++;
            $parts = explode(';', $address);
            $studentNameExamples['semicolon'][] = trim($parts[0]);
        } elseif (strpos($address, '|') !== false) {
            $patterns['pipe_separated']++;
            $parts = explode('|', $address);
            $studentNameExamples['pipe'][] = trim($parts[0]);
        } elseif (strpos($address, '-') !== false) {
            $patterns['dash_separated']++;
            $parts = explode('-', $address);
            $studentNameExamples['dash'][] = trim($parts[0]);
        } elseif (strpos($address, ' ') !== false) {
            $patterns['space_only']++;
            $parts = explode(' ', $address);
            $studentNameExamples['space'][] = trim($parts[0]);
        } else {
            $patterns['no_delimiter']++;
            $studentNameExamples['no_delimiter'][] = $address;
        }
    }
    
    echo "Address Pattern Distribution:\n";
    foreach ($patterns as $pattern => $count) {
        $percentage = round(($count / count($allAddresses)) * 100, 1);
        echo "  {$pattern}: {$count} addresses ({$percentage}%)\n";
    }
    echo "\n";
    
    // Check 3: Student name examples by delimiter
    echo "📋 Check 3: Student Name Examples by Delimiter\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    foreach ($studentNameExamples as $delimiter => $names) {
        if (!empty($names)) {
            $uniqueNames = array_unique($names);
            echo "Delimiter '{$delimiter}' - " . count($names) . " addresses (" . count($uniqueNames) . " unique names):\n";
            
            // Show first 10 examples
            $examples = array_slice($uniqueNames, 0, 10);
            foreach ($examples as $name) {
                echo "  - '{$name}'\n";
            }
            
            if (count($uniqueNames) > 10) {
                echo "  (+" . (count($uniqueNames) - 10) . " more unique names)\n";
            }
            echo "\n";
        }
    }
    
    // Check 4: Recommended delimiter
    echo "📋 Check 4: Recommended Approach\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    $maxPattern = array_keys($patterns, max($patterns))[0];
    $maxCount = max($patterns);
    $maxPercentage = round(($maxCount / count($allAddresses)) * 100, 1);
    
    echo "Most common pattern: {$maxPattern} ({$maxCount} addresses, {$maxPercentage}%)\n";
    
    if ($maxPattern === 'comma_separated') {
        echo "✅ Recommended: Use comma (,) as primary delimiter\n";
        echo "✅ Implementation: explode(',', \$address)[0]\n";
    } elseif ($maxPattern === 'space_only') {
        echo "✅ Recommended: Use space ( ) as primary delimiter\n";
        echo "✅ Implementation: explode(' ', \$address)[0]\n";
    } else {
        echo "✅ Recommended: Use multiple delimiters with priority\n";
        echo "✅ Implementation: Try comma first, then space, then other delimiters\n";
    }
    
    // Check 5: Test extraction function
    echo "\n📋 Check 5: Test Student Name Extraction Function\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    function extractStudentName($address) {
        if (empty($address)) {
            return '';
        }
        
        // Try different delimiters in order of preference
        $delimiters = [',', ';', '|', '-'];
        
        foreach ($delimiters as $delimiter) {
            if (strpos($address, $delimiter) !== false) {
                $parts = explode($delimiter, $address);
                return trim($parts[0]);
            }
        }
        
        // If no delimiter found, try space but only take first word
        $parts = explode(' ', $address);
        return trim($parts[0]);
    }
    
    echo "Testing extraction function on sample addresses:\n";
    foreach (array_slice($sampleAddresses->toArray(), 0, 10) as $address) {
        $extractedName = extractStudentName($address->customer_address);
        echo "  '{$address->customer_address}' → '{$extractedName}'\n";
    }
    
    // Check 6: Validation
    echo "\n📋 Check 6: Validation and Quality Check\n";
    echo "-" . str_repeat("-", 70) . "\n";
    
    $extractedNames = [];
    $emptyNames = 0;
    $shortNames = 0;
    $longNames = 0;
    
    foreach ($allAddresses as $address) {
        $extractedName = extractStudentName($address);
        $extractedNames[] = $extractedName;
        
        if (empty($extractedName)) {
            $emptyNames++;
        } elseif (strlen($extractedName) < 2) {
            $shortNames++;
        } elseif (strlen($extractedName) > 50) {
            $longNames++;
        }
    }
    
    $uniqueExtractedNames = array_unique($extractedNames);
    
    echo "Extraction Quality:\n";
    echo "  Total addresses processed: " . count($allAddresses) . "\n";
    echo "  Unique student names extracted: " . count($uniqueExtractedNames) . "\n";
    echo "  Empty names: {$emptyNames}\n";
    echo "  Very short names (< 2 chars): {$shortNames}\n";
    echo "  Very long names (> 50 chars): {$longNames}\n";
    
    $successRate = round(((count($allAddresses) - $emptyNames - $shortNames - $longNames) / count($allAddresses)) * 100, 1);
    echo "  Success rate: {$successRate}%\n";
    
    echo "\nSample extracted student names:\n";
    foreach (array_slice($uniqueExtractedNames, 0, 15) as $name) {
        if (!empty($name) && strlen($name) >= 2 && strlen($name) <= 50) {
            echo "  - '{$name}'\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Summary:\n";
echo "- Analyze customer address patterns\n";
echo "- Determine best delimiter for student name extraction\n";
echo "- Test extraction function\n";
echo "- Validate extraction quality\n";
echo "- Provide implementation recommendation\n";
