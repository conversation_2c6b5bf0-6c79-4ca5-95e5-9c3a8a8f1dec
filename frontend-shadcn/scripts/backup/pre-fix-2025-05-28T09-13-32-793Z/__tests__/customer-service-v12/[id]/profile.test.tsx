import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Profile } from '@/components/customer-service-v12/[id]/profile';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Profile', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Profile />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Profile')).toBeInTheDocument();
  });
});