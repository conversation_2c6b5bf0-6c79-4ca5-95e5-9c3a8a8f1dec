<?php

namespace App\Http\Controllers\Api\V1;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\DeliveryStatusUpdateRequest;
use App\Http\Requests\OrderDeliveryRequest;
use App\Services\Contracts\DeliveryServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DeliveryController extends Controller
{
    /**
     * @var \App\Services\Contracts\DeliveryServiceInterface
     */
    private DeliveryServiceInterface $deliveryService;
    
    /**
     * Create a new controller instance.
     *
     * @param \App\Services\Contracts\DeliveryServiceInterface $deliveryService
     */
    public function __construct(DeliveryServiceInterface $deliveryService)
    {
        $this->deliveryService = $deliveryService;
    }
    
    /**
     * Get orders for delivery.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrders(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $locationId = $request->input('location_id');
            $date = $request->input('date', date('Y-m-d'));
            
            $orders = $this->deliveryService->getDeliveryOrders($userId, $locationId, $date);
            
            // Format response to match legacy API
            $formattedOrders = [];
            foreach ($orders as $order) {
                $formattedOrders[] = [
                    'pk_order_no' => $order->pk_order_no,
                    'order_no' => $order->order_no,
                    'customer_name' => $order->customer_name,
                    'customer_phone' => $order->customer_phone,
                    'ship_address' => $order->ship_address,
                    'order_date' => $order->order_date,
                    'delivery_status' => $order->delivery_status,
                    'order_status' => $order->order_status,
                    'location' => $order->location ? $order->location->location : null,
                    'city' => $order->location ? $order->location->city : null,
                    'sub_city_area' => $order->location ? $order->location->sub_city_area : null,
                    'pin' => $order->location ? $order->location->pin : null,
                    'amount' => $order->amount,
                    'tax' => $order->tax,
                    'delivery_charges' => $order->delivery_charges,
                    'applied_discount' => $order->applied_discount,
                    'total' => $order->total,
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $formattedOrders,
                'pagination' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
    
    /**
     * Search orders.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchOrders(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;
            $searchTerm = $request->input('search');
            $locationId = $request->input('location_id');
            
            if (empty($searchTerm)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search term is required',
                ], 400);
            }
            
            $orders = $this->deliveryService->searchOrders($userId, $searchTerm, $locationId);
            
            // Format response to match legacy API
            $formattedOrders = [];
            foreach ($orders as $order) {
                $formattedOrders[] = [
                    'pk_order_no' => $order->pk_order_no,
                    'order_no' => $order->order_no,
                    'customer_name' => $order->customer_name,
                    'customer_phone' => $order->customer_phone,
                    'ship_address' => $order->ship_address,
                    'order_date' => $order->order_date,
                    'delivery_status' => $order->delivery_status,
                    'order_status' => $order->order_status,
                    'location' => $order->location ? $order->location->location : null,
                    'city' => $order->location ? $order->location->city : null,
                    'sub_city_area' => $order->location ? $order->location->sub_city_area : null,
                    'pin' => $order->location ? $order->location->pin : null,
                    'amount' => $order->amount,
                    'tax' => $order->tax,
                    'delivery_charges' => $order->delivery_charges,
                    'applied_discount' => $order->applied_discount,
                    'total' => $order->total,
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $formattedOrders,
                'pagination' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
    
    /**
     * Update delivery status.
     *
     * @param \App\Http\Requests\DeliveryStatusUpdateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateDeliveryStatus(DeliveryStatusUpdateRequest $request): JsonResponse
    {
        try {
            $dto = new DeliveryStatusUpdateDTO(
                $request->input('order_id'),
                $request->user()->id,
                $request->input('order_completed', false)
            );
            
            $result = $this->deliveryService->updateDeliveryStatus($dto);
            
            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'success',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update delivery status',
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
