<?php

namespace App\Providers;

use App\Repositories\Contracts\DeliveryRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Repositories\DeliveryRepository;
use App\Repositories\LocationRepository;
use App\Services\Contracts\DeliveryServiceInterface;
use App\Services\DeliveryService;
use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use App\Services\ThirdPartyDelivery\YourGuy;
use Illuminate\Support\ServiceProvider;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(DeliveryRepositoryInterface::class, DeliveryRepository::class);
        $this->app->bind(LocationRepositoryInterface::class, LocationRepository::class);
        
        // Register services
        $this->app->bind(DeliveryServiceInterface::class, DeliveryService::class);
        $this->app->bind(TPDeliveryInterface::class, YourGuy::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
