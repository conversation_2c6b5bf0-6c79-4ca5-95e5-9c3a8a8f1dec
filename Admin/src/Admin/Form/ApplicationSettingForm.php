<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\Utility;

class ApplicationSettingForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('setting');
        $this->service_locator = $sm;
        $utility = Utility::getInstance ();
		$setting_session = new Container( "setting" );
		$setting = $setting_session->setting;
		$currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);  

        $this->setAttribute('method', 'post');
        
        $this->add(array(
            'name' => 'id',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes' => array(
                            'id' => 'id',
            ),
            'options' => array(
                            'label' => 'undefined',
            ),
        ));
        
        
        
        $this->add(array(
            'name'=>'CATALOGUE_STATUS',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                //'class'=>'selectpicker small',
                'id' => 'CATALOGUE_STATUS',
            ),
            'options'=>array(
                'label'=>'Catalogue Status<span class="red">*</span>',
                'label_attributes' => array(
                                'class' => 'inline right mr5',
                ),
                'value_options' => array(
                                'online' => 'Online',
                                'offline' => 'Offline',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
      
        $this->add(array(
            'name' => 'GLOBAL_CUSTOMER_PAYMENT_MODE',
            'type' => 'Zend\Form\Element\MultiCheckbox',
            'options' => array(
                'label' => 'Payment Modes<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right mr5',
                ),
                'attributes' => array(
                    'id' => 'GLOBAL_CUSTOMER_PAYMENT_MODE',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            ),
        ));
        
        $options = array(
                    
                    array(
                        'value' => 'neft',
                        'label' => 'NEFT',
                        'attributes' => array(
                                'class'=>"neftcheck" ,
                                'data-close'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    ),
                    array(
                        'value' => 'cheque',
                        'label' => 'Cheque',
                        'attributes' => array(
                                'class'=>"chequecheck" ,
                                'data-close'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    ),
                   
                );
        
        
        
        if($utility->checkSubscription('customer_wallet','allowed')){
            $wallet = array(
                'value' => 'wallet',
                'label' => 'Wallet',
                'attributes' => array(
                        'class'=>"walletcheck" ,
                        'data-open'=>".onlinerow"
                ),
                'label_attributes' => array(
                        'class'  => 'inline left mr5',
                        'style'=>'line-height: 0.9em;'
                ),
            );
            
            array_push($options,$wallet);
        }
        
        if($utility->checkSubscription('payment_online','allowed')){
            $online =  array(
                        'value' => 'online',
                        'label' => 'Pay Online',
                        'attributes' => array(
                                'class'=>"onlinecheck" ,
                                'data-open'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    );
            
            array_push($options,$online);
        }
        
        if($utility->checkSubscription('payment_cod','allowed')){
            $cash = array(
                        'value' => 'cash',
                        'label' => 'Cash',
                        'attributes' => array(
                                'class'=>"cashcheck" ,
                                'data-close'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    );
            
            array_push($options,$cash);
        }
        $this->get('GLOBAL_CUSTOMER_PAYMENT_MODE')->setValueOptions($options);
        
        $this->add(array(
            'name'=>'FOOD_TYPE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                
                'id' => 'FOOD_TYPE',
            ),
            'options'=>array(
                'label'=>'Food Type <span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right',
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'GLOBAL_MIN_ORDER_PRICE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter amount',
                'id' => 'GLOBAL_MIN_ORDER_PRICE',
            ),
            'options'=>array(
                'label'=>'Minimum Order Amount ('.$currencySymbol.')<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        
        $this->add(array(
            'name'=>'GLOBAL_MAX_ORDER_PRICE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter amount',
                'id' => 'GLOBAL_MAX_ORDER_PRICE',
            ),
            'options'=>array(
                'label'=>'Maximum Amount for COD ('.$currencySymbol.')<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
    
        $this->add(array(
            'name'=>'ADMIN_WEB_URL',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'http://www.fooddialer.com/',
                'id' => 'ADMIN_WEB_URL',
            ),
            'options'=>array(
                'label'=>'Fooddialer Web URL<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'CLIENT_WEB_URL',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'http://www.fooddialer.com/	',
                'id' => 'CLIENT_WEB_URL',
            ),
            'options'=>array(
                'label'=>'Client Web URL<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        
        $this->add(array(
            'name'=>'MERCHANT_COMPANY_NAME',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter company name',
                'id' => 'MERCHANT_COMPANY_NAME',
            ),
            'options'=>array(
                'label'=>'Company Name<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name' => 'MERCHANT_POSTAL_ADDRESS',
            'type' => 'Zend\Form\Element\Textarea',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Postal Address...',
                'id' => 'MERCHANT_POSTAL_ADDRESS',
                //'required' => 'required',
            ),
            'options' => array(
                'label' => 'Postal Address<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
            ),
        ));
        
        $this->add(array(
            'name'=>'CATALOGUE_MOBILE_APP_VERSION',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'v1,v2,v3',
                'id' => 'CATALOGUE_MOBILE_APP_VERSION',
            ),
            'options'=>array(
                'label'=>'App Version<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        
        $this->add(array(
            'name'=>'RESTAURANT_MOBILE_APP_VERSION',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'v1,v2,v3',
                'id' => 'RESTAURANT_MOBILE_APP_VERSION',
            ),
            'options'=>array(
                'label'=>'Restaurant Version<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'FORCE_CUSTOMER_TO_USE_PASSWORD',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'FORCE_CUSTOMER_TO_USE_PASSWORD',
            ),
            'options'=>array(
                'label'=>'Force Customer to Use Password<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'DATE_FORMAT',
            'type' => 'Zend\Form\Element\Select',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'dd/mm/yy, mm/dd/yy, yy/mm/dd',
                //	  'multiple' => 'multiple',
            ),
            'options'=>array(
                'label'=>'Date Format<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'value_options' => array(
                    'd-m-Y' => 'dd-mm-yyyy',
                    'd/m/Y' => 'dd/mm/yyyy',
                    'm/d/Y' => 'mm/dd/yyyy',
                    'm-d-Y' => 'mm-dd-yyyy',
                    'F j, Y' => 'mmm dd,yyyy',
                    'm.d.y' => 'm.d.y',
                    'j F, Y' => 'dd mmm yyyy',
                ),
                'label_options' => array('disable_html_escape' => true),

            )
        ));
        
         $this->add(array(
            'name'=>'MERCHANT_BANK_ACCOUNT_NAME',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter account name',
                'id' => 'MERCHANT_BANK_ACCOUNT_NAME',
            ),
            'options'=>array(
                'label'=>'Bank Account Name<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        
       
        $this->add(array(
            'name'=>'MERCHANT_BANK_ACCOUNT_NO',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter account number',
                'id' => 'MERCHANT_BANK_ACCOUNT_NO',
            ),
            'options'=>array(
                'label'=>'Bank Account No.<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'MERCHANT_BANK_NAME',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter bank name',
                'id' => 'MERCHANT_BANK_NAME',
            ),
            'options'=>array(
                'label'=>'Bank Name<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
      
        $this->add(array(
            'name'=>'MERCHANT_BANK_IFSC_CODE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter IFSC code',
                'id' => 'MERCHANT_BANK_IFSC_CODE',
            ),
            'options'=>array(
                'label'=>'IFSC Code<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name' => 'MERCHANT_BANK_BRANCH_ADDRESS',
            'type' => 'Zend\Form\Element\Textarea',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter branch address...',
                'id' => 'MERCHANT_BANK_BRANCH_ADDRESS',
                //'required' => 'required',
            ),
            'options' => array(
                'label' => 'Branch Address<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
            ),
        ));
        

        
        $this->add(array(
            'name'=>'ORDER_EXPIRY_SMS_DAYS_BEFORE',
            'type' => 'Zend\Form\Element\Select',
            'attributes'=>array(
                'class'=>'small timePicker',
                'id' => 'ORDER_EXPIRY_SMS_DAYS_BEFORE'
                //	  'multiple' => 'multiple',
            ),
            'options'=>array(
                'label'=>'Order Expiry First Reminder<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'value_options' => array(
                    '0' => '1 Day Before',
                    '1' => '2 Day Before',
                    '2' => '3 Day Before',
                    '3' => '4 Day Before',
                    '4' => '5 Day Before',
                    '5' => '6 Day Before',
                    '6' => '7 Day Before',
                    '7' => '8 Day Before',
                    '8' => '9 Day Before',
                    '9' => '10 Day Before',
                ),
                'label_options' => array('disable_html_escape' => true),

            )
        ));
        
        
        
        $this->add(array(
            'name'=>'PRINT_LABEL_TEMPLATE',
            'type' => 'Zend\Form\Element\Select',
            'attributes'=>array(
                'class'=>'small timePicker',
                'id' => 'PRINT_LABEL_TEMPLATE'
                //	  'multiple' => 'multiple',
            ),
            'options'=>array(
                'label'=>'Print Label Template<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'value_options' =>$this->getLabelTemplate(),               
                'label_options' => array('disable_html_escape' => true),

            )
        ));
        
        $this->add(array(
            'name'=>'ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND',
            'type' => 'Zend\Form\Element\Select',
            'attributes'=>array(
                'class'=>'small timePicker',
                'id' => 'ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND'
                //	  'multiple' => 'multiple',
            ),
            'options'=>array(
                'label'=>'Order Expiry Second Reminder<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'value_options' => array(
                    '0' => '1 Day Before',
                    '1' => '2 Day Before',
                    '2' => '3 Day Before',
                    '3' => '4 Day Before',
                    '4' => '5 Day Before',
                    '5' => '6 Day Before',
                    '6' => '7 Day Before',
                    '7' => '8 Day Before',
                    '8' => '9 Day Before',
                    '9' => '10 Day Before',
                ),
                'label_options' => array('disable_html_escape' => true),

            )
        ));        
        
        $this->add(array(
            'name'=>'PRINT_LABEL',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABLE',
            ),
            'options'=>array(
                'label'=>'Dispatch Label<span class="red">*</span>',
                'label_attributes' => array(
                                'class' => 'inline right mr5',
                ),
                'value_options' => array(
                                'mealwise' => 'Mealwise',
                                'orderwise' => 'Orderwise',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_CUSTOMER_PHONE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_CUSTOMER_PHONE',
            ),
            'options'=>array(
                'label'=>'Show Customer Phone No<span class="red">*</span>',
                'label_attributes' => array(
                                'class' => 'inline right mr5',
                ),
                'value_options' => array(
                                'yes' => 'Yes',
                                'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_DIBBAWALA_CODE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_DIBBAWALA_CODE',
            ),
            'options'=>array(
                'label'=>'Show Dibbawala Code<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                    'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_ITEM_DETAILS',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_ITEM_DETAILS',
            ),
            'options'=>array(
                'label'=>'Show Product Details<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_BARCODE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_BARCODE',
            ),
            'options'=>array(
                'label'=>'Show Barcode<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_MERCHANT_PHONE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_MERCHANT_PHONE',
            ),
            'options'=>array(
                'label'=>'Show Merchant Phone No.<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_MERCHANT_WEBSITE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_MERCHANT_WEBSITE',
            ),
            'options'=>array(
                'label'=>'Show Merchant Website<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_DELIVERY_PERSON',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_DELIVERY_PERSON',
            ),
            'options'=>array(
                'label'=>'Show Delivery Person<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_TEXT_COLOR',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter color',
                'id' => 'PRINT_LABEL_SHOW_TEXT_COLOR',
            ),
            'options'=>array(
                'label'=>'Text Color',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_NONVEG_DAY_COLOR',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'placeholder' => 'Enter day:#color-code',
                'id' => 'PRINT_LABEL_SHOW_NONVEG_DAY_COLOR',
            ),
            'options'=>array(
                'label'=>'Non Veg Day Color',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE',
            ),
            'options'=>array(
                'label'=>'Customer Preference <span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_PRICE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_PRICE',
            ),
            'options'=>array(
                'label'=>'Show Price <span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_SHOW_DELIVERY_TYPE',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_SHOW_DELIVERY_TYPE',
            ),
            'options'=>array(
                'label'=>'Show Delivery Type <span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'PRINT_LABEL_ORDER_BY',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                'class'=>'selectpicker small',
                'id' => 'PRINT_LABEL_ORDER_BY',
            ),
            'options'=>array(
                'label'=>'Display Order By <span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'no' => ' Default',
                    'customer' => ' By Customer',
                    'location' => ' By Location',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
            'name'=>'GLOBAL_CATALOG_BY_CATEGORY',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                //'class'=>'selectpicker small',
                'id' => 'GLOBAL_CATALOG_BY_CATEGORY',
            ),
            'options'=>array(
                'label'=>'Show Catalogue by Category<span class="red">*</span>',
                'label_attributes' => array(
                                'class' => 'inline right mr5',
                ),
                'value_options' => array(
                                'yes' => 'Yes',
                                'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));

        $this->add(array(
            'name'=>'GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                //'class'=>'selectpicker small',
                'id' => 'GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION',
            ),
            'options'=>array(
                'label'=>'Skip Extra/Add-on Page<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));

        

        $this->add(array(
            'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
            'type' => 'Zend\Form\Element\Radio',
            'attributes'=>array(
                //'class'=>'selectpicker small',
                'id' => 'SHOW_PRODUCT_AND_MEAL_CALENDAR',
            ),
            'options'=>array(
                'label'=>'Show Product/Meal Calendar<span class="red">*</span>',
                'label_attributes' => array(
                    'class' => 'inline right mr5',
                ),
                'value_options' => array(
                    '1' => 'Yes',
                    '0' => 'No',
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true,
            )
        ));
        
        $this->add(array(
           'name'=>'MERCHANT_SENDER_ID',
           'type' => 'Zend\Form\Element\Text',
           'attributes'=>array(
               'class'=>'small',
               'placeholder' => 'SMS Sender Id',
               'id' => 'MERCHANT_SENDER_ID',
           ),
           'options'=>array(
               'label'=>'SMS Sender Id<span class="red">*</span>',
               'label_attributes' => array(
                   'class'  => 'inline right'
               ),
               'label_options' => array('disable_html_escape' => true),
           )
       ));
        
         $this->add(array(
            'name'=>'MERCHANT_SUPPORT_EMAIL',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Support Email Id',
                'id' => 'MERCHANT_SUPPORT_EMAIL',
            ),
            'options'=>array(
                'label'=>'Support Email Id<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));

         $this->add(array(
            'name'=>'MERCHANT_WORKING_HOURS',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Company Working Hours',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Company Working Hours<span class="red">*</span>',
                'label_attributes' => array(
                                'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
         $this->add(array(
            'name'=>'GLOBAL_SOCIAL_MEDIA_FACEBOOK',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Your Facebook ID',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Facebook',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));

         $this->add(array(
            'name'=>'GLOBAL_SOCIAL_MEDIA_INSTAGRAM',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Your Instagram ID',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Instagram',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
         $this->add(array(
            'name'=>'GLOBAL_SOCIAL_MEDIA_TWITTER',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Your Twitter ID',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Twitter',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
         $this->add(array(
            'name'=>'GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Your Google Plus ID',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Google_plus',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
         $this->add(array(
            'name'=>'GOOGLE_TRACKING_ID',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Your Google Tracking ID',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'Google Tracking ID',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
           $this->add(array(
            'name'=>'GLOBAL_APP_STORE_PAGE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'App Store Page',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
            $this->add(array(
            'name'=>'GLOBAL_PLAY_STORE_PAGE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small timePicker',
                'id' => 'MERCHANT_WORKING_HOURS',
            ),
            'options'=>array(
                'label'=>'App Play store Page',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        ));
         $this->add(array(
            'name'=>'SIGNATURE_COMPANY_NAME',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Company Email Signature',
                'id' => 'SIGNATURE_COMPANY_NAME',
            ),
            'options'=>array(
                'label'=>'Company Email Signature<span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        )); 

        $this->add(array(
            'name'=>'MERCHANT_GST_NO',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Company GST Number',
                'id' => 'MERCHANT_GST_NO',
            ),
            'options'=>array(
                'label'=>'Company GST Number',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        )); 

         $this->add(array(
            'name'=>'GLOBAL_WEBSITE_PHONE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Company Phone',
                'id' => 'GLOBAL_WEBSITE_PHONE',
            ),
            'options'=>array(
                'label'=>'Company Phone <span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        )); 

         $this->add(array(
            'name'=>'CONTACTUS_GOOGLE_LATITUDE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Google Latitude',
                'id' => 'CONTACTUS_GOOGLE_LATITUDE',
            ),
            'options'=>array(
                'label'=>'Google Map Latitude <span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        )); 

         $this->add(array(
            'name'=>'CONTACTUS_GOOGLE_LONGITUDE',
            'type' => 'Zend\Form\Element\Text',
            'attributes'=>array(
                'class'=>'small',
                'placeholder' => 'Enter Google Longitude',
                'id' => 'CONTACTUS_GOOGLE_LONGITUDE',
            ),
            'options'=>array(
                'label'=>'Google Map Longitude <span class="red">*</span>',
                'label_attributes' => array(
                    'class'  => 'inline right'
                ),
                'label_options' => array('disable_html_escape' => true),
            )
        )); 
        
         
        $this->add(array(
            'name'=>'backurl',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes'=>array(
                'id'=>'backurl',
                'value'=>'/dashboard',
            ),
        ));
        
        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
            'name'=>'submit',
            'attributes'=>array(
                'type'=>'submit',
                'value'=>'Save',
                'class'=>'dark-greenBg',
                'id' => 'submitbutton',
            )
        ));
        
        $this->add(array(
            'name' => 'cancel',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Cancel',
                'id' => 'cancelbutton',
                'class'=>'button left tiny left5 redBg'

            ),
        ));
        
        $this->add(array(
            'name'=>'backurl',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes'=>array(
                'id'=>'backurl',
                'value'=>'/setting/application-setting',
            ),
        ));
        
    }

   
    public function getLabelTemplate(){
        $sql = new QSql($this->service_locator);
           
        $select = $sql->select();
        $select->from("label_templates");
        
        $result = $sql->execQuery($select);
        
        foreach($result as $val){                
            $returnData[$val['pk_template_id']] = $val['name'];
        }        
        
        return $returnData; 
    }
}

