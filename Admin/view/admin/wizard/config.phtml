<section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
        </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Step 1: Update initial configuration for your catalogue.<br>
             <span class="x-font-size">(Update the basic configurations to enable your kitchen dependencies)</span></h4>
         </div>
       </div>
       <div class="mt-10 form-box">
        <form  id="form-container" name="form-container">
          <div class="row">
             <div class="col-sm-12">
             <div class="clearfix form-head">
                   <div class="pull-left">
                      <h4 class="txt-color title">Catalogue Setup</h4>
                   </div>
                   <div class="pull-right">
                      <p class="txt-color title">Step 1 of 3</p>
                   </div>
                </div>
                <div class="col-sm-6">
                   <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> What type of menu you offer to customers?</p>

                      <?php foreach($menu_types as $menu){ ?>
                      <div class="input-control checkbox mb0">
                         <label>
                            <input type="checkbox" name="K1_MENU_TYPE" class="third" value="<?php echo $menu?>"  <?php echo (in_array($menu, $settings['K1_MENU_TYPE'])) ? 'checked' : '' ?>>
                            <span class="check"></span> <?php echo ucfirst($menu);?>
                         </label>
                      </div>
                      <?php } ?>
                      <?php foreach($menu_types as $menu){ ?>
                      <div class="form-group <?php echo $menu;?>-field <?php echo in_array($menu, $settings['K1_MENU_TYPE']) ? 'show': 'hide';?> ">
                         <fieldset>
                            <legend>
                               <?php echo ucfirst($menu);?> Settings:
                            </legend>
                            <div class="form-group float-label-control">
                               <label for="">Order cut off day</label>
                               <select class="form-control" name="K1_<?php echo strtoupper($menu);?>_ORDER_CUT_OFF_DAY"> 
                                <option value = "0" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 0) ? 'selected' : ''; ?>>Same Day</option>
                                <option value = "1" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 1) ? 'selected' : ''; ?>>One Day Before</option>
                                <option value = "2" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 2) ? 'selected' : ''; ?>>Two Day Before</option>
                                <option value = "3" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 3) ? 'selected' : ''; ?>>Three Day Before</option>
                                <option value = "4" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 4) ? 'selected' : ''; ?>>Four Day Before</option>
                                <option value = "5" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 5) ? 'selected' : ''; ?>>Five Day Before</option>
                                <option value = "6" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 6) ? 'selected' : ''; ?>>Six Day Before</option>
                                <option value = "7" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'] == 7) ? 'selected' : ''; ?>>Seven Day Before</option>
                               </select>
                            </div>
                            <div class="form-group float-label-control">
                               <label for="">Order cancel cut off day</label>
                               <select class="form-control"  name="K1_<?php echo strtoupper($menu);?>_ORDER_CANCEL_CUT_OFF_DAY">
                                <option value = "0" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CANCEL_CUT_OFF_DAY'] == 0) ? 'selected' : ''; ?>>Same Day</option>
                                <option value = "1" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CANCEL_CUT_OFF_DAY'] == 1) ? 'selected' : ''; ?>>One Day Before</option>
                                <option value = "2" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CANCEL_CUT_OFF_DAY'] == 2) ? 'selected' : ''; ?>>Two Day Before</option>
                                <option value = "3" <?php echo ($settings['K1_'.strtoupper($menu).'_ORDER_CANCEL_CUT_OFF_DAY'] == 3) ? 'selected' : ''; ?>>Three Day Before</option>
                               </select>
                            </div>
                            <div class="form-group float-label-control">
                               <label for="">Auto delivery</label>
                               <select class="form-control"  name="K1_<?php echo strtoupper($menu);?>_AUTO_DELIVERY">
                                <option value = "0" <?php echo ($settings['K1_'.strtoupper($menu).'_AUTO_DELIVERY'] == 0) ? 'selected' : ''; ?>>Same Day</option>
                                <option value = "1" <?php echo ($settings['K1_'.strtoupper($menu).'_AUTO_DELIVERY'] == 1) ? 'selected' : ''; ?>>One Day After</option>
                                <option value = "2" <?php echo ($settings['K1_'.strtoupper($menu).'_AUTO_DELIVERY'] == 2) ? 'selected' : ''; ?>>Two Day After</option>
                                <option value = "3" <?php echo ($settings['K1_'.strtoupper($menu).'_AUTO_DELIVERY'] == 3) ? 'selected' : ''; ?>>Three Day After</option>
                               </select>
                            </div>
                         </fieldset>
                      </div>
                      <?php }?>
                   </div>
                   <hr>
                    <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> What type of food do you serve?</p>
                       <div class="input-control checkbox mb0">
                          <label>
                             <input type="checkbox" class="third" name="food_type" value="veg" <?php echo ( in_array('veg',$settings['FOOD_TYPE']) ) ? 'checked' : '' ?> >
                             <span class="check"></span> Veg
                          </label>
                       </div>
                       <div class="input-control checkbox mb0">
                          <label>
                             <input type="checkbox" class="third"  name="food_type" value="nonveg" <?php echo ( in_array('nonveg',$settings['FOOD_TYPE']) ) ? 'checked' : '' ?> >
                             <span class="check"></span> Non-Veg
                          </label>
                       </div>
                       <div class="input-control checkbox mb0">
                          <label>
                             <input type="checkbox" class="third"  name="food_type" value="jain" <?php echo ( in_array('jain',$settings['FOOD_TYPE']) ) ? 'checked' : '' ?> >
                             <span class="check"></span> Jain
                          </label>
                       </div>

                  </div>
                   <hr>
                   <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> How many days do you serve your food?</p>
                      <div class="form-group float-label-control" > 
                            <label for="">Select working days</label>
                             <select class="form-control" id="weekoff-list" name="WEEKOFF">
                                 <option value="mf"  <?php echo ($_SESSION['wizard']['WEEKOFF'] == mf) ? 'selected' : ''; ?>>Monday - Friday</option>
                                 <option value="ms" <?php echo ($_SESSION['wizard']['WEEKOFF'] == ms) ? 'selected' : ''; ?>>Monday - Saturday</option>
                                 <option value="msu" <?php echo ($_SESSION['wizard']['WEEKOFF'] == msu) ? 'selected' : ''; ?>>Monday - Sunday</option>
                             </select>
                      </div>
                   </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> Are tax inclusive or exclusive in food price?</p>
                       <div class="div-flex">
                          <div class="input-control radio default-style mb-10" data-role="input-control">
                             <label class="pull-left mr10">
                                <input type="radio" id="" name="GLOBAL_TAX_METHOD" value="exclusive" <?php echo (isset($settings['GLOBAL_TAX_METHOD']) && $settings['GLOBAL_TAX_METHOD'] == 'exclusive') ? 'checked' : '' ?> > <span class="check"></span> Exclusive                                
                             </label>                              
                          </div>
                          <div class="input-control radio default-style mb-10" data-role="input-control">
                             <label class="pull-left mr10">
                                <input type="radio" id="" name="GLOBAL_TAX_METHOD" value="inclusive" <?php echo (isset($settings['GLOBAL_TAX_METHOD']) && $settings['GLOBAL_TAX_METHOD'] == 'inclusive') ? 'checked' : '' ?> > <span class="check"></span> Inclusive                                
                             </label>                              
                          </div>
                        </div>
                       <span class ="error" id="GLOBAL_TAX_METHOD_err" style="color:#FC6E51"></span>
                    </div>
                    <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> What is the minimum order amount customer can buy in single order?</p>
                      <div class="form-group float-label-control">
                         <input type="text" class="form-control empty" name="GLOBAL_MIN_ORDER_PRICE" value="<?php echo $settings['GLOBAL_MIN_ORDER_PRICE']; ?>">
                         <label for="">eg: Rs. 100 </label>
                      </div>
                    </div>
                    <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> What is the maximum order amount customer can buy for CASH-ON-DELIVERY ?</p>
                       <div class="form-group float-label-control">
                          <input type="text" class="form-control empty" name="GLOBAL_MAX_ORDER_PRICE" value="<?php echo $settings['GLOBAL_MAX_ORDER_PRICE']; ?>">
                          <label for="">eg: Rs.100 </label>
                       </div>
                    </div>
                </div>
             </div>
          </div> 
        </form>    
       </div>
    </div>
 </section>

<script >
$(document).ready(function() {
    
    var menu_types = <?php echo json_encode($menu_types) ?>;
    
    $('input[type="checkbox"]').click(function() {
            
            var ele = this;
            $.each(menu_types, function(i, menu){
                if ($(ele).attr("value") == menu) {
                    if($(ele).is(':checked')){
                        $("."+menu+"-field").removeClass('hide').addClass('show');
                    }else{
                         $("."+menu+"-field").removeClass('show').addClass('hide');
                    }
                }
            });          
    });
});

</script>