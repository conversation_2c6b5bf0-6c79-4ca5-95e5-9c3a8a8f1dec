import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueMetrics } from '@/components/catalogue-service-v12/metrics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueMetrics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueMetrics />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueMetrics')).toBeInTheDocument();
  });
});