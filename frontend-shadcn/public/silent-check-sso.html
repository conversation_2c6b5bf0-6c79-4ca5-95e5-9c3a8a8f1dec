<!DOCTYPE html>
<html>
<head>
    <title>Silent SSO Check</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <script>
        // This page is used by Keycloak for silent SSO checks
        // It should be served from the same domain as the main application
        
        // Get the parent window reference
        const parent = window.parent;
        
        if (parent && parent !== window) {
            try {
                // Extract the authentication result from the URL
                const urlParams = new URLSearchParams(window.location.search);
                const fragment = window.location.hash.substring(1);
                const fragmentParams = new URLSearchParams(fragment);
                
                // Combine search and fragment parameters
                const allParams = new URLSearchParams();
                for (const [key, value] of urlParams) {
                    allParams.set(key, value);
                }
                for (const [key, value] of fragmentParams) {
                    allParams.set(key, value);
                }
                
                // Send the result back to the parent window
                const result = {
                    type: 'keycloak-silent-check-sso',
                    success: !allParams.has('error'),
                    error: allParams.get('error'),
                    errorDescription: allParams.get('error_description'),
                    code: allParams.get('code'),
                    state: allParams.get('state'),
                    sessionState: allParams.get('session_state')
                };
                
                parent.postMessage(result, window.location.origin);
            } catch (error) {
                console.error('Silent SSO check error:', error);
                
                // Send error result to parent
                parent.postMessage({
                    type: 'keycloak-silent-check-sso',
                    success: false,
                    error: 'silent_check_error',
                    errorDescription: error.message
                }, window.location.origin);
            }
        }
    </script>
</body>
</html>
