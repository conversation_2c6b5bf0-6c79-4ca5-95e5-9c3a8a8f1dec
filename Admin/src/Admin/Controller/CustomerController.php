<?php
/**
 * This File used to manage customers
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;

//use Front\Form\FrontForm;
use QuickServe\Model\CustomerValidator;


use Zend\View\Model\JsonModel;
use Zend\Session\Container;

use PHPExcel;
use PHPExcel_IOFactory;
use DOMPDFModule\View\Model\PdfModel;

use Lib\QuickServe\Db\Sql\QSelect;

use Lib\S3;
use Lib\Utility;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\QuickServe\Catalogue as QSCatalogue;

use QuickServe\Model\ImportCustomerValidator;
use QuickServe\Model\LocationValidator;

use Admin\Form\WalletForm;
use Admin\Form\FilterForm;
use Admin\Form\ImportCustomerForm;
use Admin\Form\CustomerForm;


class CustomerController extends AbstractActionController
{
    /**
     * It has an instance of QuickServe\Model\OrderTable model
     *
     * @var QuickServe\Model\OrderTable $ordertable
     */
    protected $ordertable;
    /**
     * It has an instance of QuickServe\Model\CustomerTable model
     *
     * @var QuickServe\Model\CustomerTablee $customertable
     */
    protected $customertable;
    /**
     * It has an instance of AuthService model
     *
     * @var AuthService $authservice
     */
    protected $authservice;

    /**
     * It has an instance of QuickServe\Model\WalletTable model
     *
     * @var QuickServe\Model\WalletTable $wallettable
     */
    protected $wallettable;

    /**
     * It has an instance of v\Model\UserTable model
     *
     * @var QuickServe\Model\UserTable $userTable
     */
    protected $userTable;
    
    /**
     * To displays the list of customers
     * @method indexAction()
     * @method getLocationData() to get all location data
     * @method getAllDeliveryPerson() to get all delivery persons
     * @return \Zend\View\Model\ViewModel
     */
    public function indexAction(){

    if (! $this->authservice)
    {
        $this->authservice = $this->getServiceLocator()
        ->get('AuthService');
    }

    $iden = $this->authservice->getIdentity();
    
    $session_setting = new Container('setting');

    $menus = $session_setting->setting['MENU_TYPE'];

    $search_form = new FilterForm($this->getServiceLocator());
   
    $search_form->getForm();
    
    $menus = $session_setting->setting['MENU_TYPE'];
    $client_web_url = $session_setting->setting['CLIENT_WEB_URL'];
    $client_redirection = (isset($session_setting->setting['CLIENT_REDIRECTION']) ? $session_setting->setting['CLIENT_REDIRECTION'] : 'no') ;
    $location_data=$this->getOrderTable()->getLocationData();
     
    $deliverypersons=$this->getuserTable()->getAllDeliveryPerson();

    $layoutviewModel = $this->layout();
    $acl =$layoutviewModel->acl;
    $loguser = $layoutviewModel->loggedUser;

    $this->layout()->setVariables(array('page_title'=>"Customers",'description'=>"All Customers",'breadcrumb'=>"Customers"));
    return new ViewModel(array(
        'acl' => $acl,
        'loggedUser' => $loguser,
        'menus' => $menus,
        'client_web_url' => $client_web_url,
        'client_redirection' => $client_redirection, 
        'locations' =>$location_data,
        'deliverypersons' => $deliverypersons,
        'flashMessages'=> $this->flashMessenger()->getMessages(),
        'filter'	=> $search_form,
    ));
}

/**
 * use to get list of customer
 * @method ajxCustomerAction()
 * @method fetchAll($select,$page,$filter) to get customer detailed information
 * @param string menu
 * @param string location
 * @param string deliverypers
 * @param string order_date
 * @return \Zend\View\Model\JsonModel
 */
public function ajxCustomerAction()
{
    if (! $this->authservice)
        {
   $this->authservice = $this->getServiceLocator()
   ->get('AuthService');
    }

    $menu = $this->params()->fromQuery('menu');
    $location = $this->params()->fromQuery('location');
    $deliverypers = $this->params()->fromQuery('deliverypers');
    $order_date = $this->params()->fromQuery('order_date');
    $status = $this->params()->fromQuery('status');

    $filter = array(
        'menu' => $menu,
        'location' => $location,
        'deliveryperson' => $deliverypers,
        'orderdate' => $order_date,	
        'status' => $status
    );
    
    $session_setting = new Container("setting");
    $setting = $session_setting->setting;

    $utility = Utility::getInstance();

    $layout = $this->layout();
    $acl = $layout->acl;

    $viewModel = new ViewModel();

    $loggedUser = $layout->loggedUser;

    $select = new QSelect();

    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
    $arrColumns = array('0'=>'pk_customer_code','1'=>'customer_name','2'=>'phone','3'=>'email_address',"4"=>"registered_on","5"=>"status");

    $order_by = $arrColumns[$arrOrder[0]['column']];
    $order = $arrOrder[0]['dir'];

    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
    $status = $this->params()->fromQuery('status');

    $columns = $this->params()->fromQuery('columns');


    $select->where(

        new \Zend\Db\Sql\Predicate\PredicateSet(
            array(
                new \Zend\Db\Sql\Predicate\Operator('customers.pk_customer_code', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('customers.customer_name', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('customers.phone', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('customers.email_address', 'LIKE', '%'.$search.'%'),
                new \Zend\Db\Sql\Predicate\Operator('customers.registered_on', 'LIKE', '%'.$search.'%'),
            ),
            // optional; OP_AND is default
            \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
        )

    );

        $select->order($order_by . ' ' . $order);

    $customers = $this->getCustomerTable()->fetchAll($select,$page,$filter);
    
    $customers->setCurrentPageNumber($page)
        ->setItemCountPerPage($itemsPerPage)
        ->setPageRange(7);
    
    $returnVar = array();
    $returnVar['draw'] = $draw;
    $returnVar['recordsTotal'] = $customers->getTotalItemCount();
    $returnVar['recordsFiltered'] = $customers->getTotalItemCount();
    $returnVar['data'] = array();

    foreach($customers as $customer){
        
        $arrTmp = array();

        array_push($arrTmp,$customer['pk_customer_code']);
        $customer_name="<a href=". $this->url()->fromRoute('customer', array('action' => 'order', 'id' => $customer['pk_customer_code'])).">".$customer['customer_name']."</a>";
        array_push($arrTmp,$customer_name);
        array_push($arrTmp,$customer['phone']);
        array_push($arrTmp,$customer['email_address']);

        array_push($arrTmp,$utility->displayDate($customer['registered_on'],$setting['DATE_FORMAT']));

        $status =  ($customer['customer_status']=="1" )? '<span class="active">Active</span>':'<span class="inactive">Inactive</span>';

        array_push($arrTmp,$status);

        $str = "";
        $textadd = ($customer['customer_status']) == "0"? 'Activate' :'Deactivate';

            if($acl->isAllowed($loggedUser->rolename,'customer','edit')){ 

                 $str .= '<button class="smBtn blueBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('customer', array('action'=>'edit', 'id' => $customer['pk_customer_code'])).'\'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button>';
             } 

            if($acl->isAllowed($loggedUser->rolename,'customer','delete')){ 
            $str .=	' <a href="'.$this->url()->fromRoute('customer',array('action'=>'delete', 'id' => $customer['pk_customer_code'])).'" onclick="return confirm(\'Are you sure you want to '.$textadd.' this customer ?\')">';
            if($textadd=='Deactivate'){
                    $str .= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"  data-text-swap="Wait.."><i class="fa fa-ban"></i></button>';
            }else{
                    $str.='<button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
            }
            $str.= '</a>';            
            }
            if($customer['customer_status']=="1")
            {
                if($utility->checkSubscription('customer_wallet','allowed')){
                    $str .= '<button class="smBtn greenBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('customer', array('action'=>'paymentoption', 'id' => $customer['pk_customer_code'])).'\'" data-tooltip title="Add Amount" data-text-swap="Wait.."><i class="fa fa-money"></i></button>';
                }
            }


            if(isset($customer['phone']) && $customer['phone']!="")
            {
                $phone = $customer['phone'];
            }
            else if(isset($customer['email_address']) && $customer['email_address']!="")
            {
                $phone =  "'".$customer['email_address']."'";
            }
            //echo "<pre>";print_r($phone);die;
            //$str .='<button class="clsorderbk smBtn greenBg has-tip tip-top" onClick="location.href=\''.$this->url()->fromRoute('backorder', array('action'=>'index', 'phone' => $phone)).'\'" data-tooltip title="Place Order" data-text-swap="Wait.."><i class="fa fa-caret-square-o-right"></i></button>';
            if($customer['customer_status']=="1")
            {
                $rolename = "'".$_SESSION['Zend_Auth']['storage']->rolename."'";
                $name = "'".$_SESSION['Zend_Auth']['storage']->first_name." ".$_SESSION['Zend_Auth']['storage']->last_name."'";
                if($acl->isAllowed($loggedUser->rolename,'backorder','index')){
                    $str .='<button id="redirect" class="clsorderbk smBtn greenBg has-tip tip-top" data-phone='.$phone.' onClick="getUser('.$phone.', '.$rolename.' ,'.$name.', this);" data-tooltip title="Place Order" data-text-swap="Wait.."><i class="fa fa-cart-plus"></i></button>';
                }
            }

        array_push($arrTmp,$str);
        array_push($returnVar['data'],$arrTmp);
       
    }

    //echo "<pre>"; dd($returnVar); echo "</pre>"; die;

    return new JsonModel($returnVar);
}

public function unsetFlagAction(){
    unset($_SESSION['flag']);
    return new JsonModel($_SESSION);
}

/**
 * To add a new customer
 * @method getCustomerCount()  use to count customer
 * @method checkSubscription()  use to check whether user has subscribe to the email notification
 * @method saveCustomer()  use to save customer information into customer and customer address table
 * @method sendemailAuthenticationEmail()  use to send email authentication email
 * @method saveActivityLog() use to save activity log
 * @return \Zend\View\Model\ViewModel
 */

public function addAction()
{

    $flag = $this->params('flag');
    if($flag=="new-customer-backend")
    {
            $_SESSION['flag']="$flag";
    }
    $layoutviewModel = $this->layout();
    $acl =$layoutviewModel->acl;
    $loguser = $layoutviewModel->loggedUser;

    $selected_menu_arr=array();
    $new_arr_menu_err=array();
    $sm = $this->getServiceLocator();
    
    //$adapt = $sm->get('Write_Adapter');
    $libCustomer = QSCustomer::getInstance($sm);
    $libCommon = QSCommon::getInstance($sm);
    
    //$adapt = $sm->get('Write_Adapter');
    $form = new CustomerForm($sm);
                                
    $setting_session = new Container('setting');
    $setting = $setting_session->setting;		
    $error_message =array();

    $s3 = $sm->get('S3');
    $bucketName = $s3::$bucketInfo['bucket'];
    $bucketFolder = $setting['S3_BUCKET_URL'];
    $hostname = $s3->getHostname();

    $menus_arr = $setting['MENU_TYPE'];
    $thirdparty= (isset($setting['THIRDPARTY']))?$setting['THIRDPARTY']:'';
    $utility = Utility::getInstance();

    $form->get('submit')->setAttribute('value', 'Save');
    
    $request = $this->getRequest();
    
    $errStr = "";

    $loc_code = "";
    $loc_name = "";

    $lunch_code = "";
    $lunch_name = "";

    $dinner_code = "";
    $dinner_name = "";

    $group_code = 0;
    $group_name = "";

    $city = "";
    $city_name = "";

    $checkedflg = false;
//	echo "<pre>"; print_r($form->get('delivery_person[]')); die();
    if ($request->isPost())
     {	
        if($request->getPost('city')!=''){

                $arr3 =explode('#', $_POST['city']);
                $city = $arr3[0];
                $city_name = $arr3[1];
            }

         if($_POST['group_code']!="" && $_POST['group_code']!=0){
                $arr4 =explode('#', $_POST['group_code']);
                $group_code = $arr4[0];
                $group_name = $arr4[1];
            }

                $selected_menu_arr=$request->getPost('menus');
                if(empty($selected_menu_arr))
                {
                    $error_message['menu_error']="Please select aleast one address";
                }

                $data =array(
                    'location_code' =>	$loc_code,
                    'location_name' => $loc_name,
                    'lunch_code' => $lunch_code,
                    'lunch_name' => $lunch_name,
                    'dinner_code' => $dinner_code,
                    'dinner_name' => $dinner_name,
                    'city' =>$city,
                    'city_name' => $city_name,
                    'group_code'=> $group_code,
                    'group_name' => $group_name,
                    'thirdparty' =>$thirdparty

                );
               
                $customer = new CustomerValidator();
                if($request->getPost('sameadd')=='on'){
                    $checkedflg = true;
                } else{
                    $checkedflg = false;
                }
                if($_POST['email_address']=='' && $_POST['phone']==''){
                    $customer->getInputFilter()->get('phone')->setRequired(true);
                    $customer->getInputFilter()->get('email_address')->setRequired(false);
                    $form->setValidationGroup('customer_name','phone','location_code','status','registered_on','city','company_name','dabbawala_code','food_preference');

                }
                elseif(isset($_POST['phone']) && $_POST['phone']!='' && $_POST['email_address']=='' ){
                    $customer->getInputFilter()->get('phone')->setRequired(true);
                    $customer->getInputFilter()->get('email_address')->setRequired(false);
                    $form->setValidationGroup('customer_name','phone','location_code','status','registered_on','city','company_name','dabbawala_code','food_preference');

                }
                elseif(isset($_POST['email_address']) && $_POST['email_address']!='' && $_POST['phone']==''){

                    $customer->getInputFilter()->get('email_address')->setRequired(true);
                    $customer->getInputFilter()->get('phone')->setRequired(false);
                    $form->setValidationGroup('customer_name','email_address','location_code','status','registered_on','city','company_name','dabbawala_code','food_preference');
                }
                else{
                    $form->setValidationGroup('customer_name','phone','email_address','location_code','status','registered_on','city','company_name','food_preference');
                }
                
                $customer->getInputFilter()->get('phone')
                    ->getValidatorChain()                  // Filters are run second w/ FileInput
                    ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                            'table'     => 'customers',
                            'field'     => 'phone',
                            'adapter'   => $sm->get('Read_Adapter'),
                            'message'   => 'Phone already exists',
                        )
                        ));

                $customer->getInputFilter()->get('email_address')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                    'table'     => 'customers',
                    'field'     => 'email_address',
                    'adapter'   => $sm->get('Read_Adapter'),
                    'message'   => 'Email address already exists',

                )
                ));
                
                //$customer->setAdapter($adapt);
                
                $post = array_merge(
                    $this->getRequest()->getPost()->toArray(),
                    $this->getRequest()->getFiles()->toArray()
                );
                


                $form->setInputFilter($customer->getInputFilter());
                $form->setData($post);
                
                $customerCount = $this->getCustomerTable()->getCustomerCount();
                $subscriptionCheck = $utility->checkSubscription("customer_active",'count',$customerCount['count']);

                if(!$subscriptionCheck){

                    $errStr = " Maximum no. of customer limit has reached ";
                    goto SHOWDETAILS;
                }

                if ($post['dabbawala_code_type'] == 'image' && ($_FILES['dabbawala_image']['error'] == 0)) {
                    $customer->addImageFilter();
                }

                $selected_menu_arr1 = $selected_menu_arr;


                $selected_menu_arr=array();
                foreach($selected_menu_arr1 as $selmenukey=>$selmenuval){
                    foreach($menus_arr as $menuarrkey=>$menuarrval){
                        if($menuarrval==$selmenuval){
                            $selected_menu_arr[$menuarrkey]=$selmenuval;
                        }
                    }
                }

                $location_code_arr=$request->getPost('location_code');
                $location_address_arr=$request->getPost('location_address');
                $delivery_location_arr=$request->getPost('delivery_person');
                $dabbawala_code_type=$request->getPost('dabbawala_code_type');
                $dabbawala_code_text=$request->getPost('dabbawala_text');

//                                echo "<pre>"; print_r( $dabbawala_code_text); die();
                $dabbawala_code_image=$post['dabbawala_image'];	
                $new_array=array();
                $new_arr_menu_err=array();
                $new_arr_dabbawala=array();

//                                echo "<pre>"; print_r( $request); die();

                foreach($dabbawala_code_type as $dbcodetypekey=>$dbcodetypeval)
                {
                    foreach($dabbawala_code_text as $dbcodetextkey=>$dbcodetextval)
                    {
                        foreach($dabbawala_code_image as $dbcodeimgkey=>$dbcodeimgval)
                        {
                            if($dbcodetypekey==$dbcodetextkey && $dbcodetextkey==$dbcodeimgkey)
                            {
                                $new_arr_dabbawala[$dbcodetypekey]['dabawala_type']=$dbcodetypeval;
                                $new_arr_dabbawala[$dbcodetypekey]['dabawala_image']=$dbcodeimgval;
                                $new_arr_dabbawala[$dbcodetypekey]['dabawala_text']=$dbcodetextval; 
                            }
                        }
                    }
                }                               
//				 echo "<pre>"; print_r( $new_arr_dabbawala); die();
                foreach($location_code_arr as $lococodekey=>$loccodeval)
                {
                    foreach($location_address_arr as $locaddkey=>$locaddval)
                    {
                        if($lococodekey==$locaddkey)
                        {                            
                            if($locaddval=="" && $loccodeval!="")
                            {
                                $new_array[$lococodekey]['error']="Please enter address";
                                $new_array[$lococodekey]['error_address']="Please enter address";
                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                $new_array[$lococodekey]['location_address']=$locaddval;
                            }
                            else if($locaddval!="" && $loccodeval=="")
                            {
                                $new_array[$lococodekey]['error']="Please select location";
                                $new_array[$lococodekey]['error_location']="Please select location";
                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                $new_array[$lococodekey]['location_address']=$locaddval;
                            }
                            else if($locaddval=="" && $loccodeval=="")
                            {
                                $new_array[$lococodekey]['error']="Please enter location & address";
                                $new_array[$lococodekey]['error_location']="Please select location";
                                $new_array[$lococodekey]['error_address']="Please enter address";
                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                $new_array[$lococodekey]['location_address']=$locaddval;
                            }
                            else if($locaddval!="" && $loccodeval!="")
                            {
                                $new_array[$lococodekey]['error']="";
                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                $new_array[$lococodekey]['location_address']=$locaddval;
                            }
                        }
                    }
                }
                
                $validRegex = new \Zend\Validator\Regex(array('pattern' => "/^[a-zA-Z0-9\s\,\*\?\_\.\@\#\'\']+$/"));
                $tmpFlg = 0;
                $checkForValid = true;
                foreach($location_code_arr as $lococodekey=>$loccodeval)
                {
                    foreach($location_address_arr as $locaddkey=>$locaddval)
                    { 
                        switch($locaddkey) {               
                            case 'location_address[]':                    
                            if($checkForValid){ 
                                if((isset($locaddval) && !empty($locaddval))){                              
                                    if(!$tmpFlg && !$validRegex->isValid($locaddval)){                                 
                                        $tmpFlg = 1;
                                        $str = "Please enter valid  address.";                                
                                    }                                                       
                                }
                            }
                        }
                    }
                }

               // dd($tmpFlg);
                foreach($new_array as $newarrkey=>$newarrval)
                {
                    foreach($selected_menu_arr as $selmenuarrkey=>$selmenuarrval)
                    {
                        if($newarrkey==$selmenuarrkey)
                        {
                            if($newarrval['error']!="")
                            { 
                                if($newarrval['location_code']=="" && $newarrval['location_address']!="")
                                {
                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                        $error_message[]="Please select $selmenuarrval code";
                                }
                                else if($newarrval['location_address']=="" && $newarrval['location_code']!="")
                                {
                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                        $error_message[]="Please enter $selmenuarrval address";
                                }                                
                                else if($newarrval['location_code']=="" && $newarrval['location_address']=="")
                                {
                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                        $error_message[]="Please select $selmenuarrval code & enter address";
                                }
                            }
                            else
                            {
                                $new_arr_menu_err[$selmenuarrval]=$newarrval;
                            }
                        }
                    }
                }



                foreach($new_arr_menu_err as $newarrmenukey=>$newarrmenuval)
                {
                    foreach($delivery_location_arr as $dellocarrkey=>$dellocarrval)
                    {
                        if($newarrmenukey==$dellocarrkey)
                        {
                                $new_arr_menu_err[$newarrmenukey]['delivery_person'] = $dellocarrval;
                        }
                    }
                }

                foreach ($new_arr_menu_err as $new_arr_menu_err_key=>$new_arr_menu_err_val)
                {
                    foreach($new_arr_dabbawala as $new_arr_dabbawala_key=>$new_arr_dabbawala_val)
                    {
                        if($new_arr_menu_err_key == $new_arr_dabbawala_key)
                        {
                                $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_type']=$new_arr_dabbawala_val['dabawala_type'];
                                $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_image']=$new_arr_dabbawala_val['dabawala_image']['name'];
                                $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_text']=$new_arr_dabbawala_val['dabawala_text'];
                        }
                    }
                }
               
                if ($form->isValid() && empty($error_message))
                {
                    $fdata = $form->getData();
                    $customer->exchangeArray($post);

                    //suraj change start
                    $customer->registered_from = 'Admin';
                    $customer->phone_verified = 1;
                    $customer->isguest = 'N';
                    //suraj change end                                        
                  //echo "<pre>"; print_r($customer); die();
                    $data_customer=$libCustomer->saveCustomer($customer);
                   
                         
//				echo "<pre>"; print_r($customer); die();
                    $cust_result = array();
                    foreach ($data_customer as $key=>$value)
                    {
                            $cust_result[$key] = $value;
                    }
                    $cust_session = new Container('customer');
                    $cust_session->customer = $cust_result;
                    
                    $res = $libCommon->sendWelcomeSMS();
             
                    ($data_customer) ?$this->flashMessenger()->addSuccessMessage("Customer added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Customer.");

                    if($data_customer)
                    {
                        $save_customer_id = $data_customer['pk_customer_code'];						
                        $imageDir = $_SERVER['DOCUMENT_ROOT']."/data/";
                        if(!is_dir($imageDir."$save_customer_id")){
                            mkdir($imageDir."$save_customer_id",0755);
                            chmod($imageDir."/"."$save_customer_id/", 0777);
                        }

                        foreach($_FILES['dabbawala_image']['name'] as $n=>$name) {
                                if($_FILES['dabbawala_image']['error'][$n]==0)
                            {
                                $image_name = $_FILES['dabbawala_image']['name'][$n];
                                move_uploaded_file($_FILES['dabbawala_image']['tmp_name'][$n],$imageDir."$save_customer_id/".$image_name);

                                $uri = $bucketFolder."/dabbawala/".$image_name;
                                $uploadFile = $imageDir."$save_customer_id/".$image_name;

                                $old_image = basename($uploadFile);

                                // Deleting old image file before uploading new image.
                                $delete_uri = $bucketFolder."/dabbawala/".$old_image;
                                if($s3->getObject($bucketName, $delete_uri)) {
                                        $s3->deleteObject($bucketName, $delete_uri);
                                }
                                // Adding new image file with public read access
                                if($s3->putObjectFile($uploadFile, $bucketName, $uri, S3::ACL_PUBLIC_READ)){
                                    if(file_exists($uploadFile)){
                                            unlink($uploadFile);
                                    }
                                    else{
                                        exit("\nError: No such file for delete: $uploadFile\n\n");
                                    }
                                }
                                    else{
                                        exit("\nError: Uploading file : $uploadFile on aws\n\n");
                                    }								
                            }
                        }
                    }

                    $customer_code = $data_customer['pk_customer_code'];
                    $libCommon->sendemailAuthenticationEmail($customer_code);
                    $cust_session->customer = (array) $data_customer;
                    $full_name= $data_customer['customer_name'];

                    if($data_customer['phone']=="")
                    {
                            $phone = $data_customer['email_address'];
                    }
                    else
                    {
                            $phone = $data_customer['phone'];
                    }

                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']= $loguser->pk_user_code; 
                    $activity_log_data['context_name']= $loguser->first_name." ".$loguser->last_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'Customer';
                    $activity_log_data['action']= 'add';
                    $activity_log_data['description']= "Customer : $full_name new account created.";
                    $libCommon->saveActivityLog($activity_log_data);

                    //echo "<pre>fgfg"; print_r($data_customer); die;

                    $libsession =  $libCustomer->createSession($customer_code, 'admin');

                    if ( $libsession ) {
                            echo "<script type=\"text/javascript\">window.location.href='/customer';window.open('/', '_blank');</script>";

                    }

                    //return $this->redirect()->toRoute('customer');

                }
                 else
                {
                     
//                        echo '<pre>';print_r($form->getMessages());exit;
                }  
    }

    SHOWDETAILS:

    $this->layout()->setVariables(array('page_title'=>"Add Customer",'breadcrumb'=>"Add Customer"));
    return array(
        'checkedflg'=>$checkedflg,
        'form' => $form,
        'errStr'=>$errStr,
        'thirdparty' => $thirdparty,
        'menus' => $menus_arr,
        'new_arr_menu_err'=>$new_arr_menu_err,
        'selected_menu_arr' =>$selected_menu_arr,
        'error_message' => $error_message
    );
}


/**
 * use to get delivery person list based on location
 * @method ajaxDeliveryPersonAction()
 * @method getDPbyLocation($loc) use to get delivery person information based on location
 * @param string location
 * @return \Zend\View\Model\JsonModel
 */
public function ajaxDeliveryPersonAction() {
   $request = $this->getRequest();
   $loc=$request->getPost('location');
   $dp=$this->getUserTable()->getDPbyLocation($loc); 
   return new JsonModel($dp);
}

/**
 * To update customer using specified customer id
 * @param int id
 * @method getCustomer() use to get customer information
 * @method getCustomerAddress() use to get customer address information
 * @method saveCustomer($customerValidator) use to save updated customer information
 * @method deleteCustomerAddress($main_delete_array,$id) use to delete existing customer address
 * @method saveActivityLog($activity_log_data) use to save activity data of a customer
 * @return \Zend\View\Model\ViewModel
 */
public function editAction()
{
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        $errStr="";
        $error_message=array();
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;

        $thirdparty= (isset($setting['THIRDPARTY']))?$setting['THIRDPARTY']:'';

        $id = (int) $this->params('id');
        if (!$id)
        {
                return $this->redirect()->toRoute('customer', array('action' => 'add'));
        }

        $sm = $this->getServiceLocator();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $s3 = $sm->get('S3');
        $bucketName = $s3::$bucketInfo['bucket'];
        $bucketFolder = $setting['S3_BUCKET_URL'];
        $hostname = $s3->getHostname();

        $menus_arr = $setting['MENU_TYPE'];

        $customer = $libCustomer->getCustomer($id,'id');
        $customer_address = $libCustomer->getCustomerAddress($id);
        $customer_address_menus = $customer_address['addresses'];

        if(!empty($customer_address_menus)){
            foreach($customer_address_menus as $custaddrmenukey=>$custaddrmenuval)
            {
                $selected_menu_arr[$custaddrmenuval['pk_customer_address_code']]=$custaddrmenuval['menu_type'];
            }
        }   
        
        if(!empty($menus_arr)){
            foreach($menus_arr as $menuarrkeysel=>$menuarrvalsel)
            {   
                if($customer_address['addresses'] != ''){
                    foreach($customer_address['addresses'] as $custaddrkey=>$custaddrval)
                    {
                        if($menuarrvalsel==$custaddrval['menu_type'])
                        {
                            $new_arr_menu_err[$menuarrkeysel]['location_code']=$custaddrval['location_code']."#".$custaddrval['location_name'];
                            $new_arr_menu_err[$menuarrkeysel]['location_address']=$custaddrval['location_address'];
                            $new_arr_menu_err[$menuarrkeysel]['delivery_person']=$custaddrval['delivery_person_id'];
                            $new_arr_menu_err[$menuarrkeysel]['dabbawala_code_type']=$custaddrval['dabbawala_code_type'];
                            $new_arr_menu_err[$menuarrkeysel]['dabbawala_code']=$custaddrval['dabbawala_code'];
                            $new_arr_menu_err[$menuarrkeysel]['dabbawala_image']=$hostname."/".$bucketFolder."/dabbawala/".$custaddrval['dabbawala_image'];
                            $new_arr_menu_err[$menuarrkeysel]['menu_type']=$custaddrval['menu_type'];
                        }
                    }
                }
            }
        }    
        $config_variables = $sm->get('config');
        $form = new CustomerForm($sm);
        $form->bind($customer);
        $form->get('city')->setValue($customer['city'].'#'.$customer['city_name']);
        $form->get('isguest')->setValue($customer['isguest']);
        $form->get('group_code')->setValue($customer['group_code'].'#'.$customer['group_name']);

        $form->get('submit')->setAttribute('value', 'Edit');
        $valemail = $customer['email_address'];
        $valphone = $customer['phone'];
        $request = $this->getRequest();
        $checkedflg = false;

        if ($request->isPost())
        {

                $customerValidator = new CustomerValidator();

                $customerValidator->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'customers',
                                'field'     => 'phone',
                                'adapter'   => $sm->get('Read_Adapter'),
                                'message'   => 'Phone already exists',
                                'exclude' => array(
                                        'field' => 'phone',
                                        'value' => $valphone,
                                )
                )
                ));

                $customerValidator->getInputFilter()->get('email_address')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'customers',
                                'field'     => 'email_address',
                                'adapter'   => $sm->get('Read_Adapter'),
                                'message'   => 'Email address already exists',
                                'exclude' => array(
                                        'field' => 'email_address',
                                        'value' => $valemail,
                                )

                )
                ));

                $arr3 = explode('#',$_POST['city']);
                $city = $arr3[0];
                $city_name = $arr3[1];			
                $arr4 = explode('#',$_POST['group_code']);
                $group_code = $arr4[0];
                $group_name = $arr4[1];

                $data =array(
                        'city' => $city,
                        'city_name' => $city_name,
                        'group_code' =>$group_code,
                        'group_name' => $group_name,
                        'thirdparty' => $thirdparty,

                );
                //dd($data);
                //$customerValidator->setAdapter($adapt);

                if($request->getPost('sameadd')=='on'){
                        $checkedflg = true;
                } else{
                        $checkedflg = false;
                }


                if($_POST['email_address']=='' && $_POST['phone']==''){
                    $customerValidator->getInputFilter()->get('phone')->setRequired(true);
                    $customerValidator->getInputFilter()->get('email_address')->setRequired(false);
                    $form->setValidationGroup('pk_customer_code','customer_name','phone','status','isguest','registered_on','city','company_name','food_preference');

                }
                elseif(isset($_POST['phone']) && $_POST['phone']!='' && $_POST['email_address']=='' ){
                    $customerValidator->getInputFilter()->get('phone')->setRequired(true);
                    $customerValidator->getInputFilter()->get('email_address')->setRequired(false);
                    $form->setValidationGroup('pk_customer_code','customer_name','phone','status','isguest','registered_on','city','company_name','food_preference');

                }
                elseif(isset($_POST['email_address']) && $_POST['email_address']!='' && $_POST['phone']==''){

                    $customerValidator->getInputFilter()->get('email_address')->setRequired(true);
                    $customerValidator->getInputFilter()->get('phone')->setRequired(false);
                    $form->setValidationGroup('pk_customer_code','customer_name','email_address','status','isguest','registered_on','city','company_name','food_preference');
                }
                else{
                        $form->setValidationGroup('pk_customer_code','customer_name','phone','email_address','status','isguest','registered_on','city','company_name','food_preference');
                }

                $selected_menu_arr=$request->getPost('menus');

                /*
                if(empty($selected_menu_arr))
                {
                    $error_message['menu_error']="Please select aleast one address";
                }
                */

                $post = array_merge(
                    $this->getRequest()->getPost()->toArray(),
                    $this->getRequest()->getFiles()->toArray()
                );

                $form->setInputFilter($customerValidator->getInputFilter());

                $form->setData($post);

                if ($post['dabbawala_code_type'] == 'image' && ($customerValidator->dabbawala_image=='' || $_FILES['dabbawala_image']['error'] == 0)) {
                        $customerValidator->addImageFilter();
                }

                $dabbawala_image =  $customerValidator->dabbawala_image;

                $selected_menu_arr1 = $selected_menu_arr;
                $selected_menu_arr=array();

                if(!empty($selected_menu_arr1)) {
                    foreach($selected_menu_arr1 as $selmenukey=>$selmenuval)
                        {
                            foreach($menus_arr as $menuarrkey=>$menuarrval)
                            {
                                if($menuarrval==$selmenuval)
                                {
                                        $selected_menu_arr[$menuarrkey]=$selmenuval;
                                }
                            }
                    }                    
                }

                $location_code_arr      = $request->getPost('location_code');
                $location_address_arr   = $request->getPost('location_address');
                $delivery_location_arr  = $request->getPost('delivery_person');
                $dabbawala_code_type    = $request->getPost('dabbawala_code_type');
                $dabbawala_code_text    = $request->getPost('dabbawala_text');
                $dabbawala_code_image   = $post['dabbawala_image'];

                $new_array=array();
                $new_arr_menu_err=array();
                $new_arr_dabbawala=array();

                if(!empty($dabbawala_code_type)) {
                    foreach($dabbawala_code_type as $dbcodetypekey=>$dbcodetypeval)
                    {
                            foreach($dabbawala_code_text as $dbcodetextkey=>$dbcodetextval)
                            {
                                    foreach($dabbawala_code_image as $dbcodeimgkey=>$dbcodeimgval)
                                    {
                                            if($dbcodetypekey==$dbcodetextkey && $dbcodetextkey==$dbcodeimgkey)
                                            {
                                                    $new_arr_dabbawala[$dbcodetypekey]['dabawala_type']=$dbcodetypeval;
                                                    $new_arr_dabbawala[$dbcodetypekey]['dabawala_image']=$dbcodeimgval;
                                                    $new_arr_dabbawala[$dbcodetypekey]['dabawala_text']=$dbcodetextval;
                                            }
                                    }
                            }
                    }       
                }

                foreach($location_code_arr as $lococodekey=>$loccodeval)
                {
                        foreach($location_address_arr as $locaddkey=>$locaddval)
                        {
                                if($lococodekey==$locaddkey)
                                {
                                        if($locaddval=="" && $loccodeval!="")
                                        {
                                                $new_array[$lococodekey]['error']="Please enter address";
                                                $new_array[$lococodekey]['error_address']="Please enter address";
                                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                                $new_array[$lococodekey]['location_address']=$locaddval;
                                        }
                                        else if($locaddval!="" && $loccodeval=="")
                                        {
                                                $new_array[$lococodekey]['error']="Please select location";
                                                $new_array[$lococodekey]['error_location']="Please select location";
                                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                                $new_array[$lococodekey]['location_address']=$locaddval;
                                        }
                                        else if($locaddval=="" && $loccodeval=="")
                                        {
                                                $new_array[$lococodekey]['error']="Please enter location and address";
                                                $new_array[$lococodekey]['error_location']="Please select location";
                                                $new_array[$lococodekey]['error_address']="Please enter address";
                                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                                $new_array[$lococodekey]['location_address']=$locaddval;
                                        }
                                        else if($locaddval!="" && $loccodeval!="")
                                        {
                                                $new_array[$lococodekey]['error']="";
                                                $new_array[$lococodekey]['location_code']=$loccodeval;
                                                $new_array[$lococodekey]['location_address']=$locaddval;
                                        }
                                }
                        }
                }

                foreach($new_array as $newarrkey=>$newarrval)
                {
                        foreach($selected_menu_arr as $selmenuarrkey=>$selmenuarrval)
                        {
                                if($newarrkey==$selmenuarrkey)
                                {
                                        if($newarrval['error']!="")
                                        {
                                                if($newarrval['location_code']=="" && $newarrval['location_address']!="")
                                                {
                                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                                        $error_message[]="Please select $selmenuarrval code";
                                                }
                                                else if($newarrval['location_address']=="" && $newarrval['location_code']!="")
                                                {
                                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                                        $error_message[]="Please enter $selmenuarrval address";
                                                }
                                                else if($newarrval['location_code']=="" && $newarrval['location_address']=="")
                                                {
                                                        $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                                        $error_message[]="Please select $selmenuarrval code & enter address";
                                                }
                                        }
                                        else
                                        {
                                                $new_arr_menu_err[$selmenuarrval]=$newarrval;
                                        }
                                }
                        }
                }


                foreach($new_arr_menu_err as $newarrmenukey=>$newarrmenuval)
                {
                        foreach($delivery_location_arr as $dellocarrkey=>$dellocarrval)
                        {
                                if($newarrmenukey==$dellocarrkey)
                                {
                                        $new_arr_menu_err[$newarrmenukey]['delivery_person'] = $dellocarrval;
                                }
                        }
                }

                foreach ($new_arr_menu_err as $new_arr_menu_err_key=>$new_arr_menu_err_val)
                {
                    foreach($new_arr_dabbawala as $new_arr_dabbawala_key=>$new_arr_dabbawala_val)
                    {
                        if($new_arr_menu_err_key == $new_arr_dabbawala_key)
                        {
                            $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_type']=$new_arr_dabbawala_val['dabawala_type'];
                            $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_image']=$new_arr_dabbawala_val['dabawala_image']['name'];
                            $new_arr_menu_err[$new_arr_menu_err_key]['dabawala_text']=$new_arr_dabbawala_val['dabawala_text'];
                        }
                    }
                }

                if ($form->isValid() && empty($error_message))
                {
                    //dd($customer_address_menus);
                    $delete_address_array = array();
                    $fdata = $form->getData();
                    $full_form_data = $this->getRequest()->getPost();

                    //echo "<pre>"; print_r($full_form_data); die;
                    $post_data_location_menus = $full_form_data['menus'];

                    if(!empty($customer_address_menus)) {
                        foreach($customer_address_menus as $custaddmenukey=>$custaddmenuval)
                        {
                            if(!in_array( $custaddmenukey ,$post_data_location_menus ) )
                            {
                                $delete_address_array[$custaddmenukey]=$custaddmenuval;
                            }
                        }
                    }
				
                    $customerValidator->exchangeArray($post);

                    $customerValidator->registered_from = $loguser->rolename;

                    $customerValidator->city = $city;
                    $customerValidator->city_name = $city_name;
                    $customerValidator->isguest = $full_form_data['isguest'];

                    $data_customer = $libCustomer->saveCustomer($customerValidator);
                    
                    if($data_customer)
                    {
                        $save_customer_id = $id;

                        $imageDir = $_SERVER['DOCUMENT_ROOT']."/data/";
                        if(!is_dir($imageDir."$save_customer_id")){
                            mkdir($imageDir."$save_customer_id",0755);
                            chmod($imageDir."/"."$save_customer_id/", 0777);
                        }

                        foreach($_FILES['dabbawala_image']['name'] as $n=>$name) {
                            if($_FILES['dabbawala_image']['error'][$n]==0)
                            {
                                $image_name = $_FILES['dabbawala_image']['name'][$n];
                                move_uploaded_file($_FILES['dabbawala_image']['tmp_name'][$n],$imageDir."$save_customer_id/".$image_name);
                                $uri = $bucketFolder."/dabbawala/".$image_name;
                                $uploadFile = $imageDir."$save_customer_id/".$image_name;

                                $old_image = basename($uploadFile);

                                // Deleting old image file before uploading new image.
                                $delete_uri = $bucketFolder."/dabbawala/".$old_image;
                                if($s3->getObject($bucketName, $delete_uri)) {
                                        $s3->deleteObject($bucketName, $delete_uri);
                                }                			
                                // Adding new image file with public read access
                                if($s3->putObjectFile($uploadFile, $bucketName, $uri, S3::ACL_PUBLIC_READ)){
                                    if(file_exists($uploadFile)){
                                        unlink($uploadFile);
                                    }
                                    else{
                                        exit("\nError: No such file for delete: $uploadFile\n\n");
                                    }
                                }
                                else{
                                    exit("\nError: Uploading file : $uploadFile on aws\n\n");
                                }                			
                            }
                        }
                    }
                  
                    if($data_customer)
                    {
                        $main_delete_array = array();
                        if(!empty($delete_address_array))
                        {
                            foreach($delete_address_array as $deladdarraykey=>$deladdarrayval)
                            {
                                array_push($main_delete_array,$deladdarrayval['menu_type']);
                            }
                            $this->deleteCustomerAddress($main_delete_array,$id);
                        }
                    }

                    $this->flashMessenger()->addSuccessMessage("Customer updated successfully");

                    $full_name = $customer->customer_name;

                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']= $loguser->pk_user_code; //$loguser->pk_user_code;
                    $activity_log_data['context_name']= $loguser->first_name." ".$loguser->last_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'Customer';
                    $activity_log_data['action']= 'edit';
                    $activity_log_data['description']= "Customer : $full_name account updated.";
                    $libCommon->saveActivityLog($activity_log_data);
                    return $this->redirect()->toRoute('customer');
                }
        }

        $dabbawala_code_type = (isset($post['dabbawala_code_type'])) ? $post['dabbawala_code_type'] : $customer->dabbawala_code_type;
        $this->layout()->setVariables(array('page_title'=>"Edit Customer Details",'breadcrumb'=>"Customer Details"));
        return array(
                'id' => $id,
                'form' => $form,
                'customer' => $customer,
                'dabbawala_code_type' => $dabbawala_code_type,
                'thirdparty' => $thirdparty,
                'checkedflg'=>$checkedflg,
                'errStr'=>$errStr,
                'thirdparty' => $thirdparty,
                'menus' => $menus_arr,
                'new_arr_menu_err'=>$new_arr_menu_err,
                'selected_menu_arr' =>$selected_menu_arr,
                'error_message' => $error_message,
        );
}

/**
 * use to add amount to wallet
 * @method addwalletAction()
 * @return multitype:\Admin\Form\WalletForm
 */
public function addwalletAction()
{
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $form = new WalletForm($adapt);
        $form->get('submit')->setAttribute('value', 'Save');
        $request = $this->getRequest();
        $this->layout()->setVariables(array('page_title'=>"Add Amount",'breadcrumb'=>"Add Amount"));
        return array('form' => $form);
}

/**
 * use to delete customer old addresses
 * @method deleteCustomerAddress($delete_address_array,$customer_id) use to delete customer address for specifiec customer id
 * @param array $delete_address_array
 * @param int $customer_id
 * @return boolean
 */
public function deleteCustomerAddress($delete_address_array,$customer_id)
{
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCustomer = QSCustomer::getInstance($sm);
        $result = $libCustomer->deleteCustomerAddress($delete_address_array,$customer_id);
}

/**
 * use to add amount to customer wallet
 * @method ajxWalletAction()
 * @method getTransactionAmt($select,$custid,$page) use to get transaction amount of customer based on customer_id
 * @param int id
 * @return \Zend\View\Model\JsonModel
 */
public function ajxWalletAction()
{
    $setting_session = new Container('setting');
    $setting = $setting_session->setting;
    $date_format=$setting['DATE_FORMAT'];

    $utility = Utility::getInstance();

    $custid = $this->params()->fromQuery('id');

    if (! $this->authservice)
    {
            $this->authservice = $this->getServiceLocator()->get('AuthService');
    }

    $iden = $this->authservice->getIdentity();

    $layout = $this->layout();
    $acl = $layout->acl;

    $viewModel = new ViewModel();

    $loggedUser = $layout->loggedUser;

    $select = new QSelect();

    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
    $arrColumns = array('0'=>'payment_date','1'=>'description','2'=>'wallet_amount','3'=>'amount_type','4'=>'payment_type','5'=>"context");
    $order_by="customer_wallet_id";
    $order = $arrOrder[0]['dir'];
    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";

    $wallettype = $this->params()->fromQuery('wallet');

    $columns = $this->params()->fromQuery('columns');

    $select->where(

                new \Zend\Db\Sql\Predicate\PredicateSet(
                                array(
                                                new \Zend\Db\Sql\Predicate\Operator('payment_date', 'LIKE', '%'.$search.'%'),
                                                new \Zend\Db\Sql\Predicate\Operator('description', 'LIKE', '%'.$search.'%'),
                                                new \Zend\Db\Sql\Predicate\Operator('wallet_amount', 'LIKE', '%'.$search.'%'),
                                                new \Zend\Db\Sql\Predicate\Operator('amount_type', 'LIKE', '%'.$search.'%'),
                                                new \Zend\Db\Sql\Predicate\Operator('payment_type', 'LIKE', '%'.$search.'%'),
                                                new \Zend\Db\Sql\Predicate\Operator('context', 'LIKE', '%'.$search.'%'),
                                ),
                                // optional; OP_AND is default
                                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
                )

    );

    $select->order($order_by . ' ' . $order);
    
    $custwalletdata = $this->getCustomerTable()->getTransactionAmt($select,$custid,$page,$type);
    
    $custwalletdata->setCurrentPageNumber($page)
    ->setItemCountPerPage($itemsPerPage)
    ->setPageRange(7);

    $returnVar = array();
    $returnVar['draw'] = $draw;
    $returnVar['recordsTotal'] = $custwalletdata->getTotalItemCount();
    $returnVar['recordsFiltered'] = $custwalletdata->getTotalItemCount();
    $returnVar['data'] = array();
 
    if(!empty($custwalletdata)){
        foreach($custwalletdata as $custwalletdata){
                $str="";
                $arrTmp = array();

                array_push($arrTmp,date($date_format,strtotime($custwalletdata['payment_date'])));
                array_push($arrTmp,$custwalletdata['description']);
                if($custwalletdata['amount_type']=='lock')
                {
                        //array_push($arrTmp,'<i class="fa fa-rupee"></i> <span class="wallet_amount"> '.$custwalletdata['wallet_amount'].' </span> <i class="fa fa-lock"></i>');
                        array_push($arrTmp,'<span class="wallet_amount"> '.$utility->getLocalCurrency($custwalletdata['wallet_amount']).' </span> <i class="fa fa-lock"></i>');
                }
                else 
                {
                        //array_push($arrTmp,'<i class="fa fa-rupee"></i> <span class="wallet_amount">'.$custwalletdata['wallet_amount']).'</span>';
                        array_push($arrTmp, '<span class="wallet_amount">'.$utility->getLocalCurrency($custwalletdata['wallet_amount'])).'</span>';
                }
                array_push($arrTmp,$custwalletdata['amount_type']);
                array_push($arrTmp,$custwalletdata['payment_type']);
                array_push($arrTmp,$custwalletdata['context']);
                $customer_wallet_id=$custwalletdata['customer_wallet_id'];
                if($custwalletdata['amount_type']=='lock')
                {
                        $confirmMsg="Do you really want to tranfer lock amount to debit amount?";
                        $str='<button data-id="'.$customer_wallet_id.'" class="smBtn blueBg has-tip tip-top lockedit" data-tooltip="" data-selector="tooltip-ib8x1zax0" title="Edit"><i class="fa fa-edit"></i></button>';
                        $str.='<button data-id="'.$customer_wallet_id.'" class="smBtn blueBg has-tip tip-top locktranfer" title="Convert to Debit" data-tooltip=""><i class="fa fa-chevron-circle-right"></i></button>';
                }
                array_push($arrTmp,$str);
                array_push($returnVar['data'],$arrTmp);
                
        }          
    }                                           
        return new JsonModel($returnVar);
}
        
/**
 * Use to save cheque, cash, neft, debit and lock amount.
 * @method getCustomer($id) use to fetch customer information
 * @method fetchamtpaid($id) use to fetch customer wallet information from customer_wallet table
 * @method getBal($id,true,true,true) use to get customer balance information
 * @method saveCustCashAmt($paymentopt,$id,$formtype,$name) use to save customer wallet transaction information 
 * @method saveActivityLog($activity_log_data) use to save activity information
 * @return multitype:unknown number \Admin\Form\CustomerForm
 */
public function paymentoptionAction()
{   
        $id = (int) $this->params('id');		
        if (!$id)
        {
                return $this->redirect()->toRoute('customer');
        }

        $utility = Utility::getInstance();
        
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;
        $sm = $this->getServiceLocator();

        $libCommon = QSCommon::getInstance($sm);
        $libCustomer = QSCustomer::getInstance($sm);
        $customer = $libCustomer->getCustomer($id,'id');

        $customer_name = $customer['customer_name'];
        
        $layoutviewModel = $this->layout();
        $acl = $layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;

        $custamtpaiddata = $this->getCustomerTable()->fetchamtpaid($id);
                                          
        $getCustBalance = $libCustomer->getBal($id,true,true,true);
        
        $config_variables = $sm->get('config');

        $form = new CustomerForm($sm);
        $form1 = new CustomerForm($sm);
        $form2 = new CustomerForm($sm);
        $form3 = new CustomerForm($sm);
        $form4 = new CustomerForm($sm);
       
        $request = $this->getRequest();

        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;

        $name = $loguser->first_name." ".$loguser->last_name;

        if ($request->isPost())
        {  
                $frmdata = $this->getRequest()->getPost()->toArray();
                $formtype = $frmdata['type'];
                $paymentopt = new CustomerValidator();

                if($formtype =="cash")
                {
                     
                        $form->setValidationGroup('cash_amt');

                        $paymentopt->getInputFilter();
                        //$paymentopt->setAdapter($adapt);
                        $form->setInputFilter($paymentopt->getInputFilter());
                        $data = $this->getRequest()->getPost()->toArray();
                        $data_amount=$data['cash_amt'];
                        $form->setData($data);

                        if ($form->isValid())
                        {
                             
                                $paymentopt->exchangeArray($data);
                                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                                $paymentopt->updated_by = $authservice->pk_user_code;
                                $pay_data = $this->getCustomerTable()->saveCustCashAmt($paymentopt,$id,$formtype,$name);
                                  
                                if($pay_data)
                                {
                                    /* send sms on successful transactions */
                                        if($customer['phone'] && $customer['phone_verified'] == 1){

                                                $mailer = new \Lib\Email\Email();
                                                $mailer->setAdapter($sm);

                                                //get sms configuration
                                                $sms_config = $sm->get('config')['sms_configuration'];
                                                $sms_common = $libCommon->getSmsConfig($setting);

                                                //SET sms configuration to mailer
                                                $mailer->setSMSConfiguration($sms_config);
                                                //check for mobile no and give it to
                                                $mailer->setMobileNo($customer['phone']);
                                                $mailer->setMerchantData($sms_common);
                                                $sms_array = array(
                                                        //'wallet_amount' => $libCommon->getCurrencyEntity($settings['GLOBAL_CURRENCY'],$data_amount,'SMS'),
                                                        //'wallet_amount'	=> $data_amount,
                                                        'wallet_amount' => $utility->getLocalCurrency($data_amount,'','','SMS'),
                                                );

                                                $message = $libCommon->getSMSTemplateMsg('wallet_update',$sms_array);

                                                if($message){
                                                        $mailer->setSMSMessage($message);
                                                        $sms_returndata = $mailer->sendmessage();
                                                }
                                        }						
                                        $full_name=$loguser->first_name." ".$loguser->last_name;
                                        $activity_log_data=array();
                                        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                                        $activity_log_data['context_name']= $full_name;
                                        $activity_log_data['context_type']= 'user';
                                        $activity_log_data['controller']= 'customer';
                                        $activity_log_data['action']= 'paymentoption';
                                        $activity_log_data['description']= "Wallet : ".$customer_name." wallet credit with ".$utility->getLocalCurrency($data_amount);
                                        $libCommon->saveActivityLog($activity_log_data);
                                }


                                 if(isset($pay_data))
                                {
                                        return $this->redirect()->toRoute('customer',array('action'=>'paymentoption','id'=>$id));
                                }
                        }
                  /* else
                         {
                                 $messages = $form->getMessages();
                                 //echo'<pre>';print_r($expression);die;
                                print_r($messages); exit();

                         } */ 


                }
                elseif ($formtype =="cheque")
                {
                        $form1->setValidationGroup('cheque_no','cheque_amt','bank_name');

                        $paymentopt->getInputFilter();
                        //$paymentopt->setAdapter($adapt);
                        $form1->setInputFilter($paymentopt->getInputFilter());
                        $data = $this->getRequest()->getPost()->toArray();

                        $data_cheque_no=$data['cheque_no'];
                        $data_cheque_amt=$data['cheque_amt'];
                        $data_bank_name=$data['bank_name'];

                        $form1->setData($data);

                        if ($form1->isValid())
                        {
                                $paymentopt->exchangeArray($data);
                                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                                $paymentopt->updated_by = $authservice->pk_user_code;
                                $pay_data = $this->getCustomerTable()->saveCustCashAmt($paymentopt,$id,$formtype,$name);

                                if($pay_data)
                                {
                                        /* send sms on successful transactions */
                                        if($customer['phone'] && $customer['phone_verified'] == 1){

                                                $mailer = new \Lib\Email\Email();
                                                //$adapt = $sm->get('Write_Adapter');
                                                $mailer->setAdapter($sm);

                                                //get sms configuration
                                                $sms_config = $sm->get('config')['sms_configuration'];
                                                $sms_common = $libCommon->getSmsConfig($setting);

                                                //SET sms configuration to mailer
                                                $mailer->setSMSConfiguration($sms_config);
                                                //check for mobile no and give it to
                                                $mailer->setMobileNo($customer['phone']);
                                                $mailer->setMerchantData($sms_common);
                                                $sms_array = array(
                                                        'wallet_amount' => $utility->getLocalCurrency($data_cheque_amt,'','','SMS'),
                                                );

                                                $message = $libCommon->getSMSTemplateMsg('wallet_update',$sms_array);

                                                if($message){
                                                        $mailer->setSMSMessage($message);
                                                        $sms_returndata = $mailer->sendmessage();
                                                }
                                        }						
                                        $full_name=$loguser->first_name." ".$loguser->last_name;
                                        $activity_log_data=array();
                                        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                                        $activity_log_data['context_name']= $full_name;
                                        $activity_log_data['context_type']= 'user';
                                        $activity_log_data['controller']= 'customer';
                                        $activity_log_data['action']= 'paymentoption';
                                        $activity_log_data['description']= "Wallet : ".$customer_name." wallet added with cheque amount ".$utility->getLocalCurrency($data_cheque_amt)."  (cheque number=>".$data_cheque_no." and bank name=>".$data_bank_name.")";
                                        $libCommon->saveActivityLog($activity_log_data);
                                }

                                if(isset($pay_data))
                                {
                                        return $this->redirect()->toRoute('customer',array('action'=>'paymentoption','id'=>$id));
                                }
                        }

                }
                elseif ($formtype =="neft")
                {

                        $form2->setValidationGroup('trans_id','neft_amt','neft_date');

                        $paymentopt->getInputFilter();
                        //$paymentopt->setAdapter($adapt);
                        $form2->setInputFilter($paymentopt->getInputFilter());
                        $data = $this->getRequest()->getPost()->toArray();
                        $form2->setData($data);

                        $data_trans_id=$data['trans_id'];
                        $data_neft_amt=$data['neft_amt'];
                        $data_neft_date=$data['neft_date'];

                        if ($form2->isValid())
                        {
                                $paymentopt->exchangeArray($data);
                                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                                $paymentopt->updated_by = $authservice->pk_user_code;
                                $pay_data = $this->getCustomerTable()->saveCustCashAmt($paymentopt,$id,$formtype,$name);

                                if($pay_data)
                                {
                                        /* send sms on successful transactions */
                                        if($customer['phone'] && $customer['phone_verified'] == 1){

                                                $mailer = new \Lib\Email\Email();
                                                $mailer->setAdapter($sm);

                                                //get sms configuration
                                                $sms_config = $sm->get('config')['sms_configuration'];
                                                $sms_common = $libCommon->getSmsConfig($setting);

                                                //SET sms configuration to mailer
                                                $mailer->setSMSConfiguration($sms_config);
                                                //check for mobile no and give it to
                                                $mailer->setMobileNo($customer['phone']);
                                                $mailer->setMerchantData($sms_common);
                                                $sms_array = array(
                                                        'wallet_amount' => $utility->getLocalCurrency($data_neft_amt,'','','SMS'),
                                                );

                                                $message = $libCommon->getSMSTemplateMsg('wallet_update',$sms_array);

                                                if($message){
                                                        $mailer->setSMSMessage($message);
                                                        $sms_returndata = $mailer->sendmessage();
                                                }
                                        }						
                                        $full_name = $loguser->first_name." ".$loguser->last_name;
                                        $activity_log_data = array();
                                        $activity_log_data['context_ref_id'] = $loguser->pk_user_code;
                                        $activity_log_data['context_name'] = $full_name;
                                        $activity_log_data['context_type'] = 'user';
                                        $activity_log_data['controller'] = 'customer';
                                        $activity_log_data['action'] = 'paymentoption';
                                        $activity_log_data['description'] = "Wallet : ".$customer_name." wallet amount added with neft amount".$utility->getLocalCurrency($data_neft_amt)." (transaction id=> ".$data_trans_id." and neft date=>".$data_neft_date.") by ".$full_name;
                                        $libCommon->saveActivityLog($activity_log_data);
                                }

                                if(isset($pay_data))
                                {
                                        return $this->redirect()->toRoute('customer',array('action'=>'paymentoption','id'=>$id));
                                }
                        }
                }
                elseif($formtype=="debit")
                {
                        $form3->setValidationGroup('debit_amt');
                        $paymentopt->getInputFilter();
                        //$paymentopt->setAdapter($adapt);
                        $form3->setInputFilter($paymentopt->getInputFilter());
                        $data = $this->getRequest()->getPost()->toArray();
                        $data_debit_amt=$data['debit_amt'];
                        $form3->setData($data);

                        if($form3->isValid())
                        {
                                $paymentopt->exchangeArray($data);
                                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                                $paymentopt->updated_by = $authservice->pk_user_code;
                                $pay_data = $this->getCustomerTable()->saveCustCashAmt($paymentopt,$id,$formtype,$name);

                                if($pay_data){

                                        $full_name=$loguser->first_name." ".$loguser->last_name;
                                        $activity_log_data=array();
                                        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                                        $activity_log_data['context_name']= $full_name;
                                        $activity_log_data['context_type']= 'user';
                                        $activity_log_data['controller']= 'customer';
                                        $activity_log_data['action']= 'paymentoption';
                                        $activity_log_data['description']= "Wallet : ".$customer_name." walled debit with ".$utility->getLocalCurrency($data_debit_amt);
                                        $libCommon->saveActivityLog($activity_log_data);
                                }

                                 if(isset($pay_data))
                                {
                                        return $this->redirect()->toRoute('customer',array('action'=>'paymentoption','id'=>$id));
                                }
                        }
                }
                elseif($formtype=="lock")
                {
                        $form4->setValidationGroup('lock_amt');
                        $paymentopt->getInputFilter();
                        //$paymentopt->setAdapter($adapt);
                        $form4->setInputFilter($paymentopt->getInputFilter());
                        $data = $this->getRequest()->getPost()->toArray();
                        $hdn_customer_wallet_id=$data['hdn_customer_wallet_id'];
                        $flag_lock_amt_transfer=$data['flag_lock_amt_transfer'];
                        $data_lock_amt=$data['lock_amt'];

                        $form4->setData($data);

                        if ($form4->isValid())
                        {
                            
                                $paymentopt->exchangeArray($data);
                                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                                $paymentopt->updated_by = $authservice->pk_user_code;
                                $pay_data = $this->getCustomerTable()->saveCustCashAmt($paymentopt,$id,$formtype,$name);


                                if($pay_data)
                                {
                                        $full_name=$loguser->first_name." ".$loguser->last_name;
                                        $activity_log_data=array();
                                        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                                        $activity_log_data['context_name']= $full_name;
                                        $activity_log_data['context_type']= 'user';
                                        $activity_log_data['controller']= 'customer';
                                        $activity_log_data['action']= 'paymentoption';
                                        $activity_log_data['description']= "Wallet : ".$customer_name." wallet locked with ".$utility->getLocalCurrency($data_lock_amt);

                                        $libCommon->saveActivityLog($activity_log_data);
                                }
                                
                                 if(isset($pay_data))
                                {
                                        return $this->redirect()->toRoute('customer',array('action'=>'paymentoption','id'=>$id));
                                }
                        }
                }
        } 

        $this->layout()->setVariables(array('page_title'=>"Add Amount",'description'=>"User Account",'breadcrumb'=>"Add Amount"));
        return array(
                        'id' => $id,
                        'form' => $form,
                        'form1' => $form1,
                        'form2' => $form2,
                        'form3' => $form3,
                        'form4' => $form4,
                        'customer' => $customer,
                        'custamtpaiddata' =>$custamtpaiddata,
                        'currentbal' =>$custbalance,
                        'date_format'=>$setting['DATE_FORMAT'],
                        'custBalance' => $getCustBalance,
        );
}

/**
 * use to display order information
 * @method orderAction()
 * @method getBal($id,true,true,true) use to get balance information of customer
 * @method getLocationData() use to get all delivery location
 * @method getAllDeliveryPerson() use to get all delivery person's
 * @method getCustomerData($id) get all information about customer based on customer_id
 * @method getCustomerAddress($id) get all information about customer address based on customer_id
 * @method getCustomerPrintData($data) use to get customer entire data including order, address, dibbewala code 
 * @param int id
 * @return array
 */
public function orderAction()
{
    
        $sm = $this->getServiceLocator();

        $s3 = $sm->get('S3');
        //$bucketName = $s3::$bucketInfo['bucket'];
        $hostname = $s3->getHostname();

        //$adapt = $sm->get('Write_Adapter');
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;

        $bucketFolder = $setting['S3_BUCKET_URL'];

        $id = (int) $this->params('id');
        $hdncustomercode=$id;
        if (!$id) {
                return $this->redirect()->toRoute('order', array('action' => 'add'));
        }

        $libCustomer = QSCustomer::getInstance($sm);

        $getCustBalance = $libCustomer->getBal($id,true,true,true);
        //echo "<pre>"; print_r($getCustBalance); die;

        $location_data=$this->getOrderTable()->getLocationData();
        $deliverypersons=$this->getuserTable()->getAllDeliveryPerson();
        $empdetails = $this->getCustomerTable()->getCustomerData($id);
        
        $empdetailsarray = $empdetails->toArray();
       
        $menuSelected=$setting['MENU_TYPE'][0];
        $libCustomer = QSCustomer::getInstance($sm);
        $customer_address_data=$libCustomer->getCustomerAddress($id);

        $request = $this->getRequest();
        
        if ($request->isPost()) {
            
                $params = $this->params()->fromPost();

                if(isset($params['subaction'])){

                        if($params['subaction']=='print' || $params['subaction']=='quickbook' || $params['subaction']=='tally' || $params['subaction']=='export'){
                                $purpose = 'export';
                        }else{
                                $purpose ='view';
                        }

                        $selected_column1 = "pk_customer_code,customer_name,phone,location_name";
                        $selected_column = explode(',', $selected_column1);

                        $data = array(
                                'selected_columns'	=>$selected_column1,
                                'request'  =>$params
                        );
                        $exportData = $this->getCustomerTable()->fetchCustomerInfo(null,null,$params['menu'],$id,$params['minDate'],$params['maxDate'],$params['location'],$params['deliveryperson']);
                       
                        switch($params['subaction']){

                                case "tally":
                                case "quickbook":
                                case "print":

                                        $formData = $request->getPost();
                                        $formData['export_type'] = $params['subaction'];

                                        return $this->forward()->dispatch('Admin\Controller\Customer', array(
                                                        'printData' => $exportData,
                                                        'action' => 'printReport',
                                                        'formData' =>$formData
                                        ));
                                        break;

                                case "export":
                                        $formData = $request->getPost();
                                        $formData['export_type'] = 'pdf';
                                        return $this->forward()->dispatch('Admin\Controller\Customer', array(
                                        'printData' => $exportData,
                                        'service' => $params['service'],
                                        'action' => 'printReport',
                                        'subaction' => $params['subaction'],
                                        'formData' =>$formData,
                                        'minDate' => $request->getPost('minDate'),
                                        'maxDate'	=> $request->getPost('maxDate')
                                        ));
                                        break;
                                case "search";
                                break;

                        }
                }
        }

        $this->layout()->setVariables(array('page_title'=>"Customer Details",'breadcrumb'=>"Customer Details")); 
        return array(
                        'id' => $id,
                        'page_price' => (isset($config_variables['page_price']))?$config_variables['page_price']:'',
                        'employee'=>$empdetailsarray,
                        'date_format'=>$setting['DATE_FORMAT'],
                        'setting'=>$setting_session->setting,
                        'menuSelected'=>$menuSelected,
                        'hdncustomercode'=>$hdncustomercode,
                        'custBalance' =>$getCustBalance,
                        'customer_address_data' => $customer_address_data,
                        'location_data'=>$location_data,
                        'deliverypersons' => $deliverypersons,
                        'aws_url' => $hostname."/".$bucketFolder,
                );
}


/**
 * use to get order information of customer
 * @method ajxOrderAction()
 * @method fetchCustomerInfo($select,$page,$menu_type,$customer_code,$from_date,$to_date,$location_code,$deliveryperson) use to fetch customer information based on filters
 * @param string menu_type
 * @param int customer_code
 * @param string fromdate
 * @param string todate
 * @return \Zend\View\Model\JsonModel
 */
public function ajxOrderAction()
{
        $menu_type = $this->params()->fromQuery('menu_type');
        $customer_code=$this->params()->fromQuery('customer_code');
        $from_date=$this->params()->fromQuery('fromdate');
        $to_date=$this->params()->fromQuery('todate');
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;
        $date_format=$setting['DATE_FORMAT'];
        $utility = Utility::getInstance();

        $location_code = $this->params()->fromQuery('location_code');
        $deliveryperson=$this->params()->fromQuery('deliveryperson');

        if (! $this->authservice)
        {
                $this->authservice = $this->getServiceLocator()->get('AuthService');
        }

        $iden = $this->authservice->getIdentity();

        $layout = $this->layout();
        $acl = $layout->acl;

        $viewModel = new ViewModel();

        $loggedUser = $layout->loggedUser;

        $select = new QSelect();

        $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
        $arrColumns = array('0'=>'pk_order_no','1'=>'order_date','2'=>'product_type','3'=>'product_description','4'=>'location_name','5'=>"amount","6"=>"delivery_status");

        $order_by = $arrColumns[$arrOrder[0]['column']];
        $order = $arrOrder[0]['dir'];

        $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

        $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
        $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
        $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
        $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
        $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";

        $status = $this->params()->fromQuery('status');
        $columns = $this->params()->fromQuery('columns');
        if($search !=""){
            $select->where(

                new \Zend\Db\Sql\Predicate\PredicateSet(
                    array(
                            new \Zend\Db\Sql\Predicate\Operator('pk_order_no', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.order_date', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('order_details.product_type', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('product_description', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('orders.location_name', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('amount', 'LIKE', '%'.$search.'%'),
                            new \Zend\Db\Sql\Predicate\Operator('delivery_status', 'LIKE', '%'.$search.'%'),

                    ),
                    // optional; OP_AND is default
                    \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
                )

            );
        }
        $select->order($order_by . ' ' . $order);
        $orders = $this->getCustomerTable()->fetchCustomerInfo($select,$page,$menu_type,$customer_code,$from_date,$to_date,$location_code,$deliveryperson);
        $orders->setCurrentPageNumber($page)
        ->setItemCountPerPage($itemsPerPage)
        ->setPageRange(7);

        $returnVar = array();
        $returnVar['draw'] = $draw;
        $returnVar['recordsTotal'] = $orders->getTotalItemCount();
        $returnVar['recordsFiltered'] = $orders->getTotalItemCount();
        $returnVar['data'] = array();
        
        
        foreach($orders as $orders){     
                $arrTmp = array();
                array_push($arrTmp,$orders['order_no']);
                array_push($arrTmp, $utility->displayDate( $orders['order_date'], $setting['DATE_FORMAT']));
                array_push($arrTmp,$orders['product_type']);
                array_push($arrTmp,$orders['product_descriptions']);
                array_push($arrTmp,$orders['location_name']);
                array_push($arrTmp,$utility->getLocalCurrency($orders['net_amount']));
                //array_push($arrTmp,$orders['net_amount']);
                array_push($arrTmp,$orders['delivery_status']);
                array_push($returnVar['data'],$arrTmp);

        }

        return new JsonModel($returnVar);
}

/**
 * To delete customer using given customer id
 * @method getCustomer($id,'id') use to get customer informaton depending upon customer id
 * @method saveActivityLog($activity_log_data) use to save customer activity data
 * @method deleteCustomer($id) use to soft delete customer information
 * @param int id
 * @return route customer
 */
public function deleteAction()
{
        $sm = $this->getServiceLocator();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $id = (int) $this->params('id');
        $customer_data = $libCustomer->getCustomer($id,'id');

        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;
        $status = $customer_data['status'];		

        $full_name=$customer_data['customer_name'];
        $activity_log_data=array();
        if($status==1)
        {
                $activity_log_data['description']= "Customer : $full_name deactivated.";
        }
        else
        {
                $activity_log_data['description']= "Customer : $full_name activated.";
        }

        $location_name=$location->location;
        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
        $activity_log_data['context_name']= $full_name;
        $activity_log_data['context_type']= 'user';
        $activity_log_data['controller']= 'customer';
        $activity_log_data['action']= 'delete';

        $libCommon->saveActivityLog($activity_log_data);

        if (!$id)
        {
                return $this->redirect()->toRoute('customer');
        }
        $data_customer=$this->getCustomerTable()->deleteCustomer($id);
        ($data_customer) ?$this->flashMessenger()->addSuccessMessage("Customer updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Customer.");
        return $this->redirect()->toRoute('customer');
}


/**
 * Get instance of QuickServe\Model\CustomerTable
 * @method getCustomerTable()
 * @return QuickServe\Model\CustomerTable
 */
public function getCustomerTable()
{
        if (!$this->customertable)
        {
                $sm = $this->getServiceLocator();
                $this->customertable = $sm->get('QuickServe\Model\CustomerTable');
        }
        return $this->customertable;
}

/**
 * use to get instance of WalletTable
 * @method getWalletTable()
 * @return QuickServe\Model\WalletTable
 */
public function getWalletTable()
{
        if (!$this->wallettable)
        {
                $sm = $this->getServiceLocator();
                $this->wallettable = $sm->get('QuickServe\Model\CustomerWalletTable');
        }
        return $this->wallettable;
}

/**
 * Get instance of getOrderTable
 * @method getOrderTable()
 * @return QuickServe\Model\OrderTable
 */
public function getOrderTable()
{
        if (!$this->ordertable)
        {
                $sm = $this->getServiceLocator();
                $this->ordertable = $sm->get('QuickServe\Model\OrderTable');
        }
        return $this->ordertable;
}

/**
 * Get instance of getUserTable
 * @method getUserTable() 
 * @return QuickServe\Model\UserTable
 */
public function getUserTable() 
{
if(!$this->userTable)
{
   $sm = $this->getServiceLocator();
           $this->userTable = $sm->get('QuickServe\Model\UserTable');
}
return $this->userTable;
}                             

/**
* It send notification to customer and cancel or carryforward order based on setting 
* @method sendNotificationAction()
* @method getNotificationTemplates() get template for sending notification
* @method fetchAll($pre_select,$page,$filter) to get customer information
* @return \Zend\View\Model\JsonModel|\Zend\View\Model\ViewModel (return success if ordecancelled or forwarded and on msg sent)
*/

public  function sendNotificationAction(){
    
    $count_operation=0;
    $operation_msg='';
    $kitchenexceed='';
    $view = new ViewModel();
    $view->setTerminal(true);

    $sm = $this->getServiceLocator();
    $libCustomer = QSCustomer::getInstance($sm);
    $libCommon = QSCommon::getInstance($sm);
    $libOrder = QSOrder::getInstance($sm);
    $libCatalogue = QSCatalogue::getInstance($sm);

    $setting_session = new Container('setting');
    $setting = $setting_session->setting;
    //dd($setting['GLOBAL_LOCALE']);
    $locale = $libCommon->getCountryByLanguageCode($setting['GLOBAL_LOCALE']);
    //$setting['GLOBAL_COUNTRY'] = $locale['country_name'];

    $sms_templates = $libCommon->getNotificationTemplates();
    $request = $this->getRequest();
    $menu = $this->params()->fromRoute('menu');
    $location = $this->params()->fromRoute('location');
    $deliverypers = $this->params()->fromRoute('deliverypers');
    $order_date = $this->params()->fromRoute('order_date');

    $filter = array(
            'menu' => $menu,
            'location' => $location,
            'deliveryperson' => $deliverypers,
            'orderdate' => $order_date
    );
    $pre_select = new QSelect();
    $pre_select->where(array('status'=>1));
    $page = null;
    $customers = $this->getCustomerTable()->fetchAll($pre_select,$page,$filter);

    $count=count($customers->toArray());

    $holidays  = $libCommon->fetchHolidaysList('holiday');

    $holidays_list = array();

    foreach ($holidays as $key=>$vals){

        $holidays_list[$key] = strval(Date('Y/m/d',strtotime($vals['holiday_date'])));
    }

    $weekOff = $libCommon->fetchHolidaysList('weekoff');


    if($request->isPost()){

        $templates = $request->getPost('templates');
        $menu = $request->getPost('menu');
        $location = $request->getPost('location');
        $deliverypers = $request->getPost('deliverypers');
        $order_date = $request->getPost('order_date');
        $operation = $request->getPost('operations');
        $post = $request->getPost();
        $filter = array(
            'menu' => $menu,
            'location' => $location,
            'deliveryperson' => $deliverypers,
            'orderdate' => $order_date,
        );
        foreach ($templates as $key=>$post){

           $notification_template = $libCommon->getTemplateById($post['templateId']);

            $matches = array();
            preg_match_all('/\#[a-zA-Z][a-zA-Z0-9_-]*\#/', $notification_template->sms_content, $matches);

            $mailer = new \Lib\Email\Email();

            $mailer->setAdapter($sm);

            //get sms configuration
            $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
            //SET sms configuration to mailer
            $mailer->setSMSConfiguration($sms_config);
            //check for mobile no and give it to
            $var_keys = array();

            foreach ($matches[0] as $match){
                $str = str_replace('#','',$match);
                $var_keys[] = $str;
            }
            if(!empty($post['str'])){
                $sms_array = array_combine($var_keys,$post['str']);
            }
            $select = new QSelect();
            $select->where(array('status'=>1));
            /**
             *  fetch all active customer and send sms
             */

            $page = null;
            $customers = $this->getCustomerTable()->fetchAll($select,$page,$filter);
           
            foreach ($customers as $cust){
                $mailer->setMobileNo($cust['phone']);
                $sms_common = $libCommon->getSmsConfig($setting);
                $mailer->setMerchantData($sms_common);
                
                $message = $libCommon->getSMSTemplateMsg($notification_template->template_key,$sms_array);

                if($message){
                    $mailer->setSMSMessage($message);
                    $sms_returndata = $mailer->sendmessage();
                } 
                if($notification_template->template_key =='service_unavailable'){                    
                    if($filter['menu']=='' || empty($filter['menu'])){
                        $filter['menu']=false;
                    }
                    
                    $todaysorder = $libOrder->getOrderByFilter($cust['pk_customer_code'],$filter['menu'],$filter['orderdate']);
                    
                    $todaysorder = $todaysorder->toArray();
                    
                    if(isset($todaysorder) && !empty($todaysorder)){
                        
                        if($operation=="carryFrw"){
                            
                            foreach ($todaysorder as $order){

                                $lastdate = $libOrder->fetchLastDateofOrder($order['customer_code'],$order['order_no'],$order['order_menu']);

                                $tempOrders = $libOrder->getNewOrders($order['customer_code'],$order['order_no'],$order['order_menu'],array($order['order_date']));
                                ///////////// get week offs in string format (e.g 1,2,3 etc...) ///////////////
                                $strWeekOffs = $weekOff[0]['holiday_description'];

                                $arr = [0,1,2,3,4,5,6];

                                if(!empty($order['days_preference'])){
                                    $arrWeekOffs = array_diff($arr,explode(',',$order['days_preference']));
                                    $strWeekOffs = implode(",",$arrWeekOffs);
                                }else{
                                    $kitchenwise_menu_weekoff_setting = $setting['K'.$order['fk_kitchen_code'].'_'.  strtoupper($order['order_menu']) .'_WEEKOFFS'];
                                    if($kitchenwise_menu_weekoff_setting != null && $kitchenwise_menu_weekoff_setting != ""){ // change by shil to allow 0 as true value.
                                        $strWeekOffs =  $kitchenwise_menu_weekoff_setting;
                                    }
                                }
                                /// calendar based start. //////
                                $availableDates = array();
                                $filterAvailableDates = array();
                                // Check if calendar based or non calendar based.
                                if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){
                                    $today = date("Y-m-d");
                                    $arrWeekOffs = explode(",",$strWeekOffs);
                                    $filterAvailableDates = array();
                                    $availableDatesProducts = array();
                                    $pCnt = 0;
                                    foreach ($tempOrders as $rowData){
                                        if($rowData['product_type']=='Meal'){
                                            $pCnt++;
                                            $availableDatesProducts[$rowData['product_code']] = $libCatalogue->getCalendarProducts($rowData['product_code'],1,true,$order['order_menu'],$order['fk_kitchen_code']);
                                            $availableDates = array_keys($availableDatesProducts[$rowData['product_code']]);                                           
                                            if(empty($availableDates)){
                                                return new JsonModel(array('status'=>'error','msg'=>"Menu is not available yet for {$rowData["product_name"]} ."));
                                            }
                                            $aCnt = 0;
                                            // If menu prepared then check for how many days it is prepared.
                                            foreach($availableDates as $aDate){

                                                if(isset($data['date_selected'])){
                                                    if(!in_array(date('w', strtotime($aDate)), $arrWeekOffs) && !in_array($aDate, $holidays_list)
                                                        && $aDate == $lastdate && !in_array($aDate,$cancelDays) ){
                                                        array_push($filterAvailableDates,$aDate);
                                                        $aCnt++;
                                                    }
                                                }else{
                                                    if(!in_array(date('w', strtotime($aDate)), $arrWeekOffs) && !in_array($aDate, $holidays_list)
                                                        && $aDate > $lastdate && !in_array($aDate,$cancelDays) ){
                                                        array_push($filterAvailableDates,$aDate);
                                                        $aCnt++;
                                                    }
                                                }
                                            }
                                            if($aCnt < count($cancelDays)){
                                                continue 3;
                                                //return new JsonModel(array('status'=>'error','msg'=>"{$rowData["product_name"]} menu is not available for ".count($cancelDays)." day(s)"));
                                            }
                                        }
                                    }
                                    // If order contains more than one meals then order should shift to common available dates.
                                    if($pCnt > 1){
                                        // Find out common dates amongs meals available dates , these dates should be more than or equal to cancel days...
                                        if(!empty($filterAvailableDates)){
                                            $withoutDuplicates = array_unique($filterAvailableDates);
                                            $duplicates = array_diff_assoc($filterAvailableDates, $withoutDuplicates);
                                            if(count($duplicates) < count($cancelDays)){
                                                return new JsonModel(array('status'=>'error','msg'=>" Menu is not available for ".count($cancelDays)." day(s)"));
                                            }
                                            $filterAvailableDates = $duplicates;
                                        }else{
                                            continue 3;
                                            //return new JsonModel(array('status'=>'error','msg'=>"No dates available"));
                                        }

                                    }

                                }
                                /////// calendar based end ////////
                                $newDates = $libOrder->getNewDates($lastdate,1,$holidays_list,false,$weekOff[0]['holiday_description'],$filterAvailableDates);
                                $newD = array();
                                foreach (array($order['order_date']) as $key=>$vals){
                                    $newDates[strval(date('Y-m-d',strtotime($vals)))] = date('Y-m-d',strtotime($newDates[$key]));
                                    $newD[$key] = strval(date('Y-m-d',strtotime($newDates[$key])));
                                    unset($newDates[$key]);
                                }

                                $existingOrderDates = array();
                                foreach ($tempOrders as $key=>$vals){
                                    foreach ($newDates as $keys=>$val){
                                        if(strval($vals['order_date'])==$keys){
                                                $existingOrderDates[$newDates[$keys]] = $tempOrders[$key]['order_date'];
                                                $tempOrders[$key]['order_date']=$newDates[$keys];
                                        }
                                    }
                                }
                                /////// temp order detail fetch according to detail. /////////////////////////
                                if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'] == 1){
                                    $tempOrdersDetails = array();
                                    foreach($tempOrders as $oProduct){
                                        foreach ($existingOrderDates as $key=>$vals){
                                            foreach ($availableDatesProducts[$oProduct['product_code']][$key] as $aProduct){
                                                $tmpDetails = array();
                                                $tmpDetails['ref_order_no'] = $id;
                                                $tmpDetails['meal_code'] = $aProduct->fk_product_code;
                                                $tmpDetails['product_code'] = $aProduct->product_code;
                                                $tmpDetails['product_name'] = $aProduct->product_name;
                                                $tmpDetails['quantity'] = $oProduct['quantity'] * $aProduct->product_qty;
                                                $tmpDetails['product_type'] = $oProduct['product_type'];
                                                $tmpDetails['order_date'] = $key;
                                                array_push($tempOrdersDetails,$tmpDetails);
                                            }
                                        }
                                    }
                                }else{
                                    $tempOrdersDetails = $libOrder->getNewOrderDetails($order['order_no'],array($order['order_date']));
                                    foreach ($tempOrdersDetails as $key=>$vals){
                                        foreach ($newDates as $keys=>$val){
                                            if(strval($vals['order_date'])==$keys){
                                                $tempOrdersDetails[$key]['order_date'] = $newDates[$keys];
                                            }
                                        }
                                    }
                                }
                                //////// tax added by shilbhushan on 22nd Apr 16. /////////
                                $arrBills = $libOrder->getOrderTable()->getOrderBillNos(array($order['order_no']),array($order['order_date']));
                                $arrTaxDetails = array();
                                $arrTaxDetails['new_order_dates'] = $newDates;
                                $arrTaxDetails['new_taxes'] = array();
                                foreach($arrBills as $okey=>$obill){
                                    $tempTaxDetails = $libOrder->getOrderTable()->getOrderTaxDetails($id,array($obill));
                                    $tempTaxDetails = $tempTaxDetails->toArray();
                                    foreach($tempTaxDetails as $txDetail){
                                        $date = $newDates[$txDetail['order_date']];
                                        if(!isset($arrTaxDetails['new_taxes'][$date])){
                                            $arrTaxDetails['new_taxes'][$date] = array();
                                            $arrTaxDetails['new_taxes'][$date][] = $txDetail;
                                        }else{
                                            $arrTaxDetails['new_taxes'][$date][] = $txDetail;
                                        }
                                    }                                            
                                }
                                $result_msg3=$libOrder->performKitchenOpertation($tempOrders,$order['customer_code'],$order['order_no'],$order['order_menu'],$order['fk_kitchen_code'],array($order['order_date']),$newDates,$newD,false);
                                $libOrder->canceltodaysorder($order['customer_code'],$order['order_no'],$order['order_menu'],$order['fk_kitchen_code'],array($order['order_date']));
                                $res = $libOrder->changeOrderStatus($order['customer_code'],$order['order_no'],$order['order_menu'],array($order['order_date']),'Reordered');
                                $result_msg2=$libOrder->AutoPlaceOrders($tempOrders,$tempOrdersDetails,$arrTaxDetails);
                                if(is_array($result_msg3)){
                                    $kitchenexceed.=implode(",",array_unique($result_msg3)).",";
                                }
                            }
                            $count_operation++;
                        }
                        if($operation=="ocancel"){
                            foreach ($todaysorder as $order){
                                $libOrder->canceltodaysorder($order['customer_code'],$order['order_no'],$order['order_menu'],$order['fk_kitchen_code'],array($order['order_date']));
                            }
                            $count_operation++;
                        }
                    }
                }
            }
        }
        if($count_operation>0 && $operation=="ocancel"){
            $msg="Order Cancelled Successfully";
            $status=true;
            return new JsonModel(array('success'=>$status,'msg'=>$msg));
        }

        if($count_operation>0 && $operation=="carryFrw"){
            if($kitchenexceed!=""){
                $msg="Please Increase the capacity of Kitchen on Dates :".$kitchenexceed;
                $status=false;
                return new JsonModel(array('success'=>$status,'msg'=>$msg));
            }
            else{
                $msg="Order Cancelled and Shifted Successfully!";
                $status=true;
                return new JsonModel(array('success'=>$status,'msg'=>$msg));
            }
        }
            return new JsonModel( array('success'=>true,'msg'=>'Message has been successfully sent'));
        }

        $view->setVariables(array('sms_templates' =>$sms_templates,'filter' => $filter,'count'=>$count,'GLOBAL_AUTO_FORWARD_CANCELLED_ORDER'=>$settings['GLOBAL_AUTO_FORWARD_CANCELLED_ORDER']));

        return $view;
}

/**
 * use to import customer data
 * @method importAction()
 * @method fetchAll() $objLocation use to fetch location list 
 * @method getCities() use to get all cities
 * @method fetchAll($select) $objGroup use to get group name based on location
 * @method fetchAll($select) $objThirdParty to gect list of third party
 * @method getCustomerCount() use to get customer count
 * @method checkSubscription("customer_active",'count',$activeCustomers) 
 * @method checkValidLocation($value,$validLocations)
 * @method saveImportCustomer($placeholderValues)
 * @method sendemailAuthenticationEmail($id,false)
 * @method saveCustomerAddress(array(0=>$temp_cust_adds[$adds_key]),$id,$flag)
 * @return array
 */
public function importAction(){

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $request = $this->getRequest();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $setting_session = new Container('setting');

        $setting = $setting_session->setting;

        $form = new ImportCustomerForm($sm);
        $importCustomerValidator = new ImportCustomerValidator();

        $importedData = array();

        ///////////////////////// Finding Column //////////////////////////////////////////////////////////////

        $table = "customers";

        $columns_result = $libCustomer->getColumnsFromTable($table,true);

        
        $exclude_columns = array('company_id','unit_id','city','pk_customer_code','group_code','referer','gcm_id','alt_phone','group_name','city','registered_from','location_code','location_name','lunch_loc_name','lunch_add','dabbawala_code','dinner_loc_name','dinner_add','product_code','last_modified','lunch_loc_code','dabbawala_code_type','dabbawala_image','dinner_loc_code','registered_on','otp','password','thirdparty','delivery_person_id','lunch_dp_id','dinner_dp_id','phone_verified','email_verified','food_referance');

        $columns = array_diff($columns_result,$exclude_columns);
  
        $customerTableColumn=$columns;

        $column_vals=array();

        $optional_field = array_diff($customerTableColumn,array('customer_name','city_name'));

        foreach ($setting['MENU_TYPE'] as $menu){
                array_push($columns, $menu."_location_address");
                array_push($columns, $menu."_location_name");
                array_push($columns, $menu."_dabbawala_code");
        }

        $customer_add_col = array_diff($columns,$customerTableColumn);

        ////////////////////////////////////////////////////////////////////////////////////////////////////

        $menu_field = count($setting['MENU_TYPE'])*3;

        $errors = array();

        if ($request->isPost())
        {

                $data = $request->getPost();

                if($data['submit']=='Upload'){

                        $form->setValidationGroup('import_file');

                        $post = array_merge(
                                        $this->getRequest()->getPost()->toArray(),
                                        $this->getRequest()->getFiles()->toArray()
                        );
                        //$importCustomerValidator->setAdapter($adapt);
                        $form->setInputFilter($importCustomerValidator->getInputFilter());
                        $form->setData($post);

                        if ($form->isValid())
                        {
                            
                                $data = $form->getData();
                               
                                $importCustomerValidator->exchangeArray($data);

                                $extension = substr($data['import_file']['name'],strpos($data['import_file']['name'],".")+1);

                                $filename = substr($data['import_file']['tmp_name'],strpos($data['import_file']['tmp_name'],"/data")+1);

                                $filename = $_SERVER['DOCUMENT_ROOT']."/".$filename;

                                $objPHPExcel = new PHPExcel();

                                $objExcelReader = PHPExcel_IOFactory::load($filename);

                                $worksheet = $objExcelReader->getActiveSheet();
                                $colName = $objExcelReader->getActiveSheet()->getHighestColumn();
                                $columnIndex = \PHPExcel_Cell::columnIndexFromString($colName); // in phpexcel column index starts from 1

                                foreach ($worksheet->getRowIterator() as $row) {

                                        $rowIndex = $row->getRowIndex();

                                    $cellIterator = $row->getCellIterator();
                                    $cellIterator->setIterateOnlyExistingCells(false); // Loop all cells, even if it is not set.

                                    $cnter = 1;

                                    foreach ($cellIterator as $cell) {

                                          if($cnter > $columnIndex){
                                                 break;
                                          }
                                          $importedData[$rowIndex][] = $cell->getValue();
                                          $cnter++;
                                    }

                                }

                                if(file_exists($filename)){

                                        unlink($filename);

                                }

                        }



                }elseif($data['submit']=='Import'){

                        if(count($data['importData'])<1){

                                $errors["column"][] = "";
                                $str = " Please Enter Proper Data <br />";
                                $errors["msg"] = (isset($errors["msg"])) ? $errors["msg"].$str : $str ;

                                goto SHOWDETAILS;

                        }

                        // validation data ...

                        $isEmpty = new \Zend\Validator\NotEmpty();
                        $validLength = new \Zend\Validator\StringLength(array('max' => 50));
                        $validDigit = new \Zend\Validator\Digits();
                        $validDigitLength = new \Zend\Validator\StringLength(array('max' => 15,'min'=>10));
                        $validEmailAddress = new \Zend\Validator\EmailAddress();

                        $importCustomerValidator = new ImportCustomerValidator();
                        //$importCustomerValidator->setAdapter($adapt);

                        $validRegex = new \Zend\Validator\Regex(array('pattern' => "/^[a-zA-Z0-9\s\.\'\']+$/"));

                        $objLocation = $sm->get('QuickServe\Model\LocationTable');
                        $objLocationValidator = new LocationValidator();
                       // $objLocationValidator->setAdapter($adapt);

                        $objGroup = $sm->get('QuickServe\Model\CustGroupTable');

                        $objThirdParty = $sm->get('QuickServe\Model\ThirdpartyTable');

                        $utility = Utility::getInstance();

                        $selectedColumns = array();

                        foreach($customerTableColumn as $key=>$col){

                                if($col !=""){

                                        if($col=='city_name'){

                                                $selectedColumns[] = "city";
                                        }
                                        
                                        $selectedColumns[] = $col;
                                }

                        }
                        // add default columns to be inserted  
                        $selectedColumns[] = "registered_on";
                        $selectedColumns[] = "registered_from";
                        $selectedColumns[] = "phone_verified";
                        $selectedColumns[] = "email_verified";

                        $placeholder = array_fill(0, count($selectedColumns), '?');
                        $placeholder = "(" . implode(',', $placeholder) . ")";

                        $rowCountSuccess = 0;
                        $importErrors = array();

                        $columnsStr = "(" .implode(",",$selectedColumns). ")";

                        $validStatus = array('Active','Inactive','active','inactive','ACTIVE','INACTIVE');
                        $validSubscription = array('yes','no','Yes','No','YES','NO');
                        $validLocations = $objLocation->fetchAll();
                        $validLocations = $validLocations->toArray();
                        $importCustomerValidator->setServiceLocator($sm);
                        $validCities = $importCustomerValidator->getCities();
                        $select = new QSelect();
                        $select->where(array("groups.status"=>1));
                        $validGroupCodes = $objGroup->fetchAll($select);
                        $validGroupCodes = $validGroupCodes->toArray();

                        $select = new QSelect();
                        $select->where(array("status"=>1));
                        $validThirdParties = $objThirdParty->fetchAll($select);
                        $validThirdParties = $validThirdParties->toArray();

                        $customerCount = $this->getCustomerTable()->getCustomerCount();
                        $activeCustomers = $customerCount['count'];

                        $customer_address_import_data=array();

                        foreach ($data['importData'] as $import_val){

                            $result = [];
                            $counter = 0;

                            array_map(function($v1, $v2)use(&$result, &$counter){
                                $result[!is_null($v1) ? $v1 : NULL . $counter++] = !is_null($v2) ? $v2 : NULL;     
                            }, $data['column'], $import_val);
                            
                             $new_import_data[] = $result;

                        }

                        $totalRows = count($new_import_data);

                        foreach($new_import_data as $rowkey=>$row){
                                $erow = "'".($rowkey+1)."'";
                                $flgError = 0;
                                $tmpValues = array();
                                $placeholderValues = array();
                                // Check subscription key for exceeding customer limit...

                                $subscriptionCheck = $utility->checkSubscription("customer_active",'count',$activeCustomers);

                                if(!$subscriptionCheck){

                                        $str = " Maximum no. of customer limit has reached ";

                                        if(isset($importErrors[$erow])){

                                                $importErrors[$erow] .= "<br/>".$str;

                                        }else{

                                                $importErrors[$erow] = $str;
                                        }
                                        break;
                                }

                                $temp_cust_adds=array();
                                $address_count=0;



                                if(($new_import_data[$rowkey]['email_address']=='') && empty($new_import_data[$rowkey]['email_address']) && (($new_import_data[$rowkey]['phone']=='') && empty($new_import_data[$rowkey]['phone']))){

                                        $flgError = 1;
                                        $tmpFlg = 1;
                                        $str = "Please Specify either email address or phone number.";

                                        if(isset($importErrors[$erow])){
                                                $importErrors[$erow] .= "<br/>".$str;
                                        }else{

                                                $importErrors[$erow] = $str;
                                        }

                                }
                                
                                foreach($data['column'] as $key=>$col){

                                        // Only those columns which have to be imported.
                                        if($col !=""){

                                                ///////////////////////////////////

                                                $checkForValid = true; 

                                                $value = trim($row[$col]);



                                                if(in_array($col, $optional_field)){



                                                        if($col=='email_address' || $col=="phone"){

                                                                $relCol = "";

                                                                if($col=='phone'){

                                                                        $relCol = "email_address";

                                                                }elseif($col=='email_address'){

                                                                        $relCol = "phone";
                                                                }

                                                                if($value==""){

                                                                        // check if email address is blank or not...
                                                                        $relKey = array_search($relCol,$data['column']);



                                                                        if(gettype($relKey)=='integer'){ // key found in case of integer..

                                                                                // Check if email address is provided or not..
                                                                                if(trim($row[$relKey]) !=""){

                                                                                        $checkForValid = false;
                                                                                }

                                                                        }

                                                                }

                                                        }elseif($value==""){
                                                                $checkForValid = false;
                                                        }

                                                }



                                                /////////////////////////////////////

                                                switch($col){

                                                        case "customer_name":

                                                                if($checkForValid){
                                                                        $tmpFlg = 0;
                                                                        if (!$isEmpty->isValid($value)) {

                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify the customer name";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }
                                                                        }

                                                                        if(!$tmpFlg && !$validRegex->isValid($value)){

                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify valid customer name";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }
                                                                        }
                                                                }

                                                                break;
                                                        case "phone":

                                                                if($checkForValid){
                                                                        $tmpFlg = 0;
                                                                        if((isset($value) && !empty($value))){
                                                                                // valid phone

                                                                                $tmpValues['phone_verified']="1";

                                                                                if (!$isEmpty->isValid($value)) {
                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Please specify phone no.";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{

                                                                                                $importErrors[$erow] = $str;
                                                                                        }
                                                                                }

                                                                                if (!$tmpFlg && !$validDigit->isValid($value)) {
                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Please specify valid phone no.";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{

                                                                                                $importErrors[$erow] = $str;
                                                                                        }
                                                                                }

                                                                                if (!$tmpFlg && !$validDigitLength->isValid($value)) {
                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Please enter 10-Digit Phone Number";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{

                                                                                                $importErrors[$erow] = $str;
                                                                                        }
                                                                                }

                                                                                $validator = new \Zend\Validator\Db\NoRecordExists(
                                                                                        array(
                                                                                                        'table'   => 'customers',
                                                                                                        'field'   => 'phone',
                                                                                                        'adapter' => $adapt
                                                                                        )
                                                                                );

                                                                                if (!$tmpFlg && !$validator->isValid($value)) {

                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Customer's phone no. already exists";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{
                                                                                                $importErrors[$erow] = $str;
                                                                                        }
                                                                                }

                                                                        }

                                                                }

                                                                // db record exists
                                                                break;

                                                        case "email_address":

                                                                if($checkForValid){
                                                                        $tmpFlg = 0;

                                                                        if((isset($value) && !empty($value))){
                                                                                // valid email address 
                                                                                if (!$validEmailAddress->isValid($value)) {
                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Email address is invalid";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{

                                                                                                $importErrors[$erow] = $str;
                                                                                        }
                                                                                }

                                                                                $validator = new \Zend\Validator\Db\NoRecordExists(
                                                                                        array(
                                                                                                'table'   => 'customers',
                                                                                                'field'   => 'email_address',
                                                                                                'adapter' => $adapt
                                                                                        )
                                                                                );

                                                                                if (!$tmpFlg && !$validator->isValid($value)) {

                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        foreach ($validator->getMessages() as $message) {
                                                                                                $str = "Customer's email address already exists";
                                                                                        }
                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{
                                                                                                $importErrors[$erow] = $str;
                                                                                        }

                                                                                }
                                                                        }

                                                                }

                                                                // db record exists	
                                                                break;

                                                        case "city_name":

                                                                if($checkForValid){
                                                                        $tmpFlg = 0;
                                                                        if (!$isEmpty->isValid($value)) {
                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify city Name";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }
                                                                        }

                                                                foreach ($validCities as $validCity=>$validCityName){

                                                                                if(strtolower($value)==strtolower($validCityName)){
                                                                                        $tmpFlg = 1;
// 												$value = $validCity;
                                                                                        $tmpValues['city']=$validCity;
                                                                                        // Add group name along with group code.
// 												$tmpValues[$col] = $validCityName;
                                                                                        break;
                                                                                }
                                                                        }

                                                                        if(!$tmpFlg){

                                                                                $flgError = 1;

                                                                                //$str = "Please specify valid city. Accepted value are ".implode(",",$validCities)." ";

                                                                                $str = "Please specify valid city Name.";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }

                                                                        }
                                                                }

                                                                break;

                                                        case "status":

                                                                if($checkForValid){
                                                                        $tmpFlg = 0;

                                                                        if (!$isEmpty->isValid($value)) {
                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify status";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }
                                                                        }

                                                                        if(!$tmpFlg && !in_array($value, $validStatus)){
                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify valid Status. Accepted value are Active, Inactive ";//.implode(", ",$validStatus)." ";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }


                                                                        }

                                                                        $value = (strtolower($value)=='active') ? 1 : 0;


                                                                }

                                                                break;


                                                                case "subscription_notification":

                                                                        if (!$isEmpty->isValid($value)) {
                                                                                $flgError = 1;
                                                                                $tmpFlg = 1;
                                                                                $str = "Please specify subscription notification";

                                                                                if(isset($importErrors[$erow])){
                                                                                        $importErrors[$erow] .= "<br/>".$str;
                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }
                                                                        }

                                                                        if($checkForValid){
                                                                                $tmpFlg = 0;

                                                                                if(!$tmpFlg && !in_array($value, $validSubscription)){
                                                                                        $flgError = 1;
                                                                                        $tmpFlg = 1;
                                                                                        $str = "Please specify valid subscription notification. Accepted value are yes , no ";

                                                                                        if(isset($importErrors[$erow])){
                                                                                                $importErrors[$erow] .= "<br/>".$str;
                                                                                        }else{

                                                                                                $importErrors[$erow] = $str;
                                                                                        }


                                                                                }

                                                                                $value = (strtolower($value) == 'yes') ? 'yes' : 'no';


                                                                        }

                                                                        break;
//
//                                                        case "group_name":
//
//
//                                                                if($checkForValid){
//
//                                                                        $tmpFlg = 0;
//
//                                                                        foreach ($validGroupCodes as $validGroupCode){
//
//                                                                                if(strtolower($value)==strtolower($validGroupCode['group_name'])){
//
//                                                                                        $tmpFlg = 1;
//// 												$value = $validGroupCode['group_code'];
//                                                                                        $tmpValues['group_code']=$validGroupCode['group_code'];
//                                                                                        // Add group name along with group code.
//// 												$tmpValues[$col] = $validGroupCode['group_name'];
//                                                                                        break;
//                                                                                }
//                                                                        }
//
//                                                                        if(!$tmpFlg){
//
//                                                                                $flgError = 1;
//
//                                                                                $str = "Please specify valid Group Name";
//
//                                                                                if(isset($importErrors[$erow])){
//                                                                                        $importErrors[$erow] .= "<br/>".$str;
//                                                                                }else{
//
//                                                                                        $importErrors[$erow] = $str;
//                                                                                }
//
//                                                                        }
//
//                                                                }else{
//                                                                        $tmpValues['group_code']="";
//                                                                        $tmpValues[$col] = "";
//                                                                }
//
//                                                                break;
//
//                                                        case "thirdparty":
//
//                                                                if($checkForValid){
//
//                                                                        $tmpFlg = 0;
//
//                                                                        foreach ($validThirdParties as $validThirdParty){
//
//                                                                                if(strtolower($value)==strtolower($validThirdParty['name'])){
//
//                                                                                        $tmpFlg = 1;
//
//                                                                                        $value = $validThirdParty['third_party_id'];
//                                                                                        break;
//                                                                                }
//                                                                        }
//
//                                                                        if(!$tmpFlg){
//
//                                                                                $flgError = 1;
//
//                                                                                $str = "Please specify valid Third Party";
//
//                                                                                if(isset($importErrors[$erow])){
//
//                                                                                        $importErrors[$erow] .= "<br/>".$str;
//
//                                                                                }else{
//
//                                                                                        $importErrors[$erow] = $str;
//                                                                                }
//
//                                                                        }
//
//                                                                }
//
//                                                                break;									
                                                }

                                                $adds = strpos($col, "location_address");
                                                $loc = strpos($col, "location_name");
                                                $lcode = strpos($col, "dabbawala_code");
                                                        
                                                //dd($validLocations);    
                                                if($adds || $loc  || $lcode  ){

                                                        if($loc && isset($value) && !empty($value)){

                                                                $address_count++;
                                                                
                                                                $validLocation = $importCustomerValidator->checkValidLocation($value,$validLocations);
                                                                
                                                                if($validLocation){
                                                                        $temp_adds=array();
                                                                        $temp_adds['menu']=substr($col, 0,strpos($col, "location_name")-1);
                                                                        $temp_adds['location_code']=$validLocation['pk_location_code'];
                                                                        $temp_adds['location_name']=$value;
                                                                        $temp_adds['location_address']=$new_import_data[$rowkey][substr($col, 0,strpos($col, "location_name")-1).'_location_address'];

                                                                        if(isset($temp_adds['location_address']) && !empty($temp_adds['location_address']) && $temp_adds['location_address'] != ''){

                                                                                if(isset($new_import_data[$rowkey][substr($col, 0,strpos($col, "location_name")-1).'_location_address']) && !empty($new_import_data[$rowkey][substr($col, 0,strpos($col, "location_name")-1).'_location_address'])){
                                                                                        $temp_adds['dabbawala_type']='text';
                                                                                        $temp_adds['dabbawala_text']=$new_import_data[$rowkey][substr($col, 0,strpos($col, "location_name")-1).'_dabbawala_code'];
                                                                                }else{
                                                                                        $temp_adds['dabbawala_type']='text';
                                                                                        $temp_adds['dabbawala_text']=$new_import_data[$rowkey][substr($col, 0,strpos($col, "location_name")-1).'_dabbawala_code'];

                                                                                }

                                                                                $temp_cust_adds[]=$temp_adds;

                                                                        }else{

                                                                                $flgError = 1;

                                                                                $str = "Please enter ".$temp_adds['menu']=substr($col, 0,strpos($col, "location_name")-1)." location address";

                                                                                if(isset($importErrors[$erow])){

                                                                                        $importErrors[$erow] .= "<br/>".$str;

                                                                                }else{

                                                                                        $importErrors[$erow] = $str;
                                                                                }


                                                                        }




                                                                }
                                                                else{

                                                                        $flgError = 1;

                                                                        $str = "Please specify valid ".$temp_adds['menu']=substr($col, 0,strpos($col, "location_name")-1)." location name";

                                                                        if(isset($importErrors[$erow])){

                                                                                $importErrors[$erow] .= "<br/>".$str;

                                                                        }else{

                                                                                $importErrors[$erow] = $str;
                                                                        }

                                                                }
                                                        }	

                                                }else{

                                                        $tmpValues[$col] = $value;

                                                }


                                        }

                                } // end of columns ...
                                if($address_count < 1){

                                        $flgError = 1;

                                        $str = "You should have atleast one address in customer detail with valid location name and location address";

                                        if(isset($importErrors[$erow])){

                                                $importErrors[$erow] .= "<br/>".$str;

                                        }else{

                                                $importErrors[$erow] = $str;
                                        }

                                }

                                if(!$flgError && $address_count>0){

                                        if(!empty($tmpValues)){


                                                $tmpValues['registered_on']=date("Y-m-d");// registered on.
                                                $tmpValues['registered_from']="Admin";// registered from.
                                                $tmpValues['email_verified']="no";// phone verified.


                                                $rowCountSuccess++;
//                                               / dd($tmpValues);    
                                                $placeholderValues = array_merge($placeholderValues,$tmpValues);

                                                $activeCustomers++;

                                        }
                                }
                               
                                if(!empty($placeholderValues)){

                                        $id = $libCustomer->saveImportCustomer($placeholderValues);
                                        
                                        if(isset($placeholderValues['email_address']) && !empty($placeholderValues['email_address'])){

                                                $libCommon->sendWelcomeSMS($id,false);

                                                $libCommon->sendemailAuthenticationEmail($id,false);

                                        }

                                        $flag=true;

                                        foreach ($temp_cust_adds as $adds_key=>$val_adds){

                                                $temp_cust_adds[$adds_key]['fk_customer_code']=$id;

                                                $temp_cust_adds[$adds_key]['created_on']=date("Y-m-d");

                                                $libCustomer->saveCustomerAddress(array(0=>$temp_cust_adds[$adds_key]),$id,$flag);

                                                $flag = ($flag)?false:true;

                                        }

                                }

                        }

                        // Forward request to result page...

                        return $this->forward()->dispatch('Admin\Controller\Customer', array(
                                'action' => 'import-result',
                                'rowCountSuccess' => $rowCountSuccess,
                                'totalRows' => $totalRows,
                                'importErrors' => $importErrors,
                        ));

                }

        }


        SHOWDETAILS:

        $this->layout()->setVariables(array('page_title'=>"Import Customers",'description'=>"Import Customers",'breadcrumb'=>"Import Customers",'activetab' => 'customer/import'));

        $view = new ViewModel();

        $view->setVariables(
                array(
                        'columns' =>$columns,
                        'importedData' =>$importedData,
                        'form' =>$form,
                        'errors' =>$errors
                )
        );

        return $view;

}
/**
 * use to import customer
 * @method importResultAction()
 * @return \Zend\View\Model\ViewModel
 */
public function importResultAction(){
        /* ini_set('post_max_size', '128M');
        ini_set('upload_max_filesize', '128M');
        ini_set('memory_limit', '128M');
        ini_set('max_execution_time', '200');
        ini_set('max_input_time', '200');
        ini_set('max_input_vars', '4000'); */

        $rowCountSuccess = $this->params()->fromRoute('rowCountSuccess');
        $totalRows = $this->params()->fromRoute('totalRows');
        $importErrors = $this->params()->fromRoute('importErrors');


        $this->layout()->setVariables(array('page_title'=>"Import Customers Result",'description'=>"Import Customers Result",'breadcrumb'=>"Import Customers"));

        $view = new ViewModel();

        $view->setVariables(
                array(
                        'rowCountSuccess' =>$rowCountSuccess,
                        'totalRows' =>$totalRows,
                        'importErrors' =>$importErrors
                )
        );

        return $view;

}


/**
 * use to convert lock amount to debit amount
 * @method convertLockAction()
 * @param int customer_wallet_id
 * @param string description
 * @return array $result
 */
public function convertLockAction()
{
        $customer_wallet_id=$this->request->getPost('customer_wallet_id');
        $description=$this->request->getPost('description');
        $result=$this->getCustomerTable()->updateLockAmt($customer_wallet_id,$description);
        return $result;
} 

/**
 * use to edit customer wallet amount
 * @method editcustomerwalletAction()
 * @param int id customer wallet id
 * @param string amt old amount
 * @param string description old description
 * @param string hdnavailbal available balance
 * @param int customerwalletid  customer wallet id
 * @param string custwalletamt  new entered wallet amount
 * @param string custwalletdesc new entered description
 * @return \Zend\View\Model\ViewModel
 */
public function editcustomerwalletAction()
{
        $getcustomerwalletdetails=array();
        $view = new ViewModel();
        $view->setTerminal(true);

        $customer_wallet_id = (int) $this->params()->fromRoute('id');

        $cusr_wallet_amount = $this->params()->fromRoute('amt');

        $cust_wallet_description = $this->params()->fromRoute('description');

        $available_balance = $this->params()->fromRoute('hdnavailbal');

        if($customer_wallet_id != "")
        {
                $getcustomerwalletdetails = $this->getCustomerTable()->getcustomerwalletdata($customer_wallet_id);
                $getcustomerwalletdetails['available_balance']=$available_balance;
        }

        $request = $this->getRequest();

        if ($request->isPost()) {
                $cust_wallet_id = $request->getPost('customerwalletid');
                $custwalletamt = $request->getPost('custwalletamt');
                $description = $request->getPost('custwalletdesc');
                $updatecustwalletdata = $this->getCustomerTable()->editLockAmt($cust_wallet_id,$custwalletamt,$description);
                if($updatecustwalletdata)
                {
                        echo "success";
                        exit();
                }
        }

        $view->setVariables(array('customerwalletdetails' => $getcustomerwalletdetails[0]));
        return $view;
}

/**
 * 
 * Dynamically generate the xls file for customer import
 * @return customer_import_template.xls 
 * 
 */

public function exportImportTemplateAction(){

        $sm = $this->getServiceLocator();
        $adapter = $sm->get("Write_Adapter");

        $setting_session = new Container('setting');

        $setting = $setting_session->setting;

        $libCustomer = QSCustomer::getInstance($sm);

        $view = new ViewModel();

        $view->setTemplate(true);

        $table = "customers";

        $columns_result = $libCustomer->getColumnsFromTable($table,true);

        $request = $this->getRequest();

        $exclude_columns = array('pk_customer_code','company_id','unit_id','city','food_referance','group_code','registered_from','city','location_code','location_name','lunch_loc_name','lunch_add','dabbawala_code','dinner_loc_name','dinner_add','product_code','last_modified','lunch_loc_code','dabbawala_code_type','dabbawala_image','dinner_loc_code','registered_on','otp','password','thirdparty','delivery_person_id','lunch_dp_id','dinner_dp_id','phone_verified','email_verified');

        $columns = array_diff($columns_result,$exclude_columns);
       
        $column_vals=array();

        foreach ($setting['MENU_TYPE'] as $menu){

                array_push($columns, $menu."_location_address");
                array_push($columns, $menu."_location_name");
// 			array_push($columns, $menu."_location_code");
                array_push($columns, $menu."_dabbawala_code");

        }

// 		echo "<pre>"; print_r($columns); exit();

        foreach ($columns as $col_val){
                $column_vals[$col_val]='';
        }


        ini_set("max_execution_time", 0);
        ini_set('memory_limit', '300M');

        $config = $this->getServiceLocator()->get("config");


        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;


        $libCommon = QSCommon::getInstance($sm);

        $objPHPExcel = new PHPExcel();

        // Set document properties

        $objPHPExcel->getProperties()->setCreator("Fooddialer")
        ->setLastModifiedBy("Fooddialer")
        ->setTitle("PHPExcel Document")
        ->setSubject("Fooddialer Report")
        ->setDescription("Report")
        ->setKeywords("Fooddialer")
        ->setCategory("Fooddialer");

        $count = count($columns);

        $headerSheet = array();

        foreach ($columns as $key=>$column){

                $colname = str_replace("_", " ", $column);
                $columnName =  ucwords($colname);
                $headerSheet[] = $columnName;

        }


        $objPHPExcel->getActiveSheet()->fromArray($headerSheet, '', 'A1');



        $highestColumn = $objPHPExcel->getActiveSheet()->getHighestColumn();


        for ($col = ord('A'); $col <= ord($highestColumn); $col++)
        {
                $objPHPExcel->getActiveSheet()->getColumnDimension(chr($col))->setAutoSize(true);
        }

        $header_range = "A1:{$highestColumn}1";

        $objPHPExcel->getActiveSheet()->getStyle($header_range)->getFont()->setBold(true);



        /////////////////////////////////////////// code to write data in excelsheet /////////////////////////////////

/*		$writeData = array();

        foreach ($data as $key=>$orders) {

                $tempArray = array();
                foreach ($headerSheet as $col)
                {
                        if($col=='status'){
                                if($orders[$col]==1 || $orders[$col]=='1'){
                                        $orders[$col] = 'Active';
                                }else{
                                                $orders[$col] = 'Inactive';
                                        }
                        }
                                if($col=='phone_verified'){
                                        if($orders[$col]==1 || $orders[$col]=='1'){
                                                $orders[$col] = 'Yes';
                                        }else{
                                                $orders[$col] = 'No';
                                        }
                                }
                                $tempArray[$col] = $orders[$col];
                }
                                $writeData[] = $tempArray;

        }

        $objPHPExcel->getActiveSheet()->fromArray($writeData, ' ', 'A2');
*/	


        ///////////////////////////////////////// code to write data in excelsheet /////////////////////////////////

        $objPHPExcel->getActiveSheet()->setTitle('customer_import_template');

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet

        $objPHPExcel->setActiveSheetIndex(0);

        $filename = "customer_import_template.xls";
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
        $objWriter->save($filenamePath);

        header('Content-type: application/vnd.ms-excel');

                                // It will be called file.xls
        header('Content-Disposition: attachment; filename="'.$filename.'"');

        $objWriter->save('php://output');

        $objPHPExcel->disconnectWorksheets();
        unset($objPHPExcel);
        unlink($filenamePath);


}

/**
 * use to export customer data
 * @method exportDataAction()
 * @param string table
 * @param string exporttype
 * @param string form
 * @return \Zend\View\Model\ViewModel
 */
public function exportDataAction(){

        $sm = $this->getServiceLocator();
        //$adapter = $sm->get("Write_Adapter");

        $setting_session = new Container('setting');
        $setting = $setting_session->setting;

        $utility = Utility::getInstance();
        $libCustomer = QSCustomer::getInstance($sm);

        $view = new ViewModel();
        $view->setTerminal(true);
        $type = $this->params()->fromQuery('table');
        $exporttype = $this->params()->fromQuery('exporttype');

        $filterForm = array();
        $filterSring = $this->params()->fromQuery('form');

        parse_str($filterSring,$filterForm);
        $table = "customers";


        $columns_result = $libCustomer->getColumnsFromTable($table);

        $columns=array_merge($columns_result,array('CustomerAddress','CustomerLocation','DabbawalaCode'));

        $request = $this->getRequest();


        if($request->isPost()){
                $formData = $request->getPost();
        //echo "<pre>";print_r($formData);exit; /*  customer_expload */
                switch ($formData['table']){
                        case 'customers':
                                $reportType = $formData['table'];
// 					$exportData_header=array();
                            
                                $exportData_result=array();
                                $exportData = $this->getCustomerTable()->getCustomerExportData($formData,false,false,false,false,'export',$reportType);
                                
                                if(count($exportData['customer_address'])>0){

                                        foreach ($exportData['exportdata'] as $e_key=>$e_val){

                                                foreach ($exportData['customer_expload'] as $exp_value){
                                                        $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exp_value]=$exportData['exportdata'][$e_key][$exp_value];
                                                }

                                                if ((isset($exportData['exportdata'][$e_key]['registered_on']) && !empty($exportData['exportdata'][$e_key]['registered_on']))){

                                                        $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']]['registered_on'] = $utility->displayDate($exportData['exportdata'][$e_key]['registered_on'],$setting['DATE_FORMAT']);

                                                }

                                                if (in_array("CustomerAddress", $exportData['customer_address']) && (isset($exportData['exportdata'][$e_key]['menu_type']) && !empty($exportData['exportdata'][$e_key]['menu_type']))){
                                                                $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_code']=(isset($exportData['exportdata'][$e_key]['location_code']))?$exportData['exportdata'][$e_key]['location_code']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_code']=$exportData['exportdata'][$e_key]['location_code'];
// 									unset($exportData['exportdata'][$e_key]['location_code']);

                                                                //$exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_name']=(isset($exportData['exportdata'][$e_key]['location_name']))? $exportData['exportdata'][$e_key]['location_name']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_name']=$exportData['exportdata'][$e_key]['location_name'];
// 									unset($exportData['exportdata'][$e_key]['location_name']);

                                                                $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_address']=(isset($exportData['exportdata'][$e_key]['location_address']))? $exportData['exportdata'][$e_key]['location_address']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_address']=$exportData['exportdata'][$e_key]['location_address'];
// 									unset($exportData['exportdata'][$e_key]['location_address']);

                                                }

                                                if (in_array("CustomerLocation", $exportData['customer_address']) && (isset($exportData['exportdata'][$e_key]['menu_type']) && !empty($exportData['exportdata'][$e_key]['menu_type']))){
                                                        $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_code']=(isset($exportData['exportdata'][$e_key]['location_code']))?$exportData['exportdata'][$e_key]['location_code']:'';
                                                        // 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_code']=$exportData['exportdata'][$e_key]['location_code'];
                                                        // 									unset($exportData['exportdata'][$e_key]['location_code']);

                                                        $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_name']=(isset($exportData['exportdata'][$e_key]['location_name']))? $exportData['exportdata'][$e_key]['location_name']:'';
                                                        // 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_name']=$exportData['exportdata'][$e_key]['location_name'];
                                                        // 									unset($exportData['exportdata'][$e_key]['location_name']);

                                                        //$exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_location_address']=(isset($exportData['exportdata'][$e_key]['location_address']))? $exportData['exportdata'][$e_key]['location_address']:'';
                                                        // 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_location_address']=$exportData['exportdata'][$e_key]['location_address'];
                                                        // 									unset($exportData['exportdata'][$e_key]['location_address']);

                                                }

                                                if (in_array("DabbawalaCode", $exportData['customer_address']) && (isset($exportData['exportdata'][$e_key]['menu_type']) && !empty($exportData['exportdata'][$e_key]['menu_type']))){

                                                                $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_code_type']=(isset($exportData['exportdata'][$e_key]['dabbawala_code_type']))?  $exportData['exportdata'][$e_key]['dabbawala_code_type']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_code_type']=$exportData['exportdata'][$e_key]['dabbawala_code_type'];
// 									unset($exportData['exportdata'][$e_key]['dabbawala_code_type']);

                                                                $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_code']=(isset($exportData['exportdata'][$e_key]['dabbawala_code']))? $exportData['exportdata'][$e_key]['dabbawala_code']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_code']=$exportData['exportdata'][$e_key]['dabbawala_code'];
// 									unset($exportData['exportdata'][$e_key]['dabbawala_code']);

                                                                $exportData_result[$exportData['exportdata'][$e_key]['pk_customer_code']][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_image']=(isset($exportData['exportdata'][$e_key]['dabbawala_image']))? $exportData['exportdata'][$e_key]['dabbawala_image']:'';
// 									$exportData['exportdata'][$e_key][$exportData['exportdata'][$e_key]['menu_type'].'_dabbawala_image']=$exportData['exportdata'][$e_key]['dabbawala_image'];
// 									unset($exportData['exportdata'][$e_key]['dabbawala_image']);


                                                }

                                        }

                                        foreach ($setting['MENU_TYPE'] as $m_val){
                                                if (in_array("CustomerAddress", $exportData['customer_address'])){

                                                //	array_push($exportData['customer_expload'],$m_val.'_location_name');
                                                        array_push($exportData['customer_expload'],$m_val.'_location_address');
                                                }
                                                if (in_array("CustomerLocation", $exportData['customer_address'])){

                                                        array_push($exportData['customer_expload'],$m_val.'_location_name');
                                                        //array_push($exportData['customer_expload'],$m_val.'_location_address');
                                                }
                                                if(in_array("DabbawalaCode", $exportData['customer_address'])){
                                                        array_push($exportData['customer_expload'],$m_val.'_dabbawala_code_type');
                                                        array_push($exportData['customer_expload'],$m_val.'_dabbawala_code');

                                                }
                                        }

                                        foreach ($exportData_result as $exp_key=>$exp_val){
                                                foreach ($exportData['customer_expload'] as $head_val){
                                                        if(!isset($exp_val[$head_val]) || empty($exp_val[$head_val])){
                                                                $exportData_result[$exp_key][$head_val]='';
                                                        }
                                                }
                                        }

                                }else{

                                        foreach ($exportData['exportdata'] as $key => $val){
                                                if(isset($val['registered_on'])){
                                                        $exportData['exportdata'][$key]['registered_on'] = $utility->displayDate($val['registered_on'],$setting['DATE_FORMAT']);
                                                }
                                                //array_push($exportData_result,$exportData['exportdata']);
                                        }

                                        $exportData_result=$exportData['exportdata'];
                                }

                                $formData['selected_columns']=implode(",", $exportData['customer_expload']);

                                //echo "<pre> rerte"; print_r($exportData_result); exit(); //$exportData_result  $exportData['customer_expload']

                                return $this->forward()->dispatch('Admin\Controller\Customer', array(
                                                'printData' => $exportData_result,										//$exportData['exportdata'],
                                                'action' => 'printReport',
                                                'formData' =>$formData,
                                ));
                                break;

                        case 'invoice':
                                break;
                }

        }
        $view->setVariables(array('columns' =>$columns,'table' => $type,'filterForm'=>$filterForm,'exporttype'=>$exporttype));
        return $view;
}


/**
 * use to print customer report
 * @method  printReportAction()
 * @param string printData
 * @return \DOMPDFModule\View\Model\PdfModel|\Zend\View\Model\ViewModel
 */
public function printReportAction(){
        ini_set("max_execution_time", 0);
        ini_set('memory_limit', '300M');
        $data = $this->params()->fromRoute('printData');
        //echo "<pre>";print_r($data);die;

        $formData = $this->params()->fromRoute('formData');

        $config = $this->getServiceLocator()->get("config");
        $selected_columns= array();
        if(isset($formData['selected_columns'])){
                $selected_columns = explode(',',$formData['selected_columns']);
                //array_unshift($selected_columns,'pk_order_no');
        }else{
                //$selected_columns = array('pk_order_no','customer_name','phone','location_name','product_name','amount','order_status','delivery_status','order_date');
                $selected_columns = array('order_no','customer_name','phone','location_name','product_name','amount','order_status','delivery_status','order_date');
        }

        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;

        $sm = $this->getServiceLocator();

        $adapt = $sm->get('Write_Adapter');

        $libCommon = QSCommon::getInstance($sm);

        $full_name=$loguser->first_name." ".$loguser->last_name;
        $location_name=(isset($location->location))?$location->location:'';
        $activity_log_data=array();
        $activity_log_data['context_ref_id']=$loguser->pk_user_code;
        $activity_log_data['context_name']= $full_name;
        $activity_log_data['context_type']= 'user';
        $activity_log_data['controller']= 'customer';
        $activity_log_data['action']= 'printReport';
        $activity_log_data['description']= "Export/Import : Customer data Exported (".$formData['export_type'].").";
        $libCommon->saveActivityLog($activity_log_data);

        switch($formData['export_type'])
        {

                case "pdf":

                        $model = new PdfModel();
                        $model->setOption('fileName', 'invoice-45');
                        $model->setOption('paperSize', 'A4');
                        $model->setOption('paperOrientation', 'portrait');
                        $sm = $this->getServiceLocator();
                        $config = $sm->get('config');
                        $variables = array();
                        $variables['data'] = $data;
                        $variables['root_url'] = $config['root_url'];
                        $variables['minDate'] = $formData['minDate'];
                        $variables['maxDate'] = $formData['maxDate'];
                        $variables['columns'] = $selected_columns;
                        $variables['config'] = $config;

                        $model->setVariables($variables);
                        $model->setTemplate('admin/report/templates/'.$formData['service']);
                        return $model;
                        break;

                case "xls":


                        $objPHPExcel = new PHPExcel();

                        // Set document properties

                        $objPHPExcel->getProperties()->setCreator("Fooddialer")
                        ->setLastModifiedBy("Fooddialer")
                        ->setTitle("PHPExcel Document")
                        ->setSubject("Fooddialer Report")
                        ->setDescription("Report")
                        ->setKeywords("Fooddialer")
                        ->setCategory("Fooddialer");

                        $count = count($selected_columns);
                        //$range = range('a','z');
                        //$last = $range[$count-1];
                        //$colRange = range('a',$last);
                        //$alpha ='a';

                        $headerSheet =array();
                        foreach ($selected_columns as $key=>$column){
                                $colname = str_replace("_", " ", $column);
                                $colname = ($colname == 'pk customer code')?'customer code':$colname;
                                $colname = ($colname == 'lunch loc code')?'Lunch Location Code':$colname;
                                $colname = ($colname == 'lunch loc name')?'Lunch Location Name':$colname;
                                $colname = ($colname == 'dinner loc code')?'Dinner Location Code':$colname;
                                $colname = ($colname == 'dinner loc name')?'Dinner Location Name':$colname;
                                $colname = ($colname == 'lunch add')?'Lunch Address':$colname;
                                $colname = ($colname == 'dinner add')?'Dinner Address':$colname;
                                $colname = ($colname == 'registered from')?'registered by':$colname;
                                $columnName =  ucwords($colname);
                                $headerSheet[] = $columnName;
                        }

        //echo "<pre>";print_r($formData);die;
                        //	echo $highestColumn;die;
                        $objPHPExcel->getActiveSheet()->fromArray($headerSheet, '', 'A1');


                        $highestColumn = $objPHPExcel->getActiveSheet()->getHighestColumn();

                        //	echo $highestColumn;die;

                        for ($col = ord('A'); $col <= ord($highestColumn); $col++)
                        {
                        $objPHPExcel->getActiveSheet()->getColumnDimension(chr($col))->setAutoSize(true);
                        }		
        //echo "<pre>";print_r($formData);die;

                        $header_range = "A1:{$highestColumn}1";
                        $objPHPExcel->getActiveSheet()->getStyle($header_range)->getFont()->setBold(true);

                        $writeData = array();
                        foreach ($data as $key=>$orders) {
                        $tempArray = array();
                        foreach ($selected_columns as $col)
                        {
                        if($col=='status'){
                        if($orders[$col]==1 || $orders[$col]=='1'){
                        $orders[$col] = 'Active';
                        }else{
                        $orders[$col] = 'Inactive';
                        }
                        }
                        if($col=='phone_verified'){
                        if($orders[$col]==1 || $orders[$col]=='1'){
                        $orders[$col] = 'Yes';
                        }else{
                                $orders[$col] = 'No';
                                }
                                }
                                $tempArray[$col] = $orders[$col];

                                }
                                $writeData[] = $tempArray;
                        }

                                $objPHPExcel->getActiveSheet()->fromArray($writeData, ' ', 'A2');

                                $objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');

                                // Set active sheet index to the first sheet, so Excel opens this as the first sheet
                                $objPHPExcel->setActiveSheetIndex(0);

                                $filename = "{$formData['service']}_report.xls";
                                $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
                                $filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
                                $objWriter->save($filenamePath);

                                header('Content-type: application/vnd.ms-excel');

                                // It will be called file.xls
                                header('Content-Disposition: attachment; filename="'.$filename.'"');

                                // Write file to the browser
                                $objWriter->save('php://output');

                                $objPHPExcel->disconnectWorksheets();
                                unset($objPHPExcel);
                                unlink($filenamePath);
                                break;


                                case "quickbook":

                                $strContent = '';
                                $filename = "{$formData['service']}_report_quickbook.csv";
                                ob_clean();
                                                header('Content-type: application/csv');
                                                header('Content-Disposition: attachment; filename=' . $filename);


                                                $fp = fopen('php://output', 'w');
                                                $headersArr = array('"Type"','"Num"','"Date"','"Account"','"Amount"');
                                                $headers = implode(',',$headersArr);
                                                $strContent .= "{$headers}\r\n";
                                                                $strContent.= ",,,,\r\n";

                                                foreach($data AS $values){

                                                $order_date = date('d-m-Y',strtotime($values['order_date']));
                                                //	fputcsv($fp, $values,",",'"',",");
                                                $dataArr= array('"Sales Receipt"','"'.$values['pk_order_no'].'"','"'.$order_date.'"','"Undeposited Funds"','"'.$values['amount'].'"');
                                //$dataArr= array('"Sales Receipt"',"'.$values['pk_order_no'].'","'.$values['order_date'].'",'"Undeposited Funds"',$values['amount']);

                                $strContent.= implode(',',$dataArr);
                                $strContent.= "\r\n";
                                }

                                fwrite($fp, $strContent);
                                fclose($fp);

                                die;


                                /*


                                case default:
                                break; */
                                case "print":

                                $view = new ViewModel();
                                $view->setTemplate('admin/report/templates/'.$formData['service']);
                                $variables = array();
                                $variables['data'] = $data;
                                $variables['root_url'] = $config['root_url'];
                                $variables['minDate'] = $formData['minDate'];
                                $variables['maxDate'] = $formData['maxDate'];
                                $variables['columns'] = $selected_columns;
                                $variables['export_type'] = $formData['export_type'];
                                $variables['config'] = $config;

                                $view->setTerminal(true);
                                $view->setVariables($variables);
                                return $view;
                                break;


                                case "tally":

                                    $filename = "{$formData['service']}_report_tally.xml";
                                    header('Content-Type: text/xml');
                                    header('Content-Disposition: attachment; filename=' . $filename);
                        $fp = fopen('php://output', 'w');
                                $xmlstr.="<ENVELOPE>\r\n";

                                foreach ($data as $order){
                                $order_date = date('d-m-Y',strtotime($order['order_date']));
                                $xmlstr.="<DSPVCHDATE>".$order_date."</DSPVCHDATE>\r\n";
                                $xmlstr.="<DSPVCHLEDACCOUNT>Sales</DSPVCHLEDACCOUNT>\r\n";
                                $xmlstr.="<DSPVCHTYPE>Sales Receipt</DSPVCHTYPE>\r\n";
                                $xmlstr.="<DSPVCHNARR>Rs.".$order['amount']." has been received against order no .".$order['pk_order_no']."</DSPVCHNARR>\r\n";

                                }
                                                $xmlstr.='</ENVELOPE>';
                                                                fwrite($fp, $xmlstr);
                                                                                fclose($fp);
                                                                                                die;
                                                                                                break;

        }


        }

public function getCurlResponse($url,$options=array())
{

        // create curl resource
        //$url = "http://www.fooddialer.com";
                //echo $url;exit;
        $ch = curl_init();
        // set url
        curl_setopt($ch, CURLOPT_URL, $url);
        //return the transfer as a string
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

                if(isset($options['POST']) && $options['POST'] == 1)
                {
                curl_setopt($ch, CURLOPT_POST, 1);
                        //echo $url;exit;
                        if(isset($options['POSTFIELDS'])) {
                        //echo '<pre>';print_r($options['POSTFIELDS']);exit;
                        $postfields = http_build_query($options['POSTFIELDS']);

                        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
                }

                }

                if(isset($options['SSL']) && $options['SSL'] == 1)
                {
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
                }
                if(isset($options['PEER']) && $options['PEER'] == 1)
                        {
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
                                }
                                // $output contains the output string
                                $output = curl_exec($ch);
                                //echo '<pre>';print_r(curl_getinfo($ch));exit;
                                //echo $output;die;
                                // close curl resource to free up system resources

                                if(curl_errno($ch)){
                                echo curl_error($ch);
                                }

                                curl_close($ch);

                                return $output;
                        }

/**
 * use to generate customer data pdf
 * @param string $outfile
 * @param array $option
 * @param string $filename
 * @param string $service
 * @return void
 */			
public function generatePDF($outfile,$option,$filename,$service)
{
        $config = $this->getServiceLocator()->get('config');
        $url = $config['root_url']."report/export-pdf-save";
        //echo $url;exit;
        $options['POST'] = 1;
        //echo "<pre>";print_r($option);exit;
        $options['POSTFIELDS'] = array('data' => $option);
        //echo '<pre>';print_r($options);exit;
        //echo '<pre>';print_r($options);exit;
        $pdfContent = $this->getCurlResponse($url,$options);
        //$pdfContent ='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head><body>Look at the HTML source !</body></html>';

        //echo "S".$pdfContent;exit;
        file_put_contents($outfile,$pdfContent);
        $this->downloadOrderDispatch($outfile,$filename);
        //echo file_get_contents($outfile);
        }

        public function downloadOrderDispatch($fileName,$filemainname) {
        if(!is_file($fileName)) {
        echo "Error while generating pdf file";exit;
        }

        $fileContents = file_get_contents($fileName);

        /*
        $response = $this->getResponse();
                        $response->setContent($fileContents);

                        $headers = $response->getHeaders();
                        $headers->clearHeaders()
                        ->addHeaderLine('Content-Type', 'application/pdf')
                                                                                ->addHeaderLine('Content-Disposition', 'attachment; filename="' . $filemainname . '"')
                                                                                ->addHeaderLine('Content-Length', strlen($fileContents));
                                                                                */

        header("Content-disposition: attachment; filename={$filemainname}");
        header("Content-type: application/pdf");
        readfile($fileName);

}

/** Send otp for verification
 *  @method sendOtp()
 *  @method getCustomer($customer_code,"id") use to get customer information depending upon customer code
 *  @method saveCustomer($customer_data) use to save customer information
 *  @method getSMSTemplateMsg('otp_registration',$sms_array) use to get otp registration template
 *  @method sendmessage() use to send otp message on phone
 *  @return @return \Zend\View\Model\JsonModel
 */
public function sendOtpAction(){

    $sm = $this->getServiceLocator();
    $request = $this->getRequest();
    $libCustomer = QSCustomer::getInstance($sm);
    $libCommon = QSCommon::getInstance($sm);
    $setting_session = new Container('setting');
    $setting = $setting_session->setting;


    if ($request->isPost()) {
        
        $customer_code  = $request->getPost('customer_code');
        $customer_data=$libCustomer->getCustomer($customer_code,"id");

                        if($data_customer)
                        {
                                $sms_common = $libCommon->getSmsConfig($setting);
                                $mailer = new \Lib\Email\Email();
                                $mailer->setAdapter($sm);

                                $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
                                $mailer->setSMSConfiguration($sms_config);
                                $mailer->setMobileNo($customer_phone);
                                $mailer->setMerchantData($sms_common);
                                $sms_array = array(
                                        'otp' => $otp,
                                        'website'	=> $setting['CLIENT_WEB_URL'],
                                );

                                $message = $libCommon->getSMSTemplateMsg('otp_registration',$sms_array);

                                if($message){
                                        $mailer->setSMSMessage($message);
                                        $sms_returndata = $mailer->sendmessage();
                                }

        foreach($customer_data as $custdatakey=>$custdataval)
        {
            $otp=$libCustomer->generateRandomString();
            $customer_data['otp']=$otp;
        }

        if(isset($customer_data)){
            $data_customer = $libCustomer->saveCustomer($customer_data);
            if(isset($data_customer))
            {  
                $sms_common = $libCommon->getSmsConfig($setting);
                $mailer = new \Lib\Email\Email();
                $mailer->setAdapter($sm);
                $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
                $mailer->setSMSConfiguration($sms_config);
                $mailer->setMobileNo($customer_data['phone']);
                $mailer->setMerchantData($sms_common);
                $sms_array = array(
                    'otp' => $otp,
                    'website'	=> $setting['CLIENT_WEB_URL'],
                );

                $message = $libCommon->getSMSTemplateMsg('otp_registration',$sms_array);
                
                if($message){ 
                    $mailer->setSMSMessage($message);                   
                    $sms_returndata = $mailer->sendmessage();                     
                }
                
                return  new JsonModel(array(
                    'success' => true,
                ));
            }else{
                return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' =>'Problem in generating OTP..!!',
                ));
            }
        }
    }
}
}

/**
 * Validate Phone number when OTP verification method is used
 * @method getCustomerByOtp($otp,$customer_code) use to get customer information based on otp and customer code
 * @method registerCustomer($customer_code)  use to updated customer
 * @method sendWelcomeSMS($customer_code) use to send welcome message to registered customer
 * @method sendemailAuthenticationEmail($customer_code) use to send authentication mail to registered customer
 * @method getCustomer($customer_code,'id') use to get customer information depending upon customer id
 * @return \Zend\View\Model\JsonModel
 */
public function validatePhoneAction(){

        $sm = $this->getServiceLocator();
        $adapter = $sm->get("Write_Adapter");
        $request = $this->getRequest();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        if ($request->isPost()) {

                $otp  = $request->getPost('otp');
                $customer_code  = $request->getPost('customer_code');

                $flagwelcomemail = $request->getPost('flagwelcomemail',false);

                $data_customer = $libCustomer->getCustomerByOtp($otp,$customer_code);

                if(isset($data_customer) && ($data_customer[0]['otp'] == $otp)){

                        $validate = $libCustomer->registerCustomer($customer_code);

                        $cust_result = array();
                        foreach ($data_customer as $key=>$value)
                        {
                                $cust_result[$key] = $value;
                        }
                        $cust_session = new Container('customer');
                        $cust_session->customer = $cust_result;
                        if($data_customer[0]['email_address']!="")
                        {
                                if($flagwelcomemail)
                                {
                                        $ret = $libCommon->sendWelcomeSMS($customer_code);
                                        $libCommon->sendemailAuthenticationEmail($customer_code);
                                }
                        }
                        $cust_session = new Container('customer');
                        $customerLogin  = $libCustomer->getCustomer($customer_code,'id');

                        $cust_session->customer = (array) $customerLogin;

                        return  new JsonModel(array(
                                        'success' => true,
                                        'custdetails' => $customerLogin,

                        ));
                }
                else{
                        return new JsonModel(array(
                                        'error' => true,
                                        'form_validation_error' =>'Your OTP does not match',

                        ));
                }

        }
}

/** Send email for verification
 *  @method sendEmailVerification use to send authentication email to customer
 *  @return @return \Zend\View\Model\JsonModel
 */
public function sendEmailVerificationAction()
{
        $request = $this->getRequest();
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        if ($request->isPost()) {
                $customer_code  = $request->getPost('customer_code');

                if($libCommon->sendemailAuthenticationEmail($customer_code))
                {
                        return  new JsonModel(array(
                                        'success' => true,

                        ));
                }
                else
                {
                        return new JsonModel(array(
                                        'error' => true,
                                        'form_validation_error' =>'Problem in sending verification email..!!',

                        ));
                }

        }
}

/**
 * use to print customer wallet history
 * @method printwallethistoryAction()
 * @method getWalletHistory($id)
 * @return \Zend\View\Model\ViewModel
 */
public function printwallethistoryAction(){
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libWallet = QSWallet::getInstance($sm);

        $utility = Utility::getInstance();

        $id = base64_decode($_GET['xdf']);
        $name = base64_decode($_GET['xdfn']);
        $mywallet = $libWallet->getWalletHistory($id);

        $view_Data = array();
        $walletdetail = array();

        $i = 0;

        foreach($mywallet as $wallet_key => $wallet_val) {
                $mywallet[$wallet_key]['wallet_amount'] = $utility->getLocalCurrency($wallet_val['wallet_amount']);
        }

        foreach($mywallet as $wallet){
                $walletdetail[$i] = $wallet;
                $i++;
        }

        $view_Data['name'] = $name;
        $view_Data['wallet'] = $walletdetail;
        $view = new ViewModel($view_Data);
        $view->setTerminal(true);
        return $view;		

    }
}
