<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Orders table performance indexes
        Schema::table('orders', function (Blueprint $table) {
            $table->index('customer_code', 'idx_orders_customer_code');
            $table->index('order_no', 'idx_orders_order_no');
            $table->index('order_date', 'idx_orders_order_date');
            $table->index('order_status', 'idx_orders_order_status');
            $table->index(['customer_code', 'order_date'], 'idx_orders_customer_date');
        });

        // Payment transaction performance indexes
        Schema::table('payment_transaction', function (Blueprint $table) {
            $table->index('pre_order_id', 'idx_payment_transaction_pre_order_id');
            $table->index('customer_id', 'idx_payment_transaction_customer_id');
            $table->index('status', 'idx_payment_transaction_status');
            $table->index('gateway_transaction_id', 'idx_payment_transaction_gateway_id');
        });

        // Customer wallet performance indexes
        Schema::table('customer_wallet', function (Blueprint $table) {
            $table->index('customer_code', 'idx_customer_wallet_customer_code');
            $table->index('reference_no', 'idx_customer_wallet_reference_no');
            $table->index(['customer_code', 'amount_type'], 'idx_customer_wallet_customer_type');
        });

        // Order details performance indexes
        Schema::table('order_details', function (Blueprint $table) {
            $table->index('ref_order_no', 'idx_order_details_ref_order_no');
            $table->index('product_code', 'idx_order_details_product_code');
        });

        // Temp pre orders performance indexes
        Schema::table('temp_pre_orders', function (Blueprint $table) {
            $table->index('order_no', 'idx_temp_pre_orders_order_no');
            $table->index('customer_id', 'idx_temp_pre_orders_customer_id');
            $table->index('payment_status', 'idx_temp_pre_orders_payment_status');
        });

        // Customers performance indexes
        Schema::table('customers', function (Blueprint $table) {
            $table->index('phone', 'idx_customers_phone');
            $table->index('email_address', 'idx_customers_email');
            $table->index('status', 'idx_customers_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_customer_code');
            $table->dropIndex('idx_orders_order_no');
            $table->dropIndex('idx_orders_order_date');
            $table->dropIndex('idx_orders_order_status');
            $table->dropIndex('idx_orders_customer_date');
        });

        Schema::table('payment_transaction', function (Blueprint $table) {
            $table->dropIndex('idx_payment_transaction_pre_order_id');
            $table->dropIndex('idx_payment_transaction_customer_id');
            $table->dropIndex('idx_payment_transaction_status');
            $table->dropIndex('idx_payment_transaction_gateway_id');
        });

        Schema::table('customer_wallet', function (Blueprint $table) {
            $table->dropIndex('idx_customer_wallet_customer_code');
            $table->dropIndex('idx_customer_wallet_reference_no');
            $table->dropIndex('idx_customer_wallet_customer_type');
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->dropIndex('idx_order_details_ref_order_no');
            $table->dropIndex('idx_order_details_product_code');
        });

        Schema::table('temp_pre_orders', function (Blueprint $table) {
            $table->dropIndex('idx_temp_pre_orders_order_no');
            $table->dropIndex('idx_temp_pre_orders_customer_id');
            $table->dropIndex('idx_temp_pre_orders_payment_status');
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex('idx_customers_phone');
            $table->dropIndex('idx_customers_email');
            $table->dropIndex('idx_customers_status');
        });
    }
};
