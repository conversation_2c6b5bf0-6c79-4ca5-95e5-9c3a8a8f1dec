<?php
$form = $this->form;
$form->setAttribute('action', $this->url('timeslot', array('action' => 'create')));
$form->prepare();
?>
<style>
ul, ol, dl {
  font-size: inherit;
}
div.checker, div.checker span, div.checker input {
  float: left;
    height: 19px;
    margin-top: 1px;
    width: 19px;
}
.calendar {
    left: 1px;
}
.calendar .selector {
  width: 100% !important;
  background: none !important;
}
.calendar .selector .date-selector, .calendar .selector .time-selector {
  margin-bottom: 17px !important;
  height: 25px;
    padding: 0 0 0 5px;
}
</style>      
<div id="content">
    <?php echo $this->form()->openTag($form);?>
    <div class="large-8 columns">
        <fieldset>
            <legend>
                TIMESLOT INFO 
            </legend>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('fk_kitchen_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('fk_kitchen_code'));
                  echo $this->formElementErrors($form->get('fk_kitchen_code')); 
               ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('menu_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('menu_type'));
                  echo $this->formElementErrors()
                      ->setMessageOpenFormat('<small class="error">')
                      ->setMessageCloseString('</small>')
                      ->render($form->get('menu_type'));
               ?>
              </div>
            </div>                         
          	<div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('starttime')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                  <?php  
                      //echo $this->formHidden($form->get('pk_city_id'));
                      echo $this->formElementErrors()
                          ->setMessageOpenFormat('<small class="error">')
                          ->setMessageCloseString('</small>')
                          ->render($form->get('starttime'));
                      echo $this->formElement($form->get('starttime'));
                  ?>				
                </div>
            </div>
            
          	<div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('endtime')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                  <?php  
                      //echo $this->formHidden($form->get('pk_city_id'));
                      echo $this->formElement($form->get('endtime'));
                      echo $this->formElementErrors()
                          ->setMessageOpenFormat('<small class="error">')
                          ->setMessageCloseString('</small>')
                          ->render($form->get('endtime'));
                  ?>				
                </div>
            </div>
          	<div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('interval')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                  <?php  
                      //echo $this->formHidden($form->get('pk_city_id'));
                      echo $this->formElement($form->get('interval'));
                      echo $this->formElementErrors()
                          ->setMessageOpenFormat('<small class="error">')
                          ->setMessageCloseString('</small>')
                          ->render($form->get('interval'));
                  ?>				
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('span')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                  <?php  
                      //echo $this->formHidden($form->get('pk_city_id'));
                      echo $this->formElement($form->get('span'));
                      echo $this->formElementErrors()
                          ->setMessageOpenFormat('<small class="error">')
                          ->setMessageCloseString('</small>')
                          ->render($form->get('span'));
                  ?>        
                </div>
            </div>            


            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('days')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('days'));
                  echo $this->formElementErrors($form->get('days')); 
               ?>
              </div>
            </div>            
            <div class="row">
<!--                 <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php //echo $this->formLabel($form->get('status')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                    <?php 
                        //echo $this->formElement($form->get('status'));
                        //echo $this->formElementErrors($form->get('status'));
                        //echo $this->formElement($form->get('csrf')); 
                    ?>
                </div>  -->             
               <?php echo $this->formElement($form->get('backurl'));?>
            </div>                                                                                                 
        </fieldset>
        <div class="large-12 columns pl0 pr0">
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                <div class="large-8  small-8 medium-8 columns">
                    <button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Continue &nbsp;<i	class="fa fa-save"></i></button>
                    <button	type="button" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
                </div>
            </div>
        </div>
    </div>        
    <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
</div>
<script type='text/javascript' src="/stdcatalogue/js/input-mask.js"></script>

<script type="text/javascript">
  $(document).ready(function(){ 
      $('input[id$="starttime"]').inputmask(
        "hh:mm:ss", {
        placeholder: "HH:MM:SS", 
        insertMode: false, 
        showMaskOnHover: false,
      });

     var settings = <?php echo json_encode($settings); ?>;
     var menus;
     var kitchen_code;

     $("#fk_kitchen_code").on("change", function() {

        kitchen_code = $(this).val();
        //empty menu before setting

        $("#menu_type").empty().trigger('change');

        //////////////////////////////////////////
        
        //get menus based on kitchen selection

        if($(this).val() == 0) {
          menus = settings['MENU_TYPE'];
        }
        else {
          menus = settings['K'+kitchen_code+'_MENU_TYPE'];
        }

        ///////////////////////////////////////////////////

        //set menus based on kitchen selection

        optArr = [];
        optArr[0] = "<option value=''>Select Menu</option>";
        var ctr = 1;
        $.each(menus, function(i, menu) {
          optArr[ctr] = "<option value='" + menu + "'>" + menu + "</option>";
          ctr++;
        });

        $("#menu_type").append(optArr);

        ////////////////////////////////////////////////////////////////////////
     });

     $("#menu_type").on("change", function() {

        $("#endtime").val('');
        if($(this).val() !== null ) {
          $("#endtime").val(settings['K'+kitchen_code+'_'+$(this).val().toUpperCase()+'_ORDER_CUT_OFF_TIME']);
        }
        

     });

      $("#starttime,#menu_type").on("change", function() {
        var starttime = $("#starttime").val();
        var endtime = $("#endtime").val();

        if(starttime != '' && endtime != '' && starttime != null && endtime != null){
            if(starttime >= endtime ) {
              $('<span class="error">Start date can not be greater than or equal to end date</span>').insertAfter('#starttime').delay(7000).fadeOut();
              $(':input[type="submit"]').prop('disabled', true);
            }
            else{
              $(':input[type="submit"]').prop('disabled', false);
            }
        }
     });

  });  

</script>

