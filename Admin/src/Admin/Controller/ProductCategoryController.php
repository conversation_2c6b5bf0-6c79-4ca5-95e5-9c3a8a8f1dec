<?php
/**
 * This file manages the product categories on fooddialer system
 * The activity includes add, update and delete product categories
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> Girish <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as Qscommon;

use Admin\Form\ProductCategoryForm;

class ProductCategoryController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\ProductCategoryTable model
	 *
	 * @var QuickServe\Model\ProductCategoryTable $productcategoryTable
	 */
	protected $productcategoryTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of product categories
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice) {
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'product_category_id';
		$order = $this->params()->fromRoute('order')?
				 $this->params()->fromRoute('order'): QSelect::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$productCategories = $this->getProductCategoryTable()->fetchAll($select->order($order_by . ' ' . $order));
		$returnvar = $productCategories->toArray();
		$itemsPerPage = 2;

		$productCategories->current();
		$paginator = new Paginator(new paginatorIterator($productCategories));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Menu Category",'description'=>"Menu Category information",'breadcrumb'=>"Menu Category"));
		return new ViewModel(array(
			'order_by' => $order_by,
			'order' => $order,
			'page' => $page,
			'paginator' => $returnvar,
			'acl' => $acl,
			'loggedUser' => $loguser,
			'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	/**
	 * To add new product category
	 *
	 * @return \Admin\Form\ProductCategoryForm
	 */
	public function addAction()
	{
		
		$layout = $this->layout();
		$acl = $layout->acl;
		$viewModel = new ViewModel();
		$loguser = $layout->loggedUser;
		
		//echo "Enter"; exit;
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new ProductCategoryForm($sm);

		$libCommon = Qscommon::getInstance($sm);
		
		$form->get('submit')->setAttribute('value', 'Add');


		$request = $this->getRequest();
		if ($request->isPost()) {
			
			$productCategory = new \QuickServe\Model\ProductCategory();
			$productCategory->getInputFilter()->get('product_category_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(
				new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'product_category',
					'field'     => 'product_category_name',
					'adapter'   => $adapt,
					'message'   => 'Product category already exists',
				))
			);

			//$productCategory->setAdapter($adapt);
			$form->setInputFilter($productCategory->getInputFilter());
			$form->setData($request->getPost());

			if ($form->isValid()) {
				 //echo "<pre>"; print_r($form->getData()); exit;
				$productCategory->exchangeArray($form->getData());
				//echo "<pre>"; print_r($productCategory); exit;
				$data_productCategory = $this->getProductCategoryTable()->saveProductCategory($productCategory);

				if($data_productCategory)
				{
					$product_category=$data_productCategory['product_category_name'];
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'product-category';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "Menu : New menu category '$product_category' created for $productCategory->type.";
					//$activity_log_data['description']= "'$product_category' product category added by $full_name";
					$libCommon->saveActivityLog($activity_log_data);
				}
				
				($data_productCategory) ?$this->flashMessenger()->addSuccessMessage("Product category added successfully"):$this->flashMessenger()->addErrorMessage("Error adding product category.");

				// Redirect to list of albums
				return $this->redirect()->toRoute('product_category');
			}

			//echo '<pre>';print_r($form->getMessages());exit;
		}

		$this->layout()->setVariables(array('page_title'=>"Add Menu Category",'breadcrumb'=>"Add Menu Category"));
		return array('form' => $form);
	}
	/**
	 * To update the product category of given product category id
	 *
	 * @return \Admin\Form\ProductCategoryForm
	 */
	public function editAction()
	{
		$layout = $this->layout();
		$acl = $layout->acl;
		$viewModel = new ViewModel();
		$loguser = $layout->loggedUser;
		
		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('product_category', array('action' => 'add'));
		}

		$kitchen = $_SESSION['admin_kitchen'];

		$productCategory = $this->getProductCategoryTable()->getProductCategory($id);
		$productsFromCategory = $this->getProductTable()->getProductAccCategory(trim($productCategory->product_category_name),$kitchen);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$config_variables = $sm->get('config');
		$form = new ProductCategoryForm($sm);
		//echo '<pre>';print_r($form);exit();
		$form->bind($productCategory);
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $productCategory->product_category_id;
		//$val1 = $productCategory->unique_product category_code;
		$request = $this->getRequest();

		if ($request->isPost())
		 {
			$productCategory = new \QuickServe\Model\ProductCategory();
			$productCategory->getInputFilter()->get('product_category_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(
				new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'product_category',
					'field'     => 'product_category_name',
					'adapter'   => $adapt,
					'message'   => 'Product category already exists',
					'exclude' => array(
						'field' => 'product_category_id',
						'value' => $val
					)
				))
			);

			//$productCategory->setAdapter($adapt);
			$form->setInputFilter($productCategory->getInputFilter());
			$form->setData($request->getPost());
			// echo '<pre>';print_r($form);exit;
			if ($form->isValid()) {
				//echo "hh";exit;
				$productCategory->exchangeArray($form->getData());
				$data_productCategory=$this->getProductCategoryTable()->saveProductCategory($productCategory);

				if($data_productCategory)
				{
					$product_category=$productCategory->product_category_name;;
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'product-category';
					$activity_log_data['action']= 'edit';
					$activity_log_data['description']= "Menu : Menu category '$product_category' updated.";
				
					//$activity_log_data['description']= "'$product_category' product category edited by $full_name";
					$libCommon->saveActivityLog($activity_log_data);
				}
				
				$this->flashMessenger()->addSuccessMessage("Product category updated successfully");
				// Redirect to list of albums
				return $this->redirect()->toRoute('product_category');
			}
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Product Category",'breadcrumb'=>"Edit Product Category"));
		
		return array(
			'id' => $id,
			'form' => $form
		);
	}
	/**
	 * To delete the product category of given product category id
	 *
	 * @param int id
	 * @return route product category
	 */
	public function deleteAction() {
		
		$layout = $this->layout();
		$acl = $layout->acl;
		$viewModel = new ViewModel();
		$loguser = $layout->loggedUser;
		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('product_category');
		}
		
		$select = new QSelect();
		$select->where(array('product_category_id'=>$id));
		$product_category = $this->getProductCategoryTable()->fetchAll($select);
		$product_category_arr=$product_category->toArray();
		$product_category_name=$product_category_arr[0]['product_category_name'];
		$product_category_status=($product_category_arr[0]['status'])=='1'?'deactivated':'activated';
		
		$data_productCategory=$this->getProductCategoryTable()->deleteProductCategory($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		if($data_productCategory) {
			$product_category = $this->getProductCategoryTable()->getProductCategory($id);
			if( $product_category->status == '1' ) {
				$this->flashMessenger()->addSuccessMessage('Product category "'.($product_category->product_category_name).'" is activated successfully');
			}
			else {
				$this->flashMessenger()->addInfoMessage('Product category "'.($product_category->product_category_name).'" has been deactivated successfully');
			}
			
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'product-category';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "Menu : Menu category '$product_category_name' $product_category_status.";
			//$activity_log_data['description']= "'$product_category_name' product category $product_category_status by $full_name";
			$libCommon->saveActivityLog($activity_log_data);
		}
		else {
			$this->flashMessenger()->addErrorMessage("Error updating product category.");
		}
		return $this->redirect()->toRoute('product_category');
	}
	
	public function updatesequenceAction()
	{
        
		$layout = $this->layout();
		$acl = $layout->acl;
		$viewModel = new ViewModel();
		$loguser = $layout->loggedUser;
	
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('product_category', array('action' => 'add'));
		}
	
		$productCategory = $this->getProductCategoryTable()->getProductCategory($id);
		
		$kitchen = $_SESSION['adminkitchen'];

		$productsFromCategory = $this->getProductTable()->getProductAccCategory(trim($productCategory->product_category_name),$kitchen);
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		$form = new ProductCategoryForm($sm);
//		echo '<pre>';print_r($productCategory);exit();
		$form->bind($productCategory);
		$form->get('submit')->setAttribute('value', 'Edit');
	
		$val = $productCategory->product_category_id;
		//$val1 = $productCategory->unique_product category_code;
		$request = $this->getRequest();
		if ($request->isPost())
		{
//            dd($request->isPost());
			$productCategory = new \QuickServe\Model\ProductCategory();
			$productCategory->getInputFilter()->get('product_category_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(
					new \Zend\Validator\Db\NoRecordExists(array(
							'table'     => 'product_category',
							'field'     => 'product_category_name',
							'adapter'   => $adapt,
							'message'   => 'Product category already exists',
							'exclude' => array(
									'field' => 'product_category_id',
									'value' => $val
							)
					))
			);
                        
			//$productCategory->setAdapter($adapt);
			$form->setInputFilter($productCategory->getInputFilter());
			$form->setData($request->getPost());
			// echo '<pre>';print_r($form);exit;
			if ($form->isValid()) {
//				echo "hh";exit;
				$productCategory->exchangeArray($form->getData());
				$data_productCategory=$this->getProductCategoryTable()->saveProductCategory($productCategory);
				$product_category=$productCategory->product_category_name;;
				$full_name=$loguser->first_name." ".$loguser->last_name;
				$activity_log_data=array();
				$activity_log_data['context_ref_id']=$loguser->pk_user_code;
				$activity_log_data['context_name']= $full_name;
				$activity_log_data['context_type']= 'user';
				$activity_log_data['controller']= 'product-category';
				$activity_log_data['action']= 'edit';
				$activity_log_data['description']= "'$product_category' product category edited by $full_name";
				$this->getActivityLogTable()->saveActivityLog($activity_log_data);
					
				$this->flashMessenger()->addSuccessMessage("Product category updated successfully");
				// Redirect to list of albums
				return $this->redirect()->toRoute('product_category');
			}
		}
                $product_category = $productCategory['product_category_name'];
                $breadcrum = ucfirst($product_category)." > Update Product Sequence";
                
		$this->layout()->setVariables(array('page_title'=>"Update Product Sequence",'description'=>"Product",'breadcrumb'=>"Menu Category",'breadcrumb'=>$breadcrum));
		//echo "<pre> form =";print_r($productsFromCategory->toArray());die;
        
		return array(
				'id' => $id,
				'form' => $form,
				'productsFromCategory' => $productsFromCategory
		);
	}
	
	
	/**
	 * To delete the product category of given product category id
	 *
	 * @param int id
	 * @return route product category
	 */
	public function updateProductSequenceAction() {
//        error_reporting(E_ALL);
//        ini_set('display_errors','On');
       
		$request = $this->getRequest();
		$productArr = $request->getPost('arr');
		$updateProductOrder = $this->getProductTable()->updateProductSequence($productArr);
		echo json_encode($productArr);
		exit();
	}
	
	
	/**
	 * Get instance of QuickServe\Model\ProductCategoryTable
	 *
	 * @return QuickServe\Model\ProductCategoryTable
	 */
	public function getProductCategoryTable()
	{
		if (!$this->productcategoryTable) {
			$sm = $this->getServiceLocator();
			$this->productcategoryTable = $sm->get('QuickServe\Model\ProductCategoryTable');
		}
		return $this->productcategoryTable;
	}
	
	/**
	 * Get instance of QuickServe\Model\ProductTable
	 *
	 * @return QuickServe\Model\ProductTable
	 */
	public function getProductTable()
	{
		if (!$this->productTable) {
			$sm = $this->getServiceLocator();
			$this->productTable = $sm->get('QuickServe\Model\ProductTable');
		}
		return $this->productTable;
	}
}