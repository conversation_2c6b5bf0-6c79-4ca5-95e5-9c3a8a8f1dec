<?php
/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Session\Container;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\Utility;

use Lib\QuickServe\Db\Sql\QSql;

class LocationForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
     private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;
        $currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);        

        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'pk_location_code',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'pk_location_code',
        		),
        		'options' => array(
      		),
        ));

        $this->add(array(
            'name' => 'location',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            		'class'=> 'smallinput',
                'id' => 'location',
                'placeholder' => 'Enter Location',
                // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Location<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'city',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'city',
              //  'required' => 'required',
                'value' => '1',
            ),
            'options' => array(
                'label' => 'City<span class="red">*</span>',
              	'value_options' => $this->getCities(),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'pin',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'pin',
            	'placeholder' => 'Enter Postal Code',
              //  'required' => 'required',
            ),
            'options' => array(
            		'label' => 'Postal Code',
            		'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'sub_city_area',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            		'class'=> 'smallinput',
                'id' => 'sub_city_area',
                'placeholder' => 'Enter Sub City Area',
              //  'required' => 'required',
            ),
            'options' => array(
                'label' => 'Sub City Area',
            ),
        ));
        $this->add(array(
        		'name' => 'delivery_charges',
        		'type' => 'Zend\Form\Element\Text',
        		'required' => false,
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'delivery_charges',
        				'placeholder' => 'Enter Delivery Charges',
        				//  'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Delivery Charges('.$currencySymbol.')<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
				),
		));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Go',
        				'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/location',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $kitchenScreens = array();
		
		/*for($screen=1;$screen <= $subscription_keys_session->keys['KITCHEN_SCREEN_COUNT'];$screen++){
			
			$kitchenScreens[$screen] = "Screen ".$screen;
			
		}*/
		$kitchenScreens = $this->getKitchenScreens();


		$this->add(array(
				'name' => 'fk_kitchen_code',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
					'class' => 'smallinput',
					'id' => 'fk_kitchen_code',
					//	'required' => 'required',
					'value' => '0',
				),
				'options' => array(
                'label' => 'Kitchen Screen<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
				'value_options' => $kitchenScreens
            ),
		));

        $this->add(array(
            'name' => 'delivery_time',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                    'class'=> 'smallinput',
                'id' => 'delivery_time',
                'placeholder' => 'Enter Delivery Time in Minutes',
               // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Delivery Time(min)<span class="red">*</span></br>(Only for Instant Orders)',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));
    }
    
    public function getCities()
    {
    	//$sql = "SELECT pk_city_id,city FROM `city` WHERE status=1";
		//$vendors = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
		$sql = new QSql($this->service_locator);
    	$select = $sql->select();
		$select->from('city');
		$select->columns(array('pk_city_id','city'));
		$select->where(array('status'=>'1'));

    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$cities = $statement->execute();
        $cities = $sql->execQuery($select);
    	$returnData = array();
    	
    	foreach($cities as $city){
    		$returnData[$city['pk_city_id']] = $city['city'];
    	}
    	return $returnData;
    }
    
    private function getKitchenScreens() {
    	//$kitchen_array = array('' => 'Select the kitchen');
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('kitchen_master');
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
    
        $results = $sql->execQuery($select);
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
    	}// end of foreach
    	return $kitchen_array;
    }
}