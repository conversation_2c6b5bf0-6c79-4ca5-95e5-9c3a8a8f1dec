<?php
/**
 * This File used to manage collection of money paid by customers
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustGroupController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use QuickServe\Model\CustGroupTable;
use QuickServe\Model\CustGroupValidator;

use Admin\Form\CustGroupForm;

class CustGroupController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\CustGroupTable model
	 *
	 * @var QuickServe\Model\CustGroupTable $custgroupTable
	 */
	protected $custgroupTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of customer groups
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice) {
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'group_code';
		$order = $this->params()->fromRoute('order')?
				 $this->params()->fromRoute('order'): QSelect::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$custgroup = $this->getCustGroupTable()->fetchAll($select->order($order_by . ' ' . $order));
		$returnvar = $custgroup->toArray();
		$itemsPerPage = 2;

		$custgroup->current();
		$paginator = new Paginator(new paginatorIterator($custgroup));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Group",'description'=>"Delivery Group Info",'breadcrumb'=>"Group"));
		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));

	}
	/**
	 * To add a new Customer group
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function addAction()
	{
		//echo "Enter"; exit;
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new CustGroupForm($sm);

		$form->get('submit')->setAttribute('value', 'Add');

		$request = $this->getRequest();
		if ($request->isPost()) {

			$custgroup = new CustGroupValidator();
			$custgroup->getInputFilter()->get('group_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'groups',
					'field'     => 'group_name',
					'adapter'   => $adapt,
					'message'   => 'Group Name Already exists',
			)
			));

			//$custgroup->setAdapter($adapt);
			$form->setInputFilter($custgroup->getInputFilter());
			$form->setData($request->getPost());

			if ($form->isValid()) {
				//echo "<pre>"; print_r($form->getData()); exit;
				$custgroup->exchangeArray($form->getData());
				//echo "<pre>"; print_r($location); exit;
				$data_cust_group= $this->getCustGroupTable()->saveCustGroup($custgroup);

				($data_cust_group) ?$this->flashMessenger()->addSuccessMessage("Customer Group added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Customer Group.");


				// Redirect to list of albums
				return $this->redirect()->toRoute('custgroup');
			}
			//echo '<pre>';print_r($form->getMessages());exit;
		}
		//$form->get('number_of_pages')->setOptions();
		$this->layout()->setVariables(array('page_title'=>"Add Group",'breadcrumb'=>"Add Group"));
		return array('form' => $form);
	}
	/**
	 * To update the customer group of given customer group id
	 *
	 *@param integer id
	 * @return \Zend\View\Model\ViewModel
	 */
	public function editAction()
	{
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('custgroup', array('action' => 'add'));
		}

		$custgroup = $this->getCustGroupTable()->getCustGroup($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		$form = new CustGroupForm($adapt);
		//echo '<pre>';print_r($form);exit();
		$form->bind($custgroup);
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $custgroup->group_name;
		$request = $this->getRequest();

		if ($request->isPost())
		{
			$custgroup = new CustGroupValidator();
			$custgroup->getInputFilter()->get('group_name')
			->getValidatorChain() // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'groups',
					'field'     => 'group_name',
					'adapter'   => $adapt,
					'message'   => 'Group Name Already exists',
					'exclude' => array(
							'field' => 'group_name',
							'value' => $val,
					)
			)
			));

			//$custgroup->setAdapter($adapt);
			$form->setInputFilter($custgroup->getInputFilter());
			$form->setData($request->getPost());
			// echo '<pre>';print_r($form);exit;
			if ($form->isValid()) {
				//echo "hh";exit;
				$custgroup->exchangeArray($form->getData());
				$data_cust_group=$this->getCustGroupTable()->saveCustGroup($custgroup);
				$this->flashMessenger()->addSuccessMessage("Customer Group updated successfully");

				$this->flashMessenger()->addMessage('Record saved successfully!');
				// Redirect to list of albums
				return $this->redirect()->toRoute('custgroup');
			}
		}
	
		$this->layout()->setVariables(array('page_title'=>"Edit Group",'breadcrumb'=>"Edit Group"));
		return array(
				'id' => $id,
				'form' => $form
		);
	}
	/**
	 * To delete customer group of given custmer group id
	 *
	 * @param int id
	 * @return route custgroup
	 */
	public function deleteAction() {
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('custgroup');
		}
		$data_cust_group=$this->getCustGroupTable()->deleteCustGroup($id);
		($data_cust_group) ?$this->flashMessenger()->addSuccessMessage("Customer Group updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Customer Group.");
		return $this->redirect()->toRoute('custgroup');
	}
	/**
	 * Get instance of QuickServe\Model\CustGroupTable
	 *
	 * @return QuickServe\Model\CustGroupTable
	 */
	public function getCustGroupTable()
	{
		if (!$this->custgroupTable) {
			$sm = $this->getServiceLocator();
			$this->custgroupTable = $sm->get('QuickServe\Model\CustGroupTable');
		}
		return $this->custgroupTable;
	}

}