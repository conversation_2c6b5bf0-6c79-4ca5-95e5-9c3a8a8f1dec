import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]Preferences } from '@/components/customer-service-v12/[id]/preferences';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]Preferences', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]Preferences />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]Preferences')).toBeInTheDocument();
  });
});