import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id] } from '@/components/customer-service-v12/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]')).toBeInTheDocument();
  });
});