{"success": true, "message": "Children retrieved successfully", "data": [{"id": 1, "parent_customer_id": 1, "full_name": "<PERSON>", "school_id": 1, "school_name": "Delhi Public School", "grade_level": "5th", "grade_section": "A", "roll_number": "12345", "date_of_birth": "2014-05-15", "age": 9, "dietary_restrictions": ["nuts", "dairy"], "medical_conditions": ["asthma"], "emergency_contact_relationship": "grandmother", "profile_photo": "https://example.com/photos/alice.jpg", "active_subscriptions": 2, "created_at": "2024-01-15T10:30:00Z", "updated_at": "2024-01-20T14:45:00Z"}, {"id": 2, "parent_customer_id": 1, "full_name": "<PERSON>", "school_id": 2, "school_name": "Modern School", "grade_level": "3rd", "grade_section": "B", "roll_number": "67890", "date_of_birth": "2016-08-20", "age": 7, "dietary_restrictions": [], "medical_conditions": [], "emergency_contact_relationship": "uncle", "profile_photo": null, "active_subscriptions": 1, "created_at": "2024-01-16T11:30:00Z", "updated_at": "2024-01-21T15:45:00Z"}], "meta": {"timestamp": "2024-01-28T10:30:00Z", "api_version": "v2", "correlation_id": "test-correlation-id-002"}}