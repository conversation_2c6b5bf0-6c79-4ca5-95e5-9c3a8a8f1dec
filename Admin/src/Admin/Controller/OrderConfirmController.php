<?php
/**
 * This file manages the unconfirmed orders on fooddialer system
 * It shows all the unconfirmed order
 * <PERSON><PERSON> can confirmed these orders
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 2.1: OrderConfirmController.php 2015-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Wallet as QSWallet;

class OrderConfirmController extends AbstractActionController
{
	protected $authservice;
	protected $preorderTable;
	protected $invoicetable;
	protected $orderconfirmTable;
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderTable $ordertable
	 */
	protected $ordertable;
	/**
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
    	$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		$deliverypersons=$this->getuserTable()->getAllDeliveryPerson();
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$cust_session = new Container('customer');
		$customer = $cust_session->customer;
		
		$iden = $this->authservice->getIdentity();
		
		
		$setting_session = new Container('setting');
		$menus = $setting_session->setting['MENU_TYPE'];
		
		$menuSelected = $this->params()->fromQuery('status','');
		
		$location_data=$this->getOrderTable()->getLocationData();
		
		$status =array(
			'all' => 'all',
			'success' =>'success',
			'pending' =>'pending',
		);
	
		if($menuSelected =="")
		{
			$menuSelected = "all";
		}
	
        $layoutviewModel = $this->layout();
        $acl =$layoutviewModel->acl;
        $loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Confirm Orders",'description'=>"Confirm the orders",'breadcrumb'=>"Confirm Orders"));
		return new ViewModel(array(
            'acl' => $acl,
            'loggedUser' => $loguser,
            'status' =>$status,
            'menuSelected'=>$menuSelected,
            'kitchen_data' =>$kitchen_data,
            'menus' => $menus,
            'location_data'=>$location_data,
            'deliverypersons' => $deliverypersons,
        	'setting' => $setting_session->setting
		));
	}
	
	public function ajxOrderconfirmAction()
	{
        
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		 $cust_session = new Container('customer');
		 $customer = $cust_session->customer;
		 $totalamot='';
		 $iden = $this->authservice->getIdentity();

		 $sm = $this->getServiceLocator();
		 $libCommon = QSCommon::getInstance($sm);

		 $menuSelected = $this->params()->fromQuery('status',NULL);
		 $kitchenscreen = $this->params()->fromQuery('kitchenscreen');
		 $menu = $this->params()->fromQuery('menu');
		 $location_code = $this->params()->fromQuery('location_code');
		 $deliveryperson = $this->params()->fromQuery('deliveryperson');
		 $delivery_type = $this->params()->fromQuery('delivery_type', null);
		 $fromdate = $this->params()->fromQuery('minDate');
		 $todate = $this->params()->fromQuery('maxDate');
		 
		 $session_setting = new Container("setting");
		 $setting = $session_setting->setting;
		 $select = new QSelect();
         
		 $qryParams = $this->params()->fromQuery();
		 $qryPost = $this->params()->fromPost();
		 
		 $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		 $arrColumns = array('0'=>'id','1'=>'customer_name','2'=>'phone','3'=>'email','4'=>'date','5'=>'total_amt','6'=>'total_applied_discount','7'=>'total_delivery_charges','8'=>'total_amt','9'=>'type','10'=>'status','11'=>'order_menu');
		 
		 
		 $order_by = $arrColumns[$arrOrder[0]['column']];
		 $order = $arrOrder[0]['dir'];
		 
		 $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		 
		 $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		 //$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;
		 $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		 $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		 $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		 $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		 
		 
		 $columns = $this->params()->fromQuery('columns');
		 if(isset($search) && $search !=""){
		 	$select->where->like ('temp_pre_orders.customer_name','%' . $search . '%')
		 	->or->like ( 'temp_pre_orders.customer_name', '%' . $search . '%' )
		 	->or->like ( 'date', '%' . $search . '%' )
		 	->or->like ( 'type', '%' . $search . '%' )
		 	//->or->like ( 'temp_pre_orders.order_menu', '%' . $search . '%' )
		 	->or->like ( 'temp_pre_orders.order_menu', '%' . $search . '%' );
		 }
		 
		 if($kitchenscreen!='all')
		 {
		 	$select->where("temp_pre_orders.fk_kitchen_code = $kitchenscreen");
		 }else{
            $select->where->in('temp_pre_orders.fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
		 if($menu!='all')
		 {
		 	$select->where("temp_pre_orders.order_menu = '".$menu."'");
		 }
		 if($location_code!='all' && $location_code !='')
		 {
		 	$select->where("temp_pre_orders.location_code = '$location_code'");
		 }
		 if($deliveryperson!="" && $deliveryperson!="all")
		 {
		 	//$select->join('customer_address','temp_pre_orders.customer_code = customer_address.fk_cudtomer_code AND temp_pre_orders.order_menu = customer_address.menu_type ',array('delivery_person_id'));
		 	$select->where(array('customer_address.delivery_person_id'=>$deliveryperson));
		 }
		 if($delivery_type!="" && $delivery_type!="all")
		 {
		 	$select->where(array('temp_pre_orders.delivery_type'=>$delivery_type));
		 }
		  if($menuSelected!="" && $menuSelected!="all")
		 {
		 	//$select->join('customer_address','temp_pre_orders.customer_code = customer_address.fk_cudtomer_code AND temp_pre_orders.order_menu = customer_address.menu_type ',array('delivery_person_id'));
		 	$select->where(array('temp_order_payment.status'=>$menuSelected));
		 }
		 
		 if(isset($fromdate) && $fromdate!='')
		 {
		 	$fromdate = date('Y-m-d',strtotime($fromdate));
		 	$select->where("temp_pre_orders.order_date >= '$fromdate'");
		 }
		  
		 if(isset($todate) && $todate!='')
		 {
		 	$todate = date('Y-m-d',strtotime($todate));
		 	$select->where("temp_pre_orders.order_date <= '$todate'");
		 }
		
		 $select->order($order_by . ' ' . $order);
		 		
                    
		 $where = '';
//	echo $select->getSqlString();die;	 
//		echo "<pre>";print_r($select->getSqlString());die;
		 $orders = $this->getOrderConfirmTable()->fetchAll($select,$where,$page);
		
//                 print_r($orders);die();

                 $orders->setCurrentPageNumber($page)
		 ->setItemCountPerPage($itemsPerPage)
		 ->setPageRange(7);
		 
		 $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		 
		 $returnVar = array();
		 $returnVar['draw'] = $draw;
		 $returnVar['recordsTotal'] = $orders->getTotalItemCount();
		 $returnVar['recordsFiltered'] = $orders->getTotalItemCount();
		 $returnVar['data'] = array();
		 
		 $utility = new \Lib\Utility();
		 
		 $todaysdate = date('Y-m-d');
	 
		 foreach($orders as $order){
		 	//echo "<pre>"; print_r($order);
		 	$custname =  $order->customer_name;
		 	$custphone = $order->phone;
		 	$custemail = $order->email;
		 	$arrTotalAmount = $this->getOrderConfirmTable()->getAmount($order->temp_preorder_id,'all');
//		 	echo "<pre>"; print_r($arrTotalAmount); exit();
		 	
		 	$order_temp_amount = $arrTotalAmount['amount'];
 		 	
		 	$dates_all = explode(',',$order['order_days']);
		 //	echo "before<pre>"; print_r($dates_all);
		 	$mostRecent = 0;
		 	$date_arr = [];
		 	
		 	foreach ($dates_all as $day){
		 		 
		 		 $curDate = strtotime($day);
		 		 if ($curDate > $mostRecent) {
		 			 $mostRecent = $curDate;
		 		 }
		 	
		 		$date_arr[] = $utility->displayDate($day,$setting['DATE_FORMAT']);
		 	}
		  
		 	$datearray = implode(', ',$date_arr);

		 	//Add timeslot if present
		 	if(isset($order['delivery_time']) && !empty($order['delivery_time']) && $order['delivery_end_time'] && !empty($order['delivery_end_time']) ) {
		 		$time_slot = '<div class="mb5">Time Slot: '.date('h:i a', strtotime($order['delivery_time'])).'-'.date('h:i a', strtotime($order['delivery_end_time'])).'</div>';
		 	}

			$strDates = $date_arr[0];
                        $strDates .= '<span  data-title="'.$datearray.'" >';
                        $strDates .= '<i class="pl5 fa fa-info-circle" aria-hidden="true"></i></span>';
                        $strDates .= $time_slot;

		 	$mostrecent = date('Y-m-d',$mostRecent);
		 	$arrTmp = array();

			$strOrderIcon = $libCommon->getSourceIcon($order['order_source']);

			$strOrder = $strOrderIcon." ".$order->id;
		 	array_push($arrTmp,$strOrder);

			array_push($arrTmp,$custname);
			array_push($arrTmp,$custphone);
			array_push($arrTmp,$custemail);
			array_push($arrTmp,$strDates);
			array_push($arrTmp,$order->product_name);
			
        	$strDetails = "<div class='mb5'>Price: ".$utility->getLocalCurrency($arrTotalAmount['amount'])."</div>";
        	
        	$strDetails .= "<div class='mb5'>Tax: ".$utility->getLocalCurrency($arrTotalAmount['tax'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Discount : ".$utility->getLocalCurrency($arrTotalAmount['discount'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Delivery Charges : ".$utility->getLocalCurrency($arrTotalAmount['delivery'])." </div>";
        	
        	$strDetails .= "<div class='mb5'>Service Charges  : ".$utility->getLocalCurrency($arrTotalAmount['service_charges'])." </div>";
        	
                if($order['tp_aggregator']){
                    $strDetails .= "<div class='mb5'>Third-Party Aggregator Commission  : ".$utility->getLocalCurrency($order['tp_aggregator_charges'])." (".$order['tp_aggregator_charges_type'].")</div>";
                }
                
                if($order['tp_delivery']){
                    $strDetails .= "<div class='mb5'>Third-Party Delivery Commission  : ".$utility->getLocalCurrency($order['tp_delivery_charges'])." (".$order['tp_delivery_charges_type'].") </div>";
                }
                
        	$strAmt = $utility->getLocalCurrency($arrTotalAmount['lockedamt']);
        	$strAmt .= '<span  data-title="'.$strDetails.'" >';
        	$strAmt .= '<i class="pl5 fa fa-info-circle" aria-hidden="true"></i></span>';

        	array_push($arrTmp,$strAmt);

                array_push($arrTmp,$order->type);
                array_push($arrTmp,$order->status);
//                array_push($arrTmp,$order->order_menu); // commented. removed from listing
                
                /* added sankalp pickup 14July*/
                if(array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ){
                    
                    $strHtml = '';
                    if($order['delivery_type'] == 'pickup'){
                        $strHtml = '<button class="smBtn yellowBg has-tip tip-top" data-selector="tooltip-ir5zd4uo2" title="'.$order->delivery_type.'">
                                <i class="fa fa-map-marker" aria-hidden="true"></i>
                        </button>';
                    }else{
                        $strHtml = '<button class="smBtn greenBg has-tip tip-top" data-selector="tooltip-ir5zd4uo0" title="'.$order->delivery_type.'">
                                <i class="fa fa-truck" aria-hidden="true"></i>
                        </button>';
                    }
                    
                    array_push($arrTmp, $strHtml);
                }
        	
		 	if($order->customer_code=="")
		 	{
		 		$custcode  = $order->cust_code;
		 	}
		 	else 
		 	{
		 		$custcode = $order->customer_code;
		 	}
		 	
		 	 if ($todaysdate <= $mostrecent) {
		 	 
			 	if($order->status=='pending'){
			 	$str = '<a class="tiffin_view" data-id='.$order->id.' data-amt='.$order_temp_amount.' data-custcode='.$custcode.'>Confirm</a>';
			 	$str .= '<input type="hidden" name="amt" id="amt" value="'.$order_temp_amount.'">'; // number_format($totalamot,2,'.','')
			 		
			 	$str ='<button  data-id='.$order->id.' data-amt='.$arrTotalAmount['lockedamt'].' data-custcode='.$custcode.' data-text-swap="Wait.." class="tiffin_view smBtn blueBg" title="Confirm Order"><i class="fa fa-check-square-o"></i></button>';
			 		
			 	$str .= '<input type="hidden" name="amt" id="amt" value="'.$totalamot.'">';
			 	$str .= '<input type="hidden" name="custcode" id="custcode" value="'.$custcode.'">';
			 	
			 	}else{
			 		$str = "Confimed";
			 	}
		 	 }
		 	 else 
		 	 {
		 	 	$str = "--";
		 	 }
		 	array_push($arrTmp,$str);
		 	array_push($returnVar['data'],$arrTmp);
		 	
		 }
		 //exit();
//                 echo "<pre>"; print_r($returnVar); die();
		 return new JsonModel($returnVar);
	}
	
        public function exportDataAction(){
            $view = new ViewModel();
            $view->setTerminal(true);
//            $type = $this->params()->fromQuery('table');
            $exporttype = $this->params()->fromQuery('exporttype');

            $filterForm = array();
            $filterSring = $this->params()->fromQuery('form');

            parse_str($filterSring,$filterForm);
//            $table = 'temp_order_payment';
            $table = 'temp_pre_orders'; // added by sankalp
            
            //$columns = array('0'=>'id','1'=>'customer_name','2'=>'date','3'=>'meal','4'=>'amount','5'=>'payment','6'=>'status','7'=>'order_menu', '8' => 'delivery_type');
            $columns = array('0'=>'id','1'=>'customer_name','2'=>'phone','3'=>'email','4'=>'date','5'=>'meal','6'=>'amount','7'=>'payment','8'=>'status','9'=>'order_menu', '10' => 'delivery_type');
            $request = $this->getRequest();

            if($request->isPost()){

                    $formData = $request->getPost();

                    $reportType = $formData['table'] = $table;
                    $exportData = $this->orderConfirmExportData($formData);
                    return $this->forward()->dispatch('Admin\Controller\Report', array(
                            'printData' => $exportData,
                            'action' => 'printReport',
                            'formData' =>$formData
                    )); 
            }
            
            $view->setVariables(array('columns' =>$columns,'table' => $type,'filterForm'=>$filterForm,'exporttype'=>$exporttype));
            return $view;
	}
	
        public function orderConfirmExportData($formData)
        {
//            echo "<pre>"; print_r($formData); die();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
                
		 $cust_session = new Container('customer');
		 $customer = $cust_session->customer;
		 $totalamot='';
		 $iden = $this->authservice->getIdentity();
		
		 $menuSelected = $formData['status'];
//                 echo $menuSelected; die();
		 $kitchenscreen     = $formData['kitchenscreen'];

                 $menu              = $formData['menu'];
		 $location_code     = $formData['location_code'];
		 $deliveryperson    = $formData['deliveryperson'];
		 $delivery_type    = $formData['delivery_type'];
		 $fromdate          = $formData['minDate'];
		 $todate            = $formData['maxDate'];
		 
		 
		 $session_setting = new Container("setting");
		 $setting = $session_setting->setting;
//		 dd($formData);
		 $select = new QSelect();
         $select->from($formData['table']);
                 
		 //$arrColumns = array('0'=>'id','1'=>'customer_name','2'=>'date','3'=>'meal','4'=>'amount','5'=>'payment','6'=>'status','7'=>'order_menu', '8' => 'delivery_type');
		 $arrColumns = array('0'=>'id','1'=>'customer_name','2'=>'phone','3'=>'email','4'=>'date','5'=>'meal','6'=>'amount','7'=>'payment','8'=>'status','9'=>'order_menu', '10' => 'delivery_type');
		 
		 $columns = $this->params()->fromQuery('columns');
		 
		 if($kitchenscreen!='all')
		 {
		 	$select->where("temp_pre_orders.fk_kitchen_code = $kitchenscreen");
		 }else{
            $select->where->in('temp_pre_orders.fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
		 if($menu!='all')
		 {
		 	$select->where("temp_pre_orders.order_menu = '".$menu."'");
		 }
		 if($location_code!='all' && $location_code !='')
		 {
		 	$select->where("temp_pre_orders.location_code = '$location_code'");
		 }
		 if($deliveryperson!="" && $deliveryperson!="all")
		 {
		 	//$select->join('customer_address','temp_pre_orders.customer_code = customer_address.fk_cudtomer_code AND temp_pre_orders.order_menu = customer_address.menu_type ',array('delivery_person_id'));
		 	$select->where(array('customer_address.delivery_person_id'=>$deliveryperson));
		 }
		 if($delivery_type!="" && $delivery_type!="all")
		 {
		 	$select->where(array('temp_pre_orders.delivery_type'=>$delivery_type));
		 }
		  if($menuSelected!="" && $menuSelected!="all")
		 {
		 	//$select->join('customer_address','temp_pre_orders.customer_code = customer_address.fk_cudtomer_code AND temp_pre_orders.order_menu = customer_address.menu_type ',array('delivery_person_id'));
		 	$select->where(array('temp_order_payment.status'=>$menuSelected));
		 }
		 
		 if(isset($fromdate) && $fromdate!='')
		 {
		 	$fromdate = date('Y-m-d',strtotime($fromdate));
		 	$select->where("temp_pre_orders.order_date >= '$fromdate'");
		 }
		  
		 if(isset($todate) && $todate!='')
		 {
		 	$todate = date('Y-m-d',strtotime($todate));
		 	$select->where("temp_pre_orders.order_date <= '$todate'");
		 }
		
//		 $select->order($order_by . ' ' . $order);
		 
		 $where = '';
		 
//		echo "<pre>sadas";print_r($select->getSqlString());die;
		 $orders = $this->getOrderConfirmTable()->fetchAll($select,$where,null);
		 
        //echo "<pre>"; print_r($orders->toArray());die();

		 $returnVar = array();
		 $returnVar['data'] = array();
		 
		 $utility = new \Lib\Utility();
		 
		 $todaysdate = date('Y-m-d');
	 
		 foreach($orders as $order){
		 	//echo "<pre>"; print_r($order);
		 	$custname =  $order->customer_name;
		 	$custphone = $order->phone;
		 	$custemail = $order->email;
		 	$arrTotalAmount = $this->getOrderConfirmTable()->getAmount($order->temp_preorder_id,'all');
		 	
		 	$order_temp_amount=$arrTotalAmount['amount'];
// 		 	echo "<pre>"; print_r($arrTotalAmount); exit();
		 	
		 	$dates_all = explode(',',$order['order_days']);
		 //	echo "before<pre>"; print_r($dates_all);
		 	$mostRecent = 0;
		 	$date_arr = [];
		 	
		 	foreach ($dates_all as $day){
		 		 
		 		 $curDate = strtotime($day);
		 		 if ($curDate > $mostRecent) {
		 			 $mostRecent = $curDate;
		 		 }
		 	
		 		$date_arr[] = $utility->displayDate($day,$setting['DATE_FORMAT']);
		 	}
		 //	echo "after<pre>"; print_r($date_arr); 
		 	$datearray = implode(', ',$date_arr);
		 	$mostrecent = date('Y-m-d',$mostRecent);
		 	$arrTmp = array();
		 	array_push($arrTmp,$order->id);
			array_push($arrTmp,$custname);
			array_push($arrTmp,$custphone);
			array_push($arrTmp,$custemail);
			array_push($arrTmp,$datearray);
			array_push($arrTmp,$order->product_name);
		 	array_push($arrTmp,number_format($arrTotalAmount['lockedamt'],2, '.', ''));
		 	array_push($arrTmp,$order->type);
		 	array_push($arrTmp,$order->status);
		 	array_push($arrTmp,$order->order_menu);
		 	array_push($arrTmp,$order->delivery_type); //sankalp
                        
		 	if($order->customer_code=="")
		 	{
		 		$custcode  = $order->cust_code;
		 	}
		 	else 
		 	{
		 		$custcode = $order->customer_code;
		 	}
		 	
		 	 if ($todaysdate <= $mostrecent) {
		 	 
			 	if($order->status=='pending'){
			 	$str = '<a class="tiffin_view" data-id='.$order->id.' data-amt='.$order_temp_amount.' data-custcode='.$custcode.'>Confirm</a>';
			 	$str .= '<input type="hidden" name="amt" id="amt" value="'.$order_temp_amount.'">'; // number_format($totalamot,2,'.','')
			 		
			 	$str ='<button  data-id='.$order->id.' data-amt='.$arrTotalAmount['lockedamt'].' data-custcode='.$custcode.' data-text-swap="Wait.." data-tooltip=""  class="tiffin_view smBtn blueBg has-tip tip-top" data-selector="tooltip-igm46z5u0" title=""><i class="fa fa-check-square-o"></i></button>';
			 		
			 //	$str .= '<a class="tiffin_view" data-id='.$order->id.' data-amt='.number_format($arrTotalAmount['lockedamt'],2).' data-custcode='.$custcode.'>Confirm</a>';
			 	$str .= '<input type="hidden" name="amt" id="amt" value="'.$totalamot.'">';
			 	$str .= '<input type="hidden" name="custcode" id="custcode" value="'.$custcode.'">';
			 	
			 	}else{
			 		$str = "Confimed";
			 	}
		 	 }
		 	 else 
		 	 {
		 	 	$str = "--";
		 	 }

		 	array_push($returnVar['data'],$arrTmp);

		 }
              
        foreach($returnVar['data'] as $value){
        	
            $tmpData[] = array_combine($arrColumns, $value);
           
        } 
        return $tmpData;
	
    }
        
	public function confirmorderAction()
	{
		$view = new ViewModel();
		$view->setTerminal(true);
	
		$setting_session = new Container('setting');
        
		$setting = $setting_session->setting;
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$libCustomer = QSCustomer::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libWallet = QSWallet::getInstance($sm);
		$skipKitchenCheck = $setting['GLOBAL_SKIP_KITCHEN'];
		
		$data = $this->params()->fromPost();
		
	
		$orderid = (int) $this->params()->fromRoute('id');
               
		$amt = $this->params()->fromRoute('amt');
		 
		$custcode = $this->params()->fromRoute('custcode');
                
		$cust_session = new Container('customer');
		$customer = $cust_session->customer;
		$data =array();
               
		$utility = new \Lib\Utility();
                
		if($orderid != "")
		{
			$getorderdetails = $this->getOrderConfirmTable()->getOrderDetails($orderid);
		}

		$request = $this->getRequest();
               
		if ($request->isPost()) {
			
		    
			$hdnordid = $request->getPost('orderid');
			$orderflg = $request->getPost('orderflag');
			$orderidfrm = $request->getPost('orderidfrm');
			$amount = $request->getPost('amount');
			$cust_code =$request->getPost('custcode');
			$payment_type = $request->getPost('paymenttype');
			$pay_method = $request->getPost('pay_method');
			$ref_no = $request->getPost('ref_no');
			
			$customerDetails = $libCustomer->getCustomer($cust_code,'id');
			$customerDetails['customer_address'] = $libCustomer->getCustomerAddress($cust_code); // added sankalp 9 june 16
			//echo "<pre>";print_r($customerDetails);echo "</pre>";die;
           
			/* $orderid and  $orderidfrm are same */
			
			$getdata  = $this->getOrderConfirmTable()->getTempOrderPayment($orderidfrm);
           
			$pre_messages['pre_order_name'] = "order";
			$pre_messages['order_id'] = $hdnordid;
			$sms_message = '';
			$order_details = $this->getOrderConfirmTable()->getOrderdata($hdnordid,$orderflg);
            
			$order_menu = $order_details[0]['order_menu'];
			$promo_code = $order_details[0]['promo_code'];
			
			$cartarray = array();
	
			$pre_messages=array(
				'mobile'=>	$order_details[0]['phone'],
				'email_id'=> $order_details[0]['email_address'],
				'cust_name'=> $order_details[0]['customer_name'],
				'customer_code'=>$order_details[0]['customer_code']
			);
	
			$arr =array();
			$finalArray ='';
	
			$pre_messages['total_amt'] ='';
           
			foreach ($order_details as $key =>$val){ 
				
				$sms_message.=$val['quantity']." ".$val['productname'].",";
										
				$pre_messages['cart'][$val['product_code']]=array(
					'id'=>	$val['product_code'],
					'items'=>$val['items'],
					'quantity'=>$val['quantity'],
					'name'=>$val['productname'],
					'type' =>$val['product_type'],
					'price'=>$val['unit_price'],
                    'menu' => $val['order_menu'],
                    'kitchenScreen' => $val['fk_kitchen_code'],
                    'payment_mode' => $val['payment_mode'],
				);
	
			}
			$cartarray = $pre_messages['cart'];

			$pre_messages['total_amt']= $amount;
			$sms_message = rtrim($sms_message,",");
			$pre_messages['sms_message'] = $sms_message;
			$pre_messages['send_sms'] = 1;
			
			$temporder = $libOrder->placeOrder($hdnordid,$cartarray,$customerDetails,$payment_type,$skipKitchenCheck);
			
			if($getdata['recurring_status'] == 1) {

				$orderNo = $temporder['order_id'];
				$orderDate = $temporder['order_dates'];
				$dayPreference = $order_details[0]['days_preference'];
				$libOrder->insertRecurringOrder($orderNo,$orderDate,$dayPreference,$getdata['recurring_status']);
			}
						
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$cust_code;
			$activity_log_data['context_name']= $pre_messages['cust_name'];
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'Order';
			$activity_log_data['action']= 'confirmorder';
			$activity_log_data['description']= $temporder['order_id']." confirmed ".$payment_type." for ".$pre_messages['cust_name'];
												
			$libCommon->saveActivityLog($activity_log_data);
			
			$data['order_id'] = $temporder['order_id'];
			$pre_messages['order_id'] = $temporder['order_id'];
			$pre_messages['total_amt'] = $temporder['total_amt'];
			$pre_messages['amount_paid'] = $temporder['amount_paid'];
			$pre_messages['payment_method'] = $temporder['payment_method'];	
			$pre_messages['order_menu'] = $temporder['order_menu'];		
			
			if(isset($pre_messages['preoreder_dates'])){
				$pre_messages['dates'] = $temporder['order_dates'].",".$pre_messages['preoreder_dates'];
			}else{
				$pre_messages['dates'] = $temporder['order_dates'];
			}
			
			$pre_messages['preoreder_dates'] = $pre_messages['dates'];
			
 			//echo "<pre>"; print_r($pre_messages); exit();
			
			$this->orderBookingEmailSMS($pre_messages,$cust_code);
            $this->orderConfirmationEmailSMS($pre_messages,$order_details);
			if($order_menu=='instantorder'){
				$libOrder->notifyBookingSMS($temporder,$setting);
			}
			
			$data['phone'] = $pre_messages['mobile'];
			$data['bill_month'] = date('M-Y');
			$data['customer_name'] = $pre_messages['cust_name'];
			$data['amount'] = $pre_messages['total_amt'];
			$data['payment_method'] = $temporder['payment_method'];
				
			$changestatus = $this->getOrderConfirmTable()->confirmOrder($orderidfrm);
            
			// if payment is recieved then only wallet is updated
			if($payment_type =="withpayment"){
			
				$walletData = array();
				$walletData['amount'] = $amount;
				$walletData['id'] = $cust_code;
				$walletData['payment_date'] = date('Y-m-d');
				$walletData['created_date'] = date('Y-m-d');
				$walletData['amount_type'] = "cr";
				$walletData['reference_no'] = $getdata['reference_no'];
				
				if(isset($getdata['bank_name'])){
					$walletData['bank_name'] = $getdata['bank_name'];
				}

				if($order_details[0]['payment_mode']=='online'){

					$walletDesc = 'Rs '.number_format($walletData['amount'],2).' received by Online payment.';
				
				}elseif($order_details[0]['payment_mode']=='cash'){

					$walletDesc = 'Rs '.number_format($walletData['amount'],2).' received by Cash.';

				}elseif($order_details[0]['payment_mode']=='cheque'){

					$walletDesc = 'Rs '.number_format($walletData['amount'],2).' received by Cheque Number '.$getdata['reference_no'].'.';
					
				}elseif($order_details[0]['payment_mode']=='neft'){

					$walletDesc = 'Rs '.number_format($walletData['amount'],2).' received by NEFT Transaction id '.$getdata['reference_no'].'.';
					$walletData['neft_date'] = date('Y-m-d');
				}

				$walletData['description'] = $walletDesc;
					
				$libWallet->saveWalletTransaction($walletData,$order_details[0]['payment_mode'],'customer');
				
				///// Payment confirmation message../////////////////////
				$msg_res = $libOrder->sendPaymentConfirmationSMS($data);
				
			}
				$this->flashmessenger()->addSuccessMessage('Order Confirmed Successfully');
				echo "success";die;
			}

			$view->setVariables(array('orderdetails' => $getorderdetails[0],'orderid' =>$orderid,'dateformat' => $setting['DATE_FORMAT'],'amount' => $amt,'customercode' =>$custcode));
            return $view;
		}
	
  


	/**
	 * Send order confirmation email & SMS
	 *
	 * @param array $pre_messages
	 * @param boolean $orderdata
	 */
	
	private function orderConfirmationEmailSMS($pre_messages,$orderdata=false){
		$sm = $this->getServiceLocator();        
        $libCommon = QSCommon::getInstance($sm);
        
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;

		$mailer = new \Lib\Email\Email();
		$sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
		$mailer->setSMSConfiguration($sms_config);
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData($sms_common);
		$mailer->setMobileNo($pre_messages['mobile']);		
		$storage_adapter = $sm->get("Write_Adapter");
		$mailer->setAdapter($sm);
		$sms_array = array(
			'type_of_order' => $pre_messages['sms_message'],
			'order_no'	=> $pre_messages['order_id'],
			'cust_name'	=> $pre_messages['cust_name'],
			'website'	=> $setting['CLIENT_WEB_URL'],
		);
		
		$message = $libCommon->getSMSTemplateMsg('order_confirmation',$sms_array);
		if($message){
			$mailer->setSMSMessage($message);
			$mailer->sendmessage();
		}
		if($pre_messages['email_id']!=''){
			 
			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			 
			$utility = new \Lib\Utility();
			$date= date('d-m-Y h:i A');
			$order_datetime = $utility->displayDate($date,$setting['DATE_FORMAT']);
			 
			foreach($pre_messages['cart'] as $cart){ 
				$order_products = $cart['name'];
                $order_quanty= $cart['quantity'];
                $payment_mode = $cart['payment_mode'];
			}
			$promo_code_message = false;
			
			if(!empty($pre_messages['discount_arr']) && array_key_exists('discount',$pre_messages['discount_arr'])){
				if($pre_messages['discount_arr']['discount']){
					$promo_code_message = '<p><b>Promo Code '.$pre_messages['discount_arr']['promo_code'].' Applied Successfully.</b><br/>You got Discount of '.$libCommon->getCurrencyEntity($setting['GLOBAL_CURRENCY'],$pre_messages['discount_arr']['discount']).' on '.$pre_messages['discount_arr']['product'].'</p>';
				}
			}
			//end option1
			//option 2
			if( ($orderdata->applied_discount) > 0){
				$promo_code_message = '<p><b>Promo Code Applied Successfully.</b><br/>You got Discount of '.$libCommon->getCurrencyEntity($setting['GLOBAL_CURRENCY'],$orderdata->applied_discount).' </p>';
			}
			//end option 2
			$email_vars_array = array(
				'type_of_order' => $pre_messages['sms_message'],
				'order_no'	=> $pre_messages['order_id'],
				'cust_name'	=> $pre_messages['cust_name'],
				'order_date'	=> $order_datetime,
				'order_products' => $order_products,
				'promocode_msg'	=> $promo_code_message,
                'order_quanty' => $order_quanty,				
				'total' => $utility->getLocalCurrency($pre_messages['total_amt'],'','','Email'),
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                'payment_mode' => $payment_mode,
            );
			
			$signature_vars_array = array(
					'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
			);
			
			$email_data = $libCommon->getEmailTemplateMsg('order_confirmation',$email_vars_array, $signature_vars_array);
			$contenttype = $email_data['type'];
			$signature = $email_data['signature'];
			
			$mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
			$mailer->setConfiguration($mailer_config);
			$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
			
			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);
			
			if($email_data['subject']!="" && $email_data['body']!="")
			{
				$mailer->sendmail(array(), array( $pre_messages['cust_name'] => $pre_messages['email_id'] ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
			}
		}
	}
	
	public function orderBookingEmailSMS($pre_messages,$cId=false){
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		$sm = $this->getServiceLocator();
		$libCustomer = QSCustomer::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);

		$utility = new \Lib\Utility();
		
		$mailer = new \Lib\Email\Email();
		$sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
		$mailer->setSMSConfiguration($sms_config);
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData($sms_common);
		$mailer->setMobileNo($pre_messages['mobile']);
		$mailer->setAdapter($sm);
		$no_dates_array = explode(',',$pre_messages['dates']);
		$no_of_days = count($no_dates_array);
		$no_of_day_suffix = ($no_of_days > 1)? ' days' : ' day';
		$no_of_day_string = $no_of_days.$no_of_day_suffix;
		$sms_array = array(
			'type_of_order' => $pre_messages['sms_message'],
			'order_no'	=> $pre_messages['order_id'],
			'cust_name'	=> $pre_messages['cust_name'],
			'days' => $no_of_day_string,
		);
		if($cId){
			$sendnoti=$libCommon->isSubscriptionNotificationChecked($cId);
			$emailverified=$libCommon->isEmailVerified($cId);
		}else{
			$sendnoti=true;
			$emailverified=true;
		}
		
		$message = $libCommon->getSMSTemplateMsg('order_booking',$sms_array);

		if($message){
			$mailer->setSMSMessage($message);
			$mailer->sendmessage();
		}				
		 
		$utility = new \Lib\Utility();        
		$date = date('d-m-Y h:i A');
		$order_datetime = $utility->displayDate($date,$setting['DATE_FORMAT']);
        $meal = "";
        $extra = "";

		foreach($pre_messages['cart'] as $cart){   
            if($cart['type']=='Meal'){
                $meal = $cart['name'].'('.$cart['quantity'].')';     
            }
            if($cart['type']=='Extra'){
                $extra .= $cart['name'].'('.$cart['quantity'].'),';
            }
		}
        
         foreach($pre_messages['cart'] as $key_values=>$item){               
            $keyTime = strtoupper("K".$item['kitchenScreen']."_".$item['menu'])."_ORDER_CUT_OFF_TIME";
            $cutTime = date('h:ia', strtotime($setting[$keyTime]));
        }
        
		if(!empty($pre_messages['discount_arr']) && array_key_exists('discount',$pre_messages['discount_arr']))
		{
			if($pre_messages['discount_arr']['discount'])
			{
				$promo_code_message = '<p><b>Promo Code '.$pre_messages['discount_arr']['promo_code'].' Applied Successfully.</b><br/>You got Discount of '.$libCommon->getCurrencyEntity($setting['GLOBAL_CURRENCY'], $pre_messages['discount_arr']['discount']).' on '.$pre_messages['discount_arr']['product'].'</p>';
			}
		}
		
		$email_vars_array = array(
				'type_of_order' => $pre_messages['sms_message'],
				'order_no'	=> $pre_messages['order_id'],
				'cust_name'	=> $pre_messages['cust_name'],
				'order_date'	=> $order_datetime,
				'meal' => $meal,
                'extra' => $extra,        
				'promocode_msg'	=> $promo_code_message,
				'pre_order_dates'	=> $pre_messages['preoreder_dates'], 
				'payment_method' => $pre_messages['payment_method'],
				'payment_rs' => $utility->getLocalCurrency($pre_messages['total_amt'],'','','Email'),
				'order_status' => $pre_messages['amount_paid']?"Paid":"Unpaid",                
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                'cut_off_time' => $cutTime,
                'order_menu' => ucfirst($pre_messages['order_menu']),
		);
		
		$signature_vars_array = array(
				'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
		);
		
		$email_data = $libCommon->getEmailTemplateMsg('order_booking',$email_vars_array,$signature_vars_array);        
		if($email_data['send_to_admin'] == 'yes'){
            $email_conf = $libCommon->getEmailID($email_data, $cId);

            $signature = $email_data['signature'];
            $contenttype = $email_data['type'];

            $mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
            $mailer->setConfiguration($mailer_config);

            $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
            $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
            $queue = new \Lib\Email\Queue();
            $queue->setStorage($mail_storage);
            $mailer->setQueue($queue);

            //SEND EMAIL TO THE USER
            if($email_data['subject']!="" && $email_data['body']!=""){
                if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
                    $mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
                }				
            }
        }
	
	}
	
	/**
	 * Get instance of QuickServe\Model\ProductTable
	 *
	 * @return QuickServe\Model\ProductTable
	 */
	public function getOrderConfirmTable() {
	
		if (!$this->orderconfirmTable) {
			$sm = $this->getServiceLocator();
			$this->orderconfirmTable = $sm->get('QuickServe\Model\OrderConfirmTable');
		}
		return $this->orderconfirmTable;
	}
	
	/**
	 * This function creates an instance of \Misscall\Controller\PreOrderTable model
	 *
	 * @return \Misscall\Controller\PreOrderTable
	 */
	public function getPreorderTable()
	{
		if (!$this->preorderTable)
		{
			$sm = $this->getServiceLocator();
			$this->preorderTable = $sm->get('preorder');
		}
		return $this->preorderTable;
	}
	
	public function getInvoiceTable()
	{
		if (!$this->invoicetable)
		{
			$sm = $this->getServiceLocator();
			$this->invoicetable = $sm->get('QuickServe\Model\InvoiceTable');
		}
		return $this->invoicetable;
	}
	
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable()
	{
         
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
	/**
	 * Get instance of getUserTable
	 * @method getUserTable()
	 * @return QuickServe\Model\UserTable
	 */
	public function getUserTable()
	{
		if(!$this->userTable)
		{
			$sm = $this->getServiceLocator();
			$this->userTable = $sm->get('QuickServe\Model\UserTable');
		}
		return $this->userTable;
	}
}