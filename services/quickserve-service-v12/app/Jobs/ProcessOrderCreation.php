<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Order;
use App\Models\OrderDetail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessOrderCreation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private array $orderData,
        private string $orderNo
    ) {
        $this->onQueue('order-processing');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing order creation in background', [
                'order_no' => $this->orderNo,
                'job_id' => $this->job->getJobId()
            ]);

            DB::transaction(function () {
                $this->createOrderDetails();
                $this->updateInventory();
                $this->createKitchenData();
                $this->lockWalletAmount();
            });

            $this->sendNotifications();

            Log::info('Order creation processing completed', [
                'order_no' => $this->orderNo
            ]);

        } catch (Exception $e) {
            Log::error('Failed to process order creation', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Create order details in bulk
     */
    private function createOrderDetails(): void
    {
        if (!isset($this->orderData['items']) || empty($this->orderData['items'])) {
            return;
        }

        $orderDetails = [];
        foreach ($this->orderData['items'] as $item) {
            $orderDetails[] = [
                'ref_order_no' => $this->orderNo,
                'product_code' => $item['product_code'],
                'product_name' => $item['product_name'],
                'quantity' => $item['quantity'],
                'amount' => $item['amount'],
                'tax' => $item['tax'] ?? 0,
                'company_id' => $this->orderData['company_id'] ?? 1,
                'unit_id' => $this->orderData['unit_id'] ?? 1,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        // Bulk insert all order details
        OrderDetail::insert($orderDetails);

        Log::info('Order details created in bulk', [
            'order_no' => $this->orderNo,
            'items_count' => count($orderDetails)
        ]);
    }

    /**
     * Update inventory for ordered items
     */
    private function updateInventory(): void
    {
        if (!isset($this->orderData['items'])) {
            return;
        }

        foreach ($this->orderData['items'] as $item) {
            // Update product inventory
            DB::table('products')
                ->where('product_code', $item['product_code'])
                ->decrement('available_quantity', $item['quantity']);
        }

        Log::info('Inventory updated for order', [
            'order_no' => $this->orderNo
        ]);
    }

    /**
     * Create kitchen data for meal preparation
     */
    private function createKitchenData(): void
    {
        try {
            $kitchenData = [
                'order_no' => $this->orderNo,
                'customer_code' => $this->orderData['customer_code'],
                'order_date' => $this->orderData['order_date'],
                'meal_type' => $this->orderData['order_menu'] ?? 'lunch',
                'quantity' => $this->orderData['quantity'],
                'status' => 'pending',
                'created_at' => now()
            ];

            DB::table('kitchen_data')->insert($kitchenData);

            Log::info('Kitchen data created', [
                'order_no' => $this->orderNo
            ]);

        } catch (Exception $e) {
            Log::warning('Failed to create kitchen data', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage()
            ]);
            // Don't fail the job for kitchen data issues
        }
    }

    /**
     * Lock wallet amount for cancellation tracking
     */
    private function lockWalletAmount(): void
    {
        try {
            if (!isset($this->orderData['wallet_amount']) || $this->orderData['wallet_amount'] <= 0) {
                return;
            }

            DB::table('customer_wallet')->insert([
                'customer_code' => $this->orderData['customer_code'],
                'amount_type' => 'dr',
                'wallet_amount' => $this->orderData['wallet_amount'],
                'reference_no' => $this->orderNo,
                'context' => 'order_wallet_lock',
                'description' => "Wallet amount locked for order {$this->orderNo}",
                'created_at' => now()
            ]);

            Log::info('Wallet amount locked for order', [
                'order_no' => $this->orderNo,
                'amount' => $this->orderData['wallet_amount']
            ]);

        } catch (Exception $e) {
            Log::warning('Failed to lock wallet amount', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send notifications (email, SMS, etc.)
     */
    private function sendNotifications(): void
    {
        try {
            // Send order confirmation email
            if (isset($this->orderData['customer_email'])) {
                // Queue email notification
                // SendOrderConfirmationEmail::dispatch($this->orderData);
            }

            // Send SMS notification
            if (isset($this->orderData['customer_phone'])) {
                // Queue SMS notification
                // SendOrderConfirmationSMS::dispatch($this->orderData);
            }

            Log::info('Notifications queued for order', [
                'order_no' => $this->orderNo
            ]);

        } catch (Exception $e) {
            Log::warning('Failed to send notifications', [
                'order_no' => $this->orderNo,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Order creation job failed', [
            'order_no' => $this->orderNo,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Optionally notify administrators or trigger compensating actions
    }
}
