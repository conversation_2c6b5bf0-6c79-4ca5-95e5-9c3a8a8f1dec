<section class="wizard-info">
               <div class="row">
                  <div class="col-sm-12">
      	            	<ul class="progress-indicator">
      		               <li class="inprogress">
      		                  <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
      		               </li>
      		               <li class="">
      		                  <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
      		               </li>
      		               <li class="">
      		                  <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
      		               </li>
                           <li class="">
                              <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                           </li>
                           <li class="">
                              <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                           </li>
      		            </ul>
      			   </div>
               </div>
               <div class="step-2-1">
                  <div class="row">
                  	<div class="col-sm-12">
                  		<h4 class="info-about-table">Add your company details and click on next to continue.</h4>
                  	</div>
                  </div>
                  <div class="mt-10 form-box">
                    <form  id="form-container" name="form-container">
                     <div class="row">
                        <div class="col-sm-12">
                        <div class="clearfix form-head">
                              <div class="pull-left">
                                 <h4 class="txt-color title">Portal Setup</h4>
                              </div>
                              <div class="pull-right">
                                 <p class="txt-color title">Step 1 of 1</p>
                              </div>
                           </div>
                           <div class="col-sm-6">
                              <div class="form-group">
                                 <div class="form-group float-label-control">
                                    <input type="text" class="form-control empty" name="MERCHANT_COMPANY_NAME" value="<?php echo $settings['MERCHANT_COMPANY_NAME']; ?>" >
                                    <label for="">Company Name</label>
                                    <span class ="error" id="MERCHANT_COMPANY_NAME_err" style="color:#FC6E51"></span>
                                 </div>
                                 <div class="form-group float-label-control">
                                    <input type="textarea" class="form-control empty" name="MERCHANT_POSTAL_ADDRESS" value="<?php echo $settings['MERCHANT_POSTAL_ADDRESS']; ?>" >
                                    <label for="">Postal Address</label>
                                    <span class ="error" id="MERCHANT_POSTAL_ADDRESS_err" style="color:#FC6E51"></span>
                                 </div>
                                  <!-- <div class="form-group">
                                    <p class="qus"><i class="fa fa-check-circle circle-active"></i> How many days do you serve your food?</p>
                                    <div class="form-group float-label-control">

                                           <select class="form-control" id="weekoff-list" name="WEEKOFF" style="display: none;">
                                               <option value="mf" selected="selected" style="color: red;">Monday - Friday</option>
                                               <option value="ms">Monday - Saturday</option>
                                               <option value="msu">Monday - Sunday</option>
                                           </select><input type="text" class="form-control replaced-select-field" id="weekoff-list-styled">
                                    <label for="">Select working days</label><ul class="dropdown-menu select-dropdown" id="weekoff-list-dropdown"><li><a href="#" data-option="mf">Monday - Friday </a></li><li><a href="#" data-option="ms">Monday - Saturday </a></li><li><a href="#" data-option="msu">Monday - Sunday</a></li></ul></div>
                                 </div> -->
                                 <div class="form-group float-label-control" id="select-field">
                                    <label for="city">City</label>
                                    <select class="form-control" id="city" name="city">
                                        <?php foreach($cities as $city){ ?>
                                          <option value="<?php echo $city['pk_city_id'].'#'.$city['city']?>" <?php echo ($city['pk_city_id'] == $_SESSION['wizard']['city']) ? 'selected' : ''; ?> ><?php echo $city['city']?></option>
                                            <!-- <option value="<?php //echo $city['pk_city_id']?>" <?php //echo ($city['pk_city_id'] == $_SESSION['wizard']['city']) ? 'selected="selected"' : ''; ?> ><?php //echo $city['city']?></option> -->
                                        <?php }?>
                                    </select>
                                    <span class ="error" id="city_err" style="color:#FC6E51"></span>
                                 </div>
                                 <div class="form-group float-label-control">
                                    <input type="phone" class="form-control empty" name="GLOBAL_WEBSITE_PHONE" value="<?php echo $settings['GLOBAL_WEBSITE_PHONE']; ?>" >
                                    <label for="">Contact Details (phone Number)</label>
                                    <span class ="error" id="GLOBAL_WEBSITE_PHONE_err" style="color:#FC6E51"></span>
                                 </div>
                              </div>
                           </div>
                           <div class="col-sm-6">
                              <div class="form-group">

                                  <div class="form-group float-label-control">
                                    <input type="phone" class="form-control empty" name="MERCHANT_SUPPORT_EMAIL" value="<?php echo $settings['MERCHANT_SUPPORT_EMAIL']; ?>" >
                                    <label for="">Email Address</label>
                                    <span class ="error" id="MERCHANT_SUPPORT_EMAIL_err" style="color:#FC6E51"></span>
                                </div>
                                 <div class="form-group float-label-control">
                                    <input type="text" class="form-control empty" name="ADMIN_WEB_URL" value="<?php echo $settings['ADMIN_WEB_URL']; ?>">
                                    <label for="">Website url (optional) / Catalog URL </label>
                                    <span class ="error" id="ADMIN_WEB_URL_err" style="color:#FC6E51"></span>
                                 </div>
<!--                                 <div class="form-group float-label-control">
                                    <input type="text" class="form-control empty" name="someNewField" data-placement="left" data-toggle="popover" data-trigger="hover" data-content="*You may add here your custom domain name.">
                                    <label for="">Secondary url (Only Sub domain part editable)</label>
                                 </div>-->
                                 <div class="form-group float-label-control">
                                    <input type="text" class="form-control empty" name="MERCHANT_SENDER_ID" value="<?php echo $settings['MERCHANT_SENDER_ID']?>" data-placement="left" data-toggle="popover" data-trigger="hover" data-content="*Add here your desired 6-character SMS sender id. This will reflect in your portal after approval, which may take a couple of days.">
                                    <label for="">SMS sender id</label>
                                    <span class ="error" id="MERCHANT_SENDER_ID_err" style="color:#FC6E51"></span>
                                 </div>
                                 

                                
                              </div>
                           </div>
                        </div>
                        <!-- <div class="col-sm-12 cancel-div">
                           <a href="wizard_3.html" class="cancel-btn">Cancel &#187;</a>    
                        </div> -->
                     </div> 
                    </form>
                  </div>
               </div>
            </section>