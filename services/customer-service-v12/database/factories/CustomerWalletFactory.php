<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\CustomerWallet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerWallet>
 */
class CustomerWalletFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerWallet::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'fk_customer_code' => Customer::factory(),
            'wallet_amount' => $this->faker->randomFloat(2, 10, 1000),
            'amount_type' => $this->faker->randomElement(['cr', 'dr', 'lock']),
            'description' => $this->faker->sentence(),
            'payment_date' => $this->faker->date(),
            'payment_type' => $this->faker->randomElement(['cash', 'online', 'card']),
            'context' => 'customer',
            'company_id' => 1,
            'unit_id' => 1,
            'created_by' => 1,
            'updated_by' => 1,
            'reference_no' => $this->faker->unique()->regexify('[A-Z0-9]{10}'),
        ];
    }

    /**
     * Create a credit transaction.
     */
    public function credit(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'amount_type' => 'cr',
            'description' => 'Credit transaction',
        ]);
    }

    /**
     * Create a debit transaction.
     */
    public function debit(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'amount_type' => 'dr',
            'description' => 'Debit transaction',
        ]);
    }

    /**
     * Create a locked amount transaction.
     */
    public function locked(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'amount_type' => 'lock',
            'description' => 'Locked amount',
        ]);
    }

    /**
     * Create a transaction with specific amount.
     */
    public function withAmount(float $amount): Factory
    {
        return $this->state(fn (array $attributes) => [
            'wallet_amount' => $amount,
        ]);
    }
}
