<?php
/**
 * This File is not used for the backorderprocess
 * As BackOrder access the form library from Front Module
 * This File is not recommended & it will be deleted in future
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *  @deprecated No longer used by internal code and not recommended.
 */

namespace Admin\Form;

use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class NewCustomerForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{

		parent::__construct('newcustomer');
		$this->service_locator = $sm;
		$this->setAttribute('method', 'post');
		//$this->setAttribute('id', 'login');
		//$this->setAttribute('class', 'stdform');
		$this->add(array(
				'name'=>'customer_name',
				'attributes'=>array(
						'type'=>'text',
						'class'=>'form-control',
						'placeholder' => 'Enter your full name',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'Name<span class="red">*</span>'
				)
		));
		$this->add(array(
				'name'=>'phone',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'type' => 'number',
						'class'=>'form-control',
						'placeholder' => 'Enter your contact number',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'Enter Contact<span class="red">*</span>'
				)
		));
		$this->add(array(
				'name'=>'email_address',
				'attributes'=>array(
						'type'=>'text',
						'class'=>'form-control',
						'placeholder' => 'Enter your email id',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'Email ID'
				)
		));
		$this->add(array(
				'name'=>'location_code',
				'type' => 'Zend\Form\Element\Select',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'Delivery Location',
						'value_options' => $this->getLocations()
				)
		));
		
		$this->add(array(
				'name'=>'city',
				'type' => 'Zend\Form\Element\Select',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'City',
						'value_options' => $this->getCities()
				)
		));
		
		$this->add(array(
				'name'=>'company_name',
				'attributes'=>array(
						'type' => 'text',
						'class'=>'form-control',
						'placeholder' => 'Enter your company name'
				),
				'options'=>array(
						'label'=>'Company Name'
				)
		));
		$this->add(array(
				'name'=>'customer_Address',
				'type' => 'Zend\Form\Element\Textarea',
				'attributes'=>array(
						'class'=>'form-control',
						'placeholder' => 'Enter your Address',
						'required' => 'required',
				),
				'options'=>array(
						'label'=>'Address'
				)
		));
		/*$this->add(array(
				'name' => 'food_referance',
				'type' => 'Zend\Form\Element\Radio',
				'attributes' => array(
						'class' => 'check',

				),
				'options' => array(
						'label' => 'Food Preference',
						'label_attributes' => array(
								'class' => 'padd',
						),
						'value_options' => array(
								'Veg' => 'Veg',
								'Non-Veg' => 'Non-Veg',
						),
				),
		));*/
		$this->add(array(
				'name' => 'terms',
				'type' => 'Zend\Form\Element\Checkbox',
				'attributes' => array(
						'class' => 'check',
				),
				'options' => array(
						'label' => 'I agree to Terms and Privacy Policy',
						'use_hidden_element' => true,
						'checked_value' => 1,
						'unchecked_value' => 'no'
				),
		));
		$this->add(array(
				'name' => 'csrf',
				'type' => 'Zend\Form\Element\Csrf',
		));
		$this->add(array(
				'name'=>'submit',
				'attributes'=>array(
						'type'=>'submit',
						'value'=>'Submit >>',
						'class'=>'button left tiny',
				)
		));
	}
	public function getLocations()
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('delivery_locations');
		$select->where('status',1);
		//$select->order('price_name ASC');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		$selectData['default'] = "select any location";
		foreach ($results as $res) {
			$selectData[$res['pk_location_code']] = $res['location'];
		}
		//echo '<pre>';print_r($selectData);exit();
		return $selectData;

	}

	public function getCities()
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('city');
		$select->where('status',1);
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		$selectData['default'] = "select any city";
		foreach ($results as $res) {
			$selectData[$res['pk_city_id']] = $res['city'];
		}
		
		return $selectData;
	
	}
	
}