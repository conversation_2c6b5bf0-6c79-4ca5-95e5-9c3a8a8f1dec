<?php
$form = $this->form;
$form->setAttribute('action', $this->url('city', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>      
<div id="content">
    <?php echo $this->form()->openTag($form);?>
    <div class="large-6 columns">
        <fieldset>
            <legend>
                City Info
            </legend>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('city')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                    <?php  
                       echo $this->formHidden($form->get('pk_city_id'));
                       echo $this->formElement($form->get('city'));
                       echo $this->formElementErrors()
                           ->setMessageOpenFormat('<small class="error">')
                           ->setMessageCloseString('</small>')
                           ->render($form->get('city'));
                    ?>
                </div>
            </div>        
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                    <?php 
                        echo $this->formElement($form->get('status'));
                        echo $this->formElementErrors($form->get('status'));
                        echo $this->formElement($form->get('csrf')); 
                     ?>
                </div>
            </div>
        </fieldset>
        <div class="large-12 columns pl0 pr0">
            <?php echo $this->formElement($form->get('backurl'));
                 ?>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                <div class="large-8  small-8 medium-8 columns">
                    <button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                    <button	type="button" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
                </div>
            </div>
        </div>
    </div>    
    <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
</div>

