# Automated Gap-Filling Workflow

## Overview

The Automated Gap-Filling Workflow is a comprehensive system designed to systematically implement missing API endpoints and frontend integrations for the Laravel 12 microservices migration project. This workflow addresses the API mapping gaps identified in `docs/api-mapping.md` by implementing both backend Laravel routes and frontend MFE (micro-frontend) callers in a structured, iterative manner.

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Gap-Filling Workflow                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Queue Gen     │  │  Gap Controller │  │ Quality Gates   │ │
│  │                 │  │                 │  │                 │ │
│  │ • Parse API     │  │ • Process Items │  │ • Code Quality  │ │
│  │   Mapping       │  │ • Orchestrate   │  │ • Test Coverage │ │
│  │ • Generate      │  │   Workflow      │  │ • Integration   │ │
│  │   Queues        │  │ • Track Status  │  │ • Performance   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │         │
│           ▼                     ▼                     ▼         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Backend Impl    │  │ Frontend Impl   │  │ Kong Config     │ │
│  │                 │  │                 │  │                 │ │
│  │ • Controllers   │  │ • API Services  │  │ • Route Config  │ │
│  │ • DTOs          │  │ • React Hooks   │  │ • Plugins       │ │
│  │ • Services      │  │ • Components    │  │ • Validation    │ │
│  │ • Repositories  │  │ • Types         │  │ • Health Checks │ │
│  │ • Tests         │  │ • Tests         │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Initial Setup (One-time)

```bash
# Create feature branch
git checkout -b feature/fill-api-gaps

# Run initial setup
make setup

# Generate work queues from API mapping
make generate-queues

# Start development environment
make docker-up
```

### 2. Process API Gaps

```bash
# Process next gap automatically
make fill-gap

# Process multiple gaps in batch
make fill-gaps

# Check queue status
make gap-status

# Run quality gates
make quality-gates
```

### 3. Using Laravel Artisan Command

```bash
# Process next item
php artisan gap:next

# Show queue status
php artisan gap:next --status

# Generate queues
php artisan gap:next --generate

# Process specific item
php artisan gap:next --item=BE-001

# Process multiple items
php artisan gap:next --loop=5
```

## Workflow Components

### 1. Queue Generator (`generate-queues.php`)

Parses `docs/api-mapping.md` to extract API gaps and generates prioritized work queues:

- **Backend Queue**: Frontend endpoints that exist but lack corresponding Laravel routes
- **Frontend Queue**: Laravel routes that exist but lack MFE caller implementations

**Features:**
- Priority-based sorting
- Service inference from path patterns
- Configurable priority weights
- Progress tracking

### 2. Gap Controller (`gap-controller.php`)

Main orchestrator that implements the `gap:next` functionality:

- Selects next item based on priority
- Orchestrates backend/frontend implementation
- Tracks item status and progress
- Provides queue management

**Modes:**
- `backend_first`: Prioritize backend implementation
- `frontend_first`: Prioritize frontend implementation  
- `balanced`: Select highest priority from both queues

### 3. Backend Implementation (`backend-implementation.php`)

Implements missing Laravel API endpoints following Domain-Driven Design patterns:

**Generated Components:**
- API Controllers with proper validation
- DTOs for data transfer
- Service classes for business logic
- Repository interfaces and implementations
- Feature and unit tests
- Route definitions
- OpenAPI specifications

**Standards:**
- PSR-4 autoloading
- SOLID principles
- Laravel best practices
- Comprehensive test coverage

### 4. Frontend Implementation (`frontend-implementation.php`)

Implements missing frontend API integrations:

**Generated Components:**
- React Query hooks for API calls
- TypeScript types from OpenAPI specs
- Zod schemas for runtime validation
- UI components using shadcn/ui
- Cypress e2e tests
- Route configurations

**Standards:**
- TypeScript strict mode
- React Query for state management
- Component composition patterns
- Accessibility compliance

### 5. Quality Gates (`quality-gates.php`)

Comprehensive quality checks ensuring production readiness:

**Quality Checks:**
- **Code Quality**: PHPStan, ESLint, TypeScript compilation
- **Test Coverage**: ≥80% for both PHP and TypeScript
- **Integration**: API endpoints, Kong gateway, RabbitMQ
- **Performance**: <200ms API response times
- **Security**: Dependency audits, vulnerability scans
- **Kong Validation**: Configuration validation with decK

## Configuration

### Gap Controller Configuration (`config/gap-controller.json`)

```json
{
  "priority_mode": "balanced",
  "stop_on_failure": false,
  "max_concurrent_items": 1,
  "timeout_seconds": 300,
  "quality_gates": {
    "run_after_implementation": true,
    "required_coverage": 80
  }
}
```

### Quality Gates Configuration (`config/quality-gates.json`)

```json
{
  "min_backend_coverage": 80.0,
  "min_frontend_coverage": 80.0,
  "max_response_time_ms": 200,
  "max_phpstan_errors": 10,
  "security_checks": {
    "max_critical_vulnerabilities": 0,
    "max_high_vulnerabilities": 2
  }
}
```

## Queue Format

### Backend Queue Item
```json
{
  "id": "BE-001",
  "type": "BACKEND_FIRST",
  "method": "POST",
  "path": "/v2/customers",
  "service": "customer-service-v12",
  "mfe": "unified-frontend",
  "priority": 4,
  "status": "pending",
  "created_at": "2025-05-22T18:25:08.054Z"
}
```

### Frontend Queue Item
```json
{
  "id": "FE-001", 
  "type": "FRONTEND_FIRST",
  "method": "GET",
  "path": "/v2/customers/search",
  "service": "customer-service-v12",
  "mfe": "frontend-shadcn",
  "priority": 3,
  "status": "pending",
  "created_at": "2025-05-22T18:25:08.054Z"
}
```

## Implementation Patterns

### Backend Implementation Pattern

1. **Controller Generation**
   ```php
   // Generated API Controller
   class CustomersController extends Controller
   {
       public function store(CustomerCreateRequest $request): JsonResponse
       {
           $dto = CustomerCreateDTO::from($request->validated());
           $customer = $this->customerService->create($dto);
           return response()->json($customer, 201);
       }
   }
   ```

2. **Service Layer**
   ```php
   // Generated Service Class
   class CustomerService
   {
       public function create(CustomerCreateDTO $dto): Customer
       {
           return $this->repository->create($dto->toArray());
       }
   }
   ```

3. **Repository Pattern**
   ```php
   // Generated Repository
   class CustomerRepository implements CustomerRepositoryInterface
   {
       public function create(array $data): Customer
       {
           return Customer::create($data);
       }
   }
   ```

### Frontend Implementation Pattern

1. **API Service**
   ```typescript
   // Generated API Service
   export const customerService = {
     create: async (data: CustomerCreateRequest): Promise<Customer> => {
       const response = await apiClient.post('/v2/customers', data);
       return response.data;
     }
   };
   ```

2. **React Query Hook**
   ```typescript
   // Generated Hook
   export const useCustomers = () => {
     const queryClient = useQueryClient();
     
     const createMutation = useMutation({
       mutationFn: customerService.create,
       onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ['customers'] });
       }
     });
     
     return { create: createMutation.mutate };
   };
   ```

3. **Component Integration**
   ```tsx
   // Generated Component
   export const CustomerForm: React.FC = () => {
     const { create } = useCustomers();
     
     return (
       <form onSubmit={(data) => create(data)}>
         {/* Form implementation */}
       </form>
     );
   };
   ```

## Kong API Gateway Integration

All implemented endpoints are automatically configured in Kong with:

- **Routing Pattern**: `/v2/{service-name}/*`
- **Authentication**: JWT with RS256
- **Rate Limiting**: 100 requests/minute
- **CORS**: Configured for frontend origins
- **Health Checks**: Every 30 seconds
- **Circuit Breakers**: 5-failure threshold

## Monitoring and Observability

### Progress Tracking
- Real-time queue status
- Item processing history
- Success/failure rates
- Performance metrics

### Quality Metrics
- Test coverage reports
- Code quality scores
- Performance benchmarks
- Security vulnerability counts

### Integration Health
- API endpoint availability
- Kong gateway status
- RabbitMQ message flow
- Database connectivity

## Success Criteria

The workflow is considered successful when:

1. **Complete Coverage**: `docs/api-mapping.md` shows zero "Gap" entries
2. **Quality Standards**: All test coverage reports meet ≥80% threshold
3. **Integration Tests**: Cypress e2e tests pass with Kong routing and Keycloak authentication
4. **Performance**: Smoke test suite reports zero failing endpoints with <200ms response times
5. **Architecture Compliance**: Each implementation follows established microservices patterns

## Troubleshooting

### Common Issues

1. **Queue Generation Fails**
   ```bash
   # Check API mapping file exists
   ls -la docs/api-mapping.md
   
   # Verify script permissions
   chmod +x scripts/gap-filling/generate-queues.php
   ```

2. **Implementation Fails**
   ```bash
   # Check service directory exists
   ls -la services/
   
   # Verify dependencies installed
   cd services/auth-service-v12 && composer install
   ```

3. **Quality Gates Fail**
   ```bash
   # Run individual checks
   make phpstan
   make test-coverage
   make kong-validate
   ```

### Debug Mode

Enable debug logging:
```bash
export GAP_FILLING_LOG_LEVEL=debug
php artisan gap:next
```

## Contributing

When contributing to the gap-filling workflow:

1. Follow the established patterns
2. Add comprehensive tests
3. Update documentation
4. Run quality gates before committing
5. Use conventional commit messages

## Related Documentation

- [API Mapping Analysis](./api-mapping.md)
- [Laravel 12 Migration Guide](./laravel-12-migration.md)
- [Kong API Gateway Configuration](./kong-configuration.md)
- [Frontend Architecture](./frontend-architecture.md)
- [Testing Strategy](./testing-strategy.md)
