<h1><?php echo $this->translate('An error occurred') ?></h1>
<h2><?php echo $this->message ?></h2>

<?php if (isset($this->display_exceptions) && $this->display_exceptions): ?>

<?php if(isset($this->exception) && $this->exception instanceof Exception): ?>
<hr/>
<h2><?php echo $this->translate('Additional information') ?>:</h2>
<h3><?php echo get_class($this->exception); ?></h3>
<dl>
    <dt><?php echo $this->translate('File') ?>:</dt>
    <dd>
        <pre class="prettyprint linenums"><?php echo $this->exception->getFile() ?>:<?php echo $this->exception->getLine() ?></pre>
    </dd>
    <dt><?php echo $this->translate('Message') ?>:</dt>
    <dd>
        <pre class="prettyprint linenums"><?php echo $this->exception->getMessage() ?></pre>
    </dd>
    <dt><?php echo $this->translate('Stack trace') ?>:</dt>
    <dd>
        <pre class="prettyprint linenums"><?php echo $this->exception->getTraceAsString() ?></pre>
    </dd>
</dl>
<?php
    $e = $this->exception->getPrevious();
    if ($e) :
?>
<hr/>
<h2><?php echo $this->translate('Previous exceptions') ?>:</h2>
<ul class="unstyled">
    <?php while($e) : ?>
    <li>
        <h3><?php echo get_class($e); ?></h3>
        <dl>
            <dt><?php echo $this->translate('File') ?>:</dt>
            <dd>
                <pre class="prettyprint linenums"><?php echo $e->getFile() ?>:<?php echo $e->getLine() ?></pre>
            </dd>
            <dt><?php echo $this->translate('Message') ?>:</dt>
            <dd>
                <pre class="prettyprint linenums"><?php echo $e->getMessage() ?></pre>
            </dd>
            <dt><?php echo $this->translate('Stack trace') ?>:</dt>
            <dd>
                <pre class="prettyprint linenums"><?php echo $e->getTraceAsString() ?></pre>
            </dd>
        </dl>
    </li>
    <?php
        $e = $e->getPrevious();
        endwhile;
    ?>
</ul>
<?php endif; ?>

<?php else: ?>

<h3><?php echo $this->translate('No Exception available') ?></h3>

<?php endif ?>

<?php endif ?>
