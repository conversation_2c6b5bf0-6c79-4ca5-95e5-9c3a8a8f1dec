<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: UserForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;

class UserForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	
	private $service_locator;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('promo_code');
        $this->service_locator = $sm;

        $this->setAttribute('method', 'post');
        $this->add(array(
            'name' => 'pk_user_code',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));
        
        $this->add(array(
            'name' => 'first_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
            	 'id' => 'first_name',
                'placeholder' => 'Enter First Name...',
            	'autofocus' => true
            ),
            'options' => array(
                'label' => 'First Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
                        'name' => 'last_name',
                        'type' => 'Zend\Form\Element\Text',
                        'attributes' => array(
                                        'class' => 'smallinput',
                                        'id' => 'last_name',
                                        'placeholder' => 'Enter Last Name...',
                                        //'required' => 'required',
                        ),
                        'options' => array(
                                        'label' => 'Last Name',
                                        'label_options' => array('disable_html_escape' => true),
                        ),
        ));
		
        $this->add(array(
            'name' => 'phone',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
            	'id' => 'phone',
                'placeholder' => 'Enter Contact number...',
               // 'required' => 'required',

            ),
            'options' => array(
                'label' => 'Contact Number<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

       $this->add(array(
            'name' => 'email_id',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
            	'id' => 'email_id',
                'placeholder' => 'Enter Email Address...',
//                'required' => 'required',
            ),
            'options' => array(
                'label' => 'Email Id<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

       $this->add(array(
       		'name' => 'password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'password',
       				'placeholder' => 'Password Here...',
       		),
       		'options' => array(
       				'label' => 'Password<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       		),
       ));

       $this->add(array(
       		'name' => 'password_verify',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'password_verify',
       				'placeholder' => 'Verify Password Here...',
       				//'required' => 'required',
       		),
       		'options' => array(
       				'label' => 'Confirm Password',
       				'label_options' => array('disable_html_escape' => true),
       		),
       ));

		/*
		$this->add(array(
            'name' => 'city',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'required' => 'required',
            ),
            'options' => array(
                'label' => 'City',
                'value_options' => array(
                    'Mumbai' => 'Mumbai',
                    'Navi Mumbai' => 'Navi Mumbai',
                ),
            ),
        ));

		 $this->add(array(
            'name' => 'country',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'required' => 'required',
            ),
            'options' => array(
                'label' => 'Country',
                'value_options' => $this->getCountry()
            ),
        ));
		*/
		$this->add(array(
            'name' => 'role_id',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
               // 'required' => 'required',
            	'id' => 'role_id',
            ),
            'options' => array(
                'label' => 'Role<span class="red">*</span>',
                'value_options' => $this->getRoles(),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
		
        $this->add(array(
            'name'=>'gender',
       		'type' => 'Zend\Form\Element\Radio',
            
       		'attributes'=>array(
                'value' => 'M',
       		),
       		'options'=>array(
       				'label'=>'Gender<span class="red">*</span>',
       				'label_attributes' => array(
                                    'class'  => 'left mr10' // this class is applied to all labels(ie label+option label)
                    ),
       				'value_options' => ['M' => 'male', 'F' => 'female'],
       				'label_options' => array(
                        'disable_html_escape' => true,
//                        'class'  => 'left mr10 zaza'
                        ),
                    
       		),
        ));
        
		$kitchenScreens = $this->getKitchenScreens();
		
		$this->add(array(
				'name' => 'fk_kitchen_code',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'fk_kitchen_code',
						'class' => 'chosen-select',
						'multiple'=>'multiple',
				),
				'options' => array(
						'label' => 'Kitchen<span class="red">*</span>',
						'value_options' => $kitchenScreens,
						'label_options' => array('disable_html_escape' => true),
						'disable_inarray_validator' => true
				),
		));

		$this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
						'label_options' => array('disable_html_escape' => true),
				),
		));

        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        		),
        ));

        $this->add(array(
        	'name'=>'backurl',
        	'type' => 'Zend\Form\Element\Hidden',
        	'attributes'=>array(
        			'id'=>'backurl',
        			'value'=>'/users',
        ),
        ));
    }
    

       
	/**
	 * To get the list of active user roles
	 *
	 * @return array
	 */
    public function getRoles()
    {
       	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('roles');
    	$select->where('status = 1');
//    	$select->where('role_type = "application"'); // added by sankalp
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute(); 
    	//$selectData[''] = "Select Role";
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
                
                $value          = array('value' => $res['pk_role_id'], 'label' => $res['role_name']);
                if($res['role_name'] == 'Third-Party Delivery' || $res['role_name'] == 'Third-Party Aggregator'){
                    $value['disabled'] = 'disabled';
                }
                
                $selectData[]   =  $value;
    	}
    	return $selectData;
    }
	/**
	 * To get the list of active countries
	 *
	 * @return array
	 */
	public function getCountry()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('countries');
    	//$select->where('currency_code',1);
    	$select->where('active',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$selectData[$res['country_name']] = $res['country_name'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    }
 
    private function getKitchenScreens() {
    	$kitchen_array = array('' => 'Select the kitchen', '0'=>'All');
    	$sql = new QSql($this->service_locator);
    
    	$select = $sql->select();
    	$select->from('kitchen_master');
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
    	}// end of foreach
    	
    	
    //	echo "<pre>";print_r($kitchen_array);die;
    	return $kitchen_array;
    }
    
       
}
