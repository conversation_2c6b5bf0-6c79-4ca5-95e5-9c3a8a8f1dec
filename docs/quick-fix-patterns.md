# Quick Fix Patterns Reference

## 🔧 Database Transaction Issues
**Error**: `PDOException: There is already an active transaction`
**Quick Fix**:
```php
// tests/TestCase.php
protected function setUp(): void {
    parent::setUp();
    if (DB::transactionLevel() > 0) DB::rollBack();
}
protected function tearDown(): void {
    while (DB::transactionLevel() > 0) DB::rollBack();
    parent::tearDown();
}
```

## 🔐 Authentication Issues  
**Error**: `Auth guard [sanctum] is not defined`
**Quick Fix**:
```bash
composer require laravel/sanctum
```
```php
// config/auth.php
'guards' => [
    'sanctum' => ['driver' => 'sanctum', 'provider' => 'users'],
],

// Test setup
$user = User::factory()->create();
$this->actingAs($user, 'sanctum');
```

## 🏗️ Facade Root Issues
**Error**: `RuntimeException: A facade root has not been set`
**Quick Fix**:
```php
// Change from PHPUnit\Framework\TestCase to:
use Tests\TestCase;

// Mock facades:
Log::shouldReceive('error')->andReturn(null);
```

## 🎭 Mock Issues
**Error**: Mock expectations not matching
**Quick Fix**:
```php
// Set object IDs explicitly:
$payment->id = 1;

// Match return types:
->andReturn(true); // Not object for update methods

// Handle multiple parameters:
->with($data, \Mockery::type('Illuminate\Http\Request'))
```

## 📊 Progress Tracking
```bash
# Quick system check
./vendor/bin/phpunit 2>&1 | tail -5

# Error pattern analysis  
./vendor/bin/phpunit --testdox 2>&1 | grep -A 3 "✘" | head -20

# Full progress report
bash scripts/test-progress-tracker.sh
```

## 🎯 Success Indicators
- ✅ **Payment Service**: 100% (78/78)
- ✅ **QuickServe Service**: 70% (157/223) 
- ✅ **Overall System**: 69% (446/645)
- ✅ **Improvement**: +124 tests fixed

## 🚀 Next Targets
1. **QuickServe**: 157/223 → 200/223 (+43 tests)
2. **Delivery**: 15/34 → 30/34 (+15 tests)  
3. **Analytics**: 1/70 → 35/70 (+34 tests)
