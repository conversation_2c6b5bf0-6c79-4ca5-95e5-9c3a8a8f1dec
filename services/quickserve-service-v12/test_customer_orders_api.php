<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testing Customer Orders API - Product Name Issue\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // Test 1: Check orders table structure
    echo "📋 Test 1: Orders Table Structure\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $columns = DB::select("SHOW COLUMNS FROM orders");
    
    echo "Orders table columns:\n";
    foreach ($columns as $column) {
        if (stripos($column->Field, 'product') !== false || stripos($column->Field, 'name') !== false) {
            echo "  ✅ {$column->Field} ({$column->Type})\n";
        }
    }
    echo "\n";
    
    // Test 2: Check if orders exist for customer 1
    echo "📊 Test 2: Sample Orders for Customer 1\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $sampleOrders = DB::table('orders')
        ->where('customer_code', 1)
        ->select([
            'pk_order_no',
            'order_no', 
            'order_date',
            'product_name',
            'product_code',
            'order_status'
        ])
        ->limit(5)
        ->get();
    
    if ($sampleOrders->isEmpty()) {
        echo "❌ No orders found for customer 1\n";
        
        // Check if any orders exist at all
        $anyOrders = DB::table('orders')->limit(5)->get(['pk_order_no', 'customer_code', 'order_no', 'product_name']);
        
        if ($anyOrders->isEmpty()) {
            echo "❌ No orders found in the entire table\n";
        } else {
            echo "✅ Found orders for other customers:\n";
            foreach ($anyOrders as $order) {
                echo "  Order {$order->order_no} - Customer {$order->customer_code} - Product: {$order->product_name}\n";
            }
        }
    } else {
        echo "✅ Found orders for customer 1:\n";
        foreach ($sampleOrders as $order) {
            echo "  Order {$order->order_no} - Date: {$order->order_date} - Product: '{$order->product_name}' - Status: {$order->order_status}\n";
        }
    }
    echo "\n";
    
    // Test 3: Test the actual query used in getCustomerOrders
    echo "🔍 Test 3: Actual API Query Test\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $customerId = 1;
    $apiQuery = DB::table('orders as o')
        ->where('o.customer_code', $customerId)
        ->select([
            'o.pk_order_no as order_id',
            'o.order_no',
            'o.order_date',
            'o.order_date as delivery_date',
            'o.order_status',
            'o.delivery_status',
            'o.payment_mode',
            'o.amount_paid',
            'o.amount as total_amount',
            'o.delivery_time',
            'o.delivery_end_time',
            DB::raw('1 as recurring_status'),
            DB::raw('"" as days_preference'),
            'o.ship_address as customer_address',
            'o.location_name',
            'o.city_name',
            'o.food_type as food_preference',
            'o.product_code',
            'o.product_name',  // This should be included
            'o.quantity',
            'o.amount as item_amount',
            'o.product_type'
        ])
        ->orderBy('o.order_date', 'desc')
        ->limit(3)
        ->get();
    
    if ($apiQuery->isEmpty()) {
        echo "❌ API query returned no results for customer 1\n";
    } else {
        echo "✅ API query results:\n";
        foreach ($apiQuery as $order) {
            echo "  Order ID: {$order->order_id}\n";
            echo "  Order No: {$order->order_no}\n";
            echo "  Product Name: '{$order->product_name}'\n";
            echo "  Product Code: {$order->product_code}\n";
            echo "  Order Status: {$order->order_status}\n";
            echo "  ---\n";
        }
    }
    echo "\n";
    
    // Test 4: Check if product_name is NULL or empty
    echo "🔎 Test 4: Product Name Data Quality Check\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $productNameStats = DB::table('orders')
        ->selectRaw('
            COUNT(*) as total_orders,
            COUNT(product_name) as orders_with_product_name,
            COUNT(CASE WHEN product_name IS NULL THEN 1 END) as null_product_names,
            COUNT(CASE WHEN product_name = "" THEN 1 END) as empty_product_names,
            COUNT(CASE WHEN product_name IS NOT NULL AND product_name != "" THEN 1 END) as valid_product_names
        ')
        ->first();
    
    echo "Product Name Statistics:\n";
    echo "  Total Orders: {$productNameStats->total_orders}\n";
    echo "  Orders with Product Name: {$productNameStats->orders_with_product_name}\n";
    echo "  NULL Product Names: {$productNameStats->null_product_names}\n";
    echo "  Empty Product Names: {$productNameStats->empty_product_names}\n";
    echo "  Valid Product Names: {$productNameStats->valid_product_names}\n\n";
    
    // Test 5: Sample valid product names
    echo "📝 Test 5: Sample Valid Product Names\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $validProductNames = DB::table('orders')
        ->whereNotNull('product_name')
        ->where('product_name', '!=', '')
        ->select('product_name')
        ->distinct()
        ->limit(10)
        ->pluck('product_name');
    
    if ($validProductNames->isEmpty()) {
        echo "❌ No valid product names found\n";
    } else {
        echo "✅ Sample valid product names:\n";
        foreach ($validProductNames as $productName) {
            echo "  - '{$productName}'\n";
        }
    }
    echo "\n";
    
    // Test 6: Check if the issue is in the response formatting
    echo "🔧 Test 6: Response Formatting Check\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    // Simulate what happens in the controller
    $orders = $apiQuery;
    
    if (!$orders->isEmpty()) {
        $firstOrder = $orders->first();
        echo "Raw order object properties:\n";
        foreach (get_object_vars($firstOrder) as $key => $value) {
            echo "  {$key}: '{$value}'\n";
        }
        echo "\n";
        
        // Convert to array (as done in the controller)
        $orderArray = json_decode(json_encode($firstOrder), true);
        echo "Order as array:\n";
        foreach ($orderArray as $key => $value) {
            echo "  {$key}: '{$value}'\n";
        }
    }
    
    echo "\n";
    
    // Test 7: Make actual HTTP request to API
    echo "🌐 Test 7: Actual API Request\n";
    echo "-" . str_repeat("-", 60) . "\n";
    
    $apiUrl = 'http://192.168.1.16:8003/api/v2/order-management/customer/1';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        echo "❌ Failed to make API request\n";
    } else {
        echo "✅ API Response (HTTP {$httpCode}):\n";
        $responseData = json_decode($response, true);
        
        if (isset($responseData['data']['all']) && !empty($responseData['data']['all'])) {
            $firstApiOrder = $responseData['data']['all'][0];
            echo "First order from API response:\n";
            foreach ($firstApiOrder as $key => $value) {
                if (is_array($value)) {
                    echo "  {$key}: " . json_encode($value) . "\n";
                } else {
                    echo "  {$key}: '{$value}'\n";
                }
            }
            
            if (isset($firstApiOrder['product_name'])) {
                echo "\n✅ product_name is present: '{$firstApiOrder['product_name']}'\n";
            } else {
                echo "\n❌ product_name is MISSING from API response\n";
            }
        } else {
            echo "No orders found in API response\n";
            echo "Response: " . substr($response, 0, 500) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Summary:\n";
echo "- Check if product_name column exists in orders table\n";
echo "- Verify if orders exist for customer 1\n";
echo "- Test the actual API query\n";
echo "- Check data quality of product_name field\n";
echo "- Make actual HTTP request to API\n";
echo "- Identify where product_name might be getting lost\n";
