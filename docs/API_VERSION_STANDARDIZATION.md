# API Version Standardization Notice

**Date: May 19, 2023**

## Summary

OneFood Dialer is standardizing all API endpoints to use the `v1` version prefix. Legacy `v2` endpoints are now deprecated and will be removed on **December 31, 2025**. This document outlines the changes, migration path, and timeline for this standardization.

## Background

Our API has historically used a mix of `v1` and `v2` version prefixes across different services and endpoints. This inconsistency has led to confusion for developers and increased maintenance overhead. To address this, we are standardizing all endpoints to use the `v1` prefix.

## Changes

### Endpoints Affected

All API endpoints that currently use the `v2` prefix are affected. The most commonly used endpoints include:

- `/api/v2/auth/*` → `/api/v1/auth/*`
- `/api/v2/users/*` → `/api/v1/users/*`
- `/api/v2/payments/*` → `/api/v1/payments/*`
- `/api/v2/orders/*` → `/api/v1/orders/*`

### Backward Compatibility

To ensure a smooth transition, we will maintain backward compatibility with `v2` endpoints until the deprecation date. During this period:

- All `v2` endpoints will continue to function as before
- Deprecation warnings will be included in HTTP headers for `v2` endpoint responses
- Usage of `v2` endpoints will be logged and monitored

## Migration Guide

### For API Consumers

1. **Update API Client Configuration**:
   - Change all API endpoint URLs to use the `v1` prefix instead of `v2`
   - Update any hardcoded URLs in your application

2. **Update API Client Libraries**:
   - If you're using our official client libraries, update to the latest version
   - If you've built custom client libraries, update the endpoint URLs

3. **Test Your Application**:
   - Thoroughly test your application with the updated endpoints
   - Pay special attention to authentication, error handling, and response parsing

### Example Changes

#### JavaScript/TypeScript

```javascript
// Before
const AUTH_API_PREFIX = '/api/v2/auth';

// After
const AUTH_API_PREFIX = '/api/v1/auth';
```

#### PHP

```php
// Before
$apiUrl = 'https://api.onefooddialer.com/api/v2/auth/login';

// After
$apiUrl = 'https://api.onefooddialer.com/api/v1/auth/login';
```

#### Configuration Files

```yaml
# Before
api_base_url: https://api.onefooddialer.com/api/v2

# After
api_base_url: https://api.onefooddialer.com/api/v1
```

## Timeline

- **May 19, 2023**: Announcement of API version standardization
- **June 1, 2023**: Deprecation warnings added to `v2` endpoint responses
- **January 1, 2024**: Email notifications to API consumers still using `v2` endpoints
- **July 1, 2024**: Increased frequency of email notifications
- **January 1, 2025**: Final notice to API consumers still using `v2` endpoints
- **December 31, 2025**: Removal of `v2` endpoints

## Monitoring and Support

We have implemented monitoring tools to track usage of deprecated `v2` endpoints. If you're an API consumer, you can:

1. Check the `X-API-Deprecation-Warning` header in responses from `v2` endpoints
2. Contact our support team for assistance with migration
3. Request access to our API version usage dashboard to monitor your application's usage of deprecated endpoints

## FAQs

### Why are we standardizing on `v1` instead of `v2`?

The `v1` prefix is used by the majority of our services and has the most complete implementation. Standardizing on `v1` minimizes the number of changes required for both our internal systems and API consumers.

### Will the API functionality change?

No, the functionality of the API will remain the same. Only the version prefix in the URL is changing.

### What happens if I don't update my application?

After December 31, 2025, any requests to `v2` endpoints will receive a 410 Gone response with a message indicating that the endpoint has been removed.

### Will there be any breaking changes in the `v1` endpoints?

No, the `v1` endpoints are functionally equivalent to the `v2` endpoints. The only change is the URL prefix.

### How can I get help with migration?

Contact our support <NAME_EMAIL> for assistance with migration. We can provide guidance, answer questions, and help troubleshoot any issues you encounter.

## Contact

For questions or concerns about this standardization, please contact:

- **API Support**: <EMAIL>
- **Developer Relations**: <EMAIL>
- **Technical Support**: <EMAIL>
