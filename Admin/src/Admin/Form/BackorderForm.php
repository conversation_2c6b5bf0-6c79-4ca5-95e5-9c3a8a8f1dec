<?php

/**
 * This File is not used for the backorderprocess
 * As BackOrder access the form library from Front Module
 * This File is not recommended & it will be deleted in future
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: BackorderForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */

namespace Admin\Form;
use Zend\Form\Form;

class BackorderForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 * It adds an input fields which are needed to create customer login form
	 * @var string $name
	 * @return void
	 */
	public function __construct($name = FALSE)
	{
		parent::__construct('backorder');
		$this->setAttribute('method', 'post');
        $this->setAttribute('id', 'backorder');
		$this->add(array(
			'name'=>'phone',
			'type' => 'Zend\Form\Element\Text',
			'attributes'=>array(
				'class'=>'mar-top',
				'placeholder' => 'Enter your registered mobile number OR Email-ID',
				'id'	=> 'cno',
				'required' => 'required'
			),
			'options'=>array(
				'label'=>'Phone'
			)
		));
		$this->add(array(
			'name' => 'csrf',
			'type' => 'Zend\Form\Element\Csrf',
		));
		$this->add(array(
			'name'=>'submit',
			'attributes'=>array(
				'type'=>'submit',
				'value'=>'Submit',
				'id'=>'btnsubmit',
				'class'=>'button left tiny',
				'data-text-swap'=>"Wait..",
			)
		));
	}

}