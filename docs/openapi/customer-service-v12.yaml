openapi: 3.1.0
info:
  title: Customer Service API
  description: Comprehensive customer management service with profile, wallet, and verification capabilities
  version: 2.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000/v2/customers
    description: Development server via Kong API Gateway
  - url: https://api.cubeonebiz.com/v2/customers
    description: Production server via Kong API Gateway

security:
  - BearerAuth: []

paths:
  /:
    get:
      summary: List customers
      description: Retrieve a paginated list of customers
      tags:
        - Customer Management
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: per_page
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: search
          in: query
          description: Search term for customer name, email, or phone
          schema:
            type: string
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerListResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: Create customer
      description: Create a new customer account
      tags:
        - Customer Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /{id}:
    get:
      summary: Get customer
      description: Retrieve a specific customer by ID
      tags:
        - Customer Management
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      summary: Update customer
      description: Update customer information
      tags:
        - Customer Management
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Delete customer
      description: Delete a customer account
      tags:
        - Customer Management
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: integer
      responses:
        '200':
          description: Customer deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /search:
    get:
      summary: Search customers
      description: Search customers by various criteria
      tags:
        - Customer Lookup
      parameters:
        - name: query
          in: query
          required: true
          description: Search query
          schema:
            type: string
        - name: per_page
          in: query
          description: Number of results per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
      responses:
        '200':
          description: Search results retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerListResponse'

  /lookup:
    post:
      summary: Customer lookup
      description: Look up customer by phone, email, or code
      tags:
        - Customer Lookup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  example: "+1234567890"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                code:
                  type: string
                  example: "CUST001"
      responses:
        '200':
          description: Lookup completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    oneOf:
                      - $ref: '#/components/schemas/Customer'
                      - type: 'null'
                  found:
                    type: boolean
                    example: true

  /phone/{phone}:
    get:
      summary: Get customer by phone
      description: Retrieve customer by phone number
      tags:
        - Customer Lookup
      parameters:
        - name: phone
          in: path
          required: true
          description: Customer phone number
          schema:
            type: string
      responses:
        '200':
          description: Customer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /email/{email}:
    get:
      summary: Get customer by email
      description: Retrieve customer by email address
      tags:
        - Customer Lookup
      parameters:
        - name: email
          in: path
          required: true
          description: Customer email address
          schema:
            type: string
            format: email
      responses:
        '200':
          description: Customer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /verify:
    post:
      summary: Verify customer
      description: Verify customer using OTP
      tags:
        - Customer Verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - customer_id
                - otp
              properties:
                customer_id:
                  type: integer
                  example: 1
                otp:
                  type: string
                  pattern: '^[0-9]{6}$'
                  example: "123456"
                method:
                  type: string
                  enum: [sms, email]
                  default: sms
      responses:
        '200':
          description: Verification result
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Customer verified successfully"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Customer:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        customer_code:
          type: string
          example: "CUST001"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"
        email_verified_at:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T10:30:00Z"
        phone_verified_at:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T10:30:00Z"
        created_at:
          type: string
          format: date-time
          example: "2024-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    CreateCustomerRequest:
      type: object
      required:
        - name
        - email
        - phone
      properties:
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        password:
          type: string
          format: password
          example: "securePassword123"

    UpdateCustomerRequest:
      type: object
      properties:
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"

    CustomerResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer retrieved successfully"
        data:
          $ref: '#/components/schemas/Customer'

    CustomerListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Customer'
            current_page:
              type: integer
              example: 1
            last_page:
              type: integer
              example: 10
            per_page:
              type: integer
              example: 15
            total:
              type: integer
              example: 150

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"

    ValidationErrorResponse:
      type: object
      properties:
        message:
          type: string
          example: "The given data was invalid."
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

tags:
  - name: Customer Management
    description: Core customer CRUD operations
  - name: Customer Lookup
    description: Customer search and lookup operations
  - name: Customer Verification
    description: Customer verification and OTP operations

externalDocs:
  description: Find more info about the Customer Service
  url: https://docs.cubeonebiz.com/customer-service
