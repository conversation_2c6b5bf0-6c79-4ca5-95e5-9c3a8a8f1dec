<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Customer\CustomerServiceClient;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class CachedCustomerService
{
    private const CACHE_TTL_CUSTOMER = 300; // 5 minutes
    private const CACHE_TTL_WALLET = 60;    // 1 minute
    private const CACHE_TTL_CONFIG = 3600;  // 1 hour

    public function __construct(
        private CustomerServiceClient $customerService
    ) {}

    /**
     * Get customer data with caching
     */
    public function getCustomer(int $customerId): ?array
    {
        $cacheKey = "customer:{$customerId}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL_CUSTOMER, function () use ($customerId) {
                Log::info('Fetching customer data from service', ['customer_id' => $customerId]);
                
                $customer = $this->customerService->getCustomer($customerId);
                
                if ($customer) {
                    Log::info('Customer data cached', ['customer_id' => $customerId]);
                }
                
                return $customer;
            });

        } catch (Exception $e) {
            Log::error('Failed to get customer data', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);

            // Try to get from cache even if expired
            $cachedData = Cache::get($cacheKey);
            if ($cachedData) {
                Log::info('Using expired cached customer data', ['customer_id' => $customerId]);
                return $cachedData;
            }

            throw $e;
        }
    }

    /**
     * Get customer wallet balance with caching
     */
    public function getCustomerWallet(int $customerId): ?array
    {
        $cacheKey = "customer_wallet:{$customerId}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL_WALLET, function () use ($customerId) {
                Log::info('Fetching customer wallet from service', ['customer_id' => $customerId]);
                
                $wallet = $this->customerService->getWallet($customerId);
                
                if ($wallet) {
                    Log::info('Customer wallet data cached', ['customer_id' => $customerId]);
                }
                
                return $wallet;
            });

        } catch (Exception $e) {
            Log::error('Failed to get customer wallet data', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);

            // Try to get from cache even if expired
            $cachedData = Cache::get($cacheKey);
            if ($cachedData) {
                Log::info('Using expired cached wallet data', ['customer_id' => $customerId]);
                return $cachedData;
            }

            throw $e;
        }
    }

    /**
     * Get customer by phone with caching
     */
    public function getCustomerByPhone(string $phone): ?array
    {
        $cacheKey = "customer_phone:{$phone}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL_CUSTOMER, function () use ($phone) {
                Log::info('Fetching customer by phone from service', ['phone' => $phone]);
                
                $customer = $this->customerService->getCustomerByPhone($phone);
                
                if ($customer) {
                    Log::info('Customer data cached by phone', ['phone' => $phone]);
                    
                    // Also cache by customer ID for future lookups
                    if (isset($customer['customer_id'])) {
                        Cache::put("customer:{$customer['customer_id']}", $customer, self::CACHE_TTL_CUSTOMER);
                    }
                }
                
                return $customer;
            });

        } catch (Exception $e) {
            Log::error('Failed to get customer by phone', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Invalidate customer cache
     */
    public function invalidateCustomerCache(int $customerId): void
    {
        $keys = [
            "customer:{$customerId}",
            "customer_wallet:{$customerId}"
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        Log::info('Customer cache invalidated', ['customer_id' => $customerId]);
    }

    /**
     * Invalidate customer cache by phone
     */
    public function invalidateCustomerCacheByPhone(string $phone): void
    {
        Cache::forget("customer_phone:{$phone}");
        Log::info('Customer cache invalidated by phone', ['phone' => $phone]);
    }

    /**
     * Get payment gateway configuration with caching
     */
    public function getPaymentGatewayConfig(): array
    {
        return Cache::remember('payment_gateway_config', self::CACHE_TTL_CONFIG, function () {
            Log::info('Fetching payment gateway configuration');
            
            // This would typically come from database or external service
            return [
                'razorpay' => [
                    'key_id' => config('services.razorpay.key_id'),
                    'key_secret' => config('services.razorpay.key_secret'),
                    'webhook_secret' => config('services.razorpay.webhook_secret'),
                    'enabled' => true
                ],
                'stripe' => [
                    'public_key' => config('services.stripe.public_key'),
                    'secret_key' => config('services.stripe.secret_key'),
                    'webhook_secret' => config('services.stripe.webhook_secret'),
                    'enabled' => false
                ]
            ];
        });
    }

    /**
     * Get product information with caching
     */
    public function getProduct(int $productCode): ?array
    {
        $cacheKey = "product:{$productCode}";

        return Cache::remember($cacheKey, self::CACHE_TTL_CONFIG, function () use ($productCode) {
            Log::info('Fetching product data', ['product_code' => $productCode]);
            
            // This would typically come from database or product service
            $product = \DB::table('products')
                ->where('product_code', $productCode)
                ->first();

            if ($product) {
                return (array) $product;
            }

            return null;
        });
    }

    /**
     * Warm up cache for frequently accessed data
     */
    public function warmUpCache(array $customerIds = []): void
    {
        Log::info('Warming up customer cache', ['customer_count' => count($customerIds)]);

        foreach ($customerIds as $customerId) {
            try {
                $this->getCustomer($customerId);
                $this->getCustomerWallet($customerId);
            } catch (Exception $e) {
                Log::warning('Failed to warm up cache for customer', [
                    'customer_id' => $customerId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Warm up configuration cache
        try {
            $this->getPaymentGatewayConfig();
        } catch (Exception $e) {
            Log::warning('Failed to warm up payment gateway config cache', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        // This would require Redis or another cache store that supports stats
        return [
            'cache_store' => config('cache.default'),
            'customer_cache_ttl' => self::CACHE_TTL_CUSTOMER,
            'wallet_cache_ttl' => self::CACHE_TTL_WALLET,
            'config_cache_ttl' => self::CACHE_TTL_CONFIG
        ];
    }

    /**
     * Clear all customer-related cache
     */
    public function clearAllCache(): void
    {
        // This is a simplified version - in production you'd want more sophisticated cache clearing
        Cache::flush();
        Log::info('All customer cache cleared');
    }
}
