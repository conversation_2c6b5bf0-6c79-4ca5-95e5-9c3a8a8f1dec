openapi: 3.1.0
info:
  title: Authentication Service API
  description: Comprehensive authentication and authorization service for the microservices architecture
  version: 2.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000/v2/auth
    description: Development server via Kong API Gateway
  - url: https://api.cubeonebiz.com/v2/auth
    description: Production server via Kong API Gateway

security:
  - BearerAuth: []

paths:
  /login:
    post:
      summary: User login
      description: Authenticate user with email/phone and password
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - identifier
                - password
              properties:
                identifier:
                  type: string
                  description: Email address or phone number
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  description: User password
                  example: "securePassword123"
                remember_me:
                  type: boolean
                  description: Whether to remember the user session
                  default: false
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /logout:
    post:
      summary: User logout
      description: Logout user and invalidate tokens
      tags:
        - Authentication
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /refresh-token:
    post:
      summary: Refresh access token
      description: Refresh the access token using a valid refresh token
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refresh_token
              properties:
                refresh_token:
                  type: string
                  description: Valid refresh token
                  example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          description: Invalid or expired refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user:
    get:
      summary: Get authenticated user
      description: Retrieve the authenticated user's profile information
      tags:
        - User Profile
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /mfa/request:
    post:
      summary: Request MFA OTP
      description: Request a one-time password for multi-factor authentication
      tags:
        - Multi-Factor Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - method
              properties:
                method:
                  type: string
                  enum: [email, sms]
                  description: Delivery method for OTP
                  example: "email"
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "OTP sent successfully"
                  data:
                    type: object
                    properties:
                      expires_in:
                        type: integer
                        description: OTP expiration time in seconds
                        example: 600
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /mfa/verify:
    post:
      summary: Verify MFA OTP
      description: Verify the one-time password for multi-factor authentication
      tags:
        - Multi-Factor Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - otp
              properties:
                otp:
                  type: string
                  pattern: '^[0-9]{6}$'
                  description: 6-digit OTP code
                  example: "123456"
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "OTP verified successfully"
        '400':
          description: Invalid or expired OTP
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Login successful"
        data:
          type: object
          properties:
            user:
              $ref: '#/components/schemas/User'
            access_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            refresh_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            token_type:
              type: string
              example: "Bearer"
            expires_in:
              type: integer
              example: 86400

    TokenResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Token refreshed successfully"
        data:
          type: object
          properties:
            access_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            refresh_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            token_type:
              type: string
              example: "Bearer"
            expires_in:
              type: integer
              example: 86400

    UserProfileResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "User profile retrieved successfully"
        data:
          $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        email_verified_at:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T10:30:00Z"
        created_at:
          type: string
          format: date-time
          example: "2024-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        error_code:
          type: string
          example: "AUTH_001"

    ValidationErrorResponse:
      type: object
      properties:
        message:
          type: string
          example: "The given data was invalid."
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            email: ["The email field is required."]
            password: ["The password field is required."]

tags:
  - name: Authentication
    description: User authentication operations
  - name: Multi-Factor Authentication
    description: MFA operations for enhanced security
  - name: User Profile
    description: User profile management

externalDocs:
  description: Find more info about the Authentication Service
  url: https://docs.cubeonebiz.com/auth-service
