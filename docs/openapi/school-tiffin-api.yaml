openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - School Tiffin API
  description: |
    Comprehensive API specification for the OneFoodDialer 2025 school tiffin meal subscription system.
    This API provides endpoints for managing parent-child relationships, meal plans, subscriptions, and delivery coordination.
    
    ## Authentication
    All endpoints require JWT authentication via the `Authorization: Bearer <token>` header.
    
    ## Rate Limiting
    - Parent Mobile App: 100 requests/minute, 2000 requests/hour
    - School Admin Web: 150 requests/minute, 3000 requests/hour  
    - Delivery Mobile App: 80 requests/minute, 1500 requests/hour
    
    ## Base URLs
    - Production: https://api.onefooddialer.com
    - Staging: https://staging-api.onefooddialer.com
    - Development: http://localhost:8000
  version: 2.0.0
  contact:
    name: OneFoodDialer API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: Proprietary
    url: https://onefooddialer.com/license

servers:
  - url: https://api.onefooddialer.com
    description: Production server
  - url: https://staging-api.onefooddialer.com
    description: Staging server
  - url: http://localhost:8000
    description: Development server

security:
  - BearerAuth: []

paths:
  # Parent Management Endpoints
  /v2/parents:
    get:
      tags:
        - Parent Management
      summary: List parents
      description: Retrieve a paginated list of parent customers with filtering options
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
            maximum: 100
        - name: search
          in: query
          schema:
            type: string
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended]
      responses:
        '200':
          description: List of parents retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParentListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
    
    post:
      tags:
        - Parent Management
      summary: Register new parent
      description: Register a new parent customer with profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateParentRequest'
      responses:
        '201':
          description: Parent registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /v2/parents/{parentId}/children:
    get:
      tags:
        - Parent Management
      summary: Get parent's children
      description: Retrieve all children associated with a parent
      parameters:
        - name: parentId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Children retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChildrenListResponse'
        '404':
          $ref: '#/components/responses/NotFound'
    
    post:
      tags:
        - Parent Management
      summary: Add child to parent
      description: Add a new child profile to a parent account
      parameters:
        - name: parentId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddChildRequest'
      responses:
        '201':
          description: Child added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChildProfileResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '422':
          $ref: '#/components/responses/ValidationError'

  # Meal Plan Endpoints
  /v2/meal-plans:
    get:
      tags:
        - Meal Plans
      summary: List meal plans
      description: Retrieve available meal plans with filtering options
      parameters:
        - name: school_id
          in: query
          schema:
            type: integer
        - name: dietary_preferences
          in: query
          schema:
            type: array
            items:
              type: string
        - name: price_min
          in: query
          schema:
            type: number
        - name: price_max
          in: query
          schema:
            type: number
        - name: available_only
          in: query
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Meal plans retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MealPlanListResponse'
    
    post:
      tags:
        - Meal Plans
      summary: Create meal plan
      description: Create a new meal plan for a school
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMealPlanRequest'
      responses:
        '201':
          description: Meal plan created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MealPlanResponse'

  /v2/meal-plans/{id}:
    get:
      tags:
        - Meal Plans
      summary: Get meal plan details
      description: Retrieve detailed information about a specific meal plan
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Meal plan details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MealPlanResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  # School Meal Subscription Endpoints
  /v2/school-meal-subscriptions:
    get:
      tags:
        - School Meal Subscriptions
      summary: List subscriptions
      description: Retrieve school meal subscriptions with filtering
      parameters:
        - name: parent_customer_id
          in: query
          schema:
            type: integer
        - name: child_profile_id
          in: query
          schema:
            type: integer
        - name: status
          in: query
          schema:
            type: string
            enum: [active, paused, cancelled, expired]
        - name: school_id
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: Subscriptions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionListResponse'
    
    post:
      tags:
        - School Meal Subscriptions
      summary: Create subscription
      description: Create a new school meal subscription for a child
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubscriptionRequest'
      responses:
        '201':
          description: Subscription created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionResponse'

  /v2/school-meal-subscriptions/{id}/pause:
    put:
      tags:
        - School Meal Subscriptions
      summary: Pause subscription
      description: Temporarily pause a school meal subscription
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                pause_reason:
                  type: string
                  maxLength: 500
                pause_until:
                  type: string
                  format: date
                  description: Date when subscription should resume
      responses:
        '200':
          description: Subscription paused successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionResponse'

  # School Delivery Endpoints
  /v2/school/batches:
    get:
      tags:
        - School Delivery
      summary: List delivery batches
      description: Retrieve delivery batches with filtering options
      parameters:
        - name: school_id
          in: query
          schema:
            type: integer
        - name: delivery_date
          in: query
          schema:
            type: string
            format: date
        - name: break_time_slot
          in: query
          schema:
            type: string
            enum: [morning_break, lunch_break, afternoon_break]
        - name: status
          in: query
          schema:
            type: string
            enum: [scheduled, preparing, ready, dispatched, in_transit, delivered, failed, cancelled]
      responses:
        '200':
          description: Delivery batches retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryBatchListResponse'
    
    post:
      tags:
        - School Delivery
      summary: Create delivery batch
      description: Create a new delivery batch for a school
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDeliveryBatchRequest'
      responses:
        '201':
          description: Delivery batch created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryBatchResponse'

  /v2/school/batches/{id}/status:
    put:
      tags:
        - School Delivery
      summary: Update batch status
      description: Update the status of a delivery batch with location and quality data
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDeliveryStatusRequest'
      responses:
        '200':
          description: Batch status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryBatchResponse'

  /v2/school/schools/{schoolId}/schedule:
    get:
      tags:
        - School Delivery
      summary: Get school delivery schedule
      description: Retrieve delivery schedule for a school on a specific date
      parameters:
        - name: schoolId
          in: path
          required: true
          schema:
            type: integer
        - name: date
          in: query
          required: true
          schema:
            type: string
            format: date
      responses:
        '200':
          description: School delivery schedule retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchoolScheduleResponse'

  /v2/school/performance-metrics:
    get:
      tags:
        - School Delivery
      summary: Get delivery performance metrics
      description: Retrieve comprehensive delivery performance analytics
      parameters:
        - name: school_id
          in: query
          schema:
            type: integer
        - name: delivery_person_id
          in: query
          schema:
            type: integer
        - name: start_date
          in: query
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Performance metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetricsResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the authentication service

  schemas:
    # Common schemas
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
            api_version:
              type: string
              example: "v2"

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        last_page:
          type: integer
        from:
          type: integer
        to:
          type: integer

    # Parent Management schemas
    CreateParentRequest:
      type: object
      required:
        - full_name
        - email
        - phone
        - password
      properties:
        full_name:
          type: string
          maxLength: 100
        email:
          type: string
          format: email
          maxLength: 100
        phone:
          type: string
          pattern: '^[6-9]\d{9}$'
        password:
          type: string
          minLength: 8
        address:
          type: string
          maxLength: 500
        emergency_contact_name:
          type: string
          maxLength: 100
        emergency_contact_phone:
          type: string
          pattern: '^[6-9]\d{9}$'

    ParentResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                id:
                  type: integer
                customer_code:
                  type: string
                full_name:
                  type: string
                email:
                  type: string
                phone:
                  type: string
                status:
                  type: string
                  enum: [active, inactive, suspended]
                total_children:
                  type: integer
                active_subscriptions:
                  type: integer
                created_at:
                  type: string
                  format: date-time

    ParentListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ParentResponse/properties/data'
            meta:
              allOf:
                - $ref: '#/components/schemas/PaginationMeta'
                - type: object
                  properties:
                    filters_applied:
                      type: object

    # Child Profile schemas
    AddChildRequest:
      type: object
      required:
        - full_name
        - school_id
        - grade_level
        - date_of_birth
      properties:
        full_name:
          type: string
          maxLength: 100
        school_id:
          type: integer
        grade_level:
          type: string
          maxLength: 20
        grade_section:
          type: string
          maxLength: 10
        roll_number:
          type: string
          maxLength: 20
        date_of_birth:
          type: string
          format: date
        dietary_restrictions:
          type: array
          items:
            type: string
            enum: [nuts, dairy, gluten, eggs, soy, fish, shellfish, sesame, mustard, celery, lupin, molluscs, sulphites]
        medical_conditions:
          type: array
          items:
            type: string
        emergency_contact_relationship:
          type: string
          maxLength: 50

    ChildProfileResponse:
      type: object
      properties:
        id:
          type: integer
        full_name:
          type: string
        school_name:
          type: string
        grade_level:
          type: string
        grade_section:
          type: string
        roll_number:
          type: string
        date_of_birth:
          type: string
          format: date
        age:
          type: integer
        dietary_restrictions:
          type: array
          items:
            type: string
        active_subscriptions:
          type: integer
        created_at:
          type: string
          format: date-time

    ChildrenListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ChildProfileResponse'

    # Meal Plan schemas
    CreateMealPlanRequest:
      type: object
      required:
        - school_id
        - plan_name
        - meal_type
        - meal_components
        - pricing_tiers
      properties:
        school_id:
          type: integer
        plan_name:
          type: string
          maxLength: 100
        description:
          type: string
          maxLength: 500
        meal_type:
          type: string
          enum: [breakfast, lunch, snack, dinner, combo]
        meal_components:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
              nutritional_info:
                type: object
        pricing_tiers:
          type: object
          properties:
            daily:
              type: number
            weekly:
              type: number
            monthly:
              type: number
            quarterly:
              type: number
        available_days:
          type: array
          items:
            type: string
            enum: [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
        dietary_accommodations:
          type: array
          items:
            type: string

    MealPlanResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                id:
                  type: integer
                school_id:
                  type: integer
                school_name:
                  type: string
                plan_name:
                  type: string
                description:
                  type: string
                meal_type:
                  type: string
                meal_components:
                  type: array
                  items:
                    type: object
                pricing_tiers:
                  type: object
                available_days:
                  type: array
                  items:
                    type: string
                dietary_accommodations:
                  type: array
                  items:
                    type: string
                is_available:
                  type: boolean
                created_at:
                  type: string
                  format: date-time

    MealPlanListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/MealPlanResponse/properties/data'

    # Subscription schemas
    CreateSubscriptionRequest:
      type: object
      required:
        - parent_customer_id
        - child_profile_id
        - meal_plan_id
        - start_date
        - end_date
        - subscription_type
        - billing_cycle
        - delivery_days
        - preferred_break_time
      properties:
        parent_customer_id:
          type: integer
        child_profile_id:
          type: integer
        meal_plan_id:
          type: integer
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        subscription_type:
          type: string
          enum: [daily, weekly, monthly, quarterly, annual]
        billing_cycle:
          type: string
          enum: [daily, weekly, monthly, quarterly]
        auto_renew:
          type: boolean
          default: true
        delivery_days:
          type: array
          items:
            type: string
            enum: [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
        preferred_break_time:
          type: string
          enum: [morning_break, lunch_break, both]
        meal_customizations:
          type: object
          properties:
            portion_size:
              type: string
              enum: [small, regular, large]
            extra_items:
              type: array
              items:
                type: string
        dietary_accommodations:
          type: array
          items:
            type: string
        spice_level:
          type: string
          enum: [no_spice, mild, medium, spicy]
        special_notes:
          type: string
          maxLength: 1000

    SubscriptionResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                id:
                  type: integer
                parent_customer_id:
                  type: integer
                child_profile_id:
                  type: integer
                child_name:
                  type: string
                meal_plan_id:
                  type: integer
                meal_plan_name:
                  type: string
                school_name:
                  type: string
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
                status:
                  type: string
                  enum: [active, paused, cancelled, expired]
                subscription_type:
                  type: string
                billing_cycle:
                  type: string
                daily_rate:
                  type: number
                total_amount:
                  type: number
                delivery_days:
                  type: array
                  items:
                    type: string
                preferred_break_time:
                  type: string
                next_billing_date:
                  type: string
                  format: date
                created_at:
                  type: string
                  format: date-time

    SubscriptionListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/SubscriptionResponse/properties/data'

    # Delivery schemas
    CreateDeliveryBatchRequest:
      type: object
      required:
        - school_id
        - delivery_date
        - break_time_slot
      properties:
        school_id:
          type: integer
        delivery_date:
          type: string
          format: date
        break_time_slot:
          type: string
          enum: [morning_break, lunch_break, afternoon_break]
        delivery_person_id:
          type: integer
        vehicle_number:
          type: string
          pattern: '^[A-Z]{2}[0-9]{2}[A-Z]{1,2}[0-9]{4}$'
        special_instructions:
          type: array
          items:
            type: string
        requires_refrigeration:
          type: boolean
        estimated_distance_km:
          type: number
        estimated_duration_minutes:
          type: integer

    UpdateDeliveryStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [scheduled, preparing, ready, dispatched, in_transit, delivered, failed, cancelled]
        status_notes:
          type: string
          maxLength: 500
        location_data:
          type: object
          properties:
            latitude:
              type: number
              minimum: -90
              maximum: 90
            longitude:
              type: number
              minimum: -180
              maximum: 180
            address:
              type: string
            accuracy:
              type: number
        temperature_reading:
          type: number
          minimum: -10
          maximum: 100
        quality_check_passed:
          type: boolean
        received_by_name:
          type: string
          maxLength: 100
        received_by_phone:
          type: string
          pattern: '^[6-9]\d{9}$'
        school_rating:
          type: number
          minimum: 1
          maximum: 5

    DeliveryBatchResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                id:
                  type: integer
                batch_number:
                  type: string
                school_id:
                  type: integer
                school_name:
                  type: string
                delivery_date:
                  type: string
                  format: date
                break_time_slot:
                  type: string
                scheduled_delivery_time:
                  type: string
                  format: time
                actual_delivery_time:
                  type: string
                  format: time
                status:
                  type: string
                total_meals:
                  type: integer
                total_children:
                  type: integer
                delivery_person_name:
                  type: string
                vehicle_number:
                  type: string
                on_time_delivery:
                  type: boolean
                school_rating:
                  type: number
                created_at:
                  type: string
                  format: date-time

    DeliveryBatchListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/DeliveryBatchResponse/properties/data'

    SchoolScheduleResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                school:
                  type: object
                  properties:
                    id:
                      type: integer
                    school_name:
                      type: string
                    break_times:
                      type: object
                date:
                  type: string
                  format: date
                schedule:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      break_time:
                        type: string
                      delivery_window:
                        type: object
                        properties:
                          start:
                            type: string
                          end:
                            type: string
                      batch:
                        $ref: '#/components/schemas/DeliveryBatchResponse/properties/data'
                      total_meals:
                        type: integer
                      total_children:
                        type: integer
                      status:
                        type: string

    PerformanceMetricsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total_batches:
                  type: integer
                completed_batches:
                  type: integer
                on_time_deliveries:
                  type: integer
                average_delivery_time:
                  type: number
                total_meals_delivered:
                  type: integer
                total_children_served:
                  type: integer
                average_school_rating:
                  type: number
                status_breakdown:
                  type: object
                  additionalProperties:
                    type: integer
                performance_by_date:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      total_batches:
                        type: integer
                      on_time_rate:
                        type: number
                      average_rating:
                        type: number

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Invalid request parameters"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Authentication required"

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Access denied"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Resource not found"

    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validation failed"
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Internal server error"

tags:
  - name: Parent Management
    description: Operations for managing parent customers and child profiles
  - name: Meal Plans
    description: Operations for managing school meal plans
  - name: School Meal Subscriptions
    description: Operations for managing school meal subscriptions
  - name: School Delivery
    description: Operations for managing school delivery coordination
