import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id]Items } from '@/components/catalogue-service-v12/[id]/items';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]Items', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id]Items />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]Items')).toBeInTheDocument();
  });
});