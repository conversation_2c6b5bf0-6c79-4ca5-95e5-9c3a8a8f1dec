<!--div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addloc" class="common-orange-btn-on-hover"> Add Location</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updloc" class="common-orange-btn-on-hover">Update Location details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactloc" class="common-orange-btn-on-hover">Deactivate Location</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div-->
      <!-- <PERSON><PERSON> PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Content Management</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'cms','add')){  ?>
                <!--div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('cms', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Website Page</button>
                    </div-->
                  <?php } ?>
                </li>
               
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th width="40%">Page</th>
                            <th width="30%">URI Name</th>
                            <th width="20%">Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {

	/* myPageTable.init();
	$("#customer").dataTable().fnDestroy(); */

	$('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
    	 //"aoColumns":aoColumns,
        "ajax": "/cms/ajx-cms",
        "aoColumnDefs": [
        	                {
        	                   bSortable: false,
        	                   aTargets: [ -1,-2 ]
        	                }
        	              ],
    });
    

});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addloc",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add location");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updloc",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-intro", "Click here to edit location details");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactloc",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-intro", "Click here to deactivate location.");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-intro");
    });
</script>  