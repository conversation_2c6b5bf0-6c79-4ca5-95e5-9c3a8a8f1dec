# Keycloak Integration Documentation

This document outlines the integration of Keycloak (OneSso) authentication into the existing legacy authentication system.

## Overview

The integration allows users to authenticate using either the legacy username/password method or the Keycloak single sign-on method. The system is designed to maintain backward compatibility while providing a path forward to modern authentication.

## Authentication Modes

The system supports three authentication modes:

1. **Legacy** - Only the traditional username/password authentication is available
2. **Keycloak** - Only Keycloak authentication is available
3. **Both** - Both authentication methods are available, giving users a choice

The authentication mode is controlled by the `auth_mode` setting in the database.

## Components

### 1. Backend Services

#### KeycloakClient

The `KeycloakClient` service handles communication with the Keycloak server:

- `getAuthUrl()`: Builds the authorization URL for redirecting to Keycloak
- `getTokens()`: Exchanges authorization code for tokens
- `refreshTokens()`: Refreshes expired tokens
- `getUserInfo()`: Gets user information from Keycloak
- `logout()`: Logs out from Keycloak
- `validateToken()`: Validates a token
- `isTokenExpired()`: Checks if a token is expired

#### OnessoUserService

The `OnessoUserService` manages OneSso user data:

- `getOnessoUserByUserId()`: Gets OneSso user by user ID
- `getOnessoUserByKeycloakId()`: Gets OneSso user by Keycloak ID
- `saveOnessoUser()`: Saves OneSso user data
- `saveTokens()`: Saves tokens for a user
- `getTokens()`: Gets tokens for a user
- `clearTokens()`: Clears tokens for a user

#### ConfigService

The `ConfigService` manages application configuration:

- `getConfig()`: Gets configuration value
- `setConfig()`: Sets configuration value

### 2. Database Schema

#### OneSso Users Table

The `onesso_users` table stores Keycloak user data:

```sql
CREATE TABLE IF NOT EXISTS onesso_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    user_id INTEGER NOT NULL,
    keycloak_id TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_expiry TIMESTAMP,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### Settings Table

The `settings` table includes the `auth_mode` setting:

```sql
INSERT INTO settings (setting_name, setting_value) VALUES ('auth_mode', 'legacy');
```

### 3. Authentication Flow

#### Legacy Authentication

1. User enters username and password on the login form
2. System validates credentials against the local database
3. If valid, user is logged in and redirected to the dashboard

#### Keycloak Authentication

1. User clicks the "Sign In with OneSso" button
2. System redirects to Keycloak login page
3. User authenticates with Keycloak
4. Keycloak redirects back to the application with an authorization code
5. System exchanges the code for tokens
6. System retrieves user information from Keycloak
7. System finds or creates a local user account
8. User is logged in and redirected to the dashboard

### 4. User Interface

#### Login Page

The login page displays different options based on the authentication mode:

- **Legacy**: Only shows the username/password form
- **Keycloak**: Only shows the "Sign In with OneSso" button
- **Both**: Shows both options

#### Keycloak Login Page

The Keycloak login page is styled to match the application's design.

#### Dashboard

The dashboard displays user information, including authentication method and Keycloak-specific information for users who log in with Keycloak.

## Configuration

### Keycloak Configuration

The Keycloak configuration is stored in `config/autoload/global.php`:

```php
'keycloak' => array(
    'auth_server_url' => 'http://localhost:8080/auth',
    'realm' => 'tenant',
    'client_id' => 'tenant-app',
    'client_secret' => 'your-client-secret',
    'redirect_uri' => $rootUrl.'keycloak-callback',
),
```

### Authentication Mode

The authentication mode can be changed by updating the `auth_mode` setting in the database:

```sql
UPDATE settings SET setting_value = 'legacy' WHERE setting_name = 'auth_mode';
UPDATE settings SET setting_value = 'keycloak' WHERE setting_name = 'auth_mode';
UPDATE settings SET setting_value = 'both' WHERE setting_name = 'auth_mode';
```

## Testing

A standalone PHP implementation is available for testing the authentication flow:

- `login.php`: A simple login page with both legacy and Keycloak authentication options
- `keycloak_login.php`: A simulation of the Keycloak login page
- `keycloak_callback.php`: A simulation of the Keycloak callback
- `dashboard.php`: A simple dashboard page to display user information
- `logout.php`: A simple logout page

## Next Steps

1. Set up a real Keycloak server for testing
2. Implement token refresh mechanism
3. Add proper error handling
4. Create user migration scripts
5. Add monitoring for auth-related metrics
